#!/bin/bash

source ./check/commit/common.sh

function changeLines() {
  TOTAL_LINE=$(
    git diff "$1".."$2" --numstat |
      grep -i -v "\.xsd$" |
      grep -i -v "CODEOWNERS$" |
      grep -v "\.gitleaks.toml" |
      awk -F " " \
        '{
            add += $1;
            subs += $2;
            loc += $1 + $2;
         } END {
            print loc;
         }'
  )
  echo "$TOTAL_LINE"
}

SINGLE_COMMIT_LINE_LIMIT=80000
ARRAY_MASTER_HEAD_TO_CURRENT_COMMIT=($(echo "$MASTER_HEAD_TO_CURRENT_COMMIT" | awk '{ print $0; }'))

printf "%-40s  %-40s  %-40s\n" 'Source' 'Target' 'Change lines'
for ((i = 0; i < 10000; i++)); do
  if [[ $i -ge $FRONT_REMOTE_MASTER_COUNT ]]; then
    break
  fi

  CHANGE_LINES=$(changeLines "${ARRAY_MASTER_HEAD_TO_CURRENT_COMMIT[$i + 1]}" "${ARRAY_MASTER_HEAD_TO_CURRENT_COMMIT[$i]}")
  printf "%-40s  %-40s  %d\n" "${ARRAY_MASTER_HEAD_TO_CURRENT_COMMIT[$i + 1]}" "${ARRAY_MASTER_HEAD_TO_CURRENT_COMMIT[$i]}" "$CHANGE_LINES"
  if [[ $CHANGE_LINES -gt $SINGLE_COMMIT_LINE_LIMIT ]]; then
    echo "ERROR: The number of total lines of one commit exceeds the maximum limit of $SINGLE_COMMIT_LINE_LIMIT."
    exit 1
  fi
done

TOTAL_LINE=$(changeLines "$REMOTE_MASTER_COMMIT_ID" "$CURRENT_COMMIT_ID")
printf "Sum of change lines in this MR is %d.\n" "$TOTAL_LINE"
if [[ $TOTAL_LINE -lt 1 ]]; then
  echo "ERROR: It is not allowed to commit empty."
  exit 1
fi

# Check total lines of MR in a single time
TOTAL_LINE_LIMIT=80000
if [[ $TOTAL_LINE -gt $TOTAL_LINE_LIMIT ]]; then
  echo "ERROR: The number of total lines of CR in a single time exceeds the maximum limit of $TOTAL_LINE_LIMIT."
  exit 1
fi

exit 0
