package com.bees360.image.config;

import com.bees360.image.ImageApiManager;
import com.bees360.image.ImageGroupManager;
import com.bees360.image.ImageManager;
import com.bees360.image.ImageMetadataProvider;
import com.bees360.image.ImageResourceProvider;
import com.bees360.image.ImageSource;
import com.bees360.image.ImageTagProvider;
import com.bees360.image.JooqImageGroupManager;
import com.bees360.image.Message.ImageMessage.Resource.Type;
import com.bees360.image.util.ImageCreateExceptionHandleManager;
import com.bees360.image.util.ImageFillResourceManager;
import com.bees360.image.util.ImageFillTagManager;
import com.bees360.image.util.ImageGroupFillImageManager;
import com.bees360.util.Iterables;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Config class
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class Config {

    @Bean
    public ImageMetadataProvider grpcImageMetadataProvider(
            ImageMetadataProvider jooqImageMetadataRepository) {
        return jooqImageMetadataRepository;
    }

    @Bean
    public ImageResourceProvider grpcImageResourceProvider(
            ImageResourceProvider jooqImageResourceRepository) {
        return jooqImageResourceRepository;
    }

    @Bean
    ImageMetadataProvider jooqImageMetadataRepository(
            ImageMetadataProvider jooqImageMetadataManager) {
        return jooqImageMetadataManager;
    }

    @Bean
    ImageResourceProvider jooqImageResourceRepository(
            ImageResourceProvider jooqImageResourceManager) {
        return jooqImageResourceManager;
    }

    @Bean
    Supplier<Instant> currentTimeProvider(DSLContext dsl) {
        return () -> dsl.select(DSL.currentTimestamp()).fetchOneInto(Timestamp.class).toInstant();
    }

    @Bean
    BiFunction<
                    Iterable<? extends ImageSource>,
                    IllegalArgumentException,
                    Iterable<? extends ImageSource>>
            imageFutureShootingTimeToNullAmender(Supplier<Instant> currentTimeProvider) {
        return (imageSources, e) -> {
            var currentTime = currentTimeProvider.get();
            return Iterables.transform(
                    imageSources,
                    imageSource -> {
                        var shootingTime = imageSource.getShootingTime();
                        if (shootingTime == null) {
                            return imageSource;
                        }
                        if (shootingTime.isAfter(currentTime)) {
                            shootingTime = null;
                        }
                        return ImageSource.of(
                                imageSource.getGetUrl(),
                                imageSource.getContentMD5(),
                                imageSource.getCategory(),
                                shootingTime,
                                imageSource.getId());
                    });
        };
    }

    @Bean
    ImageManager imageAmendAbnormalParameterManager(
            ImageManager jooqImageManager,
            BiFunction<
                            Iterable<? extends ImageSource>,
                            IllegalArgumentException,
                            Iterable<? extends ImageSource>>
                    imageFutureShootingTimeToNullAmender) {
        return new ImageCreateExceptionHandleManager(
                jooqImageManager, imageFutureShootingTimeToNullAmender);
    }

    @Bean
    ImageManager grpcImageServiceManager(
            ImageManager imageAmendAbnormalParameterManager,
            ImageResourceProvider jooqImageResourceRepository) {
        return new ImageFillResourceManager(
                imageAmendAbnormalParameterManager, jooqImageResourceRepository);
    }

    @Bean
    ImageGroupManager grpcImageGroupServiceManager(
            DSLContext dslContext,
            ImageManager imageAmendAbnormalParameterManager,
            ImageManager grpcImageServiceManager,
            ImageTagProvider jooqImageTagManager) {
        return new ImageGroupFillImageManager(
                new JooqImageGroupManager(dslContext, imageAmendAbnormalParameterManager),
                new ImageFillTagManager(grpcImageServiceManager, jooqImageTagManager));
    }

    @Bean
    public Function<String, String[]> imageResourceKeyParser() {
        return resourceKey -> {
            var param = resourceKey.split("/");
            if (param.length != 3 || !StringUtils.isNumeric(param[2])) {
                throw new IllegalStateException(
                        String.format("Image url format error. '%s'", resourceKey));
            }
            String imageId = param[1];
            Type type = Type.forNumber(Integer.parseInt(param[2]));
            return new String[] {imageId, type.name()};
        };
    }

    @Bean
    ImageApiManager grpcImageApiServiceManager(ImageApiManager jooqImageApiManager) {
        return jooqImageApiManager;
    }
}
