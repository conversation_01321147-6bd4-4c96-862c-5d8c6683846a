package com.bees360.image.config;

import com.bees360.image.ImageTypeDefinition;
import com.bees360.image.Message.ImageMessage.Resource.Type;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@ConfigurationProperties("image.resource")
@Data
public class ImageTypeDefinitionConfig implements ImageTypeDefinition {

    private Map<Type, TypeConfig> types;

    @Override
    public Iterable<Type> getAllTypes() {
        return types.keySet();
    }

    @Override
    public int getSideLimit(Type type) {
        return types.get(type).sideLimit;
    }

    @Override
    public int getQuality(Type type) {
        return types.get(type).quality;
    }

    @Data
    static class TypeConfig {
        int quality;
        int sideLimit;
    }
}
