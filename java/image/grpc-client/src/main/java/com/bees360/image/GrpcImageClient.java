package com.bees360.image;

import com.bees360.api.InvalidArgumentException;
import com.bees360.grpc.GrpcApi;
import com.bees360.image.ImageServiceGrpc.ImageServiceBlockingStub;
import com.bees360.image.ImageServiceGrpc.ImageServiceStub;
import com.bees360.image.Message.CreateImageRequest;
import com.bees360.image.Message.ImageMessage;
import com.bees360.image.Message.UpdateImageRequest;
import com.bees360.image.grpc.ProtobufImage;
import com.bees360.user.Message.UserMessage;
import com.bees360.util.Iterables;
import com.bees360.util.ListenableFutures;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Log4j2
public class GrpcImageClient implements ImageManager {

    private final GrpcApi<ImageServiceBlockingStub> grpcApi;

    private final ImageServiceStub asyncApi;

    public GrpcImageClient(GrpcApi<ImageServiceBlockingStub> grpcApi, ImageServiceStub asyncApi) {
        this.grpcApi = grpcApi;
        this.asyncApi = asyncApi;

        log.info("Created '{}(blockingStub={})'", this, this.grpcApi);
    }

    @Override
    public Iterable<? extends Image> getImages(Iterable<String> ids) {
        var future = new CompletableFuture<List<? extends Image>>();
        var responseObserver =
                asyncApi.get(
                        new StreamObserver<>() {
                            final List<Image> images = new ArrayList<>();

                            @Override
                            public void onNext(ImageMessage imageMessage) {
                                var image =
                                        !imageMessage.isInitialized()
                                                        || imageMessage.equals(
                                                                ImageMessage.getDefaultInstance())
                                                ? null
                                                : new ProtobufImage(imageMessage);
                                if (image != null) {
                                    images.add(image);
                                }
                            }

                            @Override
                            public void onError(Throwable t) {
                                future.completeExceptionally(GrpcApi.translateException(t));
                            }

                            @Override
                            public void onCompleted() {
                                future.complete(images);
                            }
                        });

        ids.forEach(id -> responseObserver.onNext(StringValue.of(id)));
        responseObserver.onCompleted();
        return ListenableFutures.getUnchecked(future);
    }

    @Override
    public Iterable<? extends Image> createImages(
            @NonNull Iterable<? extends ImageSource> imageSources, String createdBy) {
        var sources = Iterables.transform(imageSources, ImageSource::toMessage);
        var createMessage =
                CreateImageRequest.newBuilder()
                        .addAllSource(sources)
                        .setCreatedBy(UserMessage.newBuilder().setId(createdBy).build())
                        .build();
        // TODO zhaoshoushan
        // 目前grpc的异常处理存在问题，会将IllegalArgumentException转换成了InvalidArgumentException，问题解决后这里应该去掉try
        // cache
        try {
            var imageMessages = grpcApi.apply(api -> api.create(createMessage));
            return grpcApi.get(
                    () -> Iterables.transformAndToList(imageMessages, ProtobufImage::new));
        } catch (InvalidArgumentException e) {
            throw new IllegalArgumentException(e.getMessage(), e.getCause());
        }
    }

    @Override
    public void updateTiffOrientation(
            @NonNull String imageId, int tiffOrientation, String updatedBy) {
        var request =
                UpdateImageRequest.newBuilder()
                        .setImageId(imageId)
                        .setTiffOrientation(tiffOrientation)
                        .setUpdatedBy(UserMessage.newBuilder().setId(updatedBy).build())
                        .build();
        grpcApi.apply(api -> api.setTiffOrientation(request));
    }
}
