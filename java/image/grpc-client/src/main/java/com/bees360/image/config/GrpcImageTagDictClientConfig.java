package com.bees360.image.config;

import com.bees360.grpc.GrpcApi;
import com.bees360.image.tag.GrpcImageTagDictClient;
import com.bees360.tag.ImageTagManagerGrpc.ImageTagManagerBlockingStub;
import com.bees360.tag.ImageTagManagerGrpc.ImageTagManagerStub;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrpcImageTagDictClientConfig {

    @GrpcClient("imageManager")
    private ImageTagManagerBlockingStub imageTagManagerBlockingStub;

    @GrpcClient("imageManager")
    private ImageTagManagerStub imageTagDictManagerStub;

    @Bean
    public GrpcImageTagDictClient grpcImageTagDIctClient() {
        return new GrpcImageTagDictClient(
                GrpcApi.of(imageTagManagerBlockingStub), imageTagDictManagerStub);
    }
}
