package com.bees360.image;

import static com.bees360.jooq.persistent.image.Tables.IMAGE;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_ANNOTATION;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_GROUP;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_METADATA;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_NOTE;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_TAG_RELATION;

import static org.jooq.impl.DSL.inline;
import static org.jooq.impl.DSL.insertInto;
import static org.jooq.impl.DSL.select;
import static org.jooq.impl.DSL.val;

import com.bees360.api.common.Gps;
import com.bees360.api.common.Point;
import com.bees360.util.SecureTokens;
import com.google.common.base.Preconditions;

import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record12;
import org.jooq.Record4;
import org.jooq.Record5;
import org.jooq.Record7;
import org.jooq.Record8;
import org.jooq.Row6;
import org.jooq.Select;
import org.jooq.impl.DSL;
import org.springframework.dao.DuplicateKeyException;

import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class JooqImageCopyManager implements ImageCopyManager {
    private final DSLContext dsl;

    public JooqImageCopyManager(DSLContext dsl) {
        this.dsl = dsl;
    }

    @Override
    public List<String> copyImage(Iterable<? extends ImageCopyRequest> request, String operator) {
        Preconditions.checkArgument(
                request != null && request.iterator().hasNext(),
                "Cannot copy image: must provider at least one copy request");
        Select<Record7<String, Integer, String, String, String, String, Instant>> selectImage =
                null;
        Select<Record8<String, Gps, Float, Short, Float, Double, Double, Double>>
                selectImageMetadata = null;
        Select<Record5<String, String, String, Boolean, JSONB>> selectImageTag = null;
        Select<
                        Record12<
                                String,
                                Point[],
                                String,
                                String,
                                String,
                                String,
                                Integer,
                                Integer,
                                String,
                                String,
                                String,
                                JSONB>>
                selectImageAnnotation = null;

        Set<Row6<String, String, String, Double, String, String>> imageGroupSet = new HashSet<>();

        Select<Record4<String, String, String, String>> selectImageNote = null;
        var sequenceNo = Double.MIN_VALUE + 1.0;
        for (ImageCopyRequest r : request) {
            var originalImageId = r.getOriginalImageId();
            var newImageId = r.getNewImageId();
            if (StringUtils.isBlank(newImageId)) {
                newImageId = SecureTokens.generateRandomBase64Token();
            } else {
                Preconditions.checkArgument(
                        newImageId.length() == 32,
                        String.format(
                                "Copy image failed, image id \"%s\" must be valid", newImageId));
            }
            // get image info from original image
            var selectFromImage =
                    select(
                                    val(newImageId),
                                    IMAGE.TIFF_ORIENTATION,
                                    IMAGE.RESOURCE_URL,
                                    IMAGE.CATEGORY,
                                    IMAGE.CREATED_BY,
                                    IMAGE.UPDATED_BY,
                                    IMAGE.SHOOTING_TIME)
                            .from(IMAGE)
                            .where(IMAGE.ID.eq(originalImageId));
            selectImage =
                    selectImage == null ? selectFromImage : selectImage.unionAll(selectFromImage);

            // get image metadata info from original image
            var selectMetadataForImage =
                    select(
                                    val(newImageId),
                                    IMAGE_METADATA.GPS,
                                    IMAGE_METADATA.GPS_ALTITUDE,
                                    IMAGE_METADATA.FOCAL_LENGTH,
                                    IMAGE_METADATA.RELATIVE_ALTITUDE,
                                    IMAGE_METADATA.GIMBAL_ROLL,
                                    IMAGE_METADATA.GIMBAL_YAW,
                                    IMAGE_METADATA.GIMBAL_PITCH)
                            .from(IMAGE_METADATA)
                            .where(IMAGE_METADATA.IMAGE_ID.eq(originalImageId));
            selectImageMetadata =
                    selectImageMetadata == null
                            ? selectMetadataForImage
                            : selectImageMetadata.unionAll(selectMetadataForImage);

            // get image tag info from original image
            var selectTagFromImage =
                    select(
                                    val(newImageId),
                                    IMAGE_TAG_RELATION.TAG_ID,
                                    val(operator),
                                    IMAGE_TAG_RELATION.CREATED_BY_AI,
                                    IMAGE_TAG_RELATION.ATTRIBUTE)
                            .from(IMAGE_TAG_RELATION)
                            .where(
                                    IMAGE_TAG_RELATION
                                            .IMAGE_ID
                                            .eq(originalImageId)
                                            .and(IMAGE_TAG_RELATION.IS_DELETED.eq(0L)));
            selectImageTag =
                    selectImageTag == null
                            ? selectTagFromImage
                            : selectImageTag.unionAll(selectTagFromImage);

            // get image annotation info from original image
            var selectAnnotationFromImage =
                    select(
                                    IMAGE_ANNOTATION.IMAGE_TAG_ID,
                                    IMAGE_ANNOTATION.POLYGON,
                                    IMAGE_ANNOTATION.DESCRIPTION,
                                    val(operator),
                                    val(operator),
                                    IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID,
                                    IMAGE_ANNOTATION.SEQ_NO,
                                    IMAGE_ANNOTATION.SOURCE_TYPE,
                                    IMAGE_ANNOTATION.POLYGON_STRING,
                                    val(newImageId),
                                    IMAGE_ANNOTATION.TAG_ID,
                                    IMAGE_ANNOTATION.ATTRIBUTE)
                            .from(IMAGE_ANNOTATION)
                            .where(
                                    IMAGE_ANNOTATION
                                            .IMAGE_ID
                                            .eq(originalImageId)
                                            .and(IMAGE_ANNOTATION.IS_DELETED.eq(false)));
            selectImageAnnotation =
                    selectImageAnnotation == null
                            ? selectAnnotationFromImage
                            : selectImageAnnotation.unionAll(selectAnnotationFromImage);

            // get image group info from original image
            var imageGroup = r.getImageGroupMap();
            String finalNewImageId = newImageId;
            var imageSequenceNo = sequenceNo;
            var insertValues =
                    imageGroup.entrySet().stream()
                            .flatMap(
                                    entry ->
                                            entry.getValue().stream()
                                                    .map(
                                                            groupId ->
                                                                    DSL.row(
                                                                            inline(groupId),
                                                                            inline(entry.getKey()),
                                                                            inline(finalNewImageId),
                                                                            inline(imageSequenceNo),
                                                                            inline(operator),
                                                                            inline(operator))))
                            .collect(Collectors.toSet());
            sequenceNo = sequenceNo + 1.0;
            imageGroupSet.addAll(insertValues);

            // get image note info from original image
            var selectNoteFromImage =
                    select(val(newImageId), IMAGE_NOTE.NOTE, val(operator), val(operator))
                            .from(IMAGE_NOTE)
                            .where(
                                    IMAGE_NOTE
                                            .IMAGE_ID
                                            .eq(originalImageId)
                                            .and(IMAGE_NOTE.IS_DELETED.eq(false)));
            selectImageNote =
                    selectImageNote == null
                            ? selectNoteFromImage
                            : selectImageNote.unionAll(selectNoteFromImage);
        }

        var insertImageMetadata =
                insertInto(
                                IMAGE_METADATA,
                                IMAGE_METADATA.IMAGE_ID,
                                IMAGE_METADATA.GPS,
                                IMAGE_METADATA.GPS_ALTITUDE,
                                IMAGE_METADATA.FOCAL_LENGTH,
                                IMAGE_METADATA.RELATIVE_ALTITUDE,
                                IMAGE_METADATA.GIMBAL_ROLL,
                                IMAGE_METADATA.GIMBAL_YAW,
                                IMAGE_METADATA.GIMBAL_PITCH)
                        .select(selectImageMetadata)
                        .returning();
        var insertImageTag =
                insertInto(
                                IMAGE_TAG_RELATION,
                                IMAGE_TAG_RELATION.IMAGE_ID,
                                IMAGE_TAG_RELATION.TAG_ID,
                                IMAGE_TAG_RELATION.CREATED_BY,
                                IMAGE_TAG_RELATION.CREATED_BY_AI,
                                IMAGE_TAG_RELATION.ATTRIBUTE)
                        .select(selectImageTag)
                        .returning();
        var insertImageAnnotation =
                insertInto(
                                IMAGE_ANNOTATION,
                                IMAGE_ANNOTATION.IMAGE_TAG_ID,
                                IMAGE_ANNOTATION.POLYGON,
                                IMAGE_ANNOTATION.DESCRIPTION,
                                IMAGE_ANNOTATION.CREATED_BY,
                                IMAGE_ANNOTATION.UPDATED_BY,
                                IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID,
                                IMAGE_ANNOTATION.SEQ_NO,
                                IMAGE_ANNOTATION.SOURCE_TYPE,
                                IMAGE_ANNOTATION.POLYGON_STRING,
                                IMAGE_ANNOTATION.IMAGE_ID,
                                IMAGE_ANNOTATION.TAG_ID,
                                IMAGE_ANNOTATION.ATTRIBUTE)
                        .select(selectImageAnnotation)
                        .returning();
        var insertImageGroup =
                insertInto(
                        IMAGE_GROUP,
                        IMAGE_GROUP.GROUP_ID,
                        IMAGE_GROUP.GROUP_TYPE,
                        IMAGE_GROUP.IMAGE_ID,
                        IMAGE_GROUP.SEQ_NO,
                        IMAGE_GROUP.CREATED_BY,
                        IMAGE_GROUP.UPDATED_BY);
        for (Row6<String, String, String, Double, String, String> insertGroupRow : imageGroupSet) {
            insertImageGroup.valuesOfRows(insertGroupRow);
        }
        var insertImageGroupResult = insertImageGroup.returning();

        var insertImageNote =
                insertInto(
                                IMAGE_NOTE,
                                IMAGE_NOTE.IMAGE_ID,
                                IMAGE_NOTE.NOTE,
                                IMAGE_NOTE.CREATED_BY,
                                IMAGE_NOTE.UPDATED_BY)
                        .select(selectImageNote)
                        .returning();
        try {
            return dsl.with("insert_image_metadata")
                    .as(insertImageMetadata)
                    .with("insert_image_tag")
                    .as(insertImageTag)
                    .with("insert_image_annotation")
                    .as(insertImageAnnotation)
                    .with("insert_image_group")
                    .as(insertImageGroupResult)
                    .with("insert_image_note")
                    .as(insertImageNote)
                    .insertInto(
                            IMAGE,
                            IMAGE.ID,
                            IMAGE.TIFF_ORIENTATION,
                            IMAGE.RESOURCE_URL,
                            IMAGE.CATEGORY,
                            IMAGE.CREATED_BY,
                            IMAGE.UPDATED_BY,
                            IMAGE.SHOOTING_TIME)
                    .select(selectImage)
                    .returning(IMAGE.ID)
                    .fetch(IMAGE.ID);
        } catch (DuplicateKeyException e) {
            throw new IllegalArgumentException("Copy image failed: image id already existed.");
        }
    }
}
