package com.bees360.image;

import static com.bees360.jooq.mysql.tables.Imageannotation.IMAGEANNOTATION;

import com.bees360.image.tag.ImageTag;
import com.bees360.image.tag.ImageTagDictProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Deprecated
@Log4j2
public class MysqlImageTagProvider implements ImageTagProvider {

    private final DSLContext mysqlDsl;

    private final ImageTagDictProvider imageTagDictProvider;

    private final ImageTagProvider primaryImageTagProvider;

    public MysqlImageTagProvider(
            DSLContext mysqlDsl,
            ImageTagDictProvider imageTagDictProvider,
            ImageTagProvider primaryImageTagProvider) {
        this.mysqlDsl = mysqlDsl;
        this.imageTagDictProvider = imageTagDictProvider;
        this.primaryImageTagProvider = primaryImageTagProvider;
        log.info(
                "Created '{}(mysqlDsl='{}', imageTagDictProvider='{}',"
                        + " primaryImageTagProvider='{}')'",
                this,
                this.mysqlDsl,
                this.imageTagDictProvider,
                this.primaryImageTagProvider);
    }

    @Override
    public Map<String, Iterable<? extends ImageTag>> findByImageIds(Iterable<String> imageIds) {
        var primaryTags = primaryImageTagProvider.findByImageIds(imageIds);

        var imageTags = new HashMap<String, Iterable<? extends ImageTag>>();
        var imageAnnotations = getAnnotations(Iterables.toList(imageIds));
        if (MapUtils.isEmpty(imageAnnotations)) {
            return primaryTags;
        }
        var tagDict =
                Iterables.toStream(imageTagDictProvider.getAll())
                        .collect(Collectors.toMap(ImageTag::getId, t -> t));
        for (var entry : imageAnnotations.entrySet()) {
            var tagAnnotations =
                    entry.getValue().stream()
                            .filter(t -> tagDict.containsKey(t.getTagId()))
                            .collect(Collectors.groupingBy((ImageAnnotation::getTagId)));
            var tags = new ArrayList<ImageTag>();
            for (var tagEntry : tagAnnotations.entrySet()) {
                var mysqlImageAnnotations = tagEntry.getValue();
                var firstAnnotation = mysqlImageAnnotations.get(0);
                var imageId = firstAnnotation.getImageId();
                var createdAt = firstAnnotation.getCreatedAt();
                var createdBy = firstAnnotation.getCreatedBy();
                List<ImageAnnotation> annotations =
                        mysqlImageAnnotations.stream()
                                .filter(x -> x.getFacetId() != -1)
                                .collect(Collectors.toList());
                tags.add(
                        new MysqlImageTag(
                                tagDict.get(tagEntry.getKey()),
                                annotations,
                                imageId,
                                createdAt,
                                createdBy));
            }
            if (CollectionUtils.isNotEmpty(tags)) {
                imageTags.put(entry.getKey(), tags);
            }
        }
        mergeTags(primaryTags, imageTags);
        return primaryTags;
    }

    private void mergeTags(
            Map<String, Iterable<? extends ImageTag>> primaryTags,
            Map<String, Iterable<? extends ImageTag>> imageTags) {
        imageTags.forEach(
                (k, v) ->
                        primaryTags.merge(
                                k,
                                v,
                                (v1, v2) -> {
                                    var tagIds = new HashSet<String>();
                                    var tagList = new ArrayList<ImageTag>();
                                    v2.forEach(
                                            t -> {
                                                if (!tagIds.contains(t.getId())) {
                                                    tagList.add(t);
                                                }
                                                tagIds.add(t.getId());
                                            });
                                    v1.forEach(
                                            t -> {
                                                if (!tagIds.contains(t.getId())) {
                                                    tagList.add(t);
                                                }
                                                tagIds.add(t.getId());
                                            });
                                    return tagList;
                                }));
    }

    @Override
    public Map<String, Iterable<? extends ImageTag>> findInHistory(Iterable<String> imageIds) {
        return primaryImageTagProvider.findInHistory(imageIds);
    }

    private Map<String, List<MysqlImageAnnotation>> getAnnotations(List<String> imageIds) {
        var condition =
                imageIds.size() == 1
                        ? IMAGEANNOTATION.IMAGE_ID.eq(imageIds.get(0))
                        : IMAGEANNOTATION.IMAGE_ID.in(imageIds);
        return mysqlDsl.select(
                        IMAGEANNOTATION.ANNOTATION_ID,
                        IMAGEANNOTATION.IMAGE_ID,
                        IMAGEANNOTATION.ANNOTATION_TYPE,
                        IMAGEANNOTATION.REMARK,
                        IMAGEANNOTATION.FACET_ID,
                        IMAGEANNOTATION.CREATED_TIME,
                        IMAGEANNOTATION.CREATED_BY,
                        DSL.field("ST_AsText({0})", IMAGEANNOTATION.ANNOTATION_POLYGON)
                                .as("annotation_polygon"),
                        IMAGEANNOTATION.SOURCE_TYPE,
                        IMAGEANNOTATION.ORIGIN_ANNOTATION_ID)
                .from(IMAGEANNOTATION)
                .where(condition)
                .fetchGroups(IMAGEANNOTATION.IMAGE_ID, MysqlImageAnnotation::new);
    }
}
