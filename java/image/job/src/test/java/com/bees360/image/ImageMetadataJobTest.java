package com.bees360.image;

import static com.bees360.image.RandomImageUtil.createRandomResource;
import static com.bees360.image.RandomImageUtil.randomImageId;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.image.Message.ImageMessage.Resource.Type;
import com.bees360.image.util.ImageMetadataExtractor;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.job.command.CommandJobConfig;
import com.bees360.job.registry.ImageMetadataJob;
import com.bees360.job.registry.ImageThumbnailsJob;
import com.bees360.resource.FileResourceRepository;
import com.bees360.resource.ResourcePool;
import com.bees360.util.ListenableFutures;
import com.bees360.util.SecureTokens;

import jakarta.annotation.PostConstruct;

import lombok.Data;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

@SpringBootTest
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
class ImageMetadataJobTest {

    @Import({
        CommandJobConfig.class,
        RabbitJobScheduler.class,
        TestJobExecutorRegister.class,
        ImageTypeDefinitionConfig.class
    })
    @Configuration
    static class Config {

        @Bean
        public ImageMetadataManager imageMetadataManager() {
            return new InMemoryImageMetadataManager();
        }

        @Bean
        public ImageResourceManager imageResourceManager() {
            return new InMemoryImageResourceManager();
        }

        @Bean
        public ImageResourceWriter imageResourceGenerator(
                ImageResourceManager imageResourceManager) {
            return new ImageResourceWriter(imageResourceManager);
        }

        @Bean
        public ResourcePool grpcResourceClient() {
            return new FileResourceRepository("/tmp/command-job-test");
        }

        @Bean
        public BiFunction<String, Type, String> imageUrlProvider() {
            return (imageId, type) -> "image/" + imageId + "/" + type.getNumber();
        }

        @Bean
        public Function<String, String[]> imageResourceKeyParser() {
            return resourceKey -> {
                var param = resourceKey.split("/");
                if (param.length != 3 || !StringUtils.isNumeric(param[2])) {
                    throw new IllegalStateException(
                            String.format("Image url format error. '%s'", resourceKey));
                }
                var imageId = param[1];
                var type = Type.forNumber(Integer.parseInt(param[2]));
                return new String[] {imageId, type.name()};
            };
        }
    }

    @ConfigurationProperties("image.resource")
    @Data
    static class ImageTypeDefinitionConfig implements ImageTypeDefinition {

        private Map<Type, TypeConfig> types;

        @Override
        public Iterable<Type> getAllTypes() {
            return types.keySet();
        }

        @Override
        public int getSideLimit(Type type) {
            return types.get(type).sideLimit;
        }

        @Override
        public int getQuality(Type type) {
            return types.get(type).quality;
        }

        @Data
        static class TypeConfig {
            int quality;
            int sideLimit;
        }
    }

    @Configuration
    static class TestJobExecutorRegister {

        @Autowired private ResourcePool resourcePool;

        @Autowired private RabbitJobDispatcher jobDispatcher;

        @Autowired private ImageMetadataManager imageMetadataManager;

        @Autowired private ImageResourceWriter imageResourceGenerator;

        @Autowired private Function<String, String[]> imageResourceKeyParser;

        @PostConstruct
        public void enlistJobExecutors() {
            jobDispatcher.enlist(
                    new ImageMetadataJobExecutor(
                            resourcePool,
                            ImageMetadataJob.JOB_NAME,
                            imageMetadataManager,
                            imageResourceGenerator,
                            ImageMetadataExtractor::get));
            jobDispatcher.enlist(
                    new ImageThumbnailsJobExecutor(
                            resourcePool,
                            ImageThumbnailsJob.JOB_NAME,
                            imageResourceGenerator,
                            imageResourceKeyParser));
        }
    }

    private final RabbitJobScheduler jobScheduler;

    private final ResourcePool resourcePool;

    private final ImageMetadataProvider imageMetadataProvider;

    private final ImageResourceProvider imageResourceProvider;

    private final BiFunction<String, Type, String> imageUrlProvider;

    private final ImageTypeDefinition imageTypeDefinition;

    ImageMetadataJobTest(
            @Autowired RabbitJobScheduler jobScheduler,
            @Autowired ResourcePool resourcePool,
            @Autowired ImageMetadataProvider imageMetadataProvider,
            @Autowired ImageResourceProvider imageResourceProvider,
            @Autowired BiFunction<String, Type, String> imageUrlProvider,
            @Autowired ImageTypeDefinition imageTypeDefinition) {
        this.jobScheduler = jobScheduler;
        this.resourcePool = resourcePool;
        this.imageMetadataProvider = imageMetadataProvider;
        this.imageResourceProvider = imageResourceProvider;
        this.imageUrlProvider = imageUrlProvider;
        this.imageTypeDefinition = imageTypeDefinition;
    }

    @Test
    public void imageMetadataJobTest() {
        var imageId = randomImageId();
        var key = SecureTokens.generateRandomHexToken() + ".jpeg";
        resourcePool.put(key, createRandomResource());

        var job = ImageMetadataJob.getInstance(imageId, key);
        var schedule = jobScheduler.schedule(job);
        ListenableFutures.getUnchecked(schedule);

        var metadataMap = imageMetadataProvider.findByImageId(List.of(imageId));
        assertTrue(metadataMap.containsKey(imageId));
        var originResource = imageResourceProvider.findByImageId(imageId, Type.ORIGIN);
        var largeResource = imageResourceProvider.findByImageId(imageId, Type.LARGE);
        assertNotNull(originResource);
        assertNotNull(largeResource);
    }

    @Test
    public void imageThumbnailsJobTest() {
        var imageId = randomImageId();
        var resource = createRandomResource();
        var url = StringUtils.strip(resource.getMetadata().getETag(), "\"") + ".jpg";
        resourcePool.put(url, resource);
        var type = Type.MIDDLE;

        var job =
                ImageThumbnailsJob.getImageCompressJob(
                        imageId,
                        type,
                        imageTypeDefinition.getQuality(type),
                        imageTypeDefinition.getSideLimit(type),
                        url,
                        imageUrlProvider.apply(imageId, type));
        var schedule = jobScheduler.schedule(job);
        ListenableFutures.getUnchecked(schedule);

        var resourceGenerate = resourcePool.get(imageUrlProvider.apply(imageId, type));
        assertNotNull(resourceGenerate);

        var resources = imageResourceProvider.findByImageId(imageId, type);
        assertFalse(com.google.common.collect.Iterables.isEmpty(resources));
    }
}
