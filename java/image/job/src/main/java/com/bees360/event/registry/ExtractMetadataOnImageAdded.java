package com.bees360.event.registry;

import com.bees360.image.Message.ImageMessage.Resource.Type;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.ImageMetadataJob;
import com.bees360.job.util.EventTriggeredJob;

import java.util.function.BiFunction;

public class ExtractMetadataOnImageAdded extends EventTriggeredJob<ImageAdded> {

    private final BiFunction<String, Type, Boolean> existsResourceProvider;

    @Override
    protected Job convert(ImageAdded event) {
        return ImageMetadataJob.getInstance(event.getId(), event.getGetUrl());
    }

    @Override
    protected boolean filter(ImageAdded event) {
        return !existsResourceProvider.apply(event.getId(), Type.ORIGIN);
    }

    public ExtractMetadataOnImageAdded(
            JobScheduler jobScheduler, BiFunction<String, Type, Boolean> existsResourceProvider) {
        super(jobScheduler);
        this.existsResourceProvider = existsResourceProvider;
    }
}
