package com.bees360.image;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.image.ImageTagServiceGrpc.ImageTagServiceImplBase;
import com.bees360.image.Message.ImageMessage;
import com.bees360.image.Message.ImageTagRequest;
import com.bees360.image.Message.UpdateAnnotationSortRequest.AnnotationSort;
import com.bees360.image.grpc.ProtobufImageAnnotation;
import com.bees360.image.tag.ImageTag;
import com.bees360.util.Iterables;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/8/1 下午7:38
 */
@Log4j2
@GrpcService
@Import({ExceptionTranslateInterceptor.class})
public class GrpcImageTagService extends ImageTagServiceImplBase {

    private final ImageTagManager grpcImageTagServiceManager;

    private final ImageAnnotationManager grpcImageAnnotationServiceManager;

    public GrpcImageTagService(
            ImageTagManager grpcImageTagServiceManager,
            ImageAnnotationManager grpcImageAnnotationServiceManager) {
        this.grpcImageTagServiceManager = grpcImageTagServiceManager;
        this.grpcImageAnnotationServiceManager = grpcImageAnnotationServiceManager;
        log.info(
                "Created {}(grpcImageTagServiceManager={},grpcImageAnnotationServiceManager={})",
                this,
                grpcImageTagServiceManager,
                grpcImageAnnotationServiceManager);
    }

    private StreamObserver<StringValue> getRequestObserverOfFindingImageTag(
            StreamObserver<ImageMessage> responseObserver,
            Function<List<String>, Map<String, Iterable<? extends ImageTag>>> findFunction) {
        return new StreamObserver<>() {
            final List<String> imageIds = new ArrayList<>();

            @Override
            public void onNext(StringValue id) {
                if (StringUtils.isBlank(id.getValue())) {
                    throw new IllegalArgumentException("The image id should not be empty");
                }
                imageIds.add(id.getValue());
            }

            @Override
            public void onError(Throwable throwable) {}

            @Override
            public void onCompleted() {
                var imageTagMap = findFunction.apply(imageIds);
                for (Map.Entry<String, Iterable<? extends ImageTag>> entry :
                        imageTagMap.entrySet()) {
                    responseObserver.onNext(
                            ImageMessage.newBuilder()
                                    .setId(entry.getKey())
                                    .addAllTag(
                                            Iterables.toStream(entry.getValue())
                                                    .map(ImageTag::toMessage)
                                                    .collect(Collectors.toList()))
                                    .build());
                }
                responseObserver.onCompleted();
            }
        };
    }

    public StreamObserver<StringValue> findByImageId(
            StreamObserver<ImageMessage> responseObserver) {
        return getRequestObserverOfFindingImageTag(
                responseObserver, grpcImageTagServiceManager::findByImageIds);
    }

    @Override
    public StreamObserver<StringValue> findInHistory(
            StreamObserver<ImageMessage> responseObserver) {
        return getRequestObserverOfFindingImageTag(
                responseObserver, grpcImageTagServiceManager::findInHistory);
    }

    @Override
    public StreamObserver<ImageTagRequest> deleteImageTag(StreamObserver<Empty> responseObserver) {
        return new StreamObserver<>() {
            final List<ImageTagRequest> requestList = new ArrayList<>();

            @Override
            public void onNext(ImageTagRequest imageTagRequest) {
                requestList.add(imageTagRequest);
            }

            @Override
            public void onError(Throwable throwable) {
                log.error("A error occurs when delete image tag.", throwable);
            }

            @Override
            public void onCompleted() {
                Map<String, Iterable<String>> imageTagIds =
                        requestList.stream()
                                .collect(
                                        Collectors.toMap(
                                                ImageTagRequest::getImageId,
                                                ImageTagRequest::getTagIdList,
                                                (k1, k2) -> k1));
                var deletedBy =
                        requestList.stream()
                                .findFirst()
                                .map(ImageTagRequest::getUpdatedBy)
                                .orElseThrow(
                                        () ->
                                                new IllegalArgumentException(
                                                        "The deleted by cannot be empty."));
                grpcImageTagServiceManager.deleteAllImageTag(imageTagIds, deletedBy);
                responseObserver.onNext(Empty.getDefaultInstance());
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public StreamObserver<ImageTagRequest> addImageTag(
            StreamObserver<ImageMessage> responseObserver) {
        return new StreamObserver<>() {
            final List<ImageTagRequest> requestList = new ArrayList<>();

            @Override
            public void onNext(ImageTagRequest imageTagRequest) {
                requestList.add(imageTagRequest);
            }

            @Override
            public void onError(Throwable throwable) {
                log.error("A error occurs when add image tag.", throwable);
            }

            @Override
            public void onCompleted() {
                var imageTagList =
                        requestList.stream()
                                .map(com.bees360.image.ImageTagRequest::from)
                                .collect(Collectors.toList());
                var createdBy =
                        requestList.stream()
                                .findFirst()
                                .map(ImageTagRequest::getUpdatedBy)
                                .orElseThrow(
                                        () ->
                                                new IllegalArgumentException(
                                                        "The creator cannot be empty."));
                var imageTagAdded =
                        grpcImageTagServiceManager.addAllImageTag(imageTagList, createdBy);
                for (Map.Entry<String, Iterable<? extends ImageTag>> entry :
                        imageTagAdded.entrySet()) {
                    responseObserver.onNext(
                            ImageMessage.newBuilder()
                                    .setId(entry.getKey())
                                    .addAllTag(
                                            Iterables.toStream(entry.getValue())
                                                    .map(ImageTag::toMessage)
                                                    .collect(Collectors.toList()))
                                    .build());
                }
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public void updateImageTag(
            Message.UpdateImageTagRequest request, StreamObserver<Empty> responseObserver) {
        grpcImageTagServiceManager.updateImageTag(request);
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void deleteAnnotationById(
            Message.DeleteImageAnnotationRequest request, StreamObserver<Empty> responseObserver) {
        grpcImageAnnotationServiceManager.deleteAll(
                request.getAnnotationIdList(), request.getDeletedBy());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public StreamObserver<Message.CreateImageAnnotationRequest> createAnnotation(
            StreamObserver<ImageMessage.Tag> responseObserver) {
        return new StreamObserver<>() {
            final List<Message.CreateImageAnnotationRequest> createAnnotations = new ArrayList<>();

            @Override
            public void onNext(Message.CreateImageAnnotationRequest request) {
                createAnnotations.add(request);
            }

            @Override
            public void onError(Throwable throwable) {
                log.error("Encountered error in createAnnotation", throwable);
            }

            @Override
            public void onCompleted() {
                String createdBy =
                        createAnnotations.stream()
                                .findFirst()
                                .map(Message.CreateImageAnnotationRequest::getCreatedBy)
                                .orElseThrow(
                                        () ->
                                                new IllegalArgumentException(
                                                        "The creator cannot be empty."));
                var annotations =
                        createAnnotations.stream()
                                .map(
                                        a -> {
                                            var annotationMessage =
                                                    ImageMessage.Tag.Annotation.newBuilder()
                                                            .setDescription(a.getDescription())
                                                            .addAllPolygon(a.getPolygonList())
                                                            .setOriginAnnotationId(
                                                                    a.getOriginAnnotationId())
                                                            .setSourceType(a.getSourceType())
                                                            .setAttribute(a.getAttribute())
                                                            .build();
                                            return new ProtobufImageAnnotation(
                                                    annotationMessage,
                                                    a.getImageId(),
                                                    a.getTagId());
                                        })
                                .collect(Collectors.toList());
                var imageTags = grpcImageAnnotationServiceManager.saveAll(annotations, createdBy);
                imageTags.forEach(
                        tag ->
                                responseObserver.onNext(
                                        Optional.ofNullable(tag)
                                                .map(ImageTag::toMessage)
                                                .orElse(null)));
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public void updateAnnotationSort(
            Message.UpdateAnnotationSortRequest request, StreamObserver<Empty> responseObserver) {
        var annotationSorts =
                request.getAnnotationSortList().stream()
                        .collect(
                                Collectors.toMap(
                                        AnnotationSort::getAnnotationId,
                                        AnnotationSort::getSeqNo,
                                        (k1, k2) -> k1));
        var updatedBy = request.getUpdatedBy();
        grpcImageAnnotationServiceManager.updateAnnotationSort(annotationSorts, updatedBy);
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void updateImageAnnotation(
            Message.UpdateAnnotationRequest request, StreamObserver<Empty> responseObserver) {
        var updateAnnotationRequest = UpdateAnnotationRequest.from(request);
        grpcImageAnnotationServiceManager.update(updateAnnotationRequest);
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void findAnnotationByIds(
            com.bees360.common.Message.StringValueList request,
            StreamObserver<Message.ImageApiAnnotationList> responseObserver) {
        var ids = request.getValueList();
        var imageApiAnnotations = grpcImageAnnotationServiceManager.findByIds(ids);
        var imageApiAnnotationList =
                Message.ImageApiAnnotationList.newBuilder()
                        .addAllAnnotation(
                                Iterables.transform(
                                        imageApiAnnotations, ImageApiAnnotation::toMessage))
                        .build();
        responseObserver.onNext(imageApiAnnotationList);
        responseObserver.onCompleted();
    }
}
