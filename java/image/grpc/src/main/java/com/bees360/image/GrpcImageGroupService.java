package com.bees360.image;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.image.ImageGroupServiceGrpc.ImageGroupServiceImplBase;
import com.bees360.image.Message.AddImageGroupRequest;
import com.bees360.image.Message.CreateGroupImageRequest;
import com.bees360.image.Message.ImageGroupQueryRequest;
import com.bees360.image.Message.ImageMessage;
import com.bees360.image.Message.SetImagePositionRequest;
import com.bees360.image.Message.UpdateImageGroupRequest;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Empty;
import com.google.protobuf.Int32Value;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022/1/24
 */
@Log4j2
@GrpcService
@Import({ExceptionTranslateInterceptor.class})
public class GrpcImageGroupService extends ImageGroupServiceImplBase {

    private final ImageGroupManager grpcImageGroupServiceManager;

    public GrpcImageGroupService(ImageGroupManager grpcImageGroupServiceManager) {
        this.grpcImageGroupServiceManager = grpcImageGroupServiceManager;
    }

    public StreamObserver<CreateGroupImageRequest> createGroupImage(
            StreamObserver<ImageMessage> responseObserver) {
        return new StreamObserver<>() {
            final List<CreateGroupImageRequest> requestList = new ArrayList<>();

            @Override
            public void onNext(CreateGroupImageRequest request) {
                requestList.add(request);
            }

            @Override
            public void onError(Throwable throwable) {
                log.error("A error occurs when create group image.", throwable);
            }

            @Override
            public void onCompleted() {
                var createdBy =
                        requestList.stream()
                                .findFirst()
                                .map(a -> a.getImage().getCreatedBy().getId())
                                .orElseThrow(
                                        () ->
                                                new IllegalArgumentException(
                                                        "The creator cannot be empty."));
                List<? extends GroupImageSource> groupImageSources =
                        requestList.stream()
                                .flatMap(s -> convertToGroupImageSource(s))
                                .collect(Collectors.toList());
                var imageCreated =
                        grpcImageGroupServiceManager.createGroupImage(groupImageSources, createdBy);
                for (var image : imageCreated) {
                    responseObserver.onNext(image.toMessage());
                }
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public void deleteGroupImage(
            Message.DeleteGroupImageRequest request, StreamObserver<Empty> responseObserver) {
        var groupImages =
                request.getGroupImageList().stream()
                        .map(
                                x ->
                                        GroupImage.of(
                                                x.getGroup().getGroupKey(),
                                                x.getGroup().getGroupType(),
                                                x.getImageIdList()))
                        .collect(Collectors.toList());

        grpcImageGroupServiceManager.deleteGroupImage(groupImages, request.getDeletedBy());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private Stream<GroupImageSource> convertToGroupImageSource(CreateGroupImageRequest request) {
        var imageSource =
                request.getImage().getSourceList().stream()
                        .map(
                                s ->
                                        ImageSource.of(
                                                s.getGetUrl(),
                                                s.getContentMD5(),
                                                s.getCategory(),
                                                DateTimes.toInstant(s.getShootingTime()),
                                                s.getId()))
                        .collect(Collectors.toList());
        var groups =
                request.getGroupList().stream()
                        .map(
                                group ->
                                        GroupImageSource.Group.builder()
                                                .groupKey(group.getGroupKey())
                                                .groupType(group.getGroupType())
                                                .build())
                        .collect(Collectors.toList());
        return imageSource.stream().map(source -> GroupImageSource.of(source, groups));
    }

    @Override
    public void find(
            ImageGroupQueryRequest request, StreamObserver<ImageMessage> responseObserver) {
        var groupId = request.getGroupId();
        var groupType = request.getGroupType();
        var imagesInGroup = grpcImageGroupServiceManager.getAllImageInGroup(groupId, groupType);
        imagesInGroup.forEach(image -> responseObserver.onNext(image.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void findInTrash(
            ImageGroupQueryRequest request, StreamObserver<ImageMessage> responseObserver) {
        var groupId = request.getGroupId();
        var groupType = request.getGroupType();
        var imagesInTrash = grpcImageGroupServiceManager.getAllImageInTrash(groupId, groupType);
        imagesInTrash.forEach(image -> responseObserver.onNext(image.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void addToGroup(
            AddImageGroupRequest request, StreamObserver<Int32Value> responseObserver) {
        var addCount =
                grpcImageGroupServiceManager.addImagesToGroup(
                        request.getGroupId(),
                        request.getGroupType(),
                        request.getImageIdList(),
                        request.getCreatedBy().getId());
        responseObserver.onNext(Int32Value.of(addCount));
        responseObserver.onCompleted();
    }

    @Override
    public void setImagePosition(
            SetImagePositionRequest request, StreamObserver<BoolValue> responseObserver) {
        var isSet =
                grpcImageGroupServiceManager.setImagePosition(
                        request.getGroupId(),
                        request.getGroupType(),
                        request.getImageIdList(),
                        StringUtils.isNotBlank(request.getPositionImageId())
                                ? request.getPositionImageId()
                                : null,
                        request.getUpdatedBy().getId());

        responseObserver.onNext(BoolValue.of(isSet));
        responseObserver.onCompleted();
    }

    @Override
    public void moveToTrash(
            UpdateImageGroupRequest request, StreamObserver<BoolValue> responseObserver) {
        var isToTrash =
                grpcImageGroupServiceManager.moveToTrash(
                        request.getGroupId(),
                        request.getGroupType(),
                        request.getImageIdList(),
                        request.getUpdatedBy());

        responseObserver.onNext(BoolValue.of(isToTrash));
        responseObserver.onCompleted();
    }

    @Override
    public void recoverFromTrash(
            UpdateImageGroupRequest request, StreamObserver<BoolValue> responseObserver) {
        var isRecover =
                grpcImageGroupServiceManager.recoverFromTrash(
                        request.getGroupId(),
                        request.getGroupType(),
                        request.getImageIdList(),
                        request.getUpdatedBy());

        responseObserver.onNext(BoolValue.of(isRecover));
        responseObserver.onCompleted();
    }

    @Override
    public void delete(UpdateImageGroupRequest request, StreamObserver<Empty> responseObserver) {

        grpcImageGroupServiceManager.delete(
                request.getGroupId(),
                request.getGroupType(),
                request.getImageIdList(),
                request.getUpdatedBy());

        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void findImageByTags(
            Message.FindImagesByTagsAndGroupRequest request,
            StreamObserver<ImageMessage> responseObserver) {
        Iterable<? extends Image> findByTags =
                grpcImageGroupServiceManager.findByQuery(ImageGroupQuery.from(request));
        findByTags.forEach(image -> responseObserver.onNext(image.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void getResultCountByQuery(
            Message.FindImagesByTagsAndGroupRequest request,
            StreamObserver<Int32Value> responseObserver) {
        int countByQuery =
                grpcImageGroupServiceManager.getCountByQuery(ImageGroupQuery.from(request));
        responseObserver.onNext(Int32Value.of(countByQuery));
        responseObserver.onCompleted();
    }

    @Override
    public void findGroupIdsByTypeAndImageIds(
            Message.GroupTypeImageIdsRequest request,
            StreamObserver<Message.ImageIdGroupIdsResponse> responseObserver) {
        var groupType = request.getGroupType();
        var imageIdList = request.getImageIdList();
        var imageIdGroupKeyMap =
                grpcImageGroupServiceManager.findGroupIdByTypeAndImageId(groupType, imageIdList);
        var response = Message.ImageIdGroupIdsResponse.newBuilder();
        imageIdGroupKeyMap.forEach(
                (imageId, groupKeys) ->
                        response.addImageIdGroupIds(
                                Message.ImageIdGroupIdsMessage.newBuilder()
                                        .addAllGroupId(groupKeys)
                                        .setImageId(imageId)
                                        .build()));
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    @Override
    public void findImageFromGroup(
            Message.ImageGroupBatchListRequest request,
            StreamObserver<Message.GroupImageListResponse> responseObserver) {
        var groupIds = request.getGroupIdList();
        var groupType = request.getGroupType();
        var imagesInGroup = grpcImageGroupServiceManager.getAllImageInGroups(groupIds, groupType);
        var groupImageResponses =
                imagesInGroup.entrySet().stream()
                        .map(
                                entry ->
                                        Message.GroupImageResponse.newBuilder()
                                                .setGroupId(entry.getKey())
                                                .addAllImage(
                                                        Iterables.transform(
                                                                entry.getValue(), Image::toMessage))
                                                .build())
                        .collect(Collectors.toList());
        var response =
                Message.GroupImageListResponse.newBuilder()
                        .setGroupType(groupType)
                        .addAllGroupResponse(groupImageResponses)
                        .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
