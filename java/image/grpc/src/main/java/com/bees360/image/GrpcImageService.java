package com.bees360.image;

import static com.bees360.util.Defaults.nullIfEmpty;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.image.ImageServiceGrpc.ImageServiceImplBase;
import com.bees360.image.Message.CreateImageRequest;
import com.bees360.image.Message.ImageMessage;
import com.bees360.image.Message.UpdateImageRequest;
import com.bees360.util.DateTimes;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/1/24
 */
@Log4j2
@GrpcService
@Import({ExceptionTranslateInterceptor.class})
@RequiredArgsConstructor
public class GrpcImageService extends ImageServiceImplBase {

    private final ImageManager grpcImageServiceManager;

    @Override
    public void create(CreateImageRequest request, StreamObserver<ImageMessage> responseObserver) {
        var images =
                grpcImageServiceManager.createImages(
                        request.getSourceList().stream()
                                .map(
                                        message ->
                                                ImageSource.of(
                                                        message.getGetUrl(),
                                                        message.getContentMD5(),
                                                        message.getCategory(),
                                                        DateTimes.toInstant(
                                                                message.getShootingTime()),
                                                        nullIfEmpty(message.getId())))
                                .collect(Collectors.toList()),
                        request.getCreatedBy().getId());
        images.forEach(image -> responseObserver.onNext(image.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public StreamObserver<StringValue> get(StreamObserver<ImageMessage> responseObserver) {
        return new StreamObserver<>() {
            final List<String> imageIds = new ArrayList<>();

            @Override
            public void onNext(StringValue id) {
                if (StringUtils.isBlank(id.getValue())) {
                    throw new IllegalArgumentException("The image id should not be empty");
                }
                imageIds.add(id.getValue());
            }

            @Override
            public void onError(Throwable throwable) {}

            @Override
            public void onCompleted() {
                grpcImageServiceManager
                        .getImages(imageIds)
                        .forEach(image -> responseObserver.onNext(image.toMessage()));

                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public void setTiffOrientation(
            UpdateImageRequest request, StreamObserver<Empty> responseObserver) {
        grpcImageServiceManager.updateTiffOrientation(
                request.getImageId(), request.getTiffOrientation(), request.getUpdatedBy().getId());

        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
