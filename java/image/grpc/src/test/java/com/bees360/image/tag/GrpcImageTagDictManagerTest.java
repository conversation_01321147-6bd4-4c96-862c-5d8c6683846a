package com.bees360.image.tag;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.image.InMemoryImageTagDictManager;
import com.bees360.image.config.GrpcImageTagDictClientConfig;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

/**
 * <AUTHOR>
 * @since 2022/11/1
 */
@SpringBootTest
@SpringJUnitConfig
@DirtiesContext
@ApplicationAutoConfig
public class GrpcImageTagDictManagerTest extends TestImageTagDictManager {

    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class,
        GrpcServerFactoryAutoConfiguration.class,
        GrpcClientAutoConfiguration.class
    })
    @Import({GrpcImageTagDictService.class, GrpcImageTagDictClientConfig.class})
    @Configuration
    static class Config {
        @Bean
        ImageTagDictManager imageTagDictManager() {
            return new InMemoryImageTagDictManager();
        }
    }

    public GrpcImageTagDictManagerTest(@Autowired ImageTagDictManager imageTagDictManager) {
        super(imageTagDictManager);
    }

    @Override
    @Test
    public void testSaveThenFindThenDelete() {
        super.testSaveThenFindThenDelete();
    }

    @Override
    @Test
    public void testFindByGroup() {
        super.testFindByGroup();
    }

    @Override
    @Test
    public void testSort() {
        super.testSort();
    }

    @Override
    @Test
    public void testUpdateNotSupportTag() {
        super.testUpdateNotSupportTag();
    }

    @Override
    @Test
    public void testUpdateAbbreviation() {
        super.testUpdateAbbreviation();
    }
}
