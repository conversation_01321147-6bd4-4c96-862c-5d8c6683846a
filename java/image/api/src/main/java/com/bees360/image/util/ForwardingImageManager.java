package com.bees360.image.util;

import com.bees360.image.Image;
import com.bees360.image.ImageManager;
import com.bees360.image.ImageSource;
import com.google.common.collect.ForwardingObject;

public abstract class ForwardingImageManager extends ForwardingObject implements ImageManager {

    @Override
    protected abstract ImageManager delegate();

    @Override
    public Iterable<? extends Image> createImages(
            Iterable<? extends ImageSource> imageSources, String addBy) {
        return delegate().createImages(imageSources, addBy);
    }

    @Override
    public void updateTiffOrientation(String imageId, int tiffOrientation, String updateBy) {
        delegate().updateTiffOrientation(imageId, tiffOrientation, updateBy);
    }

    @Override
    public Iterable<? extends Image> getImages(Iterable<String> ids) {
        return delegate().getImages(ids);
    }
}
