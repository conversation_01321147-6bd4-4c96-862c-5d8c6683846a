package com.bees360.image;

import com.bees360.image.tag.ImageTag;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface ImageTagManager extends ImageTagProvider {
    default void deleteImageTag(String imageId, Iterable<String> tagId, String updatedBy) {
        deleteAllImageTag(Map.of(imageId, tagId), updatedBy);
    }

    default Iterable<? extends ImageTag> addImageTag(
            String imageId, Iterable<String> tagIds, String addedBy) {
        var imageTags = addAllImageTag(Map.of(imageId, tagIds), addedBy);
        if (imageTags != null && imageTags.containsKey(imageId)) {
            return imageTags.get(imageId);
        }
        return new ArrayList<>();
    }

    /**
     * image 批量打 tag（不设置 tag attribute字段）
     *
     * @param imageTagIds A map where the keys are image IDs and the values are iterables of tag
     *     IDs.
     * @param addedBy userId
     * @return Map: key is imageId, value is all imageTags
     */
    Map<String, Iterable<? extends ImageTag>> addAllImageTag(
            Map<String, Iterable<String>> imageTagIds, String addedBy);

    void deleteAllImageTag(Map<String, Iterable<String>> imageTagIds, String deletedBy);

    /**
     * 为单个 imageId 打 tag 时带上 attribute 字段
     *
     * @param imageTag 需要插入单个 imageId 的 tag
     * @param createdBy useId
     * @return Map: key is imageId, value is all imageTags
     */
    default Map<String, Iterable<? extends ImageTag>> addImageTag(
            ImageTagRequest imageTag, String createdBy) {
        return addAllImageTag(List.of(imageTag), createdBy);
    }

    /**
     * image 批量打 tag 时带上 attribute 字段
     *
     * @param imageTagList 需要批量插入多个 image 的 tag
     * @param createdBy useId
     * @return Map: key is imageId, value is all imageTags
     */
    Map<String, Iterable<? extends ImageTag>> addAllImageTag(
            Iterable<ImageTagRequest> imageTagList, String createdBy);

    /**
     * 批量更新 image tag relation id 对应的属性值。例如 attribute 字段
     *
     * @param request update request
     */
    void updateImageTag(Message.UpdateImageTagRequest request);
}
