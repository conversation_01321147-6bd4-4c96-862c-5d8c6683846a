package com.bees360.image;

import com.bees360.image.Message.ImageMessage.Resource.Type;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import lombok.NonNull;

import java.time.Instant;

/**
 * <AUTHOR>
 * @since 2022/5/6
 */
class BasicImageResource implements ImageResource {

    private final Type type;

    private final String url;

    private final String eTag;

    private final Long contentLength;

    private final Instant lastModified;

    private final Integer width;

    private final Integer height;

    public BasicImageResource(
            @NonNull Type type,
            @NonNull String url,
            @Nullable String eTag,
            @Nullable Long contentLength,
            @Nullable Instant lastModified,
            @Nullable Integer width,
            @Nullable Integer height) {
        this.type = type;
        this.url = url;
        this.eTag = eTag;
        this.contentLength = contentLength;
        this.lastModified = lastModified;
        this.width = width;
        this.height = height;
    }

    @Nonnull
    @Override
    public Type getType() {
        return type;
    }

    @Nonnull
    @Override
    public String getUrl() {
        return url;
    }

    @Nullable
    @Override
    public String getETag() {
        return eTag;
    }

    @Nullable
    @Override
    public Long getContentLength() {
        return contentLength;
    }

    @Nullable
    @Override
    public Instant getLastModified() {
        return lastModified;
    }

    @Nullable
    @Override
    public Integer getWidth() {
        return width;
    }

    @Nullable
    @Override
    public Integer getHeight() {
        return height;
    }
}
