package com.bees360.image;

import java.util.List;

/** An interface that defines a method for copying images to different destinations. */
public interface ImageCopyManager {

    /**
     * Copies images to specified destinations based on the provided requests.
     *
     * <p>This method takes an iterable of {@link ImageCopyRequest} objects, each representing a
     * request to copy an image to one or more destinations. The method processes these requests and
     * copies the images to the specified destinations.
     *
     * @param request An iterable of {@link ImageCopyRequest} objects representing the requests to
     *     copy images to different destinations.
     * @param operator A string representing the operator initiating the copy operation.
     * @throw IllegalArgumentException If the provided request is null or empty.
     * @return A list of strings representing the ids of the copied images.
     */
    List<String> copyImage(Iterable<? extends ImageCopyRequest> request, String operator);
}
