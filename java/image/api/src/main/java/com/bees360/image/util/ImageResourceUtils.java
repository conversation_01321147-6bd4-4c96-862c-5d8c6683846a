package com.bees360.image.util;

import com.bees360.image.ImageApi;
import com.bees360.image.Message.ImageMessage.Resource.Type;
import com.bees360.util.Iterables;

public class ImageResourceUtils {

    /**
     * Get the image eTag of the corresponding type of image.
     *
     * @param image imageApi object
     * @param type imageApi resource type
     * @return image eTag value
     */
    public static String getImageETag(ImageApi image, Type type) {
        var imageResource =
                Iterables.toList(image.getResources()).stream()
                        .filter(r -> r.getType() == type)
                        .findFirst()
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                String.format(
                                                        "Cannot find image(%s) resource with type"
                                                                + " '%s': No such resource exists.",
                                                        image.getId(), type)));
        return imageResource.getETag();
    }
}
