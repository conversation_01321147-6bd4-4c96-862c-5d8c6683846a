package com.bees360.image.grpc;

import static com.bees360.image.util.AttributeMessageAdapter.attributeToJson;
import static com.bees360.util.Defaults.defaultIfNull;
import static com.bees360.util.Defaults.nullIfEmpty;
import static com.bees360.util.Defaults.nullIfZero;

import com.bees360.api.common.Point;
import com.bees360.image.ImageAnnotation;
import com.bees360.image.Message.ImageMessage.Tag;
import com.bees360.util.Iterables;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @since 2023/3/17
 */
public class ProtobufImageAnnotation implements ImageAnnotation {
    private final Tag.Annotation message;
    private final String imageId;
    private final String tagId;
    private final String attribute;

    public ProtobufImageAnnotation(Tag.Annotation message, String imageId, String tagId) {
        this.message = message;
        this.imageId = imageId;
        this.tagId = tagId;
        this.attribute = attributeToJson(message.getAttribute());
    }

    @Override
    public String getId() {
        return message.getId();
    }

    @Override
    public String getImageId() {
        return imageId;
    }

    @Override
    public String getTagId() {
        return tagId;
    }

    @Override
    public Iterable<? extends Point> getPolygon() {
        return Iterables.transform(
                Iterables.from(message.getPolygonList().iterator()),
                pointMessage -> Point.of(pointMessage.getX(), pointMessage.getY()));
    }

    @Override
    public Point getCenter() {
        return Point.of(
                message.getCircle().getCenter().getX(), message.getCircle().getCenter().getY());
    }

    @Override
    public Double getRadius() {
        return message.getCircle().getRadius();
    }

    @Nullable
    @Override
    public String getDescription() {
        return message.getDescription();
    }

    @Nullable
    @Override
    public String getOriginAnnotationId() {
        return nullIfEmpty(message.getOriginAnnotationId());
    }

    @Override
    public Integer getSourceType() {
        return defaultIfNull(nullIfZero(message.getSourceType()), 1);
    }

    @Nullable
    @Override
    public String getAttribute() {
        return attribute;
    }
}
