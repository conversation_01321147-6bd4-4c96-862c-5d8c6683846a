package com.bees360.image;

import com.bees360.util.Iterables;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

import javax.annotation.Nullable;

public interface ImageProvider {

    /**
     * 根据image id查询image
     *
     * @param id image id.
     * @return 查询到的image,可能为null.
     * @throws NoSuchElementException 查询不到image时抛出.
     */
    default Image get(final String id) {
        return Optional.ofNullable(findById(id))
                .orElseThrow(
                        () ->
                                new NoSuchElementException(
                                        String.format("Cannot find image with id '%s'", id)));
    }

    /**
     * 根据image id查询image
     *
     * @param id image id.
     * @return 查询到的image,可能为null.
     */
    @Nullable
    default Image findById(final String id) {
        return Iterables.toStream(getImages(List.of(id))).findAny().orElse(null);
    }

    /**
     * 根据image id列表查询image列表
     *
     * @param ids image id列表.
     * @return 查询到的image列表.
     */
    Iterable<? extends Image> getImages(Iterable<String> ids);
}
