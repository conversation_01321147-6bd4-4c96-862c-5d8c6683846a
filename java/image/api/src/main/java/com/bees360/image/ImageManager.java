package com.bees360.image;

import com.bees360.util.Iterables;

import java.util.List;

public interface ImageManager extends ImageProvider {

    /**
     * 创建image
     *
     * @param imageSource image源信息.
     * @param createdBy 创建人id.
     * @return 新创建的image.
     */
    default Image createImage(ImageSource imageSource, String createdBy) {
        return Iterables.toStream(createImages(List.of(imageSource), createdBy))
                .findAny()
                .orElse(null);
    }

    /**
     * 批量创建image
     *
     * @param imageSources image源信息列表.
     * @param createdBy 创建人id.
     * @return 新创建的image列表.
     * @throws IllegalArgumentException ShootingTime is greater than the current time.
     * @throws IllegalArgumentException Any image source has an ID and its illegal.
     * @throws IllegalArgumentException ID in any image source is existed.
     */
    Iterable<? extends Image> createImages(
            Iterable<? extends ImageSource> imageSources, String createdBy);

    /**
     * 更新image的tiffOrientation旋转信息
     *
     * @param imageId 更新的image id.
     * @param tiffOrientation 图片的旋转信息.
     * @param updatedBy 修改人id.
     */
    void updateTiffOrientation(String imageId, int tiffOrientation, String updatedBy);
}
