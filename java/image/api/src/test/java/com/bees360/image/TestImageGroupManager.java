package com.bees360.image;

import static com.bees360.image.RandomImageUtil.createRandomResource;
import static com.bees360.image.RandomImageUtil.randomImageGroupId;
import static com.bees360.image.RandomImageUtil.randomImageGroupType;
import static com.bees360.image.RandomImageUtil.randomImageId;
import static com.bees360.image.RandomImageUtil.randomImageSource;
import static com.bees360.image.RandomImageUtil.randomImageSources;
import static com.bees360.image.RandomImageUtil.randomUserId;
import static com.bees360.image.RandomImageUtil.removeQuotationMarks;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.api.InvalidArgumentException;
import com.bees360.image.util.ForwardingImageGroupManager;
import com.bees360.util.Iterables;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TestImageGroupManager extends ForwardingImageGroupManager {

    private static final String GROUP_TYPE_PROJECT = "GROUP_PROJECT";
    private static final String GROUP_TYPE_IMAGE = "GROUP_IMAGE";

    private final ImageManager imageManager;
    private final ImageGroupManager imageGroupManager;

    private final ImageTagManager imageTagManager;

    public TestImageGroupManager(
            ImageGroupManager imageGroupManager,
            ImageManager imageManager,
            ImageTagManager imageTagManager) {
        this.imageGroupManager = imageGroupManager;
        this.imageManager = imageManager;
        this.imageTagManager = imageTagManager;
    }

    @Override
    protected ImageGroupManager delegate() {
        return imageGroupManager;
    }

    void createGroupImageThenFindTest() {
        var userId = randomUserId();
        var groupProjectId = randomImageGroupId();
        var groupImageSources = randomGroupImageSources(groupProjectId);

        var imageCreated = imageGroupManager.createGroupImage(groupImageSources, userId);
        var imageCreatedIdSet =
                Iterables.toStream(imageCreated).map(Image::getId).collect(Collectors.toList());
        assertEquals(imageCreatedIdSet.size(), 3);

        var imageFromGroup =
                imageGroupManager.getAllImageInGroup(groupProjectId, GROUP_TYPE_PROJECT);
        var imageFromGroupIdSet =
                Iterables.toStream(imageFromGroup).map(Image::getId).collect(Collectors.toList());
        assertEquals(imageFromGroupIdSet.size(), 2);
        assertTrue(imageCreatedIdSet.containsAll(imageFromGroupIdSet));
    }

    private static ArrayList<GroupImageSource> randomGroupImageSources(String groupProjectId) {
        var groupImageId = randomImageId();
        var groupImageSources = new ArrayList<GroupImageSource>();
        groupImageSources.add(
                randomGroupImageResource(
                        1500,
                        1000,
                        groupProjectId,
                        groupImageId,
                        GROUP_TYPE_PROJECT,
                        GROUP_TYPE_IMAGE));
        groupImageSources.add(
                randomGroupImageResource(
                        1300,
                        800,
                        groupProjectId,
                        groupImageId,
                        GROUP_TYPE_PROJECT,
                        GROUP_TYPE_IMAGE));
        groupImageSources.add(
                randomGroupImageResource(
                        1100, 600, "testKeyOne", "testKeyTwo", "testTypeOne", "testTypeTwo"));
        return groupImageSources;
    }

    private static GroupImageSource randomGroupImageResource(
            int imageWidth,
            int imageHeight,
            String groupProjectId,
            String groupImageId,
            String groupTypeProject,
            String groupTypeImage) {
        var metadata = createRandomResource(imageWidth, imageHeight).getMetadata();
        return GroupImageSource.of(
                ImageSource.of(
                        removeQuotationMarks(metadata.getETag()),
                        metadata.getContentMD5(),
                        "/roof/front",
                        Instant.now().minus(1, ChronoUnit.DAYS)),
                List.of(
                        GroupImageSource.Group.builder()
                                .groupKey(groupProjectId)
                                .groupType(groupTypeProject)
                                .build(),
                        GroupImageSource.Group.builder()
                                .groupKey(groupImageId)
                                .groupType(groupTypeImage)
                                .build()));
    }

    void createThenFindThenToGroupTest() {
        var groupId = randomImageGroupId();
        var groupType = randomImageGroupType();
        var imageSources = randomImageSources();
        var userId = randomUserId();

        var createCount = addOriginalImagesToGroup(groupId, groupType, imageSources, userId);

        assertEquals(createCount, 2);

        var imagesFind = imageGroupManager.getAllImageInGroup(groupId, groupType);

        assertNotNull(imagesFind);
        var imageIds =
                Iterables.toStream(imagesFind).map(Image::getId).collect(Collectors.toList());

        assertEquals(imageIds.size(), 2);

        var toGroupId = randomImageGroupId();
        var toGroupType = randomImageGroupType();

        var addCount = imageGroupManager.addImagesToGroup(toGroupId, toGroupType, imageIds, userId);

        assertEquals(addCount, 2);

        var imagesFindInGroup =
                Iterables.toList(imageGroupManager.getAllImageInGroup(toGroupId, toGroupType));

        assertNotNull(imagesFindInGroup);
        assertEquals(imagesFindInGroup.size(), 2);

        for (var i = 0; i < imagesFindInGroup.size(); i++) {
            assertEquals(
                    imagesFindInGroup.get(i).getId(), Iterables.toList(imagesFind).get(i).getId());
        }
    }

    void createThenDeleteThenRecoverThenRemoveTest() {
        var groupId = randomImageGroupId();
        var groupType = randomImageGroupType();
        var imageSources = randomImageSources();
        var userId = randomUserId();

        addOriginalImagesToGroup(groupId, groupType, imageSources, userId);

        var imagesFind = imageGroupManager.getAllImageInGroup(groupId, groupType);

        var imageIds =
                Iterables.toStream(imagesFind).map(Image::getId).collect(Collectors.toList());

        var isToTrash = imageGroupManager.moveToTrash(groupId, groupType, imageIds, userId);

        assertTrue(isToTrash);

        var imageGroupsFind = imageGroupManager.getAllImageInGroup(groupId, groupType);

        assertTrue(com.google.common.collect.Iterables.isEmpty(imageGroupsFind));

        var imageGroupsTrash = imageGroupManager.getAllImageInTrash(groupId, groupType);

        assertNotNull(imageGroupsTrash);
        assertEquals(Iterables.toCollection(imageGroupsTrash).size(), 2);

        var isRecover = imageGroupManager.recoverFromTrash(groupId, groupType, imageIds, userId);

        assertTrue(isRecover);

        var imageGroupsFindRecover = imageGroupManager.getAllImageInGroup(groupId, groupType);

        assertNotNull(imageGroupsTrash);
        assertEquals(Iterables.toCollection(imageGroupsFindRecover).size(), 2);

        imageGroupManager.delete(groupId, groupType, imageIds, userId);

        var imageGroupsFindRemove = imageGroupManager.getAllImageInGroup(groupId, groupType);

        assertTrue(com.google.common.collect.Iterables.isEmpty(imageGroupsFindRemove));

        var imageGroupsTrashRemove = imageGroupManager.getAllImageInTrash(groupId, groupType);

        assertTrue(com.google.common.collect.Iterables.isEmpty(imageGroupsTrashRemove));
    }

    void createThenBatchDeleteThenRecoverThenRemoveTest() {
        var groupId = randomImageGroupId();
        var groupType = randomImageGroupType();
        var imageSources = randomImageSources();
        var userId = randomUserId();

        addOriginalImagesToGroup(groupId, groupType, imageSources, userId);

        var imagesFind = imageGroupManager.getAllImageInGroup(groupId, groupType);

        var imageIds =
                Iterables.toStream(imagesFind).map(Image::getId).collect(Collectors.toList());

        var isToTrash = imageGroupManager.moveToTrash(groupId, groupType, imageIds, userId);

        assertTrue(isToTrash);

        var imageGroupsFind = imageGroupManager.getAllImageInGroup(groupId, groupType);

        assertTrue(com.google.common.collect.Iterables.isEmpty(imageGroupsFind));

        var imageGroupsTrash = imageGroupManager.getAllImageInTrash(groupId, groupType);

        assertNotNull(imageGroupsTrash);
        assertEquals(Iterables.toCollection(imageGroupsTrash).size(), 2);

        var isRecover = imageGroupManager.recoverFromTrash(groupId, groupType, imageIds, userId);

        assertTrue(isRecover);

        var imageGroupsFindRecover = imageGroupManager.getAllImageInGroup(groupId, groupType);

        assertNotNull(imageGroupsTrash);
        assertEquals(Iterables.toCollection(imageGroupsFindRecover).size(), 2);

        var groupImages = List.of(GroupImage.of(groupId, groupType, imageIds));
        imageGroupManager.deleteGroupImage(groupImages, userId);

        var imageGroupsFindRemove = imageGroupManager.getAllImageInGroup(groupId, groupType);

        assertTrue(com.google.common.collect.Iterables.isEmpty(imageGroupsFindRemove));

        var imageGroupsTrashRemove = imageGroupManager.getAllImageInTrash(groupId, groupType);

        assertTrue(com.google.common.collect.Iterables.isEmpty(imageGroupsTrashRemove));
    }

    void createThenSortTest() {
        var groupId = randomImageGroupId();
        var groupType = randomImageGroupType();
        var imageSourceOne = randomImageSource();
        var imageSourceTwo = randomImageSource();
        var imageSourceThree = randomImageSource();
        var imageSourceFour = randomImageSource();
        var imageSources =
                List.of(imageSourceOne, imageSourceTwo, imageSourceThree, imageSourceFour);
        var userId = randomUserId();

        var createCount = addOriginalImagesToGroup(groupId, groupType, imageSources, userId);
        assertEquals(createCount, 4);

        var imagesFind = imageGroupManager.getAllImageInGroup(groupId, groupType);
        var imageIds =
                Iterables.toStream(imagesFind).map(Image::getId).collect(Collectors.toList());

        imageIds.forEach(imageId -> assertTrue(StringUtils.isNotBlank(imageId)));

        var imageOneId = imageIds.get(0);
        var imageTwoId = imageIds.get(1);
        var imageThreeId = imageIds.get(2);
        var imageFourId = imageIds.get(3);

        var isSetMiddle =
                imageGroupManager.setImagePosition(
                        groupId, groupType, List.of(imageThreeId, imageFourId), imageOneId, userId);

        assertTrue(isSetMiddle);

        var imageGroupsSortMiddle =
                Iterables.toList(imageGroupManager.getAllImageInGroup(groupId, groupType));

        assertEquals(imageGroupsSortMiddle.size(), 4);

        assertEquals(imageGroupsSortMiddle.get(0).getId(), imageOneId);
        assertEquals(imageGroupsSortMiddle.get(1).getId(), imageThreeId);
        assertEquals(imageGroupsSortMiddle.get(2).getId(), imageFourId);
        assertEquals(imageGroupsSortMiddle.get(3).getId(), imageTwoId);

        var isSetFirst =
                imageGroupManager.setImagePosition(
                        groupId, groupType, List.of(imageThreeId, imageFourId), null, userId);

        assertTrue(isSetFirst);

        var imageGroupsSortStart =
                Iterables.toList(imageGroupManager.getAllImageInGroup(groupId, groupType));

        assertEquals(imageGroupsSortStart.size(), 4);

        assertEquals(imageGroupsSortStart.get(0).getId(), imageThreeId);
        assertEquals(imageGroupsSortStart.get(1).getId(), imageFourId);
        assertEquals(imageGroupsSortStart.get(2).getId(), imageOneId);
        assertEquals(imageGroupsSortStart.get(3).getId(), imageTwoId);

        var isSetLatest =
                imageGroupManager.setImagePosition(
                        groupId, groupType, List.of(imageThreeId, imageFourId), imageTwoId, userId);

        assertTrue(isSetLatest);

        var imageGroupsSortEnd =
                Iterables.toList(imageGroupManager.getAllImageInGroup(groupId, groupType));

        assertEquals(imageGroupsSortEnd.size(), 4);

        assertEquals(imageGroupsSortEnd.get(0).getId(), imageOneId);
        assertEquals(imageGroupsSortEnd.get(1).getId(), imageTwoId);
        assertEquals(imageGroupsSortEnd.get(2).getId(), imageThreeId);
        assertEquals(imageGroupsSortEnd.get(3).getId(), imageFourId);
    }

    void findByTagSortTest() {
        var groupId = randomImageGroupId();
        var groupType = randomImageGroupType();
        var imageSourceOne = randomImageSource();
        var imageSourceTwo = randomImageSource();
        var imageSourceThree = randomImageSource();
        var imageSourceFour = randomImageSource();
        var imageSources =
                List.of(imageSourceOne, imageSourceTwo, imageSourceThree, imageSourceFour);
        var userId = randomUserId();

        var imageIds =
                addOriginalImagesToGroupReturnImageIds(groupId, groupType, imageSources, userId);
        assertEquals(imageIds.size(), 4);

        var roofTagCode = String.valueOf(ImageTagEnum.ROOF.getCode());
        var listRoofTag = List.of(roofTagCode);
        Map<String, Iterable<String>> addImageTagMap =
                imageIds.stream().collect(Collectors.toMap(a -> a, a -> listRoofTag));
        imageTagManager.addAllImageTag(addImageTagMap, userId);

        var imagesFindByTag =
                imageGroupManager.findByTagId(
                        groupId,
                        groupType,
                        List.of(String.valueOf(ImageTagEnum.ROOF.getCode())),
                        null);
        var imagesFindByTagIds =
                Iterables.toStream(imagesFindByTag).map(Image::getId).collect(Collectors.toList());

        var imageOneId = imagesFindByTagIds.get(0);
        var imageTwoId = imagesFindByTagIds.get(1);
        var imageThreeId = imagesFindByTagIds.get(2);
        var imageFourId = imagesFindByTagIds.get(3);

        var isSetMiddle =
                imageGroupManager.setImagePosition(
                        groupId, groupType, List.of(imageThreeId, imageFourId), imageOneId, userId);

        assertTrue(isSetMiddle);

        var imagesSortedFindByTag =
                imageGroupManager.findByTagId(
                        groupId,
                        groupType,
                        List.of(String.valueOf(ImageTagEnum.ROOF.getCode())),
                        List.of(String.valueOf(ImageTagEnum.ELEVATION.getCode())));
        var imagesSortedFindByTagIds =
                Iterables.toStream(imagesSortedFindByTag)
                        .map(Image::getId)
                        .collect(Collectors.toList());

        assertEquals(imagesSortedFindByTagIds.size(), 4);

        assertEquals(imagesSortedFindByTagIds.get(0), imageOneId);
        assertEquals(imagesSortedFindByTagIds.get(1), imageThreeId);
        assertEquals(imagesSortedFindByTagIds.get(2), imageFourId);
        assertEquals(imagesSortedFindByTagIds.get(3), imageTwoId);
    }

    void findByTagIdSortTest() {
        var groupId = randomImageGroupId();
        var groupType = randomImageGroupType();
        var imageSourceOne = randomImageSource();
        var imageSourceTwo = randomImageSource();
        var imageSourceThree = randomImageSource();
        var imageSourceFour = randomImageSource();
        var imageSources =
                List.of(imageSourceOne, imageSourceTwo, imageSourceThree, imageSourceFour);
        var userId = randomUserId();

        var imageIds =
                addOriginalImagesToGroupReturnImageIds(groupId, groupType, imageSources, userId);
        assertEquals(imageIds.size(), 4);

        var roofTagCode = String.valueOf(ImageTagEnum.ROOF.getCode());
        var listRoofTag = List.of(roofTagCode);
        Map<String, Iterable<String>> addImageTagMap =
                imageIds.stream().collect(Collectors.toMap(a -> a, a -> listRoofTag));
        imageTagManager.addAllImageTag(addImageTagMap, userId);

        var imagesFindByTag =
                imageGroupManager.findByTagId(
                        groupId,
                        groupType,
                        List.of(String.valueOf(ImageTagEnum.ROOF.getCode())),
                        null);
        var imagesFindByTagIds =
                Iterables.toStream(imagesFindByTag).map(Image::getId).collect(Collectors.toList());

        var imageOneId = imagesFindByTagIds.get(0);
        var imageTwoId = imagesFindByTagIds.get(1);
        var imageThreeId = imagesFindByTagIds.get(2);
        var imageFourId = imagesFindByTagIds.get(3);

        var isSetMiddle =
                imageGroupManager.setImagePosition(
                        groupId, groupType, List.of(imageThreeId, imageFourId), imageOneId, userId);

        assertTrue(isSetMiddle);

        var imagesSortedFindByTag =
                imageGroupManager.findByTagId(
                        groupId,
                        groupType,
                        List.of(String.valueOf(ImageTagEnum.ROOF.getCode())),
                        List.of(String.valueOf(ImageTagEnum.ELEVATION.getCode())));
        var imagesSortedFindByTagIds =
                Iterables.toStream(imagesSortedFindByTag)
                        .map(Image::getId)
                        .collect(Collectors.toList());

        assertEquals(imagesSortedFindByTagIds.size(), 4);

        assertEquals(imagesSortedFindByTagIds.get(0), imageOneId);
        assertEquals(imagesSortedFindByTagIds.get(1), imageThreeId);
        assertEquals(imagesSortedFindByTagIds.get(2), imageFourId);
        assertEquals(imagesSortedFindByTagIds.get(3), imageTwoId);
    }

    void addNotExistsImageToGroup() {
        var groupId = randomImageGroupId();
        var groupType = randomImageGroupType();
        var imageId = randomImageId();
        var userId = randomUserId();
        Assertions.assertThrows(
                InvalidArgumentException.class,
                () ->
                        imageGroupManager.addImagesToGroup(
                                groupId, groupType, List.of(imageId), userId));

        var imageSource = randomImageSource();
        Image image = imageManager.createImage(imageSource, userId);
        assertNotNull(image);
        assertNotNull(image.getId());
        Assertions.assertThrows(
                InvalidArgumentException.class,
                () ->
                        imageGroupManager.addImagesToGroup(
                                groupId, groupType, List.of(imageId, image.getId()), userId));
    }

    private List<String> addOriginalImagesToGroupReturnImageIds(
            String groupId,
            String groupType,
            Iterable<? extends ImageSource> imageResources,
            String userId) {
        var images = Iterables.toList(imageManager.createImages(imageResources, userId));

        var imageIds = images.stream().map(Image::getId).collect(Collectors.toList());
        imageGroupManager.addImagesToGroup(groupId, groupType, imageIds, userId);
        return imageIds;
    }

    private int addOriginalImagesToGroup(
            String groupId,
            String groupType,
            Iterable<? extends ImageSource> imageResources,
            String userId) {
        return addOriginalImagesToGroupReturnImageIds(groupId, groupType, imageResources, userId)
                .size();
    }

    void createThenFindByQueryTest() {
        var groupId = randomImageGroupId();
        var groupType = randomImageGroupType();
        var imageSources = randomImageSources();
        var userId = randomUserId();

        addOriginalImagesToGroup(groupId, groupType, imageSources, userId);
        var imagesFind = imageGroupManager.getAllImageInGroup(groupId, groupType);
        var queryBuilder = Message.FindImagesByTagsAndGroupRequest.newBuilder();
        var imagesFindByQuery =
                imageGroupManager.findByQuery(
                        ImageGroupQuery.from(
                                queryBuilder.setGroupId(groupId).setGroupType(groupType).build()));
        var imageCountByQuery =
                imageGroupManager.getCountByQuery(ImageGroupQuery.from(queryBuilder.build()));
        queryBuilder.clear();
        Assertions.assertNotNull(imagesFind);
        Assertions.assertNotNull(imagesFindByQuery);
        Assertions.assertEquals(
                Iterables.toList(imagesFind).size(), Iterables.toList(imagesFindByQuery).size());
        Assertions.assertEquals(Iterables.toList(imagesFind).size(), imageCountByQuery);
        Iterable<? extends Image> findByImageIdWithoutTag =
                imageGroupManager.findByQuery(
                        ImageGroupQuery.from(
                                queryBuilder
                                        .addAllImageId(
                                                Iterables.transform(imagesFind, Image::getId))
                                        .build()));
        Assertions.assertEquals(
                Iterables.toList(imagesFind).size(),
                Iterables.toList(findByImageIdWithoutTag).size());
        queryBuilder.clear();
        imagesFind.forEach(
                image ->
                        imageTagManager.addImageTag(
                                image.getId(),
                                List.of(String.valueOf(ImageTagEnum.ELEVATION.getCode())),
                                userId));
        imageTagManager.addImageTag(
                Iterables.toList(imagesFind).get(0).getId(),
                List.of(String.valueOf(ImageTagEnum.OVERVIEW.getCode())),
                userId);
        imageTagManager.addImageTag(
                Iterables.toList(imagesFind).get(1).getId(),
                List.of(
                        String.valueOf(ImageTagEnum.HAIL_DAMAGE.getCode()),
                        String.valueOf(ImageTagEnum.FOUNDATION.getCode())),
                userId);
        Iterable<? extends Image> findByTag =
                imageGroupManager.findByTag(
                        groupId, groupType, List.of(ImageTagEnum.ELEVATION), null);
        Assertions.assertNotNull(findByTag);
        Assertions.assertEquals(
                Iterables.toList(imagesFind).size(), Iterables.toList(findByTag).size());

        Iterable<? extends Image> findExcluedeByTag =
                imageGroupManager.findByTag(
                        groupId, groupType, null, List.of(ImageTagEnum.ELEVATION));
        Assertions.assertEquals(0, Iterables.toList(findExcluedeByTag).size());
        Iterable<? extends Image> findIncludesByTag =
                imageGroupManager.findByTag(
                        groupId,
                        groupType,
                        List.of(ImageTagEnum.ELEVATION, ImageTagEnum.OVERVIEW),
                        null);
        Assertions.assertEquals(1, Iterables.toList(findIncludesByTag).size());
        Iterable<? extends Image> findExcludeAndIncludeByTag =
                imageGroupManager.findByTag(
                        groupId,
                        groupType,
                        List.of(ImageTagEnum.ELEVATION),
                        List.of(ImageTagEnum.OVERVIEW));
        Assertions.assertEquals(1, Iterables.toList(findExcludeAndIncludeByTag).size());
        Iterable<? extends Image> findExcludesAndIncludeByTag =
                imageGroupManager.findByTag(
                        groupId,
                        groupType,
                        List.of(ImageTagEnum.ELEVATION),
                        List.of(ImageTagEnum.OVERVIEW, ImageTagEnum.ELEVATION));
        Assertions.assertEquals(0, Iterables.toList(findExcludesAndIncludeByTag).size());

        // test find by category
        Iterable<? extends Image> findByIncludeAnnotationCategory =
                imageGroupManager.findByQuery(
                        ImageGroupQuery.from(
                                queryBuilder
                                        .setGroupId(groupId)
                                        .setGroupType(groupType)
                                        .addAllIncludeCategory(List.of("Damage", "Hazard"))
                                        .build()));
        Assertions.assertEquals(0, Iterables.toList(findByIncludeAnnotationCategory).size());
        queryBuilder.clear();
        findByIncludeAnnotationCategory =
                imageGroupManager.findByQuery(
                        ImageGroupQuery.from(
                                queryBuilder
                                        .setGroupId(groupId)
                                        .setGroupType(groupType)
                                        .addAllIncludeTagId(
                                                List.of(
                                                        String.valueOf(
                                                                ImageTagEnum.FOUNDATION.getCode())))
                                        .addAllIncludeCategory(List.of("Damage"))
                                        .build()));
        Assertions.assertEquals(1, Iterables.toList(findByIncludeAnnotationCategory).size());
        queryBuilder.clear();
        Iterable<? extends Image> findByIncludeCategory =
                imageGroupManager.findByQuery(
                        ImageGroupQuery.from(
                                queryBuilder
                                        .setGroupId(groupId)
                                        .setGroupType(groupType)
                                        .addAllIncludeCategory(List.of("Category"))
                                        .build()));
        Assertions.assertEquals(2, Iterables.toList(findByIncludeCategory).size());
        queryBuilder.clear();
        findByIncludeCategory =
                imageGroupManager.findByQuery(
                        ImageGroupQuery.from(
                                queryBuilder
                                        .setGroupId(groupId)
                                        .setGroupType(groupType)
                                        .addAllIncludeCategory(List.of("Category", "Component"))
                                        .build()));
        Assertions.assertEquals(1, Iterables.toList(findByIncludeCategory).size());
        queryBuilder.clear();
        findByIncludeCategory =
                imageGroupManager.findByQuery(
                        ImageGroupQuery.from(
                                queryBuilder
                                        .setGroupId(groupId)
                                        .setGroupType(groupType)
                                        .addAllIncludeCategory(List.of("Category"))
                                        .addAllExcludeCategory(List.of("Component"))
                                        .build()));
        Assertions.assertEquals(1, Iterables.toList(findByIncludeCategory).size());
        queryBuilder.clear();
        Iterable<? extends Image> findByImageId =
                imageGroupManager.findByQuery(
                        ImageGroupQuery.from(
                                queryBuilder
                                        .setGroupId(groupId)
                                        .setGroupType(groupType)
                                        .addAllIncludeTagId(
                                                List.of(
                                                        String.valueOf(
                                                                ImageTagEnum.OVERVIEW.getCode())))
                                        .addAllImageId(
                                                List.of(
                                                        Iterables.toList(imagesFind)
                                                                .get(0)
                                                                .getId()))
                                        .build()));
        Assertions.assertEquals(1, Iterables.toList(findByImageId).size());
        queryBuilder.clear();
        int count =
                imageGroupManager.getCountByQuery(
                        ImageGroupQuery.from(
                                queryBuilder
                                        .setGroupId(groupId)
                                        .setGroupType(groupType)
                                        .addAllIncludeTagId(
                                                List.of(
                                                        String.valueOf(
                                                                ImageTagEnum.OVERVIEW.getCode())))
                                        .addAllImageId(
                                                List.of(
                                                        Iterables.toList(imagesFind)
                                                                .get(0)
                                                                .getId()))
                                        .build()));
        Assertions.assertEquals(1, count);
        queryBuilder.clear();
        findByImageId =
                imageGroupManager.findByQuery(
                        ImageGroupQuery.from(
                                queryBuilder
                                        .setGroupId(groupId)
                                        .setGroupType(groupType)
                                        .addAllIncludeTagId(
                                                List.of(
                                                        String.valueOf(
                                                                ImageTagEnum.HAIL_DAMAGE
                                                                        .getCode())))
                                        .addAllImageId(
                                                List.of(
                                                        Iterables.toList(imagesFind)
                                                                .get(0)
                                                                .getId()))
                                        .build()));
        Assertions.assertEquals(0, Iterables.toList(findByImageId).size());
    }

    // 将传入imageIds和groupId以imageId:groupIds的形式放入map
    private void putGroupIdAndImageIdsToMap(
            String groupId, Iterable<String> imageIds, Map<String, Iterable<String>> map) {
        imageIds.forEach(
                imageId -> {
                    var groupIds =
                            com.google.common.collect.Iterables.concat(
                                    List.of(groupId), map.getOrDefault(imageId, List.of()));
                    map.put(imageId, groupIds);
                });
    }

    // 生成指定数量的group以及对应的image并聚合成imageId:groupIds形式的map
    private Map<String, Iterable<String>> prepareImageIdGroupIdsMap(
            String groupType, int groupSize) {
        Map<String, Iterable<String>> expectImageIdGroupIdsMap = new HashMap<>();
        var groupIdOne = randomImageGroupId();
        var imageSources = randomImageSources();
        var userId = randomUserId();
        var imageIds =
                addOriginalImagesToGroupReturnImageIds(groupIdOne, groupType, imageSources, userId);
        putGroupIdAndImageIdsToMap(groupIdOne, imageIds, expectImageIdGroupIdsMap);
        for (int i = 1; i < groupSize; i++) {
            var groupId = randomImageGroupId();
            addImagesToGroup(groupId, groupType, imageIds, userId);
            putGroupIdAndImageIdsToMap(groupId, imageIds, expectImageIdGroupIdsMap);
        }
        return expectImageIdGroupIdsMap;
    }

    void findGroupIdByTypeAndImageIdTest() {
        var groupType = randomImageGroupType();
        var expectImageIdGroupIdsMap = prepareImageIdGroupIdsMap(groupType, 3);
        var acutalImageIdGroupIdsMap =
                imageGroupManager.findGroupIdByTypeAndImageId(
                        groupType, expectImageIdGroupIdsMap.keySet());
        // 对两个结果进行排序避免断言时出现内容相同但位置不同的情况
        acutalImageIdGroupIdsMap.forEach(
                (k, v) ->
                        acutalImageIdGroupIdsMap.put(
                                k, Iterables.toStream(v).sorted().collect(Collectors.toList())));
        expectImageIdGroupIdsMap.forEach(
                (k, v) ->
                        expectImageIdGroupIdsMap.put(
                                k, Iterables.toStream(v).sorted().collect(Collectors.toList())));
        Assertions.assertEquals(expectImageIdGroupIdsMap.size(), acutalImageIdGroupIdsMap.size());
        acutalImageIdGroupIdsMap.forEach(
                (k, v) -> Assertions.assertIterableEquals(expectImageIdGroupIdsMap.get(k), v));

        // remove first image group to Trash
        var deletedBy = randomUserId();
        var imageId = new ArrayList<>(expectImageIdGroupIdsMap.keySet()).get(0);
        var groupIds = expectImageIdGroupIdsMap.get(imageId);
        for (String groupId : Iterables.toList(groupIds)) {
            imageGroupManager.moveToTrash(groupId, groupType, List.of(imageId), deletedBy);
        }

        // test find result
        var acutalImageIdGroupIdsMap2 =
                imageGroupManager.findGroupIdByTypeAndImageId(
                        groupType, expectImageIdGroupIdsMap.keySet());
        expectImageIdGroupIdsMap.remove(imageId);
        // 对两个结果进行排序避免断言时出现内容相同但位置不同的情况
        acutalImageIdGroupIdsMap2.forEach(
                (k, v) ->
                        acutalImageIdGroupIdsMap2.put(
                                k, Iterables.toStream(v).sorted().collect(Collectors.toList())));
        expectImageIdGroupIdsMap.forEach(
                (k, v) ->
                        expectImageIdGroupIdsMap.put(
                                k, Iterables.toStream(v).sorted().collect(Collectors.toList())));
        Assertions.assertEquals(expectImageIdGroupIdsMap.size(), acutalImageIdGroupIdsMap2.size());
        acutalImageIdGroupIdsMap2.forEach(
                (k, v) -> Assertions.assertIterableEquals(expectImageIdGroupIdsMap.get(k), v));
    }

    void findGroupIdByEmptyImageIdTest() {
        var groupType = randomImageGroupType();
        prepareImageIdGroupIdsMap(groupType, 1);
        List<String> emptyImageIds = List.of();
        var acutalImageIdGroupIdsMap =
                imageGroupManager.findGroupIdByTypeAndImageId(groupType, emptyImageIds);
        Assertions.assertEquals(0, acutalImageIdGroupIdsMap.size());
    }

    void findGroupIdByNotExistGroupTypeTest() {
        var groupType = randomImageGroupType();
        var expectImageIdGroupIdsMap = prepareImageIdGroupIdsMap(groupType, 1);
        var acutalImageIdGroupIdsMap =
                imageGroupManager.findGroupIdByTypeAndImageId(
                        groupType + "#", expectImageIdGroupIdsMap.keySet());
        Assertions.assertEquals(0, acutalImageIdGroupIdsMap.size());
    }

    void testFindImageByGroupIds() {
        var userId = randomUserId();
        var groupProjectId1 = randomImageGroupId();
        var groupProjectId2 = randomImageGroupId();

        var groupImageId1 = randomImageId();
        var groupImageId2 = randomImageId();
        var groupImageSources = new ArrayList<GroupImageSource>();
        groupImageSources.add(
                randomGroupImageResource(
                        1500,
                        1000,
                        groupProjectId1,
                        groupImageId1,
                        GROUP_TYPE_PROJECT,
                        GROUP_TYPE_IMAGE));
        groupImageSources.add(
                randomGroupImageResource(
                        1100,
                        600,
                        groupProjectId2,
                        groupImageId2,
                        GROUP_TYPE_PROJECT,
                        GROUP_TYPE_IMAGE));

        var imageCreated = imageGroupManager.createGroupImage(groupImageSources, userId);
        var imageCreatedIdSet =
                Iterables.toStream(imageCreated).map(Image::getId).collect(Collectors.toList());
        assertEquals(imageCreatedIdSet.size(), 2);

        var imageFromGroup =
                imageGroupManager.getAllImageInGroups(
                        List.of(groupProjectId1, groupProjectId2), GROUP_TYPE_PROJECT);
        var imageFromGroupIdSet =
                imageFromGroup.values().stream()
                        .map(images -> Iterables.toList(Iterables.transform(images, Image::getId)))
                        .flatMap(List::stream)
                        .collect(Collectors.toList());
        assertEquals(imageFromGroupIdSet.size(), 2);
        assertTrue(imageCreatedIdSet.containsAll(imageFromGroupIdSet));
    }

    void testCreateGroupImageWithoutShootingTimeShouldSucceed() {
        var createdBy = randomUserId();
        var groupImageSources = generateGroupImageSourcesByShootingTime(null);

        var images = imageGroupManager.createGroupImage(groupImageSources, createdBy);

        assertNotNull(images);
        images.forEach(image -> assertNull(image.getShootingTime()));
    }

    void testCreateGroupImageFutureShootingTimeShouldThrow() {
        var createdBy = randomUserId();
        var futureShootingTime = Instant.now().plus(1, ChronoUnit.DAYS);
        var groupImageSources = generateGroupImageSourcesByShootingTime(futureShootingTime);

        assertThrows(
                IllegalArgumentException.class,
                () -> imageGroupManager.createGroupImage(groupImageSources, createdBy));
    }

    static List<GroupImageSource> generateGroupImageSourcesByShootingTime(Instant shootingTime) {
        var groupProjectId = randomImageGroupId();
        var groupImageSources = randomGroupImageSources(groupProjectId);

        return groupImageSources.stream()
                .map(
                        groupImageSource -> {
                            var futureImageSources =
                                    TestImageManager.resetShootingTime(
                                            groupImageSource.getImageSource(), shootingTime);
                            return GroupImageSource.of(
                                    futureImageSources, groupImageSource.getGroups());
                        })
                .collect(Collectors.toList());
    }
}
