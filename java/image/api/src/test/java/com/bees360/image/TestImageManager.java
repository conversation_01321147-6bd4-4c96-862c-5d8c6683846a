package com.bees360.image;

import static com.bees360.image.RandomImageUtil.getMultiSliceResource;
import static com.bees360.image.RandomImageUtil.randomErrorETagImageResource;
import static com.bees360.image.RandomImageUtil.randomImageId;
import static com.bees360.image.RandomImageUtil.randomImageSource;
import static com.bees360.image.RandomImageUtil.randomImageSourceWithId;
import static com.bees360.image.RandomImageUtil.randomImageSources;
import static com.bees360.image.RandomImageUtil.randomResourceSource;
import static com.bees360.image.RandomImageUtil.randomUserId;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.api.InvalidArgumentException;
import com.bees360.image.Message.ImageMessage.Resource.Type;
import com.bees360.image.util.ForwardingImageManager;
import com.bees360.resource.ResourcePool;
import com.bees360.util.Iterables;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

public class TestImageManager extends ForwardingImageManager {

    private final ImageManager imageManager;
    private final ImageResourceManager imageResourceManager;
    private final BiFunction<String, Type, String> imageUrlProvider;

    public TestImageManager(
            ImageManager imageManager,
            ImageResourceManager imageResourceManager,
            BiFunction<String, Type, String> imageUrlProvider) {
        this.imageManager = imageManager;
        this.imageResourceManager = imageResourceManager;
        this.imageUrlProvider = imageUrlProvider;
    }

    @Override
    protected ImageManager delegate() {
        return imageManager;
    }

    void updateImageTiffOrientation() {
        var imageSources = randomImageSources();
        var createdId = randomUserId();

        var images = Iterables.toList(imageManager.createImages(imageSources, createdId));

        assertNotNull(images);
        assertEquals(images.size(), 2);

        var imageId = images.get(0).getId();
        var tiffOrientation = images.get(0).getTiffOrientation();

        assertTrue(StringUtils.isNotBlank(imageId));

        imageManager.updateTiffOrientation(imageId, tiffOrientation + 1, createdId);

        var imagesGet = Iterables.toList(imageManager.getImages(List.of(imageId)));
        assertNotNull(imagesGet);
        assertEquals(imagesGet.size(), 1);

        assertEquals(imagesGet.get(0).getTiffOrientation(), tiffOrientation + 1);
    }

    void getImagesThrowIAEWhenIdIsBlank() {
        assertThrows(InvalidArgumentException.class, () -> imageManager.getImages(List.of("")));
    }

    void saveResourceThenFindFullImage(ResourcePool resourcePool) {
        var imageSources = randomImageSource();
        var createdId = randomUserId();

        var image = imageManager.createImage(imageSources, createdId);
        var imageId = image.getId();
        var originalResources =
                Iterables.toStream(randomResourceSource(imageId, imageUrlProvider, resourcePool))
                        .collect(Collectors.toList());

        originalResources.forEach(
                imageResource -> imageResourceManager.save(imageId, imageResource));
        var imageFull = imageManager.get(imageId);

        var imageSourceGet = Iterables.toList(imageFull.getResource());
        originalResources.remove(0);
        assertEquals(
                Iterables.toCollection(imageFull.getResource()).size(), originalResources.size());
        for (var i = 0; i < originalResources.size(); i++) {
            assertEquals(originalResources.get(i).getETag(), imageSourceGet.get(i).getETag());
        }
    }

    void saveImageResourceThrowIAEWhenETagError() {
        String imageId = randomImageId();
        var resource = randomErrorETagImageResource(imageId, imageUrlProvider);
        assertThrows(
                IllegalArgumentException.class, () -> imageResourceManager.save(imageId, resource));
    }

    void saveThenGetMultiSliceResource() {
        var imageSources = randomImageSource();
        var createdBy = randomUserId();
        var image = imageManager.createImage(imageSources, createdBy);
        String imageId = image.getId();
        var resource = getMultiSliceResource(imageId, imageUrlProvider);
        imageResourceManager.save(imageId, resource);
        var resourceGet = Iterables.toList(imageResourceManager.findByImageId(imageId, Type.LARGE));
        assertEquals(resourceGet.size(), 1);
        assertEquals(resource.getETag(), resourceGet.get(0).getETag());
    }

    void testCreateImageWithoutShootingTimeShouldSucceed() {
        var imageSources = randomImageSource();
        var createdBy = randomUserId();

        var nullShootingTimeImageSources = resetShootingTime(imageSources, null);

        var image = imageManager.createImage(nullShootingTimeImageSources, createdBy);

        assertNotNull(image);
        assertNull(image.getShootingTime());
    }

    void testSaveFutureShootingTimeShouldThrow() {
        var imageSources = randomImageSource();
        var createdBy = randomUserId();

        var futureShootingTime = Instant.now().plus(1, ChronoUnit.DAYS);
        var futureImageSources = resetShootingTime(imageSources, futureShootingTime);

        assertThrows(
                IllegalArgumentException.class,
                () -> imageManager.createImage(futureImageSources, createdBy));
    }

    void testPastShootingTimeWillBeSaved() {
        var imageSources = randomImageSource();
        var createdBy = randomUserId();

        var pastShootingTime = Instant.now().minus(1, ChronoUnit.DAYS);
        var pastImageSources = resetShootingTime(imageSources, pastShootingTime);

        var image = imageManager.createImage(pastImageSources, createdBy);

        assertNotNull(image.getShootingTime());
    }

    void testCreateImageWithIdShouldSucceed() {
        var imageId = randomImageId();
        var imageSources = randomImageSourceWithId(imageId);
        var createdBy = randomUserId();

        var pastShootingTime = Instant.now().minus(1, ChronoUnit.DAYS);
        var pastImageSources = resetShootingTime(imageSources, pastShootingTime);

        imageManager.createImage(pastImageSources, createdBy);
        var imageResult = imageManager.findById(imageId);
        assertNotNull(imageResult);
    }

    void testCreateImageWithIllegalIdShouldThrow() {
        // Create a non-32-bit string
        var imageId = RandomStringUtils.randomAlphanumeric(12);
        var imageSources = randomImageSourceWithId(imageId);
        var createdBy = randomUserId();

        var pastShootingTime = Instant.now().minus(1, ChronoUnit.DAYS);
        var pastImageSources = resetShootingTime(imageSources, pastShootingTime);
        assertThrows(
                IllegalArgumentException.class,
                () -> imageManager.createImage(pastImageSources, createdBy));
    }

    void testCreateImageWithExistedIdShouldThrow() {
        var imageId = randomImageId();
        var imageSources = randomImageSourceWithId(imageId);
        var createdBy = randomUserId();
        var pastShootingTime = Instant.now().minus(1, ChronoUnit.DAYS);
        var pastImageSources = resetShootingTime(imageSources, pastShootingTime);
        imageManager.createImage(pastImageSources, createdBy);
        // create image with same id
        var createdBy2 = randomUserId();
        var pastShootingTime2 = Instant.now().minus(1, ChronoUnit.DAYS);
        var pastImageSources2 = resetShootingTime(imageSources, pastShootingTime2);
        assertThrows(
                IllegalArgumentException.class,
                () -> imageManager.createImage(pastImageSources2, createdBy2));
    }

    static ImageSource resetShootingTime(ImageSource source, Instant shootingTime) {
        return ImageSource.of(
                source.getGetUrl(),
                source.getContentMD5(),
                source.getCategory(),
                shootingTime,
                source.getId());
    }
}
