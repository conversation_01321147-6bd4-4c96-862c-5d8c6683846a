package com.bees360.image;

import static com.bees360.image.RandomImageUtil.randomImageSources;
import static com.bees360.image.RandomImageUtil.randomNote;
import static com.bees360.image.RandomImageUtil.randomUserId;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.image.util.ForwardingImageNoteManager;
import com.bees360.image.util.ImageFillNoteManager;
import com.bees360.util.Iterables;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TestImageNoteManager extends ForwardingImageNoteManager {

    private final ImageManager imageManager;

    private final ImageNoteManager imageNoteManager;

    public TestImageNoteManager(ImageManager imageManager, ImageNoteManager imageNoteManager) {
        this.imageManager = new ImageFillNoteManager(imageManager, imageNoteManager);
        this.imageNoteManager = imageNoteManager;
    }

    @Override
    protected ImageNoteManager delegate() {
        return imageNoteManager;
    }

    void saveThenFindImageNoteAndFindByImageId() {
        List<? extends Image> images = generateAndSaveImages();
        var imageIds = images.stream().map(Image::getId).collect(Collectors.toList());

        var notes =
                imageIds.stream()
                        .flatMap(id -> Stream.of(randomNote(id), randomNote(id)))
                        .collect(Collectors.toList());
        var savedNotes = saveAll(notes);

        var findNotes = findByImageIds(imageIds);

        assertEquals(Iterables.toCollection(savedNotes).size(), notes.size());
        assertEquals(findNotes.size(), images.size());

        checkSavedAndFindNoteEquals(savedNotes, findNotes);
    }

    void saveThenDeleteByIdsThenFindHistory() {
        saveThenDeleteThenFindHistory(this::deleteByIds);
    }

    void saveThenDeleteByImageIdsThenFindHistory() {
        saveThenDeleteThenFindHistory(this::deleteByImageIds);
    }

    private void saveThenDeleteThenFindHistory(
            Consumer<Iterable<? extends ImageNote>> deleteConsumer) {
        List<? extends Image> images = generateAndSaveImages();
        var imageIds = images.stream().map(Image::getId).collect(Collectors.toList());

        var notes =
                imageIds.stream()
                        .flatMap(id -> Stream.of(randomNote(id), randomNote(id)))
                        .collect(Collectors.toList());
        var savedNotes = saveAll(notes);

        deleteConsumer.accept(savedNotes);

        var findDeleted = findByImageIds(imageIds);

        assertTrue(findDeleted.isEmpty());

        var findHistory = findHistoryByImageIds(imageIds);

        checkSavedAndFindNoteEquals(savedNotes, findHistory);
    }

    private void deleteByIds(Iterable<? extends ImageNote> savedNotes) {
        List<String> deleteNoteIds =
                Iterables.toStream(savedNotes).map(ImageNote::getId).collect(Collectors.toList());
        deleteByIds(deleteNoteIds, randomUserId());
    }

    private void deleteByImageIds(Iterable<? extends ImageNote> savedNotes) {
        List<String> deleteImageIds =
                Iterables.toStream(savedNotes)
                        .map(ImageNote::getImageId)
                        .collect(Collectors.toList());
        deleteByImageIds(deleteImageIds, randomUserId());
    }

    private List<? extends Image> generateAndSaveImages() {
        var imageSources = randomImageSources();
        var createdId = randomUserId();

        return Iterables.toList(imageManager.createImages(imageSources, createdId));
    }

    private void checkSavedAndFindNoteEquals(
            Iterable<? extends ImageNote> notes,
            Map<String, Iterable<? extends ImageNote>> findNotes) {
        Iterables.toCollection(notes)
                .forEach(
                        note -> {
                            assertTrue(findNotes.containsKey(note.getImageId()));
                            var findNoteList =
                                    Iterables.toStream(findNotes.get(note.getImageId()))
                                            .collect(
                                                    Collectors.toMap(
                                                            ImageNote::getId,
                                                            a -> a,
                                                            (k1, k2) -> k1));
                            assertTrue(findNoteList.containsKey(note.getId()));
                            assertEquals(note.getNote(), findNoteList.get(note.getId()).getNote());
                            assertEquals(
                                    note.getCreatedBy(),
                                    findNoteList.get(note.getId()).getCreatedBy());
                        });
    }
}
