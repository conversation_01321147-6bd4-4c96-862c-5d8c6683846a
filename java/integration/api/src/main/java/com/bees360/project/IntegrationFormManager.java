package com.bees360.project;

public interface IntegrationFormManager extends IntegrationFormProvider {

    /**
     * 全量更新接口
     *
     * @param id integration id
     * @param forms forms to be updated
     */
    void setIntegrationForm(String id, Iterable<? extends IntegrationForm> forms);

    /**
     * Add an integration form
     *
     * @param form Integration form Object
     * @return Integration form id
     */
    String addIntegrationForm(IntegrationForm form);
}
