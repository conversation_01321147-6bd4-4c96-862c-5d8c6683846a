package com.bees360.project;

import com.bees360.api.InvalidArgumentException;
import com.bees360.grpc.GrpcApi;
import com.bees360.grpc.ResponseListFutureStreamObserver;
import com.bees360.project.Message.ProjectIntegrationCreationRequest;
import com.bees360.project.Message.ProjectIntegrationIdRequest;
import com.bees360.project.Message.ProjectIntegrationReferenceRequest;
import com.bees360.project.Message.ProjectIntegrationSubReferenceRequest;
import com.bees360.project.Message.ProjectIntegrationTypeRequest;
import com.bees360.project.ProjectIntegrationServiceGrpc.ProjectIntegrationServiceBlockingStub;
import com.bees360.project.ProjectIntegrationServiceGrpc.ProjectIntegrationServiceStub;
import com.bees360.util.ListenableFutures;
import com.google.common.collect.Iterables;
import com.google.protobuf.StringValue;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Log4j2
public class GrpcExternalIntegrationManager implements ExternalIntegrationManager {

    private final GrpcApi<ProjectIntegrationServiceBlockingStub> grpcApi;
    private final ProjectIntegrationServiceStub asyncApi;

    public GrpcExternalIntegrationManager(
            @NonNull GrpcApi<ProjectIntegrationServiceBlockingStub> grpcApi,
            @NonNull ProjectIntegrationServiceStub asyncApi) {
        this.grpcApi = grpcApi;
        this.asyncApi = asyncApi;
        log.info("Created {}(grpcApi={},asyncApi={})", this, grpcApi, asyncApi);
    }

    @Override
    public String save(ExternalIntegration integration) {
        var result = grpcApi.apply(api -> api.save(integration.toMessage()));
        return StringUtils.isEmpty(result.getValue()) ? "" : result.getValue();
    }

    @Override
    public String create(
            String dataset,
            String integrationType,
            String referenceNumber,
            String subReferenceNumber) {
        var request =
                ProjectIntegrationCreationRequest.newBuilder()
                        .setDataset(dataset)
                        .setIntegrationType(integrationType)
                        .setReferenceNumber(referenceNumber)
                        .setSubReferenceNumber(subReferenceNumber)
                        .build();

        var id = grpcApi.apply(api -> api.create(request)).getValue();
        return StringUtils.isEmpty(id) ? null : id;
    }

    @Override
    public void setProjectId(String id, String projectId) {
        var request =
                ProjectIntegrationIdRequest.newBuilder().setId(id).setProjectId(projectId).build();
        grpcApi.apply(api -> api.setProjectId(request));
    }

    @Override
    public boolean deleteByProjectIdAndType(String projectId, String integrationType) {
        var request =
                ProjectIntegrationTypeRequest.newBuilder()
                        .setProjectId(projectId)
                        .setIntegrationType(integrationType)
                        .build();
        return grpcApi.apply(api -> api.deleteByProjectIdAndType(request)).getValue();
    }

    @Override
    public Iterable<? extends ExternalIntegration> findAllById(Iterable<String> ids) {
        if (com.google.common.collect.Iterables.isEmpty(ids)) {
            return List.of();
        }
        var response = new ResponseListFutureStreamObserver<>(ExternalIntegration::from);
        var request = asyncApi.findAllById(response);
        ids.forEach(id -> request.onNext(StringValue.of(id)));
        request.onCompleted();
        return ListenableFutures.getUnchecked(response);
    }

    @Override
    public ExternalIntegration findBySubReference(
            String integrationType, String referenceNumber, String subReferenceNumber) {
        // 由于null值作为message调用会导致npe,因此提前进行判断
        if (subReferenceNumber == null) {
            throw new InvalidArgumentException("subReferenceNumber cannot be null.");
        }
        var request =
                ProjectIntegrationSubReferenceRequest.newBuilder()
                        .setIntegrationType(integrationType)
                        .setReferenceNumber(referenceNumber)
                        .setSubReferenceNumber(subReferenceNumber)
                        .build();
        var response = grpcApi.apply(api -> api.findBySubReference(request));
        return ExternalIntegration.from(response);
    }

    @Override
    public Iterable<? extends ExternalIntegration> findAllByReference(
            String integrationType, Iterable<String> referenceNumbers) {
        if (com.google.common.collect.Iterables.isEmpty(referenceNumbers)) {
            return List.of();
        }
        var request =
                ProjectIntegrationReferenceRequest.newBuilder()
                        .setIntegrationType(integrationType)
                        .addAllReferenceNumber(referenceNumbers)
                        .build();
        var response = new ResponseListFutureStreamObserver<>(ExternalIntegration::from);
        asyncApi.findAllByReference(request, response);
        return ListenableFutures.getUnchecked(response);
    }

    @Override
    public Iterable<? extends ExternalIntegration> findAllByProjectId(Iterable<String> projectIds) {
        if (Iterables.isEmpty(projectIds)) {
            return List.of();
        }
        var response = new ResponseListFutureStreamObserver<>(ExternalIntegration::from);
        var request = asyncApi.findAllByProjectId(response);
        projectIds.forEach(id -> request.onNext(StringValue.of(id)));
        request.onCompleted();
        return ListenableFutures.getUnchecked(response);
    }
}
