package com.bees360.project;

import com.bees360.api.ApiHttpClient;
import com.bees360.http.util.URIs;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPut;

import java.net.URI;
import java.util.NoSuchElementException;

import javax.annotation.Nullable;

@Log4j2
public class HttpExternalIntegrationManagerClient implements ExternalIntegrationManager {

    private final String host;
    private final String endpoint;
    private final ApiHttpClient apiHttpClient;

    public HttpExternalIntegrationManagerClient(String host, ApiHttpClient apiHttpClient) {
        this(host, "project", apiHttpClient);
    }

    public HttpExternalIntegrationManagerClient(
            String host, String endpoint, ApiHttpClient apiHttpClient) {
        this.host = host;
        this.endpoint = endpoint;
        this.apiHttpClient = apiHttpClient;
        log.info(
                "Created {}(host={},endpoint={},apiHttpClient={})",
                this,
                host,
                endpoint,
                apiHttpClient);
    }

    public String save(String projectId, ExternalIntegration integration) {
        var path = String.format("%s/%s/integration", endpoint, projectId);
        var request = new HttpPut(createUri(path));
        request.setEntity(ApiHttpClient.protobufEntity(integration.toMessage()));
        var result = apiHttpClient.execute(request);
        return result.getProjectIntegration(0).getId();
    }

    private URI createUri(String path) {
        return URIs.build(host, uriBuilder -> uriBuilder.setPath(path));
    }

    @Override
    public String save(ExternalIntegration integration) {
        throw new UnsupportedOperationException();
    }

    @Nullable
    @Override
    public String create(
            String dataset,
            String integrationType,
            String referenceNumber,
            String subReferenceNumber) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void setProjectId(String id, String projectId) {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean deleteByProjectIdAndType(String projectId, String integrationType) {
        var path = String.format("%s/%s/integration/-/%s", endpoint, projectId, integrationType);
        try {
            apiHttpClient.execute(new HttpDelete(createUri(path)));
        } catch (NoSuchElementException ex) {
            return false;
        }
        return true;
    }

    @Override
    public Iterable<? extends ExternalIntegration> findAllById(Iterable<String> ids) {
        throw new UnsupportedOperationException();
    }

    @Nullable
    @Override
    public ExternalIntegration findBySubReference(
            String integrationType, String referenceNumber, String subReferenceNumber) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Iterable<? extends ExternalIntegration> findAllByReference(
            String integrationType, Iterable<String> referenceNumbers) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Iterable<? extends ExternalIntegration> findAllByProjectId(String projectId) {
        var path = String.format("%s/%s/integration", endpoint, projectId);
        var result = apiHttpClient.execute(new HttpGet(createUri(path)));
        return Iterables.transform(result.getProjectIntegrationList(), ExternalIntegration::from);
    }

    @Override
    public Iterable<? extends ExternalIntegration> findAllByProjectId(Iterable<String> projectIds) {
        throw new UnsupportedOperationException();
    }
}
