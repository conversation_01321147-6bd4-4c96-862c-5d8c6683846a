package com.bees360.project.listener;

import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.event.registry.PipelineTaskChanged.State;
import com.bees360.image.Image;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.ImageTagProvider;
import com.bees360.image.tag.ImageTag;
import com.bees360.image.tag.ImageTagDictProvider;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.image.ProjectImageProvider;

import jakarta.annotation.PostConstruct;

import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class SetNumberOfInteriorRoomsListenerTest {

    @Configuration
    static class Config {}

    @Mock private ProjectImageProvider projectImageProvider;

    @Mock private ProjectIIManager inspectionManager;

    @Mock private ImageTagDictProvider imageTagDictProvider;

    @Mock private PipelineService pipelineService;

    @Mock private ImageTagProvider imageTagProvider;

    static final List<ImageTag> tags =
            List.of(
                    new ImageTagTest("1518"),
                    new ImageTagTest("1513"),
                    new ImageTagTest("1512"),
                    new ImageTagTest("541"),
                    new ImageTagTest("525"),
                    new ImageTagTest("507"),
                    new ImageTagTest("506"),
                    new ImageTagTest("505"),
                    new ImageTagTest("504"),
                    new ImageTagTest("503"),
                    new ImageTagTest("502"),
                    new ImageTagTest("410"),
                    new ImageTagTest("408"));
    static final List<ImageTag> tagsWithAttribute =
            getTagWithAttribute(
                    List.of(
                            "1518", "1513", "1512", "541", "525", "507", "506", "505", "504", "503",
                            "502", "410", "408"));

    static final List<ImageTag> numberTags =
            List.of(
                    new ImageTagTest("" + ImageTagEnum.MASTER.getCode()),
                    new ImageTagTest("" + ImageTagEnum.ONE.getCode()),
                    new ImageTagTest("" + ImageTagEnum.TWO.getCode()));

    private ProjectII projectII = Mockito.mock(ProjectII.class);

    private Image image = Mockito.mock(Image.class);

    private SetNumberOfInteriorRoomsListener listener;

    @PostConstruct
    void init() {
        listener =
                new SetNumberOfInteriorRoomsListener(
                        inspectionManager,
                        imageTagDictProvider,
                        projectImageProvider,
                        pipelineService,
                        imageTagProvider);
    }

    /**
     * 测试image同时带room tag 和number tag. Size of room tag = 13, Master/One/Two tag is included. Number
     * of outbuildings = 13.
     */
    @Test
    void testCountNumberOfInteriorRoomsWithNumbers() throws IOException {
        mockMethods();
        mockFindByTagOne();

        var event = getEvent();
        listener.execute(event);
        Mockito.verify(inspectionManager, Mockito.times(1))
                .updateNumberOfInteriorRooms(
                        Mockito.any(), Mockito.argThat(number -> number == 13));
    }

    /**
     * 测试image同时带room tag 和number tag. Size of room tag = 13(每一个tag均存在三个不同的 index), Master/One/Two
     * tag is included. Number of outbuildings = 39.
     */
    @Test
    void testCountNumberOfInteriorRoomsWithTagAttribute() throws IOException {
        mockMethods();
        mockFindByTagOneWithAttribute();

        var event = getEvent();
        listener.execute(event);
        Mockito.verify(inspectionManager, Mockito.times(1))
                .updateNumberOfInteriorRooms(
                        Mockito.any(), Mockito.argThat(number -> number == 39));
    }

    /**
     * 测试image只带room tag. Size of room tag = 13, no number tag include. Number of outbuildings = 13.
     */
    @Test
    void testCountNumberOfInteriorRooms() throws IOException {
        mockMethods();
        mockFindByTagTwo();

        var event = getEvent();
        listener.execute(event);
        Mockito.verify(inspectionManager, Mockito.times(1))
                .updateNumberOfInteriorRooms(
                        Mockito.any(), Mockito.argThat(number -> number == 13));
    }

    private PipelineTaskChanged getEvent() {
        PipelineTaskChanged event = new PipelineTaskChanged();
        event.setPipelineId("121581");
        event.setTaskDefKey("count_interior_rooms");
        var state = new State();
        state.setStatus(PipelineStatus.READY);
        event.setState(state);
        return event;
    }

    void mockMethods() {
        Mockito.when(inspectionManager.findById(Mockito.any())).thenReturn(projectII);
        Mockito.when(imageTagDictProvider.findByGroup(Mockito.any(), Mockito.any()))
                .thenAnswer((e) -> tags);
    }

    void mockFindByTagOne() {
        Mockito.when(projectImageProvider.findByTag(Mockito.any(), Mockito.any()))
                .thenAnswer(e -> List.of(image));
        Mockito.when(imageTagProvider.findByImageIds(Mockito.any()))
                .thenAnswer(e -> ImageTagTestUtil.getFullTagMap(tags, numberTags));
    }

    void mockFindByTagOneWithAttribute() {
        Mockito.when(projectImageProvider.findByTag(Mockito.any(), Mockito.any()))
                .thenAnswer(e -> List.of(image));
        Mockito.when(imageTagProvider.findByImageIds(Mockito.any()))
                .thenAnswer(e -> ImageTagTestUtil.getFullTagMap(tagsWithAttribute, numberTags));
    }

    void mockFindByTagTwo() {
        Mockito.when(projectImageProvider.findByTag(Mockito.any(), Mockito.any()))
                .thenAnswer(e -> List.of(image));
        Mockito.when(imageTagProvider.findByImageIds(Mockito.any()))
                .thenAnswer(e -> ImageTagTestUtil.getOneTimeTagMap(tags, numberTags));
    }

    private static List<ImageTag> getTagWithAttribute(List<String> tagIds) {
        var tags = new ArrayList<ImageTag>();
        for (var tagId : tagIds) {
            tags.add(new ImageTagTest(tagId, "{\"index\":\"0\"}"));
            tags.add(new ImageTagTest(tagId, "{\"index\":\"1\"}"));
            tags.add(new ImageTagTest(tagId, "{\"index\":\"2\"}"));
        }
        return tags;
    }
}
