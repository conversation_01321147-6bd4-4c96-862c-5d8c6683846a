package com.bees360.todo;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.registry.ChatMessageAdded;
import com.bees360.event.registry.ChatMessageAdded.Attachment;
import com.bees360.todo.Message.Notification;
import com.bees360.todo.config.JooqTodoRepositoryConfig;

import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.io.IOException;
import java.util.List;
import java.util.Random;

@SpringBootTest
@ApplicationAutoConfig(exclude = {GrpcServerFactoryAutoConfiguration.class})
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class ChatMessageAddedListenerTest {
    @Configuration
    @Import(
            value = {
                JooqTodoRepositoryConfig.class,
                ChatMessageAddedListener.class,
            })
    static class Config {}

    @Autowired private TodoManager todoManager;

    @Autowired private ChatMessageAddedListener listener;

    @Autowired private TodoRepository notificationTodoRepository;

    @Test
    void testAddNotification() throws InterruptedException, IOException {
        ChatMessageAdded event = new ChatMessageAdded();
        event.setProjectId(randomId());
        event.setMessage("This is a test message.");
        event.setSource("BEES TEST");
        event.setSenderId(RandomStringUtils.randomAlphabetic(6));
        String user = RandomStringUtils.randomAlphabetic(6);
        event.setMentionUserIds(List.of(user));

        Attachment attachment = new Attachment();
        attachment.setUrl("TEST URL");
        attachment.setFilename("TEST FILE");
        event.setAttachments(List.of(attachment));

        listener.handle(event);

        Todo todo = todoManager.findTodoByUserId(user).iterator().next();
        assertNotification(event, todo);
    }

    private void assertNotification(ChatMessageAdded expected, Todo actual) {
        Notification notification = actual.toMessage().getNotification();
        Assertions.assertEquals(expected.getSenderId(), notification.getSenderId());
        Assertions.assertEquals(expected.getProjectId(), notification.getProjectId());
        Assertions.assertEquals(expected.getMessage(), notification.getMessage());
        Assertions.assertEquals(
                !expected.getAttachments().isEmpty(), notification.getHasAttachment());
    }

    private String randomId() {
        Random r = new Random();
        return String.valueOf(Math.abs(r.nextInt()));
    }
}
