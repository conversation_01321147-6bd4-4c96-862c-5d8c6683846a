package com.bees360.todo;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.jooq.persistent.todo.tables.TodoNotification;
import com.bees360.todo.Message.Notification;
import com.bees360.todo.Message.TodoMessage;
import com.bees360.todo.Message.TodoQueryMessage.Type;
import com.bees360.util.Defaults;
import com.bees360.util.Iterables;

import jakarta.annotation.Nullable;

import org.apache.commons.lang3.RandomStringUtils;
import org.jooq.DSLContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.List;

@SpringBootTest
@ApplicationAutoConfig
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class JooqTodoNotificationRepositoryTest extends AbstractTodoManagerTest {

    @Import({JooqConfig.class})
    @Configuration
    static class Config {
        @Bean
        JooqNotificationTodoRepository notificationTodoRepository(DSLContext dsl) {
            return new JooqNotificationTodoRepository(
                    dsl,
                    TodoNotification.TODO_NOTIFICATION,
                    TodoMessage.getDescriptor().findFieldByName("notification"),
                    Notification.getDefaultInstance(),
                    Type.NOTIFICATION);
        }

        @Bean
        TodoManager todoManager(DSLContext dsl, List<JooqTodoRepository> todoRepositoryList) {
            return new JooqTodoManager(dsl, todoRepositoryList);
        }
    }

    public JooqTodoNotificationRepositoryTest(
            @Autowired TodoManager todoManager, @Autowired TodoRepository todoRepository) {
        super(todoManager, todoRepository);
    }

    @Autowired private NotificationTodoRepository notificationTodoRepository;

    @Test
    @Override
    protected void testAddTodoTask() {
        super.testAddTodoTask();
    }

    @Test
    @Override
    protected void testFindTaskByOwnerId() {
        super.testFindTaskByOwnerId();
    }

    @Test
    @Override
    protected void testFindTaskByQuery() throws InterruptedException {
        super.testFindTaskByQuery();
    }

    @Test
    @Override
    protected void testFindTasksByIds() throws InterruptedException {
        super.testFindTasksByIds();
    }

    @Test
    @Override
    protected void testChangeTaskOwner() {
        super.testChangeTaskOwner();
    }

    @Test
    @Override
    protected void testMarkCompleted() {
        super.testMarkCompleted();
    }

    @Test
    @Override
    protected void testMarkCompletedWithCompletedBy() {
        super.testMarkCompletedWithCompletedBy();
    }

    @Test
    @Override
    protected void testMarkRead() {
        super.testMarkRead();
    }

    @Test
    @Override
    protected void testDeleted() {
        super.testDeleted();
    }

    @Test
    @Override
    protected void testRecover() {
        super.testRecover();
    }

    @Test
    protected void testMarkPinned() {
        super.testMarkPinned();
    }

    @Test
    protected void testBatchChangeTaskOwner() {
        super.testBatchChangeTaskOwner();
    }

    @Test
    protected void testFindTodoByIdShouldSucceed() {
        super.testFindTodoByIdShouldSucceed();
    }

    @Test
    void testDeleteByExternalIdShouldWork() {
        String userId = randomId(12);
        var externalId = randomId(12);
        Todo task = generateTask(externalId);
        todoRepository.addTodo(userId, task);
        notificationTodoRepository.deleteByExternalId(externalId);
        var actual = Iterables.toList(todoManager.findTodoByUserId(userId));
        Assertions.assertTrue(actual.isEmpty(), "Todo list should be empty.");
    }

    @Test
    void testFindBySenderIdShouldSucceed() {
        var userId = randomId(8);
        var task = generateTask();
        todoRepository.addTodo(userId, task);
        var todoTaskList1 = Iterables.toList(todoManager.findTodoByUserId(userId));
        var expected = todoTaskList1.get(0);
        var senderId = task.toMessage().getNotification().getSenderId();
        TodoQuery query =
                TodoQuery.TodoQueryBuilder.newBuilder().setSenderId(List.of(senderId)).build();
        var todoTaskListByQuery = Iterables.toList(todoManager.findTodoByQuery(query));
        var actual = todoTaskListByQuery.get(0);
        assertTaskEqual(expected, actual);
    }

    @Override
    protected Todo generateTask() {
        return generateTask(null);
    }

    protected Todo generateTask(@Nullable String externalId) {
        String senderId = RandomStringUtils.randomAlphabetic(6);
        String projectId = randomId(10);
        String message = RandomStringUtils.randomAlphabetic(128);
        return TestTodo.of(
                "notification",
                Notification.newBuilder()
                        .setProjectId(projectId)
                        .setSenderId(senderId)
                        .setMessage(message)
                        .setHasAttachment(true)
                        .setExternalId(Defaults.emptyIfNull(externalId))
                        .build());
    }

    @Test
    protected void testMarkTodoFollowupAndFind() {
        super.testMarkTodoFollowupAndFind();
    }

    @Test
    protected void testCancelTodoFollowup() {
        super.testCancelTodoFollowup();
    }

    @Test
    protected void testFindByFollowupTime() {
        super.testFindByFollowupTime();
    }

    @Test
    protected void testCompleteFollowupTodoShouldBeCorrect() {
        super.testCompleteFollowupTodoShouldBeCorrect();
    }

    @Test
    protected void testChangeFollowupTodoOwnerShouldBeCorrect() {
        super.testChangeFollowupTodoOwnerShouldBeCorrect();
    }

    @Test
    protected void testFindDeletedTodo() {
        super.testFindDeletedTodo();
    }

    @Override
    protected void assertTaskEqual(Todo expected, Todo actual) {
        Notification expected_message = expected.toMessage().getNotification();
        Notification actual_message = actual.toMessage().getNotification();
        Assertions.assertEquals(expected_message.getProjectId(), actual_message.getProjectId());
        Assertions.assertEquals(expected_message.getSenderId(), actual_message.getSenderId());
        Assertions.assertEquals(expected_message.getMessage(), actual_message.getMessage());
        Assertions.assertEquals(expected_message.getExternalId(), actual_message.getExternalId());
        Assertions.assertEquals(
                expected_message.getHasAttachment(), actual_message.getHasAttachment());
    }
}
