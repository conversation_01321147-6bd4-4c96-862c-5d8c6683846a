package com.bees360.mail;

import com.bees360.resource.FileResourceRepository;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.google.protobuf.ByteString;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.function.Function;

@SpringBootTest
@Import({
    HtmlResourceMailTemplateProvider.class,
})
public class HtmlResourceMailTemplateProviderTest extends TestMailTemplateProvider {

    @Configuration
    static class Config {

        @SneakyThrows
        @Bean
        public Iterable<MailTemplate> allMailTemplate() {
            String subjectTemplate = "There are [(${items.size()})] participants";
            String templateTemplate =
                    IOUtils.resourceToString("/template.html", StandardCharsets.UTF_8);
            String templateKey = "template";
            return List.of(MailTemplate.of(templateKey, subjectTemplate, templateTemplate));
        }

        @Bean
        public Function<String, Resource> htmlMailTemplateResourcePool(Iterable<MailTemplate> allMailTemplate) {
            ResourcePool resourcePool = new FileResourceRepository("/tmp/resource/mail");
            allMailTemplate.forEach(
                    mailTemplate -> {
                        var contentTemplate = mailTemplate.getContentTemplate();
                        var resource = Resource.of(ByteString.copyFromUtf8(contentTemplate));
                        resourcePool.put(mailTemplate.getTemplateKey() + ".html", resource);
                    });
            return resourcePool::get;
        }
    }

    public HtmlResourceMailTemplateProviderTest(
            @Autowired MailTemplateProvider mailTemplateProvider,
            @Autowired Iterable<MailTemplate> allMailTemplate) {

        super(mailTemplateProvider, allMailTemplate);
    }

    @Autowired private MailTemplateProvider mailTemplateProvider;

    @Test
    void testFindByTemplateKey() {
        findByTemplateKey();
    }

    @Test
    void testThrowException() {
        throwException();
    }
}
