package com.bees360.mail;

import com.bees360.api.UnavailableException;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;

import java.nio.charset.StandardCharsets;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

@Log4j2
public class HtmlResourceMailTemplateProvider implements MailTemplateProvider {

    private final Function<String, Resource> resourceProvider;
    private final String TEMPLATE_RESOURCE_NAME = "%s.html";

    public HtmlResourceMailTemplateProvider(@NonNull Function<String, Resource> htmlMailTemplateResourceProvider) {
        this.resourceProvider = htmlMailTemplateResourceProvider;
        log.info("Created '{} (resourceProvider={})'", this, resourceProvider);
    }

    @Nullable
    @Override
    public MailTemplate findByTemplateKey(String templateKey) {
        Preconditions.checkArgument(templateKey != null, "TemplateKey must not be null.");
        Resource templateResource = null;
        try {
            templateResource = resourceProvider.apply(String.format(TEMPLATE_RESOURCE_NAME, templateKey));
        } catch (Exception e) {
            throw new UnavailableException(
                    String.format("Get resource pool by templateKey: %s error", templateKey), e);
        }
        if (templateResource == null) {
            return null;
        }
        var template =
                templateResource.apply(in -> new String(in.readAllBytes(), StandardCharsets.UTF_8));

        var doc = Jsoup.parse(template);
        var subject = doc.title();
        if (StringUtils.isEmpty(subject)) {
            return null;
        }
        return MailTemplate.of(templateKey, subject, template);
    }

    @Override
    public Iterable<? extends MailTemplate> findAllByTemplateKey(Iterable<String> templateKeys) {
        Preconditions.checkArgument(templateKeys != null, "TemplateKeys must not be null.");
        return Iterables.toStream(templateKeys)
                .map(this::findByTemplateKey)
                .collect(Collectors.toList());
    }
}
