package com.bees360.mail;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.bees360.mail.util.ForwardingMailTemplateManager;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;

public class TestMailTemplateManager extends ForwardingMailTemplateManager {
    private final MailTemplateManager mailTemplateManager;

    public TestMailTemplateManager(MailTemplateManager mailTemplateManager) {
        this.mailTemplateManager = mailTemplateManager;
    }

    @Override
    protected MailTemplateManager delegate() {
        return this.mailTemplateManager;
    }

    public void saveAndThenDelete() {
        var templateKey = RandomStringUtils.randomAlphabetic(20);

        save(templateKey);

        save(templateKey);

        this.deleteByTemplateKey(templateKey);
        assertNull(this.findByTemplateKey(templateKey));
    }

    public void throwException() {
        Assertions.assertThrows(
                IllegalArgumentException.class, () -> mailTemplateManager.save(null));
        Assertions.assertThrows(
                IllegalArgumentException.class, () -> mailTemplateManager.findByTemplateKey(null));
        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> mailTemplateManager.findAllByTemplateKey(null));
        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> mailTemplateManager.deleteAllByTemplateKey(null));
        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> mailTemplateManager.deleteByTemplateKey(null));
    }

    private void save(String templateKey) {
        var actual = getRandomMailTemplate(templateKey);
        this.save(actual);
        var expected = this.findByTemplateKey(templateKey);
        assertMailTemplateEquals(expected, actual);
    }

    private void assertMailTemplateEquals(MailTemplate expected, MailTemplate actual) {
        assertNotNull(expected);
        assertEquals(expected.getSubjectTemplate(), actual.getSubjectTemplate());
        assertEquals(expected.getContentTemplate(), actual.getContentTemplate());
    }

    private MailTemplate getRandomMailTemplate(String templateKey) {
        String subjectTemplate = RandomStringUtils.randomAlphabetic(20);
        String contentTemplate = RandomStringUtils.randomAlphabetic(20);
        return MailTemplate.of(templateKey, subjectTemplate, contentTemplate);
    }
}
