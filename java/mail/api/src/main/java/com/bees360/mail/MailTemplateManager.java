package com.bees360.mail;

import com.google.common.base.Preconditions;

import java.util.List;

public interface MailTemplateManager extends MailTemplateProvider {

    /**
     * @param mailTemplate 当为null时，，抛出IllegalArgumentException
     */
    void save(MailTemplate mailTemplate);

    /**
     * @param templateKeys 当为null时，，抛出IllegalArgumentException
     */
    void deleteAllByTemplateKey(Iterable<String> templateKeys);
    /**
     * @param templateKey 当为null时，，抛出IllegalArgumentException
     */
    default void deleteByTemplateKey(String templateKey) {
        Preconditions.checkArgument(templateKey != null);
        deleteAllByTemplateKey(List.of(templateKey));
    }
}
