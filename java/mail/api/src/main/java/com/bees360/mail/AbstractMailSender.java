package com.bees360.mail;

import com.google.common.base.Preconditions;
import com.google.common.util.concurrent.ListenableFuture;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

abstract class AbstractMailSender implements MailSender {

    @Override
    public ListenableFuture<Void> send(MailMessage mailMessage) {
        Preconditions.checkArgument(mailMessage != null);
        Preconditions.checkArgument(
                CollectionUtils.isNotEmpty(mailMessage.getRecipients()),
                "The recipient of email should not be empty");
        Preconditions.checkArgument(
                StringUtils.isNotEmpty(mailMessage.getSubject()),
                "The subject of email should not be empty");
        Preconditions.checkArgument(
                StringUtils.isNotEmpty(mailMessage.getContent()),
                "The content of email should not be empty");
        return doSend(mailMessage);
    }

    abstract ListenableFuture<Void> doSend(MailMessage mailMessage);
}
