package com.bees360.mail.grpc.config;

import com.bees360.grpc.GrpcApi;
import com.bees360.mail.MailTemplateManagerGrpc.MailTemplateManagerBlockingStub;
import com.bees360.mail.MailTemplateManagerGrpc.MailTemplateManagerStub;
import com.bees360.mail.grpc.GrpcMailTemplateManager;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrpcMailTemplateManagerConfig {

    @GrpcClient("mailTemplateManager")
    private MailTemplateManagerStub mailTemplateManagerStub;

    @GrpcClient("mailTemplateManager")
    private MailTemplateManagerBlockingStub mailTemplateManagerBlockingStub;

    @Bean
    public GrpcMailTemplateManager grpcMailTemplateManager() {
        return new GrpcMailTemplateManager(
                GrpcApi.of(mailTemplateManagerBlockingStub), mailTemplateManagerStub);
    }
}
