package com.bees360.mail.config;

import com.bees360.resource.GrpcResourceClient;
import com.bees360.resource.Resource;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.TestPropertySource;

import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Tests for the GRPC-based resource provider configuration.
 */
@SpringBootTest(
        classes = {
                HtmlMailTemplateResourceProviderConfig.class,
                GrpcHtmlMailTemplateResourceProviderConfigTest.TestConfig.class
        },
        properties = {
                "mail.resource.resource-prefix=cdn://private/test/mail/"
        }
)
@TestPropertySource(locations = "", properties = {
        "spring.config.name=nonexistent"
})
public class GrpcHtmlMailTemplateResourceProviderConfigTest {

    @Configuration
    static class TestConfig {
        @Bean
        GrpcResourceClient grpcResourceClient() {
            GrpcResourceClient mockClient = Mockito.mock(GrpcResourceClient.class);
            Resource mockResource = Mockito.mock(Resource.class);
            when(mockClient.get(anyString())).thenReturn(mockResource);
            return mockClient;
        }
    }

    @Autowired
    private ApplicationContext applicationContext;

    @Value("${mail.resource.resource-prefix}")
    private String resourcePrefix;

    @Test
    void testGrpcResourceProviderCreation() {
        // Verify the bean is created
        Function<String, Resource> htmlMailTemplateResourceProvider =
                applicationContext.getBean("htmlMailTemplateResourceProvider", Function.class);

        assertNotNull(htmlMailTemplateResourceProvider,
                "htmlMailTemplateResourceProvider bean should be created");

        // Verify the resource prefix is correctly configured
        assertEquals("cdn://private/test/mail/", resourcePrefix,
                "Resource prefix should match the configured value");
    }
}
