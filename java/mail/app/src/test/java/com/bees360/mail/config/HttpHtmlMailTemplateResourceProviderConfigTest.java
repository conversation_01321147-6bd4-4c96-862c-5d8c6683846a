package com.bees360.mail.config;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.bees360.http.HttpClient;
import com.bees360.resource.Resource;

import org.apache.http.client.config.RequestConfig;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.TestPropertySource;

import java.util.function.Function;

/** Tests for the HTTP-based resource provider configuration. */
@SpringBootTest(
        classes = {
            HtmlMailTemplateResourceProviderConfig.class,
            HttpHtmlMailTemplateResourceProviderConfigTest.TestConfig.class
        },
        properties = {
             "mail.resource.resource-prefix=",
            "mail.template.resource.cdn-host=https://cdn.example.com",
            "mail.template.resource.cdn-token=test-token-123",
            "mail.template.resource.template-path=mail/templates"
        })
//@TestPropertySource(
//        // Use a non-existent config file name to avoid loading application.yml
//        properties = {
//            "mail.resource.resource-prefix=",
//            "mail.template.resource.cdn-host=https://cdn.example.com",
//            "mail.template.resource.cdn-token=test-token-123",
//            "mail.template.resource.template-path=mail/templates"
//        })
public class HttpHtmlMailTemplateResourceProviderConfigTest {

    @Configuration
    static class TestConfig {
        @Bean
        HttpClient apacheHttpClient() {
            HttpClient mockClient = Mockito.mock(HttpClient.class);
            Mockito.when(mockClient.getConfig()).thenReturn(RequestConfig.DEFAULT);
            return mockClient;
        }
    }

    @Autowired private ApplicationContext applicationContext;

    @Test
    void testHttpResourceProviderCreation() {
        // Verify the bean is created
        Function<String, Resource> htmlMailTemplateResourceProvider =
                applicationContext.getBean("htmlMailTemplateResourceProvider", Function.class);

        assertNotNull(
                htmlMailTemplateResourceProvider,
                "htmlMailTemplateResourceProvider bean should be created");
    }

    @Test
    void testMailTemplateResourcePropertiesCreation() {
        // Verify the properties bean is created
        HtmlMailTemplateResourceProviderConfig.HttpHtmlMailTemplateResourceProviderConfig
                        .MailTemplateResourceProperties
                properties =
                        applicationContext.getBean(
                                HtmlMailTemplateResourceProviderConfig
                                        .HttpHtmlMailTemplateResourceProviderConfig
                                        .MailTemplateResourceProperties.class);

        assertNotNull(properties, "MailTemplateResourceProperties bean should be created");
        assertEquals(
                "https://cdn.example.com",
                properties.getCdnHost(),
                "cdnHost should be set correctly");
        assertEquals(
                "test-token-123", properties.getCdnToken(), "cdnToken should be set correctly");
        assertEquals(
                "mail/templates",
                properties.getTemplatePath(),
                "templatePath should be set correctly");
    }
}
