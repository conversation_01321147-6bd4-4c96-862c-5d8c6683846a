package com.bees360.mail.config;

import com.bees360.http.ApacheHttpClient;
import com.bees360.http.HttpClient;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.resource.GrpcResourceClient;
import com.bees360.resource.HttpSafeResourceClient;
import com.bees360.resource.PrefixedResourcePool;
import com.bees360.resource.Resource;
import com.bees360.resource.config.GrpcResourceClientConfig;

import lombok.Data;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;
import java.util.function.Function;

@Configuration
public class HtmlMailTemplateResourceProviderConfig {

    @Configuration
    @ConditionalOnMissingBean(name = "htmlMailTemplateResourceProvider")
    static class GrpcHtmlMailTemplateResourceProviderConfig {
        @Bean
        public Function<String, Resource> htmlMailTemplateResourceProvider(
                @Value("${mail.resource.resource-prefix}") String resourcePoolPrefix,
                GrpcResourceClient grpcResourceClient) {
            var resourcePool = PrefixedResourcePool.of(grpcResourceClient, resourcePoolPrefix);
            return resourcePool::get;
        }
    }

    @Configuration
    @ConditionalOnProperty(prefix = "mail.template.resource", name = "cdn-host")
    static class HttpHtmlMailTemplateResourceProviderConfig {
        @Configuration
        @Data
        @ConfigurationProperties(prefix = "mail.template.resource")
        public static class MailTemplateResourceProperties {
            private String cdnHost;
            private String cdnToken;
            private String templatePath;
        }

        @Bean
        public Function<String, Resource> htmlMailTemplateResourceProvider(
                HttpClient apacheHttpClient,
                MailTemplateResourceProperties mailTemplateResourceProperties) {
            Function<String, String> mailResourceTemplateProvider =
                    mailTemplateName ->
                            String.format(
                                    "%s/%s/%s?%s",
                                    mailTemplateResourceProperties.getCdnHost(),
                                    mailTemplateResourceProperties.getTemplatePath(),
                                    mailTemplateName,
                                    mailTemplateResourceProperties.getCdnToken());
            var resourcePool = new HttpSafeResourceClient(URI.create(StringUtils.EMPTY), apacheHttpClient);
            return key -> resourcePool.get(mailResourceTemplateProvider.apply(key));
        }
    }

    public static void main(String[] args){
        var client =  HttpClients.custom();
        var httpSafeResourceClient = new HttpSafeResourceClient(URI.create(StringUtils.EMPTY),
                new ApacheHttpClient(client.build()));
        var resource = httpSafeResourceClient.get("https://bees360.ai/resource/0a87cf3dd82acc3e9de96acc0be671d2.html");
        System.out.println(resource);
    }
}
