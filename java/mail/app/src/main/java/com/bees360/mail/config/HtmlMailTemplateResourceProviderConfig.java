package com.bees360.mail.config;

import com.bees360.http.HttpClient;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.resource.GrpcResourceClient;
import com.bees360.resource.HttpResourceClient;
import com.bees360.resource.PrefixedResourcePool;
import com.bees360.resource.Resource;
import com.bees360.resource.config.GrpcResourceClientConfig;
import lombok.Data;
import lombok.NonNull;
import org.apache.commons.httpclient.URIException;
import org.apache.commons.httpclient.util.URIUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.net.URI;
import java.util.Objects;
import java.util.function.Function;

@Configuration
public class HtmlMailTemplateResourceProviderConfig {

    @Configuration
    @Import(value = {GrpcResourceClientConfig.class})
    @ConditionalOnProperty(prefix = "mail.resource", name = "resource-prefix")
    static class GrpcHtmlMailTemplateResourceProviderConfig {
        @Bean
        public Function<String, Resource> htmlMailTemplateResourceProvider(
                @Value("${mail.resource.resource-prefix}") String resourcePoolPrefix,
                GrpcResourceClient grpcResourceClient) {
            var resourcePool = PrefixedResourcePool.of(grpcResourceClient, resourcePoolPrefix);
            return resourcePool::get;
        }
    }

    @Configuration
    @Import(value = {ApacheHttpClientConfig.class})
    @ConditionalOnProperty(prefix = "mail.template.resource", name = "cdn-host")
    static class HttpHtmlMailTemplateResourceProviderConfig {
        static class HttpSafeResourceClient extends HttpResourceClient {

            public HttpSafeResourceClient(@NonNull URI contextURI, @NonNull HttpClient httpClient) {
                super(contextURI, httpClient);
            }

            @Override
            public URI getAbsoluteURI(final String key) {

                String encodedKey = key;
                URI uri;
                try {
                    uri = getContextURI().resolve(key);
                } catch (IllegalArgumentException ie) {

                    try {
                        encodedKey = URIUtil.encodePathQuery(key);
                    } catch (URIException e) {
                        throw new IllegalArgumentException(e);
                    }

                    uri = getContextURI().resolve(encodedKey);
                }

                if (Objects.equals(uri.toString(), encodedKey)
                        && !getContextURI().toString().equals("")) {
                    throw new IllegalArgumentException(
                            String.format(
                                    "Failed to resolve key '%s' in context '%s'",
                                    key, getContextURI()));
                }
                return uri;
            }
        }
        @Configuration
        @Data
        @ConfigurationProperties(prefix = "mail.template.resource")
        public static class MailTemplateResourceProperties {
            private String cdnHost;
            private String cdnToken;
            private String templatePath;
        }

        @Bean
        public Function<String, Resource> htmlMailTemplateResourceProvider(
                HttpClient apacheHttpClient,
                MailTemplateResourceProperties mailTemplateResourceProperties) {
            Function<String, String> mailResourceTemplateProvider =
                    mailTemplateName ->
                            String.format(
                                    "%s/%s/%s?%s",
                                    mailTemplateResourceProperties.getCdnHost(),
                                    mailTemplateResourceProperties.getTemplatePath(),
                                    mailTemplateName,
                                    mailTemplateResourceProperties.getCdnToken());
            var resourcePool = new HttpSafeResourceClient(URI.create(StringUtils.EMPTY), apacheHttpClient);
            return key -> resourcePool.get(mailResourceTemplateProvider.apply(key));
        }
    }
}
