package com.bees360.job;

import com.bees360.job.util.AbstractAsyncJobExecutor;
import com.bees360.mail.MailMessage;
import com.bees360.mail.MailSender;
import com.bees360.mail.Message;
import com.bees360.mail.util.MailMessageFactory;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.protobuf.InvalidProtocolBufferException;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.UncheckedIOException;

@Log4j2
public class SendMailExecutor extends AbstractAsyncJobExecutor<MailMessage> {

    private final MailSender mailSender;
    private final String senderName;
    private final MailMessageFactory mailMessageFactory;

    public SendMailExecutor(
            @NonNull String senderName,
            @NonNull MailSender mailSender,
            @NonNull MailMessageFactory mailMessageFactory) {
        this.mailMessageFactory = mailMessageFactory;
        if (StringUtils.isEmpty(senderName)) {
            throw new IllegalArgumentException("The param senderName must not be empty");
        }
        this.senderName = senderName;
        this.mailSender = mailSender;
        log.info(
                "Created '{} (mailSender: {}, senderName: {}, mailMessageFactory: {})'",
                this,
                this.mailSender,
                this.senderName,
                this.mailMessageFactory);
    }

    @Override
    protected MailMessage decode(Job job) {
        Message.MailMessage message;
        try {
            message = Message.MailMessage.parseFrom(job.getPayload());
        } catch (InvalidProtocolBufferException e) {
            throw new UncheckedIOException("Failed to convert jobPayload to ProtoMessage", e);
        }
        return mailMessageFactory.createFromMessage(message);
    }

    @Override
    public String getName() {
        return senderName;
    }

    @Override
    protected ListenableFuture<Void> accept(MailMessage mailMessage) {
        return mailSender.send(mailMessage);
    }
}
