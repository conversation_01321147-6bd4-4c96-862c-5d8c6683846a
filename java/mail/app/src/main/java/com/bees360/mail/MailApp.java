package com.bees360.mail;

import com.bees360.atomic.AtomicLongToIntAdapter;
import com.bees360.atomic.RedisAtomicLongProvider;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.grpc.config.GrpcClientConfig;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.mail.config.HtmlMailTemplateResourceProviderConfig;
import com.bees360.mail.config.SpringMailSenderConfig;
import com.bees360.mail.config.ThymeleafMailMessageFactoryConfig;
import com.bees360.mail.job.config.MailJobExecutorConfig;
import com.bees360.mail.job.config.MailSenderDecoratorConfig;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.redis.config.RedissonConfig;
import com.bees360.resource.GrpcResourceClient;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.config.GrpcResourceClientConfig;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@Import({
    /*JobDispatcher*/
    RabbitJobDispatcher.class,
    RabbitEventPublisher.class,
    RabbitEventDispatcher.class,
    RabbitApiConfig.class,
    RedissonConfig.class,
    RedisAtomicLongProvider.class,
    AtomicLongToIntAdapter.class,
    /*JobExecutor*/
    ApacheHttpClientConfig.class,
    GrpcResourceClientConfig.class,
    HtmlResourceMailTemplateProvider.class,
    ThymeleafMailMessageFactoryConfig.class,
    MailJobExecutorConfig.class,
    MailSenderDecoratorConfig.class,
    MailApp.MailSenderListConfig.class,
    GrpcClientConfig.class,
    HtmlMailTemplateResourceProviderConfig.class,
})
@EnableEncryptableProperties
@ApplicationAutoConfig
public class MailApp {

    @Configuration
    public static class MailSenderListConfig {

        @Import({
            SpringMailSenderConfig.class,
        })
        @Configuration
        @ConditionalOnProperty(prefix = "mail.spring.senders[0]", name = "username")
        public static class SpringMailSendersConfig {}

        @Bean
        @ConditionalOnMissingBean(
                value = NamedMailSender.class,
                parameterizedContainer = List.class)
        public List<NamedMailSender> mailSenderList(
                @Value("${mail.fake-senders}") List<String> senders) {

            final var emptySender =
                    new MailSender() {
                        @Override
                        public ListenableFuture<Void> send(MailMessage mailMessage) {
                            log.info(
                                    "Send mail {} to {}.",
                                    mailMessage.getSubject(),
                                    mailMessage.getRecipients());
                            return Futures.immediateVoidFuture();
                        }
                    };

            return senders.stream()
                    .map(name -> new NamedMailSender(name, emptySender))
                    .collect(Collectors.toList());
        }
    }

    @Bean
    public ResourcePool attachmentResourcePool(GrpcResourceClient grpcResourceClient) {
        return grpcResourceClient;
    }

    public static void main(final String[] args) {
        ExitableSpringApplication.run(MailApp.class, args);
    }
}
