spring:
  profiles:
    active: ${ENV}
    include: actuator
app:
  id: RCT_APP
jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    password: ${BEES360_SECRET_KEY}

rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password

rct:
  client:
    request:
      connectTimeout: PT60S
      callTimeout: PT180S
      followRedirects: true
      retryOnConnectionFailure: true
  form:
    enabled: false

http:
  aws-lambda:
    enabled: false
  client:
    retry:
      maxAttempts: 5
      initialInterval: 2000
      multiplier: 2.0
      maxInterval: 10000
    apache:
      maxConnPerRoute: 16
      maxConnTotal: 64
      evictIdleConnectionsAfter: PT10M
      connectionTimeToLive: PT10M
      evictExpiredConnections: true
      redirectStrategy: always
      request:
        connectTimeout: PT15S
        connectionRequestTimeout: PT30S
        socketTimeout: PT180S
        redirectEnabled: true
        expectContinueEnabled: true
        normalizeUri: true

grpc:
  client-config:
    service-config: |
      {
          "loadBalancingPolicy": "round_robin",
          "methodConfig": [
              {
                  "name": [
                      {
                          "service": "com.bees360.ResourcePool"
                      }
                  ],
                  "retryPolicy": {
                      "maxAttempts": 6,
                      "initialBackoff": "1s",
                      "maxBackoff": "60s",
                      "backoffMultiplier": 2,
                      "retryableStatusCodes": [
                          "INVALID_ARGUMENT",
                          "UNAVAILABLE",
                          "ABORTED",
                          "DEADLINE_EXCEEDED",
                          "INTERNAL",
                          "UNKNOWN"
                      ]
                  }
              }
          ],
          "retryThrottling": {
              "maxTokens": 100,
              "tokenRatio": 0.1
          }
      }

  client:
    projectIntegration:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    integrationSummaryClient:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pipelineService:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectIIManager:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectTagManager:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    userProvider:
      address: static://bees360-bifrost-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    resourcePool:
      address: static://${GRPC_SERVICE_NAME:bees360-resource-app-grpc}:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

rct.app:
  job:
    retry:
      retry-count: 3
      retry-delay: PT5M
  user:
    robot-email: <EMAIL>
  rct-task:
    update-status-vendor-submitted:
      on-returned-to-client.enabled: true
    update-status-appointment-set:
      on-inspection-scheduled-time-changed.enabled: true
      # 11: scheduling only
      service-types: [ 11 ]
