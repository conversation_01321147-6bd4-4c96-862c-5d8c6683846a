package com.bees360.rct.util;

public enum RctTaskStatus {
    NEW_REQUEST(14, "New Request"),
    ASSIGNED(15, "Assigned"),
    IN_SCHEDULING(141, "In Scheduling"),
    APPOINTMENT_SET(16, "Appointment Set"),
    IN_WRITE_UP(2400, "In Write Up"),
    VENDOR_SUBMITTED(2425, "Vendor Submitted"),
    UW_REVIEW(2470, "UW Review"),
    UW_APPROVED(2469, "UW Approved"),
    COMPLETED(17, "Completed"),
    SUSPENDED(2398, "Suspended"),
    CANCELLED(2397, "Cancelled"),
    ARCHIVED(20, "Archived"),
    PENDING(2646, "Pending"),
    DECLINED(2671, "Declined");

    private final int code;
    private final String display;

    RctTaskStatus(int code, String display) {
        this.code = code;
        this.display = display;
    }

    public int getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }
}
