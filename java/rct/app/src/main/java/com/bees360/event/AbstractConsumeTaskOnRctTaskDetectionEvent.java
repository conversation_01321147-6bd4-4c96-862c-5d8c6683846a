package com.bees360.event;

import com.bees360.event.registry.RctTaskDetectionEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.rct.RctApi;
import com.bees360.rct.config.RctDetectionProperties.RctGetTaskQuery;
import com.bees360.sdk.rct.ApiException;
import com.bees360.sdk.rct.api.TasksApi;
import com.bees360.sdk.rct.model.TaskDto;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@Log4j2
abstract class AbstractConsumeTaskOnRctTaskDetectionEvent
        extends AbstractNamedEventListener<RctTaskDetectionEvent> {
    private final RctApi<TasksApi> rctTasksApi;

    private final Duration detectionDuration;

    private final Integer batchSize;

    private final RctGetTaskQuery getTaskQuery;

    public AbstractConsumeTaskOnRctTaskDetectionEvent(
            @NonNull RctApi<TasksApi> rctTasksApi,
            @NonNull Duration detectionDuration,
            @NonNull Integer batchSize,
            @NonNull RctGetTaskQuery getTaskQuery) {
        this.rctTasksApi = rctTasksApi;
        this.detectionDuration = detectionDuration;
        this.batchSize = batchSize;
        this.getTaskQuery = getTaskQuery;
        log.info(
                "Created {}(rctTasksApi={},detectionDuration={})",
                this,
                rctTasksApi,
                detectionDuration);
    }

    @Override
    public void handle(RctTaskDetectionEvent event) throws IOException {
        List<TaskDto> tasks;
        try {
            tasks = fetchTasks(event.getTriggerTime());
        } catch (Exception ex) {
            // catch rct api exceptions
            if (!(ex instanceof IllegalArgumentException)
                    && ex.getCause() instanceof ApiException) {
                var exceptionCode = ((ApiException) ex.getCause()).getCode();
                log.warn(
                        "Fetch tasks failed: response code {} message: {}.",
                        exceptionCode,
                        ex.getMessage(),
                        ex);
            } else {
                log.error("Fetch tasks failed: response message: {}.", ex.getMessage(), ex);
            }
            return;
        }
        log.info("Received event: {}, and {} tasks found.", event, tasks.size());
        for (var task : tasks) {
            consumeTask(task.getId());
        }
    }

    private List<TaskDto> fetchTasks(Instant triggerTime) {
        var lastModifiedTime =
                OffsetDateTime.ofInstant(
                        triggerTime.minus(detectionDuration), ZoneId.systemDefault());
        List<TaskDto> addedTasks = new ArrayList<>();
        // 分页下标从1开始,因为0和1下标内容一致
        for (int pageNumber = 1; pageNumber < batchSize; pageNumber++) {
            final var finalPageNumber = pageNumber;
            var tasks =
                    rctTasksApi.apply(
                            api ->
                                    api.getTasks(
                                            getTaskQuery.getReferenceNumberOperator(),
                                            getTaskQuery.getReferenceNumberValues(),
                                            getTaskQuery.getExternalUniqueIdOperator(),
                                            getTaskQuery.getExternalUniqueIdValues(),
                                            getTaskQuery.getAccountIdOperator(),
                                            getTaskQuery.getAccountIdValues(),
                                            getTaskQuery.getPolicyNumberOperator(),
                                            getTaskQuery.getPolicyNumberValues(),
                                            getTaskQuery.getAddressIdOperator(),
                                            getTaskQuery.getAddressIdValues(),
                                            getTaskQuery.getIsVisibleOnInsuredPortalOperator(),
                                            getTaskQuery.getIsVisibleOnInsuredPortalValue(),
                                            getTaskQuery.getConsultantIdOperator(),
                                            getTaskQuery.getConsultantIdValues(),
                                            getTaskQuery.getUnderwriterIdOperator(),
                                            getTaskQuery.getUnderwriterIdValues(),
                                            getTaskQuery.getBrokerIdOperator(),
                                            getTaskQuery.getBrokerIdValues(),
                                            getTaskQuery.getAgencyIdOperator(),
                                            getTaskQuery.getAgencyIdValues(),
                                            getTaskQuery.getRequestTypeCodeOperator(),
                                            getTaskQuery.getRequestTypeCodeValues(),
                                            getTaskQuery.getWritingCompanyCodeOperator(),
                                            getTaskQuery.getWritingCompanyCodeValues(),
                                            getTaskQuery.getTaskStatusIdOperator(),
                                            getTaskQuery.getTaskStatusIdValues(),
                                            getTaskQuery.getTaskTypeIdOperator(),
                                            getTaskQuery.getTaskTypeIdValues(),
                                            getTaskQuery.getPriorityIdOperator(),
                                            getTaskQuery.getPriorityIdValues(),
                                            getTaskQuery.getDateAssignedOperator(),
                                            getTaskQuery.getDateAssignedValues(),
                                            getTaskQuery.getOriginalDueDateOperator(),
                                            getTaskQuery.getOriginalDueDateValues(),
                                            getTaskQuery.getDueDateOperator(),
                                            getTaskQuery.getDueDateValues(),
                                            getTaskQuery.getDateCompletedOperator(),
                                            getTaskQuery.getDateCompletedValues(),
                                            getTaskQuery.getOrderBy(),
                                            finalPageNumber,
                                            getTaskQuery.getPageSize(),
                                            getTaskQuery.getXVersion(),
                                            getTaskQuery.getLang()));
            // 由于没有其他的返回参数,只能通过空判断是否到达最后一页
            if (CollectionUtils.isEmpty(tasks)) {
                break;
            }
            for (var task : tasks) {
                var modifiedTime = task.getDateModified();
                if (ObjectUtils.isNotEmpty(modifiedTime)
                        && modifiedTime.isBefore(lastModifiedTime)) {
                    return addedTasks;
                }
                addedTasks.add(task);
            }
        }
        return addedTasks;
    }

    protected abstract void consumeTask(String taskId);
}
