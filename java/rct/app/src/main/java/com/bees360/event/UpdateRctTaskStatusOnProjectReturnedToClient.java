package com.bees360.event;

import com.bees360.event.registry.ProjectReturnedToClientEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.SetRctTaskStatusJob;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.util.Iterables;
import com.bees360.util.retry.RetryProperties;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.function.Predicate;

@Log4j2
public class UpdateRctTaskStatusOnProjectReturnedToClient
        extends AbstractNamedEventListener<ProjectReturnedToClientEvent> {
    private static final String RCT_INTEGRATION_TYPE = "RiskControl";
    private final JobScheduler jobScheduler;
    private final RetryProperties retry;
    private final int rctTaskStatusChangedTo;
    private final ExternalIntegrationProvider externalIntegrationProvider;

    private final Predicate<String> projectPredicate;

    public UpdateRctTaskStatusOnProjectReturnedToClient(
            @NonNull JobScheduler jobScheduler,
            @NonNull RetryProperties retry,
            int rctTaskStatusChangedTo,
            @NonNull ExternalIntegrationProvider externalIntegrationProvider,
            @NonNull Predicate<String> projectPredicate) {
        this.jobScheduler = jobScheduler;
        this.retry = retry;
        this.rctTaskStatusChangedTo = rctTaskStatusChangedTo;
        this.externalIntegrationProvider = externalIntegrationProvider;
        this.projectPredicate = projectPredicate;
        log.info(
                "Created"
                    + " {}(jobScheduler={},retry={},rctTaskStatusChangedTo={},externalIntegrationProvider={},projectPredicate={})",
                this,
                jobScheduler,
                retry,
                rctTaskStatusChangedTo,
                externalIntegrationProvider,
                projectPredicate);
    }

    @Override
    public void handle(ProjectReturnedToClientEvent event) throws IOException {
        log.info("Received event: {}", event);
        var integration = findRctIntegration(event.getProjectId());
        if (integration == null) {
            log.debug(
                    "No integration {} found for project {}.",
                    RCT_INTEGRATION_TYPE,
                    event.getProjectId());
            return;
        }
        if (!projectPredicate.test(event.getProjectId())) {
            return;
        }
        var payload =
                new SetRctTaskStatusJob()
                        .setTaskId(integration.getReferenceNumber())
                        .setTaskStatus(rctTaskStatusChangedTo);
        var job =
                RetryableJob.of(
                        Job.ofPayload(payload),
                        retry.getRetryCount(),
                        retry.getRetryDelay(),
                        retry.getRetryDelayIncreaseFactor());
        jobScheduler.schedule(job);
    }

    private ExternalIntegration findRctIntegration(String projectId) {
        var integrations = externalIntegrationProvider.findAllByProjectId(projectId);
        return Iterables.toStream(integrations)
                .filter(i -> StringUtils.equals(RCT_INTEGRATION_TYPE, i.getIntegrationType()))
                .findFirst()
                .orElse(null);
    }
}
