package com.bees360.rct.config;

import static com.bees360.resource.ResourcePools.buildPrefixCompositeResourcePool;

import com.bees360.grpc.config.GrpcClientConfig;
import com.bees360.http.HttpClient;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.resource.HttpSafeResourceClient;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.config.GrpcResourceClientConfig;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.net.URI;
import java.util.LinkedHashMap;
import java.util.Map;

@Import({
    GrpcClientConfig.class,
    GrpcResourceClientConfig.class,
    ApacheHttpClientConfig.class,
})
@Configuration
public class GeneralResourcePoolConfig {

    @Bean
    public ResourcePool resourcePool(ResourcePool grpcResourceClient, HttpClient apacheHttpClient) {
        Map<String, ResourcePool> map = new LinkedHashMap<>();
        map.put("http:", new HttpSafeResourceClient(URI.create("http:/"), apacheHttpClient));
        map.put("https:", new HttpSafeResourceClient(URI.create("https:/"), apacheHttpClient));
        map.put("", grpcResourceClient);
        return buildPrefixCompositeResourcePool(map.entrySet());
    }
}
