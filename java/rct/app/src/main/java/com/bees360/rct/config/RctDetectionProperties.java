package com.bees360.rct.config;

import com.bees360.sdk.rct.model.BooleanValue;
import com.bees360.sdk.rct.model.DateTimeOperator;
import com.bees360.sdk.rct.model.Identifier;
import com.bees360.sdk.rct.model.ModelBoolean;
import com.bees360.sdk.rct.model.Text;

import lombok.Data;

import java.time.Duration;
import java.util.List;

@Data
public class RctDetectionProperties {
    private Duration duration = Duration.ofDays(15);
    private Integer batchSize = 365;
    private RctGetTaskQuery getTaskQuery = new RctGetTaskQuery();
    private RctGetTaskQuery cancelTaskQuery = new RctGetTaskQuery();

    @Data
    public static class RctGetTaskQuery {
        private Text referenceNumberOperator;
        private List<String> referenceNumberValues;
        private Identifier externalUniqueIdOperator;
        private List<String> externalUniqueIdValues;
        private Identifier accountIdOperator;
        private List<String> accountIdValues;
        private Text policyNumberOperator;
        private List<String> policyNumberValues;
        private Identifier addressIdOperator;
        private List<String> addressIdValues;
        private ModelBoolean isVisibleOnInsuredPortalOperator;
        private BooleanValue isVisibleOnInsuredPortalValue;
        private Identifier consultantIdOperator;
        private List<String> consultantIdValues;
        private Identifier underwriterIdOperator;
        private List<String> underwriterIdValues;
        private Identifier brokerIdOperator;
        private List<String> brokerIdValues;
        private Identifier agencyIdOperator;
        private List<String> agencyIdValues;
        private Identifier requestTypeCodeOperator;
        private List<String> requestTypeCodeValues;
        private Identifier writingCompanyCodeOperator;
        private List<String> writingCompanyCodeValues;
        private Identifier taskStatusIdOperator;
        private List<String> taskStatusIdValues;
        private Identifier taskTypeIdOperator;
        private List<String> taskTypeIdValues;
        private Identifier priorityIdOperator;
        private List<String> priorityIdValues;
        private DateTimeOperator dateAssignedOperator;
        private List<String> dateAssignedValues;
        private DateTimeOperator originalDueDateOperator;
        private List<String> originalDueDateValues;
        private DateTimeOperator dueDateOperator;
        private List<String> dueDateValues;
        private DateTimeOperator dateCompletedOperator;
        private List<String> dateCompletedValues;
        private String orderBy = "DateModified desc";
        private Integer pageSize = 25;
        private String xVersion;
        private String lang;
    }
}
