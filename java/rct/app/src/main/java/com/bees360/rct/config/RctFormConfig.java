package com.bees360.rct.config;

import com.bees360.apolloconfig.annotation.RefreshableConfigurationProperties;
import com.bees360.event.EventListener;
import com.bees360.event.ScheduleUpdateRctFormOnTaskReady;
import com.bees360.event.ScheduleUpdateRctFormOnTaskReady.RctFormProperties;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.util.EventListeners;
import com.bees360.event.util.PipelineTaskChangedListeners;
import com.bees360.http.HttpClient;
import com.bees360.integration.IntegrationSummaryProvider;
import com.bees360.job.CollectDataAndUploadRctFormJobExecutor;
import com.bees360.job.JobScheduler;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.config.GrpcProjectIIMangerConfig;
import com.bees360.rct.RctApi;
import com.bees360.rct.config.RctCustomerConfig.RctCustomerProperties;
import com.bees360.rct.form.RctFormManager;
import com.bees360.resource.ResourcePool;
import com.bees360.sdk.rct.api.TasksApi;
import com.bees360.util.Functions;
import com.google.gson.Gson;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.net.URI;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

@Log4j2
@Configuration
@Import({
    AWSLambdaHttpClientConfig.class,
    RctCustomerConfig.class,
    GeneralResourcePoolConfig.class,
    GrpcProjectIIMangerConfig.class,
    AutoRegisterEventListenerConfig.class,
    AutoRegisterJobExecutorConfig.class,
})
@ConditionalOnProperty(value = "rct.form.enabled", havingValue = "true")
public class RctFormConfig {
    @Bean
    public EventListener scheduleUpdateRctFormListener(
            JobScheduler jobScheduler,
            ExternalIntegrationManager externalIntegrationManager,
            PipelineService pipelineService,
            @Qualifier("rctFormProvider")
                    BiFunction<String, ProjectII, List<RctCustomerProperties.FormData>>
                            rctFormProvider,
            ProjectIIManager projectIIManager) {
        var formProvider = formProvider(rctFormProvider, projectIIManager);
        var listener =
                new ScheduleUpdateRctFormOnTaskReady(
                        jobScheduler, externalIntegrationManager, pipelineService, formProvider);
        return PipelineTaskChangedListeners.forwardToStatusChangedListener(
                listener,
                EventListeners.getListenerName(listener.getClass()),
                "fill_out_form_on_rct",
                Message.PipelineStatus.READY);
    }

    /** Return a list of form template id by dataset and project id. */
    private BiFunction<String, String, List<RctFormProperties>> formProvider(
            BiFunction<String, ProjectII, List<RctCustomerProperties.FormData>> rctFormProvider,
            ProjectIIManager projectIIManager) {
        return (dataset, projectId) -> {
            var project = projectIIManager.findById(projectId);
            var forms = rctFormProvider.apply(dataset, project);
            return forms.stream()
                    .map(
                            f ->
                                    new RctFormProperties()
                                            .setFormId(f.getTemplateId())
                                            .setRecreateIfExist(f.isRecreateIfExist()))
                    .collect(Collectors.toList());
        };
    }

    @Bean
    public RctFormManager rctFormManager(
            RctApi<TasksApi> rctApi, FormProperties properties, ResourcePool resourcePool) {
        var formMap =
                properties.getDataset().stream()
                        .collect(
                                Collectors.toMap(
                                        FormProperties.FormData::getTemplateId,
                                        FormProperties.FormData::getName));
        return new RctFormManager(rctApi, formMap, resourcePool);
    }

    @Bean
    public CollectDataAndUploadRctFormJobExecutor collectDataAndUploadRctFormJobExecutor(
            RctFormManager rctFormManager,
            IntegrationSummaryProvider integrationSummaryProvider,
            @Qualifier("formTemplateProvider") UnaryOperator<String> formTemplateProvider,
            @Qualifier("httpFormDataProcessor") UnaryOperator<String> httpFormDataProcessor) {
        return new CollectDataAndUploadRctFormJobExecutor(
                rctFormManager,
                integrationSummaryProvider,
                formTemplateProvider,
                httpFormDataProcessor);
    }

    /** Return an internal template key for lambda function by form template id. */
    @Bean
    public UnaryOperator<String> formTemplateProvider(FormProperties properties) {
        var map =
                properties.getDataset().stream()
                        .collect(
                                Collectors.toMap(
                                        FormProperties.FormData::getTemplateId,
                                        FormProperties.FormData::getInternalTemplateKey));

        return map::get;
    }

    @Data
    @RefreshableConfigurationProperties(prefix = "rct.form")
    @Configuration
    public static class FormProperties {
        List<FormData> dataset;

        @Data
        public static class FormData {
            private String name;
            private String templateId;
            private String internalTemplateKey;
            private List<SectionData> section;
        }

        @Data
        public static class SectionData {
            private String name;
            private String templateId;
        }
    }

    /** 该function接受一个string，将其作为参数传给aws lambda function进行form template映射，并返回完整的form json */
    @Bean
    public UnaryOperator<String> httpFormDataProcessor(
            Function<String, URI> awsLambdaUriProvider, HttpClient awsLambdaHttpClient) {
        var gson = new Gson();
        UnaryOperator<String> provider =
                json -> {
                    var uri = awsLambdaUriProvider.apply("riskControl");
                    var request = new HttpPost(uri);
                    request.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
                    var response =
                            awsLambdaHttpClient.execute(
                                    request,
                                    Functions.combine(
                                            rsp ->
                                                    HttpClient.throwApiExceptionNon2xxResponse(
                                                            rsp,
                                                            HttpClient::convertResponseToString),
                                            HttpClient::convertResponseToString));
                    var formDataRsp = gson.fromJson(response, FormDataResponse.class);
                    return formDataRsp.getData();
                };
        log.info(
                "Created awsLambdaHttpClient{}(awsLambdaUriProvider={},awsLambdaHttpClient={})",
                provider,
                awsLambdaUriProvider,
                awsLambdaHttpClient);
        return provider;
    }

    @Data
    static class FormDataResponse {
        private String data;
        private String message;
    }
}
