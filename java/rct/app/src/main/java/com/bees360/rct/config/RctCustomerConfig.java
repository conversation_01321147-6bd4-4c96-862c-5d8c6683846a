package com.bees360.rct.config;

import com.bees360.apolloconfig.annotation.RefreshableConfigurationProperties;
import com.bees360.project.ProjectII;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.util.Iterables;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;

@Configuration
@EnableConfigurationProperties
@ConditionalOnProperty(name = "rct.customer-enabled", havingValue = "true")
public class RctCustomerConfig {

    @Configuration
    @RefreshableConfigurationProperties(prefix = "rct")
    @Getter
    @Setter
    public static class RctCustomerProperties {
        List<Dataset> dataset = new ArrayList<>();

        @Getter
        @Setter
        static class Dataset {
            private String id;
            private Map<ServiceTypeEnum, ServiceDataset> serviceMap = new HashMap<>();
        }

        @Getter
        @Setter
        public static class ServiceDataset {
            private List<FormData> forms;
        }

        @Getter
        @Setter
        static class FormData {
            private Set<String> ifSupplementalServiceIn;
            private String templateId;
            private boolean recreateIfExist;
        }
    }

    @Bean
    public Function<String, RctCustomerProperties.Dataset> customerDatasetProvider(
            RctCustomerProperties properties) {
        return dataset ->
                properties.getDataset().stream()
                        .filter(p -> StringUtils.equals(p.getId(), dataset))
                        .findFirst()
                        .orElse(null);
    }

    /** Get a list of form template by integration dataset and service type. */
    @Bean("rctFormProvider")
    public BiFunction<String, ProjectII, List<RctCustomerProperties.FormData>> rctFormProvider(
            Function<String, RctCustomerProperties.Dataset> customerDatasetProvider) {
        return (datasetId, projectII) -> {
            var dataset = customerDatasetProvider.apply(datasetId);
            if (dataset == null || dataset.getServiceMap().isEmpty()) {
                return List.of();
            }

            var serviceTypeMap = dataset.getServiceMap();
            var result = new ArrayList<RctCustomerProperties.FormData>();

            var forms =
                    Optional.ofNullable(serviceTypeMap.get(projectII.getServiceType()))
                            .map(RctCustomerProperties.ServiceDataset::getForms)
                            .orElse(List.of());
            var supplementalServices =
                    Optional.ofNullable(projectII.getSupplementalService()).orElse(List.of());
            for (var form : forms) {
                var included = form.getIfSupplementalServiceIn();
                if (CollectionUtils.isEmpty(included)
                        || Iterables.toStream(supplementalServices).anyMatch(included::contains)) {
                    result.add(form);
                }
            }
            return result;
        };
    }
}
