package com.bees360.event;

import com.bees360.project.ExternalIntegrationManager;
import com.bees360.rct.RctApi;
import com.bees360.rct.config.RctDetectionProperties;
import com.bees360.sdk.rct.api.TasksApi;

import lombok.NonNull;

import java.time.Duration;

public class CreateProjectIntegrationOnRctTaskDetectionEvent
        extends AbstractConsumeTaskOnRctTaskDetectionEvent {

    private static final String INTEGRATION_TYPE_RCT = "RiskControl";

    private final ExternalIntegrationManager externalIntegrationManager;

    private final String datasetId;

    public CreateProjectIntegrationOnRctTaskDetectionEvent(
            @NonNull RctApi<TasksApi> rctTasksApi,
            @NonNull String datasetId,
            @NonNull ExternalIntegrationManager externalIntegrationManager,
            @NonNull Duration detectionDuration,
            @NonNull Integer batchSize,
            @NonNull RctDetectionProperties.RctGetTaskQuery getTaskQuery) {
        super(rctTasksApi, detectionDuration, batchSize, getTaskQuery);
        this.externalIntegrationManager = externalIntegrationManager;
        this.datasetId = datasetId;
    }

    @Override
    protected void consumeTask(String taskId) {
        externalIntegrationManager.create(datasetId, INTEGRATION_TYPE_RCT, taskId);
    }
}
