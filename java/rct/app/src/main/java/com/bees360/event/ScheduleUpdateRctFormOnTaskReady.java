package com.bees360.event;

import com.bees360.api.InvalidArgumentException;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.job.Job;
import com.bees360.job.JobFuture;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.CollectDataAndUploadRctFormJob;
import com.bees360.listener.Listener;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;

@Log4j2
public class ScheduleUpdateRctFormOnTaskReady implements Listener<PipelineTaskChanged> {

    private final ExternalIntegrationManager integrationManager;

    private static final String INTEGRATION_TYPE = "RiskControl";

    private final JobScheduler jobScheduler;

    private final PipelineService pipelineService;

    // get a list of form template for update by dataset and project id.
    private final BiFunction<String, String, List<RctFormProperties>> formProvider;

    @Data
    @Accessors(chain = true)
    public static class RctFormProperties {
        private String formId;
        private boolean recreateIfExist;
    }

    public ScheduleUpdateRctFormOnTaskReady(
            JobScheduler jobScheduler,
            ExternalIntegrationManager integrationManager,
            PipelineService pipelineService,
            BiFunction<String, String, List<RctFormProperties>> formProvider) {
        this.jobScheduler = jobScheduler;
        this.integrationManager = integrationManager;
        this.pipelineService = pipelineService;
        this.formProvider = formProvider;
        log.info(
                "Created"
                    + " {}(jobScheduler={},integrationManager={},pipelineService={},formProvider={}).",
                this,
                this.jobScheduler,
                this.integrationManager,
                this.pipelineService,
                this.formProvider);
    }

    @Override
    public void execute(PipelineTaskChanged event) throws IOException {
        var projectId = event.getPipelineId();
        var integration = integrationManager.findAllByProjectId(projectId);
        var task =
                Iterables.toStream(integration)
                        .filter(i -> StringUtils.equals(i.getIntegrationType(), INTEGRATION_TYPE))
                        .findFirst()
                        .orElse(null);

        if (Objects.isNull(task)) {
            return;
        }

        List<JobFuture> jobFutures = new ArrayList<>();
        var forms = formProvider.apply(task.getDataset(), projectId);
        if (CollectionUtils.isEmpty(forms)) {
            return;
        }

        forms.forEach(
                f ->
                        Optional.ofNullable(
                                        scheduleFormJob(task, f.getFormId(), f.isRecreateIfExist()))
                                .ifPresent(jobFutures::add));

        setPipelineTaskWhenDone(jobFutures, projectId, event.getTaskDefKey());
        log.info("Successfully schedule job to upload project {} form on RiskControl.", projectId);
    }

    private JobFuture scheduleFormJob(
            ExternalIntegration task, String formTemplateId, boolean recreateIfExist) {
        var payload =
                CollectDataAndUploadRctFormJob.newBuilder()
                        .setProjectId(task.getProjectId())
                        .setTaskId(task.getReferenceNumber())
                        .setFormTemplateId(formTemplateId)
                        .setRecreateIfExist(recreateIfExist)
                        .build();
        return jobScheduler.schedule(
                RetryableJob.of(Job.ofPayload(payload), 5, Duration.ofMinutes(3), 1.0F));
    }

    private void setPipelineTaskWhenDone(
            List<JobFuture> jobFutures, String projectId, String taskKey) {
        jobFutures.forEach(
                future ->
                        Futures.addCallback(
                                future,
                                new SetPipelineTaskCallback(projectId, taskKey),
                                MoreExecutors.directExecutor()));
        Futures.whenAllSucceed(jobFutures)
                .run(
                        () ->
                                setPipelineTaskStatus(
                                        projectId, taskKey, Message.PipelineStatus.DONE, null),
                        MoreExecutors.directExecutor());
    }

    class SetPipelineTaskCallback implements FutureCallback<Void> {

        private final String pipelineId;

        private final String taskKey;

        public SetPipelineTaskCallback(String pipelineId, String taskKey) {
            this.pipelineId = pipelineId;
            this.taskKey = taskKey;
        }

        @Override
        public void onSuccess(@Nullable Void unused) {}

        @Override
        public void onFailure(Throwable throwable) {
            setPipelineTaskStatus(
                    pipelineId, taskKey, Message.PipelineStatus.ERROR, throwable.getMessage());
        }
    }

    private void setPipelineTaskStatus(
            String pipelineId, String taskKey, Message.PipelineStatus status, String comment) {
        try {
            pipelineService.setTaskStatus(pipelineId, taskKey, status, comment);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.error(
                    "Failed set pipeline '{}' key '{}' to '{}' due to {}",
                    pipelineId,
                    taskKey,
                    status,
                    e);
        }
    }
}
