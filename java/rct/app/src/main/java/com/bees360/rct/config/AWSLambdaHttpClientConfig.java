package com.bees360.rct.config;

import com.bees360.api.AbortedException;
import com.bees360.api.InternalException;
import com.bees360.api.UnavailableException;
import com.bees360.http.ApacheHttpClient;
import com.bees360.http.HttpClient;
import com.bees360.http.RetryHttpClient;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.oauth.OAuthHttpClient;
import com.bees360.oauth.OAuthToken;

import lombok.Data;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;

@Configuration
@Import({ApacheHttpClientConfig.class})
@ConditionalOnProperty(prefix = "http.aws-lambda", value = "enabled", havingValue = "true")
public class AWSLambdaHttpClientConfig {

    @Data
    @ConfigurationProperties(prefix = "http.aws-lambda")
    @Configuration
    static class AWSLambdaProperties {
        private String endpoint;
        private String clientSecret;
    }

    @ConfigurationProperties(prefix = "http.client.retry")
    @Bean
    public RetryHttpClient.RetryProperties retryProperties() {
        return RetryHttpClient.RetryProperties.builder()
                .include(
                        List.of(
                                UnavailableException.class,
                                IOException.class,
                                IllegalStateException.class,
                                InternalException.class,
                                AbortedException.class))
                .build();
    }

    @Bean
    public HttpClient awsLambdaHttpClient(
            ApacheHttpClient httpClient,
            AWSLambdaProperties properties,
            RetryHttpClient.RetryProperties retryProperties) {
        var oauthToken = OAuthToken.of(properties.getClientSecret(), LocalDateTime.MAX, null);
        return new RetryHttpClient(new OAuthHttpClient(httpClient, oauthToken), retryProperties);
    }

    @Bean
    public Function<String, URI> awsLambdaUriProvider(AWSLambdaProperties properties) {
        return s -> URI.create(properties.getEndpoint());
    }
}
