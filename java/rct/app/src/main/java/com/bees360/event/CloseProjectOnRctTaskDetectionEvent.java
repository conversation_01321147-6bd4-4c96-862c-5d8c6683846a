package com.bees360.event;

import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.CloseProjectJob;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.rct.RctApi;
import com.bees360.rct.config.RctDetectionProperties;
import com.bees360.sdk.rct.api.TasksApi;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.time.Duration;
import java.util.function.Supplier;

@Log4j2
public class CloseProjectOnRctTaskDetectionEvent
        extends AbstractConsumeTaskOnRctTaskDetectionEvent {

    private final ExternalIntegrationManager externalIntegrationManager;

    private static final String INTEGRATION_TYPE_RCT = "RiskControl";

    private final JobScheduler jobScheduler;

    private final Integer retryCount;
    private final Duration retryDelay;
    private final Float retryDelayIncreaseFactor;

    private final Supplier<String> aiUserHolder;

    public CloseProjectOnRctTaskDetectionEvent(
            @NonNull RctApi<TasksApi> rctTasksApi,
            @NonNull ExternalIntegrationManager externalIntegrationManager,
            @NonNull Duration detectionDuration,
            @NonNull Integer batchSize,
            @NonNull RctDetectionProperties.RctGetTaskQuery getCancelTaskQuery,
            @NonNull JobScheduler jobScheduler,
            Integer retryCount,
            Duration retryDelay,
            Float retryDelayIncreaseFactor,
            Supplier<String> aiUserHolder) {
        super(rctTasksApi, detectionDuration, batchSize, getCancelTaskQuery);
        this.externalIntegrationManager = externalIntegrationManager;
        this.jobScheduler = jobScheduler;
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
        this.retryDelayIncreaseFactor = retryDelayIncreaseFactor;
        this.aiUserHolder = aiUserHolder;
    }

    @Override
    protected void consumeTask(String taskId) {
        var integration = externalIntegrationManager.findByReference(INTEGRATION_TYPE_RCT, taskId);
        if (integration == null) {
            log.info(
                    "can't find integration with the type: {} and taskId:{}",
                    INTEGRATION_TYPE_RCT,
                    taskId);
            return;
        }
        var projectId = integration.getProjectId();
        var closeProjectJob = new CloseProjectJob(projectId, aiUserHolder.get(), null, null);
        jobScheduler.schedule(
                RetryableJob.of(
                        Job.ofPayload(closeProjectJob),
                        retryCount,
                        retryDelay,
                        retryDelayIncreaseFactor));
    }
}
