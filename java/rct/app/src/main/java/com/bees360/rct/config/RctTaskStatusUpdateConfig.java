package com.bees360.rct.config;

import com.bees360.apolloconfig.annotation.RefreshableConfigurationProperties;
import com.bees360.event.UpdateRctTaskStatusOnInspectionScheduledTimeChanged;
import com.bees360.event.UpdateRctTaskStatusOnProjectReturnedToClient;
import com.bees360.job.JobScheduler;
import com.bees360.job.SetRctTaskStatusJobExecutor;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.config.GrpcProjectIIMangerConfig;
import com.bees360.project.config.GrpcProjectTagManagerConfig;
import com.bees360.project.tag.Message.ProjectTagType;
import com.bees360.project.tag.ProjectTagProvider;
import com.bees360.rct.RctApi;
import com.bees360.rct.util.RctTaskStatus;
import com.bees360.sdk.rct.api.TasksApi;
import com.bees360.util.retry.RetryProperties;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Set;
import java.util.function.Predicate;

@Log4j2
@Import({
    GrpcProjectIIMangerConfig.class,
    GrpcProjectTagManagerConfig.class,
    RctAppJobRetryPropertiesConfig.class,
})
@Configuration
@ConditionalOnProperty(
        prefix = "rct.app.rct-task",
        name = "enabled-update-status",
        havingValue = "true")
@EnableConfigurationProperties
public class RctTaskStatusUpdateConfig {

    @Getter
    @Setter
    static class UpdateVendorSubmittedProperties {
        // match if any service type matches
        private Set<Integer> serviceTypes;
        // match if none project tag matches
        private Set<String> withoutProjectTags;
    }

    @Bean
    @RefreshableConfigurationProperties(prefix = "rct.app.rct-task.update-status-vendor-submitted")
    UpdateVendorSubmittedProperties vendorSubmittedProperties() {
        return new UpdateVendorSubmittedProperties();
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "rct.app.rct-task.update-status-vendor-submitted.on-returned-to-client",
            name = "enabled",
            havingValue = "true")
    UpdateRctTaskStatusOnProjectReturnedToClient updateRctTaskStatusOnProjectReturnedToClient(
            JobScheduler jobScheduler,
            @Qualifier("rctAppJobRetryProperties") RetryProperties retryProperties,
            ExternalIntegrationManager grpcExternalIntegrationManager,
            ProjectIIRepository projectIIRepository,
            ProjectTagProvider projectTagProvider,
            UpdateVendorSubmittedProperties properties) {
        Predicate<String> projectPredicate =
                projectId -> {
                    var project = projectIIRepository.findById(projectId);
                    var serviceTypes = properties.getServiceTypes();
                    var serviceTypeMatch =
                            serviceTypes != null
                                    && serviceTypes.contains(project.getServiceType().getCode());
                    if (!serviceTypeMatch) {
                        return false;
                    }
                    if (CollectionUtils.isNotEmpty(properties.getWithoutProjectTags())) {
                        var withoutTags = properties.getWithoutProjectTags();
                        for (var tagType : ProjectTagType.values()) {
                            if (tagType == ProjectTagType.UNRECOGNIZED) {
                                continue;
                            }
                            var projectTags =
                                    projectTagProvider.findByProjectIdAndType(projectId, tagType);
                            if (projectTags.stream()
                                    .anyMatch(t -> withoutTags.contains(t.getTitle()))) {
                                return false;
                            }
                        }
                    }
                    return true;
                };
        log.info("Create projectPredicate with {}", properties);
        return new UpdateRctTaskStatusOnProjectReturnedToClient(
                jobScheduler,
                retryProperties,
                RctTaskStatus.VENDOR_SUBMITTED.getCode(),
                grpcExternalIntegrationManager,
                projectPredicate);
    }

    @Getter
    @Setter
    static class UpdateAppointmentSetProperties {
        private Set<Integer> serviceTypes;
    }

    @Bean
    @RefreshableConfigurationProperties(prefix = "rct.app.rct-task.update-status-appointment-set")
    UpdateAppointmentSetProperties updateAppointmentSetProperties() {
        return new UpdateAppointmentSetProperties();
    }

    @Bean
    @ConditionalOnProperty(
            prefix =
                    "rct.app.rct-task.update-status-appointment-set.on-inspection-scheduled-time-changed",
            name = "enabled",
            havingValue = "true")
    UpdateRctTaskStatusOnInspectionScheduledTimeChanged
            updateRctTaskStatusOnInspectionScheduledTimeChanged(
                    JobScheduler jobScheduler,
                    @Qualifier("rctAppJobRetryProperties") RetryProperties retryProperties,
                    ExternalIntegrationManager grpcExternalIntegrationManager,
                    ProjectIIRepository projectIIRepository,
                    UpdateAppointmentSetProperties properties) {
        Predicate<ProjectII> projectPredicate =
                projectII -> {
                    var serviceTypes = properties.getServiceTypes();
                    return serviceTypes != null
                            && serviceTypes.contains(projectII.getServiceType().getCode());
                };
        log.info("Create projectPredicate with {}", properties);
        return new UpdateRctTaskStatusOnInspectionScheduledTimeChanged(
                jobScheduler,
                retryProperties,
                RctTaskStatus.APPOINTMENT_SET.getCode(),
                grpcExternalIntegrationManager,
                projectIIRepository,
                projectPredicate);
    }

    @Bean
    SetRctTaskStatusJobExecutor setRctTaskStatusJobExecutor(RctApi<TasksApi> rctTasksApi) {
        return new SetRctTaskStatusJobExecutor(rctTasksApi);
    }
}
