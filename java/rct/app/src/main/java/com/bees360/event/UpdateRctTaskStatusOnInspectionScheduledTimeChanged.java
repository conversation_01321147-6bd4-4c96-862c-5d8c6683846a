package com.bees360.event;

import com.bees360.event.registry.InspectionScheduledTimeChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.SetRctTaskStatusJob;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.bees360.util.Iterables;
import com.bees360.util.retry.RetryProperties;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;
import java.util.function.Predicate;

@Log4j2
public class UpdateRctTaskStatusOnInspectionScheduledTimeChanged
        extends AbstractNamedEventListener<InspectionScheduledTimeChanged> {
    private static final String RCT_INTEGRATION_TYPE = "RiskControl";
    private final JobScheduler jobScheduler;
    private final RetryProperties retry;
    private final int rctTaskStatusChangedTo;
    private final ExternalIntegrationProvider externalIntegrationProvider;
    private final ProjectIIRepository projectIIRepository;
    private final Predicate<ProjectII> projectIIPredicate;
    private static final DateTimeFormatter DATE_TIME_FORMATTER =
            DateTimeFormatter.ofPattern("MM/dd/yyyy, hh:mm a");

    private static final DateTimeFormatter DATE_TIME_FORMATTER_WITH_TZ =
            DateTimeFormatter.ofPattern("MM/dd/yyyy, hh:mm a zzz");
    private static final ZoneId DEFAULT_ZONE_ID = ZoneId.of("America/Chicago");

    public UpdateRctTaskStatusOnInspectionScheduledTimeChanged(
            @NonNull JobScheduler jobScheduler,
            @NonNull RetryProperties retry,
            int rctTaskStatusChangedTo,
            @NonNull ExternalIntegrationProvider externalIntegrationProvider,
            @NonNull ProjectIIRepository projectIIRepository,
            @NonNull Predicate<ProjectII> projectIIPredicate) {
        this.jobScheduler = jobScheduler;
        this.retry = retry;
        this.rctTaskStatusChangedTo = rctTaskStatusChangedTo;
        this.externalIntegrationProvider = externalIntegrationProvider;
        this.projectIIRepository = projectIIRepository;
        this.projectIIPredicate = projectIIPredicate;
        log.info(
                "Created"
                    + " {}(jobScheduler={},retry={},rctTaskStatusChangedTo={},externalIntegrationProvider={},projectIIRepository={},projectIIPredicate={})",
                this,
                jobScheduler,
                retry,
                rctTaskStatusChangedTo,
                externalIntegrationProvider,
                projectIIRepository,
                projectIIPredicate);
    }

    @Override
    public void handle(InspectionScheduledTimeChanged event) throws IOException {
        var project = projectIIRepository.findById(event.getProjectId());
        if (!projectIIPredicate.test(project)) {
            return;
        }
        var integration = findRctIntegration(event.getProjectId());
        if (integration == null) {
            log.debug(
                    "No integration {} found for project {}.",
                    RCT_INTEGRATION_TYPE,
                    event.getProjectId());
            return;
        }
        var notes = createNotes(event, project.getPolicy().getAddress().getTimeZone());
        var payload =
                new SetRctTaskStatusJob()
                        .setDataset(integration.getDataset())
                        .setTaskId(integration.getReferenceNumber())
                        .setTaskStatus(rctTaskStatusChangedTo)
                        .setNotes(notes)
                        .setAppendNotes(true);

        log.info("Try to set task status `{}` on event: {}", payload, event);
        var job =
                RetryableJob.of(
                        Job.ofPayload(payload),
                        retry.getRetryCount(),
                        retry.getRetryDelay(),
                        retry.getRetryDelayIncreaseFactor());
        jobScheduler.schedule(job);
    }

    private String createNotes(InspectionScheduledTimeChanged event, TimeZone timeZone) {
        var notes = new StringBuilder();
        if (event.getScheduledTime() == null) {
            notes.append("Scheduled inspection time has been removed");
        } else {
            var datetime = formatTime(event.getScheduledTime(), timeZone);
            notes.append(String.format("Inspection is scheduled for %s", datetime));
        }
        notes.append("\r\n");
        notes.append(String.format("updated at %s", formatTime(event.getUpdatedTime(), timeZone)));
        return notes.toString();
    }

    private String formatTime(long timestamp, TimeZone timeZone) {
        var instant = Instant.ofEpochMilli(timestamp);
        if (timeZone == null) {
            return DATE_TIME_FORMATTER_WITH_TZ.format(instant.atZone(DEFAULT_ZONE_ID));
        }
        return DATE_TIME_FORMATTER.format(instant.atZone(timeZone.toZoneId()));
    }

    private ExternalIntegration findRctIntegration(String projectId) {
        var integrations = externalIntegrationProvider.findAllByProjectId(projectId);
        return Iterables.toStream(integrations)
                .filter(i -> StringUtils.equals(RCT_INTEGRATION_TYPE, i.getIntegrationType()))
                .findFirst()
                .orElse(null);
    }
}
