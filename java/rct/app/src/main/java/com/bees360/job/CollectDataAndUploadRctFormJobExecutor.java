package com.bees360.job;

import com.bees360.image.Message.ImageMessage;
import com.bees360.image.Message.ImageMessage.Resource.Type;
import com.bees360.integration.IntegrationSummary;
import com.bees360.integration.IntegrationSummaryProvider;
import com.bees360.integration.Message;
import com.bees360.job.registry.CollectDataAndUploadRctFormJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.rct.form.RctFormManager;
import com.bees360.util.ProtoStructAdapter;
import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Struct;
import com.google.protobuf.util.JsonFormat;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.UnaryOperator;

@Log4j2
public class CollectDataAndUploadRctFormJobExecutor
        extends AbstractJobExecutor<CollectDataAndUploadRctFormJob> {

    private final RctFormManager rctFormManager;

    private final IntegrationSummaryProvider summaryProvider;

    private final UnaryOperator<String> formTemplateProvider;

    private final UnaryOperator<String> formDataProcessor;

    private static final Gson gson = new Gson();

    public CollectDataAndUploadRctFormJobExecutor(
            RctFormManager rctFormManager,
            IntegrationSummaryProvider summaryProvider,
            UnaryOperator<String> formTemplateProvider,
            UnaryOperator<String> formDataProcessor) {
        this.rctFormManager = rctFormManager;
        this.summaryProvider = summaryProvider;
        this.formTemplateProvider = formTemplateProvider;
        this.formDataProcessor = formDataProcessor;
        log.info(
                "Created"
                    + " {}(rctFormManager={},summaryProvider={},formTemplateProvider={},formDataProcessor={}).",
                this,
                this.rctFormManager,
                this.summaryProvider,
                this.formTemplateProvider,
                this.formDataProcessor);
    }

    @Override
    protected void handle(CollectDataAndUploadRctFormJob job) throws IOException {
        log.info("Begin to handle job {} for upload RiskControl form.", job);
        var projectId = job.getProjectId();
        var summary = summaryProvider.collectByProjectId(projectId);

        if (Objects.isNull(summary)) {
            throw new IllegalArgumentException(
                    String.format(
                            "Integration summary for update project %s to RiskControl is"
                                    + " illegal.",
                            projectId));
        }

        var formId =
                getOrAddTaskFormIfNotExist(
                        job.getTaskId(), job.getFormTemplateId(), job.isRecreateIfExist());
        var formJson = process(projectId, job.getFormTemplateId(), preHandle(summary));

        rctFormManager.updateFormByFields(job.getTaskId(), formId, formJson);
        rctFormManager.uploadTaskFormPhotos(
                job.getTaskId(),
                formId,
                formJson,
                summary.getImageSummary().getImageList(),
                Type.MIDDLE);
    }

    private Message.IntegrationSummaryMessage preHandle(IntegrationSummary summary) {
        var builder = summary.toMessage().toBuilder();
        var imageSummaryBld = summary.getImageSummary().toBuilder();
        var images = new ArrayList<ImageMessage>();
        for (var image : imageSummaryBld.getImageList()) {
            images.add(
                    ImageMessage.newBuilder()
                            .setId(image.getId())
                            .addAllTag(image.getTagList())
                            .build());
        }
        imageSummaryBld.clearImage();
        builder.setImageSummary(imageSummaryBld.addAllImage(images));
        return builder.build();
    }

    private String process(
            String projectId, String formTemplateId, Message.IntegrationSummaryMessage summary) {
        var templateKey = formTemplateProvider.apply(formTemplateId);
        if (templateKey == null) {
            throw new IllegalArgumentException(
                    String.format(
                            "No internal template key found for process RiskControl form"
                                    + " template %s ",
                            formTemplateId));
        }

        Map<String, Object> map = new HashMap<>();
        map.put("formTemplateId", templateKey);
        try {
            var formData = convertFormData(summary);
            map.put("formData", formData);
            var formDataJson = gson.toJson(map);
            return formDataProcessor.apply(formDataJson);
        } catch (Exception e) {
            throw new IllegalStateException(
                    String.format(
                            "Failed to process integration summary for project %s with template key"
                                    + " %s to RickControl.",
                            projectId, templateKey),
                    e);
        }
    }

    private String getOrAddTaskFormIfNotExist(
            String taskId, String templateId, boolean recreateIfExist) {
        final var formId = rctFormManager.getFormByTaskId(taskId, templateId);
        if (formId != null && recreateIfExist) {
            rctFormManager.deleteTaskForm(taskId, formId);
            return rctFormManager.addTaskForm(taskId, templateId);
        }
        if (formId == null) {
            return rctFormManager.addTaskForm(taskId, templateId);
        }
        return formId;
    }

    private String convertFormData(Message.IntegrationSummaryMessage summary)
            throws InvalidProtocolBufferException {
        var summaryJson = JsonFormat.printer().print(summary);
        var map = gson.fromJson(summaryJson, Map.class);
        var reportSummary = summary.getReportSummary();

        // Take additional properties as report summary if exists.
        if (!Struct.getDefaultInstance().equals(reportSummary.getAdditionalProperties())) {
            var reportSummaryJson =
                    ProtoStructAdapter.structToJson(reportSummary.getAdditionalProperties());
            map.put("reportSummary", reportSummaryJson);
        }

        return gson.toJson(map);
    }
}
