package com.bees360.rct.config;

import com.bees360.util.retry.RetryProperties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties
public class RctAppJobRetryPropertiesConfig {

    @Bean
    @ConfigurationProperties(prefix = "rct.app.job.retry")
    RetryProperties rctAppJobRetryProperties() {
        return new RetryProperties();
    }
}
