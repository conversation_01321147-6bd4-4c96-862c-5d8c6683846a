package com.bees360.rct;

import com.bees360.apolloconfig.config.ApolloClientConfig;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.event.CloseProjectOnRctTaskDetectionEvent;
import com.bees360.event.CreateProjectIntegrationOnRctTaskDetectionEvent;
import com.bees360.event.EventDispatcher;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.grpc.config.GrpcClientConfig;
import com.bees360.integration.config.GrpcIntegrationSummaryClientConfig;
import com.bees360.job.JobScheduler;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.pipeline.config.GrpcPipelineClientConfig;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.config.GrpcExternalIntegrationManagerConfig;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.rct.RctApp.Config;
import com.bees360.rct.config.RctAppUserProperties;
import com.bees360.rct.config.RctDataSetProperties;
import com.bees360.rct.config.RctDetectionProperties;
import com.bees360.rct.config.RctFormConfig;
import com.bees360.rct.config.RctTaskStatusUpdateConfig;
import com.bees360.sdk.rct.ApiClient;
import com.bees360.sdk.rct.api.TasksApi;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.user.config.GrpcUserProviderConfig;
import com.bees360.util.Iterables;
import com.bees360.util.SingletonLazyHolder;
import com.bees360.util.retry.RetryProperties;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Comparator;
import java.util.function.Supplier;

@Log4j2
@Import({
    GrpcExternalIntegrationManagerConfig.class,
    GrpcIntegrationSummaryClientConfig.class,
    GrpcPipelineClientConfig.class,
    RctOAuthConfig.class,
    RctHttpClientConfig.class,
    Config.class,
    RctFormConfig.class,
    GrpcClientConfig.class,
    GrpcUserProviderConfig.class,

    // config
    RabbitApiConfig.class,
    RabbitEventPublisher.class,
    RabbitEventDispatcher.class,
    AutoRegisterEventListenerConfig.class,
    AutoRegisterJobExecutorConfig.class,
    RabbitJobScheduler.class,
    RabbitJobDispatcher.class,
    RctTaskStatusUpdateConfig.class,

    // apollo
    ApolloClientConfig.class,
})
@EnableConfigurationProperties
@EnableEncryptableProperties
@ApplicationAutoConfig(exclude = {DataSourceAutoConfiguration.class})
public class RctApp {

    public static void main(final String[] args) {
        ExitableSpringApplication.run(RctApp.class, args);
    }

    @Configuration
    public static class Config {

        @Autowired private ExternalIntegrationManager externalIntegrationManager;

        @Bean
        @ConfigurationProperties("rct.app.detection")
        public RctDetectionProperties rctTaskDetectionProperties() {
            return new RctDetectionProperties();
        }

        @Bean
        @ConfigurationProperties("rct.app.dataset")
        public RctDataSetProperties rctDataSetProperties() {
            return new RctDataSetProperties();
        }

        @Bean
        @ConfigurationProperties("rct.app.user")
        public RctAppUserProperties rctAppUserProperties() {
            return new RctAppUserProperties();
        }

        @Bean
        @ConditionalOnProperty(
                prefix = "rct.app.job.create-project",
                name = "enabled",
                havingValue = "true",
                matchIfMissing = true)
        public CreateProjectIntegrationOnRctTaskDetectionEvent
                createProjectIntegrationOnRctTaskDetectionEvent(
                        RctApi<TasksApi> rctApi,
                        RctDataSetProperties rctDataSetProperties,
                        RctDetectionProperties rctTaskDetectionProperties) {
            return new CreateProjectIntegrationOnRctTaskDetectionEvent(
                    rctApi,
                    rctDataSetProperties.getId(),
                    externalIntegrationManager,
                    rctTaskDetectionProperties.getDuration(),
                    rctTaskDetectionProperties.getBatchSize(),
                    rctTaskDetectionProperties.getGetTaskQuery());
        }

        @Bean
        @ConditionalOnProperty(
                prefix = "rct.app.job.close-project",
                name = "enabled",
                havingValue = "true")
        public CloseProjectOnRctTaskDetectionEvent closeProjectOnRctTaskDetectionEvent(
                RctApi<TasksApi> rctApi,
                RctDetectionProperties rctTaskDetectionProperties,
                JobScheduler jobScheduler,
                RetryProperties rctAppJobRetryProperties,
                @Qualifier("robotUserIdHolder") Supplier<String> robotUserIdHolder,
                EventDispatcher eventDispatcher) {
            CloseProjectOnRctTaskDetectionEvent closeProjectOnRctTaskDetectionEvent =
                    new CloseProjectOnRctTaskDetectionEvent(
                            rctApi,
                            externalIntegrationManager,
                            rctTaskDetectionProperties.getDuration(),
                            rctTaskDetectionProperties.getBatchSize(),
                            rctTaskDetectionProperties.getCancelTaskQuery(),
                            jobScheduler,
                            rctAppJobRetryProperties.getRetryCount(),
                            rctAppJobRetryProperties.getRetryDelay(),
                            rctAppJobRetryProperties.getRetryDelayIncreaseFactor(),
                            robotUserIdHolder);
            eventDispatcher.enlist(closeProjectOnRctTaskDetectionEvent);
            return closeProjectOnRctTaskDetectionEvent;
        }

        @Bean
        Supplier<String> robotUserIdHolder(
                UserProvider userProvider, RctAppUserProperties rctAppUserProperties) {
            SingletonLazyHolder<String> robotUserIdHolder =
                    new SingletonLazyHolder<>(
                            () -> {
                                var robotUser =
                                        Iterables.toList(
                                                        userProvider.findUserByEmail(
                                                                rctAppUserProperties
                                                                        .getRobotEmail()))
                                                .stream()
                                                .min(Comparator.comparing(User::getId))
                                                .orElse(null);
                                if (robotUser == null) {
                                    throw new IllegalStateException(
                                            String.format(
                                                    "Robot User with email %s not found.",
                                                    rctAppUserProperties.getRobotEmail()));
                                }
                                return robotUser.getId();
                            });
            log.info(
                    "Created {}(userProvider={},robotUserEmail={})",
                    robotUserIdHolder,
                    userProvider,
                    rctAppUserProperties.getRobotEmail());
            return robotUserIdHolder;
        }
    }

    @Bean
    TasksApi tasksApi(ApiClient rctApiClient) {
        return new TasksApi(rctApiClient);
    }

    @Bean
    public RctApi<TasksApi> rctApi(TasksApi tasksApi) {
        return RctApi.of(tasksApi);
    }
}
