package com.bees360.job.registry;

import com.bees360.codec.ProtoGsonDecoder;
import com.bees360.project.ExternalIntegration;
import com.google.gson.annotations.JsonAdapter;

import lombok.Data;

@Data
@JobPayload("create_from_project_integration.RiskControl")
public class CreateProjectFromRctIntegrationJob {

    @JsonAdapter(ProtoGsonDecoder.class)
    private ExternalIntegration projectIntegration;
}
