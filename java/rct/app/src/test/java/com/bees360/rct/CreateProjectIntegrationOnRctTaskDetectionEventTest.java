package com.bees360.rct;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.bees360.event.CreateProjectIntegrationOnRctTaskDetectionEvent;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.RctTaskDetectionEvent;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.rct.config.RctAppJobRetryPropertiesConfig;
import com.bees360.rct.config.RctDetectionProperties;
import com.bees360.rct.config.RctDetectionProperties.RctGetTaskQuery;
import com.bees360.sdk.rct.ApiException;
import com.bees360.sdk.rct.api.TasksApi;
import com.bees360.sdk.rct.model.TaskDto;
import com.bees360.user.UserProvider;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.io.IOException;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Executor;

@SpringBootTest(classes = {CreateProjectIntegrationOnRctTaskDetectionEventTest.Config.class})
@ActiveProfiles("test")
@EnableConfigurationProperties
public class CreateProjectIntegrationOnRctTaskDetectionEventTest {

    @Import({
        RctApp.Config.class,
        InMemoryJobScheduler.class,
        RctAppJobRetryPropertiesConfig.class,
        InMemoryEventPublisher.class
    })
    @Configuration
    static class Config {

        @Bean
        ExternalIntegrationManager externalIntegrationManager() {
            return mock(ExternalIntegrationManager.class);
        }

        @Bean
        TasksApi tasksApi() {
            return mock(TasksApi.class);
        }

        @Bean
        RctApi<TasksApi> rctApi(TasksApi tasksApi) {
            return RctApi.of(tasksApi);
        }

        @Bean
        UserProvider userProvider() {
            return mock(UserProvider.class);
        }

        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @Autowired
    CreateProjectIntegrationOnRctTaskDetectionEvent createProjectIntegrationOnRctTaskDetectionEvent;

    @Autowired TasksApi tasksApi;

    @Autowired RctDetectionProperties rctTaskDetectionProperties;

    @Autowired ExternalIntegrationManager externalIntegrationManager;

    @Test
    public void fetchTaskAndCreateProjectTest() throws IOException, ApiException {
        var event = new RctTaskDetectionEvent();
        event.setTriggerTime(Instant.now());

        var pageSize = rctTaskDetectionProperties.getGetTaskQuery().getPageSize();
        var tasks = randomTasks(RandomUtils.nextInt(pageSize, pageSize + 10), null);
        var lastModifiedTime =
                OffsetDateTime.ofInstant(
                        Instant.now().minus(rctTaskDetectionProperties.getDuration()),
                        ZoneId.systemDefault());
        var legalTasks = randomTasks(RandomUtils.nextInt(0, 3), lastModifiedTime);
        var illegalTasks = randomTasks(RandomUtils.nextInt(0, 3), lastModifiedTime.minusHours(1));
        tasks.addAll(legalTasks);
        tasks.addAll(illegalTasks);
        tasks.sort(Comparator.comparing(TaskDto::getDateModified).reversed());

        mockFetchTaskResult(tasks);
        createProjectIntegrationOnRctTaskDetectionEvent.handle(event);
        // 边界时间Task会被记入,而超过边界时间的不会
        verify(externalIntegrationManager, times(tasks.size() - illegalTasks.size()))
                .create(anyString(), anyString(), anyString());
    }

    @Test
    public void fetchTaskAndIgnoreRctServiceUnavailableExceptionTest() throws ApiException {
        var event = new RctTaskDetectionEvent();
        event.setTriggerTime(Instant.now());
        var timeoutException = new ApiException(0, "mock timeout exception.");
        var getTaskQuery = rctTaskDetectionProperties.getGetTaskQuery();
        mockThrowException(timeoutException, getTaskQuery, 1);
        // ignore rct service connect timeout exception
        assertDoesNotThrow(() -> createProjectIntegrationOnRctTaskDetectionEvent.handle(event));
        // ignore rct service unavailable exception
        var serviceUnavailableException = new ApiException(503, "mock Service Unavailable.");
        mockThrowException(serviceUnavailableException, getTaskQuery, 1);
        assertDoesNotThrow(() -> createProjectIntegrationOnRctTaskDetectionEvent.handle(event));
    }

    private List<TaskDto> randomTasks(int size, OffsetDateTime dateModified) {
        if (dateModified == null) {
            dateModified = OffsetDateTime.now();
        }
        var tasks = new ArrayList<TaskDto>();
        for (int i = 0; i < size; i++) {
            var task = new TaskDto();
            task.setId(RandomStringUtils.randomNumeric(8));
            task.setDateModified(dateModified);
            tasks.add(task);
        }
        return tasks;
    }

    private void mockFetchTaskResult(List<TaskDto> tasks) throws ApiException {
        var getTaskQuery = rctTaskDetectionProperties.getGetTaskQuery();
        var batchSize = rctTaskDetectionProperties.getBatchSize();
        // 分页mock数据
        for (var pageNumber = 1; pageNumber < batchSize; pageNumber++) {
            int fromIndex = (pageNumber - 1) * getTaskQuery.getPageSize();
            if (fromIndex > tasks.size()) {
                break;
            }
            int toIndex = Math.min(pageNumber * getTaskQuery.getPageSize(), tasks.size());
            var pageTasks = tasks.subList(fromIndex, toIndex);
            mockFetchTaskPageResult(getTaskQuery, pageTasks, pageNumber);
        }
    }

    private void mockFetchTaskPageResult(
            RctGetTaskQuery getTaskQuery, List<TaskDto> tasks, int pageNumber) throws ApiException {
        doReturn(tasks)
                .when(tasksApi)
                .getTasks(
                        getTaskQuery.getReferenceNumberOperator(),
                        getTaskQuery.getReferenceNumberValues(),
                        getTaskQuery.getExternalUniqueIdOperator(),
                        getTaskQuery.getExternalUniqueIdValues(),
                        getTaskQuery.getAccountIdOperator(),
                        getTaskQuery.getAccountIdValues(),
                        getTaskQuery.getPolicyNumberOperator(),
                        getTaskQuery.getPolicyNumberValues(),
                        getTaskQuery.getAddressIdOperator(),
                        getTaskQuery.getAddressIdValues(),
                        getTaskQuery.getIsVisibleOnInsuredPortalOperator(),
                        getTaskQuery.getIsVisibleOnInsuredPortalValue(),
                        getTaskQuery.getConsultantIdOperator(),
                        getTaskQuery.getConsultantIdValues(),
                        getTaskQuery.getUnderwriterIdOperator(),
                        getTaskQuery.getUnderwriterIdValues(),
                        getTaskQuery.getBrokerIdOperator(),
                        getTaskQuery.getBrokerIdValues(),
                        getTaskQuery.getAgencyIdOperator(),
                        getTaskQuery.getAgencyIdValues(),
                        getTaskQuery.getRequestTypeCodeOperator(),
                        getTaskQuery.getRequestTypeCodeValues(),
                        getTaskQuery.getWritingCompanyCodeOperator(),
                        getTaskQuery.getWritingCompanyCodeValues(),
                        getTaskQuery.getTaskStatusIdOperator(),
                        getTaskQuery.getTaskStatusIdValues(),
                        getTaskQuery.getTaskTypeIdOperator(),
                        getTaskQuery.getTaskTypeIdValues(),
                        getTaskQuery.getPriorityIdOperator(),
                        getTaskQuery.getPriorityIdValues(),
                        getTaskQuery.getDateAssignedOperator(),
                        getTaskQuery.getDateAssignedValues(),
                        getTaskQuery.getOriginalDueDateOperator(),
                        getTaskQuery.getOriginalDueDateValues(),
                        getTaskQuery.getDueDateOperator(),
                        getTaskQuery.getDueDateValues(),
                        getTaskQuery.getDateCompletedOperator(),
                        getTaskQuery.getDateCompletedValues(),
                        getTaskQuery.getOrderBy(),
                        pageNumber,
                        getTaskQuery.getPageSize(),
                        getTaskQuery.getXVersion(),
                        getTaskQuery.getLang());
    }

    private void mockThrowException(Exception ex, RctGetTaskQuery getTaskQuery, int pageNumber)
            throws ApiException {
        doThrow(ex)
                .when(tasksApi)
                .getTasks(
                        getTaskQuery.getReferenceNumberOperator(),
                        getTaskQuery.getReferenceNumberValues(),
                        getTaskQuery.getExternalUniqueIdOperator(),
                        getTaskQuery.getExternalUniqueIdValues(),
                        getTaskQuery.getAccountIdOperator(),
                        getTaskQuery.getAccountIdValues(),
                        getTaskQuery.getPolicyNumberOperator(),
                        getTaskQuery.getPolicyNumberValues(),
                        getTaskQuery.getAddressIdOperator(),
                        getTaskQuery.getAddressIdValues(),
                        getTaskQuery.getIsVisibleOnInsuredPortalOperator(),
                        getTaskQuery.getIsVisibleOnInsuredPortalValue(),
                        getTaskQuery.getConsultantIdOperator(),
                        getTaskQuery.getConsultantIdValues(),
                        getTaskQuery.getUnderwriterIdOperator(),
                        getTaskQuery.getUnderwriterIdValues(),
                        getTaskQuery.getBrokerIdOperator(),
                        getTaskQuery.getBrokerIdValues(),
                        getTaskQuery.getAgencyIdOperator(),
                        getTaskQuery.getAgencyIdValues(),
                        getTaskQuery.getRequestTypeCodeOperator(),
                        getTaskQuery.getRequestTypeCodeValues(),
                        getTaskQuery.getWritingCompanyCodeOperator(),
                        getTaskQuery.getWritingCompanyCodeValues(),
                        getTaskQuery.getTaskStatusIdOperator(),
                        getTaskQuery.getTaskStatusIdValues(),
                        getTaskQuery.getTaskTypeIdOperator(),
                        getTaskQuery.getTaskTypeIdValues(),
                        getTaskQuery.getPriorityIdOperator(),
                        getTaskQuery.getPriorityIdValues(),
                        getTaskQuery.getDateAssignedOperator(),
                        getTaskQuery.getDateAssignedValues(),
                        getTaskQuery.getOriginalDueDateOperator(),
                        getTaskQuery.getOriginalDueDateValues(),
                        getTaskQuery.getDueDateOperator(),
                        getTaskQuery.getDueDateValues(),
                        getTaskQuery.getDateCompletedOperator(),
                        getTaskQuery.getDateCompletedValues(),
                        getTaskQuery.getOrderBy(),
                        pageNumber,
                        getTaskQuery.getPageSize(),
                        getTaskQuery.getXVersion(),
                        getTaskQuery.getLang());
    }
}
