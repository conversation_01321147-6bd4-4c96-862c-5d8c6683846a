package com.bees360.rct;

import static com.bees360.integration.TestIntegrationSummaryUtil.randomInstance;
import static com.bees360.rct.RctTestUtil.randomFormDto;
import static com.bees360.rct.RctTestUtil.randomIntegration;
import static com.bees360.rct.RctTestUtil.randomTask;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.ScheduleUpdateRctFormOnTaskReady;
import com.bees360.event.registry.Events;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.http.HttpClient;
import com.bees360.integration.IntegrationSummaryProvider;
import com.bees360.integration.Message.IntegrationSummaryMessage.ImageSummary;
import com.bees360.job.JobScheduler;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.rct.config.RctFormConfig;
import com.bees360.rct.form.RctFormManager;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.sdk.rct.ApiClient;
import com.bees360.sdk.rct.api.TasksApi;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.ByteString;
import com.google.protobuf.util.JsonFormat;
import com.jayway.jsonpath.JsonPath;

import jakarta.annotation.PostConstruct;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@SpringBootTest(
        classes = {
            RctFormITest.Config.class,
        },
        properties = {
            "GRPC_SERVICE_NAME=127.0.0.1",
            "GRPC_SERVER_PORT=9810",
        })
@ActiveProfiles("test")
@ApplicationAutoConfig
class RctFormITest {

    @Import({
        InMemoryEventPublisher.class,
        InMemoryJobScheduler.class,
        RctFormConfig.class,
    })
    @Configuration
    public static class Config {

        @Bean
        public Executor executor() {
            return MoreExecutors.directExecutor();
        }

        @MockBean public TasksApi tasksApi;

        @Bean
        public RctApi<TasksApi> rctApi() {
            return RctApi.of(tasksApi);
        }

        @SpyBean
        @Qualifier("grpcResourceClient")
        ResourcePool grpcResourceClient;

        @PostConstruct
        void config() {
            // avoid to invoke grpc services
            Mockito.doReturn(true).when(grpcResourceClient).isResourceUrlProvider();
        }
    }

    @SpyBean
    @Qualifier("awsLambdaHttpClient")
    HttpClient awsLambdaHttpClient;

    @SpyBean
    @Qualifier("resourcePool")
    ResourcePool resourcePool;

    @MockBean public PipelineService pipelineService;

    @Autowired public JobScheduler jobScheduler;

    @Autowired public EventPublisher eventPublisher;

    @MockBean public ProjectIIManager projectIIManager;

    @MockBean public IntegrationSummaryProvider summaryProvider;

    @MockBean private ExternalIntegrationManager integrationManager;

    @Autowired private TasksApi tasksApi;

    @SpyBean private RctFormManager rctFormManager;

    private static final String TASK_KEY = "fill_out_form_on_rct";
    private static final String FORM_TEMPLATE_ID_CLIENT_PHOTOS = "1694450801959674708";

    /**
     * testing:
     *
     * <ul>
     *   <li>{@link RctFormConfig#scheduleUpdateRctFormListener}
     *   <li>{@link ScheduleUpdateRctFormOnTaskReady}
     *   <li>{@link com.bees360.job.CollectDataAndUploadRctFormJobExecutor}
     * </ul>
     */
    @SneakyThrows
    @Test
    void testUpdateForms() {
        var task = randomTask();
        var integration = randomIntegration("Test A", task.getId());
        var projectId = integration.getProjectId();
        mockData(
                integration,
                ServiceTypeEnum.EXTERIOR,
                List.of(
                        "1545333116058741764",
                        "1609203926552718156",
                        "10000000012",
                        FORM_TEMPLATE_ID_CLIENT_PHOTOS));

        spyAwsLambdaHttpClient();
        spyResourcePool();

        var event = new PipelineTaskChanged();
        event.setTaskDefKey(TASK_KEY);
        event.setPipelineId(projectId);
        eventPublisher.publish(
                "pipeline_task_changed.status_changed.fill_out_form_on_rct.ready",
                Events.encode(event));

        Mockito.verify(rctFormManager, Mockito.never())
                .addTaskForm(eq(task.getId()), eq("1545333116058741764"));
        Mockito.verify(tasksApi, Mockito.times(1))
                .updateFormFieldsCall(
                        eq(task.getId()),
                        eq("1545333116058741764"),
                        Mockito.any(),
                        Mockito.any(),
                        Mockito.any(),
                        Mockito.anyList(),
                        Mockito.any());

        Mockito.verify(rctFormManager, Mockito.never())
                .addTaskForm(eq(task.getId()), eq("1609203926552718156"));
        Mockito.verify(tasksApi, Mockito.times(1))
                .updateFormFieldsCall(
                        eq(task.getId()),
                        eq("1609203926552718156"),
                        Mockito.any(),
                        Mockito.any(),
                        Mockito.any(),
                        Mockito.anyList(),
                        Mockito.any());
        // supplemental service doesn't match
        Mockito.verify(rctFormManager, Mockito.never())
                .addTaskForm(eq(task.getId()), eq("10000000012"));
        Mockito.verify(tasksApi, Mockito.never())
                .updateFormFieldsCall(
                        eq(task.getId()),
                        eq("10000000012"),
                        Mockito.any(),
                        Mockito.any(),
                        Mockito.any(),
                        Mockito.anyList(),
                        Mockito.any());

        // delete form then recreate the form
        Mockito.verify(tasksApi)
                .deleteTaskFormByIdCall(
                        eq(task.getId()),
                        eq(FORM_TEMPLATE_ID_CLIENT_PHOTOS),
                        Mockito.nullable(String.class),
                        Mockito.nullable(String.class),
                        Mockito.nullable(String.class),
                        Mockito.any());
        Mockito.verify(rctFormManager)
                .addTaskForm(eq(task.getId()), eq(FORM_TEMPLATE_ID_CLIENT_PHOTOS));
        Mockito.verify(tasksApi, Mockito.times(1))
                .updateFormFieldsCall(
                        eq(task.getId()),
                        eq(FORM_TEMPLATE_ID_CLIENT_PHOTOS),
                        Mockito.any(),
                        Mockito.any(),
                        Mockito.any(),
                        Mockito.anyList(),
                        Mockito.any());
        // upload photos
        Mockito.verify(tasksApi)
                .uploadFormPhotoAsyncCall(
                        eq(task.getId()),
                        eq(FORM_TEMPLATE_ID_CLIENT_PHOTOS),
                        Mockito.nullable(String.class),
                        eq("xx"),
                        Mockito.any(File.class),
                        Mockito.nullable(String.class),
                        Mockito.nullable(String.class),
                        Mockito.anyString(),
                        Mockito.any());
        Mockito.verify(pipelineService, Mockito.times(1))
                .setTaskStatus(
                        eq(integration.getProjectId()),
                        eq(TASK_KEY),
                        eq(Message.PipelineStatus.DONE),
                        Mockito.any());
    }

    private void spyResourcePool() {
        var resource =
                Resource.of(
                        ByteString.copyFrom("content", StandardCharsets.UTF_8),
                        ResourceMetadata.newBuilder()
                                .setETag("\"xxx\"")
                                .setContentType("image/jpg")
                                .build());
        Mockito.doReturn(resource).when(resourcePool).get(anyString());
    }

    private void spyAwsLambdaHttpClient() {
        var defaultResponseJson =
                "{`data`: `{'Fields':[{'Id':'1709564826519587000', 'Value': ['1709564578689546343']}]}`}"
                        .replace("'", "\\\"")
                        .replace('`', '\"');
        var responseJsonWithPhoto =
                "{`data`: `{'Fields':[{'Id':'1709564826519587000', 'Value': ['1709564578689546343']}],'Photos':[{'SectionTemplateId':'xx','Id':'%s','Description':'Good'}]}`}"
                        .replace("'", "\\\"")
                        .replace('`', '\"');

        Map<String, String> responseJsonProvider =
                Map.of("rct-client-photos", responseJsonWithPhoto);

        Mockito.doAnswer(
                        args -> {
                            HttpPost httpPost = args.getArgument(0);
                            var requestBody =
                                    IOUtils.toString(
                                            httpPost.getEntity().getContent(),
                                            StandardCharsets.UTF_8);
                            var formTemplateId =
                                    JsonPath.parse(requestBody)
                                            .read("$.formTemplateId", String.class);
                            var formData =
                                    JsonPath.parse(requestBody).read("$.formData", String.class);
                            var imageSummaryJson =
                                    JsonPath.parse(formData).read("imageSummary").toString();
                            var imageSummaryBuilder = ImageSummary.newBuilder();
                            JsonFormat.parser()
                                    .ignoringUnknownFields()
                                    .merge(imageSummaryJson, imageSummaryBuilder);
                            var imageId = imageSummaryBuilder.getImage(0).getId();
                            var response =
                                    responseJsonProvider.getOrDefault(
                                            formTemplateId, defaultResponseJson);
                            return String.format(response, imageId);
                        })
                .when(awsLambdaHttpClient)
                .execute(any(HttpUriRequest.class), any());
    }

    @SneakyThrows
    @Test
    void testIgnoreWhenServiceTypeNotConfigureForm() {
        var task = randomTask();
        var integration = randomIntegration("Test A", task.getId());
        var projectId = integration.getProjectId();
        mockData(integration, ServiceTypeEnum.ROOF_ONLY, List.of("1609203926552718156"));

        var event = new PipelineTaskChanged();
        event.setTaskDefKey(TASK_KEY);
        event.setPipelineId(projectId);
        eventPublisher.publish(
                "pipeline_task_changed.status_changed.fill_out_form_on_rct.ready",
                Events.encode(event));

        Mockito.verify(pipelineService, Mockito.never())
                .setTaskStatus(
                        eq(integration.getProjectId()),
                        eq(TASK_KEY),
                        eq(Message.PipelineStatus.DONE),
                        Mockito.any());
    }

    @SneakyThrows
    private void mockData(
            ExternalIntegration integration,
            ServiceTypeEnum serviceType,
            List<String> formTemplateIds) {
        var projectId = integration.getProjectId();
        var taskId = integration.getReferenceNumber();
        var project = Mockito.mock(ProjectII.class);
        Mockito.when(project.getServiceType()).thenAnswer(e -> serviceType);
        Mockito.when(project.getSupplementalService()).thenAnswer(e -> List.of("RC Report"));

        Mockito.when(projectIIManager.findById(eq(projectId))).thenAnswer(e -> project);

        Mockito.when(integrationManager.findAllByProjectId(eq(projectId)))
                .thenAnswer(e -> List.of(integration));

        var summary = randomInstance();
        Mockito.when(summaryProvider.collectByProjectId(eq(projectId))).thenAnswer(e -> summary);

        Mockito.when(
                        tasksApi.getTaskForms(
                                eq(taskId),
                                Mockito.any(),
                                Mockito.any(),
                                Mockito.any(),
                                Mockito.any()))
                .thenAnswer(
                        e ->
                                formTemplateIds.stream()
                                        .map(ftid -> randomFormDto(ftid, ftid))
                                        .collect(Collectors.toList()));

        Mockito.doAnswer(args -> args.<String>getArgument(1))
                .when(rctFormManager)
                .addTaskForm(anyString(), anyString());

        Mockito.when(tasksApi.getApiClient()).thenAnswer(e -> Mockito.mock(ApiClient.class));
    }
}
