package com.bees360.rct;

import com.bees360.project.ExternalIntegration;
import com.bees360.project.Message;
import com.bees360.sdk.rct.model.FormDto;
import com.bees360.sdk.rct.model.LocationDto;
import com.bees360.sdk.rct.model.TaskDto;

import org.apache.commons.lang3.RandomStringUtils;

import java.util.Random;

public class RctTestUtil {
    public static final String TEMPLATE_ID = "1545333116058741764";

    public static final String INTEGRATION_TYPE = "RiskControl";

    public static FormDto randomFormDto() {
        return randomFormDto(TEMPLATE_ID);
    }

    public static FormDto randomFormDto(String templateId, String formId) {
        var form = new FormDto();
        form.setId(formId);
        form.setFormTemplateId(templateId);
        return form;
    }

    public static FormDto randomFormDto(String templateId) {
        return randomFormDto(templateId, RandomStringUtils.randomAlphabetic(12));
    }

    public static TaskDto randomTask() {
        var task = new TaskDto();
        task.setId(RandomStringUtils.randomAlphabetic(12));
        var location = new LocationDto();
        location.setId(new Random().nextInt(10000));
        task.setLastAssignedLocation(location);
        return task;
    }

    public static ExternalIntegration randomIntegration(String dataset, String taskId) {
        return ExternalIntegration.from(
                Message.IntegrationMessage.newBuilder()
                        .setProjectId(RandomStringUtils.randomNumeric(8))
                        .setDataset(dataset)
                        .setIntegrationType(INTEGRATION_TYPE)
                        .setReferenceNumber(taskId)
                        .build());
    }
}
