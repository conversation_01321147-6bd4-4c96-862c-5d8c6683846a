package com.bees360.rct;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.event.CloseProjectOnRctTaskDetectionEvent;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.RctTaskDetectionEvent;
import com.bees360.job.JobScheduler;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.Message;
import com.bees360.rct.config.RctAppJobRetryPropertiesConfig;
import com.bees360.rct.config.RctAppUserProperties;
import com.bees360.rct.config.RctDetectionProperties;
import com.bees360.sdk.rct.api.TasksApi;
import com.bees360.sdk.rct.model.TaskDto;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.SneakyThrows;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executor;

@SpringBootTest(classes = {CloseProjectOnRctTaskDetectionEventTest.Config.class})
@ActiveProfiles("test")
@EnableConfigurationProperties
public class CloseProjectOnRctTaskDetectionEventTest {

    private static final String ROBOT_USER_ID = "10000";

    @Import({
        RctApp.Config.class,
        RctAppJobRetryPropertiesConfig.class,
        InMemoryEventPublisher.class,
    })
    @Configuration
    static class Config {

        @Bean
        ExternalIntegrationManager externalIntegrationManager() {
            return mock(ExternalIntegrationManager.class);
        }

        @Bean
        UserProvider userProvider() {
            return mock(UserProvider.class);
        }

        @Bean
        TasksApi tasksApi() {
            return mock(TasksApi.class);
        }

        @Bean
        RctApi<TasksApi> rctApi(TasksApi tasksApi) {
            return RctApi.of(tasksApi);
        }

        @Bean
        JobScheduler jobScheduler() {
            return mock(JobScheduler.class);
        }

        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @Autowired CloseProjectOnRctTaskDetectionEvent closeProjectOnRctTaskDetectionEvent;

    @Autowired TasksApi tasksApi;

    @Autowired RctDetectionProperties rctTaskDetectionProperties;

    @Autowired ExternalIntegrationManager externalIntegrationManager;

    @Autowired JobScheduler jobScheduler;

    @Autowired UserProvider userProvider;

    @Autowired RctAppUserProperties rctAppUserProperties;

    /** close job */
    @SneakyThrows
    @Test
    public void fetchAndCloseProject() {
        var event = new RctTaskDetectionEvent();
        event.setTriggerTime(Instant.now());

        var lastModifiedTime =
                OffsetDateTime.ofInstant(
                        Instant.now().minus(rctTaskDetectionProperties.getDuration()),
                        ZoneId.systemDefault());
        var integrationTask = new TaskDto();
        integrationTask.setId(RandomStringUtils.randomNumeric(8));
        integrationTask.setDateModified(lastModifiedTime);

        var unIntegrationTask = new TaskDto();
        unIntegrationTask.setId(RandomStringUtils.randomNumeric(8));
        unIntegrationTask.setDateModified(lastModifiedTime);

        var getTaskQuery = rctTaskDetectionProperties.getCancelTaskQuery();
        doReturn(Arrays.asList(integrationTask, unIntegrationTask))
                .when(tasksApi)
                .getTasks(
                        getTaskQuery.getReferenceNumberOperator(),
                        getTaskQuery.getReferenceNumberValues(),
                        getTaskQuery.getExternalUniqueIdOperator(),
                        getTaskQuery.getExternalUniqueIdValues(),
                        getTaskQuery.getAccountIdOperator(),
                        getTaskQuery.getAccountIdValues(),
                        getTaskQuery.getPolicyNumberOperator(),
                        getTaskQuery.getPolicyNumberValues(),
                        getTaskQuery.getAddressIdOperator(),
                        getTaskQuery.getAddressIdValues(),
                        getTaskQuery.getIsVisibleOnInsuredPortalOperator(),
                        getTaskQuery.getIsVisibleOnInsuredPortalValue(),
                        getTaskQuery.getConsultantIdOperator(),
                        getTaskQuery.getConsultantIdValues(),
                        getTaskQuery.getUnderwriterIdOperator(),
                        getTaskQuery.getUnderwriterIdValues(),
                        getTaskQuery.getBrokerIdOperator(),
                        getTaskQuery.getBrokerIdValues(),
                        getTaskQuery.getAgencyIdOperator(),
                        getTaskQuery.getAgencyIdValues(),
                        getTaskQuery.getRequestTypeCodeOperator(),
                        getTaskQuery.getRequestTypeCodeValues(),
                        getTaskQuery.getWritingCompanyCodeOperator(),
                        getTaskQuery.getWritingCompanyCodeValues(),
                        getTaskQuery.getTaskStatusIdOperator(),
                        getTaskQuery.getTaskStatusIdValues(),
                        getTaskQuery.getTaskTypeIdOperator(),
                        getTaskQuery.getTaskTypeIdValues(),
                        getTaskQuery.getPriorityIdOperator(),
                        getTaskQuery.getPriorityIdValues(),
                        getTaskQuery.getDateAssignedOperator(),
                        getTaskQuery.getDateAssignedValues(),
                        getTaskQuery.getOriginalDueDateOperator(),
                        getTaskQuery.getOriginalDueDateValues(),
                        getTaskQuery.getDueDateOperator(),
                        getTaskQuery.getDueDateValues(),
                        getTaskQuery.getDateCompletedOperator(),
                        getTaskQuery.getDateCompletedValues(),
                        getTaskQuery.getOrderBy(),
                        1,
                        getTaskQuery.getPageSize(),
                        getTaskQuery.getXVersion(),
                        getTaskQuery.getLang());

        when(externalIntegrationManager.findByReference("RiskControl", integrationTask.getId()))
                .thenReturn(
                        ExternalIntegration.from(
                                Message.IntegrationMessage.newBuilder()
                                        .setProjectId("111")
                                        .build()));
        when(externalIntegrationManager.findByReference("RiskControl", unIntegrationTask.getId()))
                .thenReturn(null);

        var user =
                User.from(
                        com.bees360.user.Message.UserMessage.newBuilder()
                                .setId(ROBOT_USER_ID)
                                .setEmail(rctAppUserProperties.getRobotEmail())
                                .build());
        when(userProvider.findUserByEmail(rctAppUserProperties.getRobotEmail()))
                .thenAnswer(args -> List.of(user));

        closeProjectOnRctTaskDetectionEvent.handle(event);
        verify(jobScheduler, times(1)).schedule(any());
    }
}
