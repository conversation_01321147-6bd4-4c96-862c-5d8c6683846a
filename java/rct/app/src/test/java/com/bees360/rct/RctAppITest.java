package com.bees360.rct;

import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.address.Message.AddressMessage;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.EventPublisher;
import com.bees360.event.UpdateRctTaskStatusOnInspectionScheduledTimeChanged;
import com.bees360.event.UpdateRctTaskStatusOnProjectReturnedToClient;
import com.bees360.event.registry.InspectionScheduledTimeChanged;
import com.bees360.event.registry.ProjectReturnedToClientEvent;
import com.bees360.job.JobScheduler;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.oauth.OAuthGrant;
import com.bees360.oauth.OAuthRefreshableToken;
import com.bees360.oauth.OAuthToken;
import com.bees360.policy.Message.PolicyMessage;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.GrpcExternalIntegrationManager;
import com.bees360.project.GrpcExternalIntegrationManagerService;
import com.bees360.project.GrpcProjectIIManager;
import com.bees360.project.GrpcProjectTagManager;
import com.bees360.project.JooqIntegrationFormManager;
import com.bees360.project.Message.IntegrationMessage;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.Message.ServiceType;
import com.bees360.project.ProjectII;
import com.bees360.project.tag.Message.ProjectTagMessage;
import com.bees360.project.tag.Message.ProjectTagType;
import com.bees360.project.tag.ProjectTag;
import com.bees360.rct.util.RctTaskStatus;
import com.bees360.resource.ResourcePool;
import com.bees360.sdk.rct.ApiCallback;
import com.bees360.sdk.rct.ApiClient;
import com.bees360.sdk.rct.ApiException;
import com.bees360.sdk.rct.api.TasksApi;
import com.bees360.sdk.rct.model.Operation;
import com.bees360.sdk.rct.model.TaskDto;
import com.bees360.util.Iterables;
import com.google.common.collect.Lists;

import jakarta.annotation.PostConstruct;

import lombok.SneakyThrows;

import okhttp3.Call;

import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ActiveProfiles;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.TimeZone;
import java.util.function.Consumer;

@SpringBootTest(
        classes = {RctApp.class, RctAppITest.Config.class},
        properties = "GRPC_SERVER_PORT=9851")
@ActiveProfiles("test")
@ApplicationAutoConfig
class RctAppITest {

    private static final String RCT_TASK_ID = "1716484599292609092";

    @Import({
        JooqConfig.class,
        JooqIntegrationFormManager.class,
        GrpcExternalIntegrationManagerService.class,
    })
    @Configuration
    public static class Config {

        @Bean
        ExternalIntegrationManager gprcServiceExternalIntegrationManager() {
            var mgr = Mockito.mock(ExternalIntegrationManager.class);

            when(mgr.findAllByProjectId(anyString()))
                    .thenAnswer(
                            args -> {
                                String projectId = args.getArgument(0);
                                var rctIntegration =
                                        IntegrationMessage.newBuilder()
                                                .setProjectId(projectId)
                                                .setIntegrationType("RiskControl")
                                                .setReferenceNumber(RCT_TASK_ID + projectId)
                                                .build();
                                var xaIntegration =
                                        IntegrationMessage.newBuilder()
                                                .setProjectId(projectId)
                                                .setIntegrationType("INTEGRATION_XACTANALYSIS")
                                                .setReferenceNumber("0987654321")
                                                .build();
                                return List.of(
                                        ExternalIntegration.from(rctIntegration),
                                        ExternalIntegration.from(xaIntegration));
                            });
            return mgr;
        }

        @Bean
        ExternalIntegrationManager externalIntegrationManager(
                GrpcExternalIntegrationManager grpcExternalIntegrationManager) {
            return grpcExternalIntegrationManager;
        }

        @Bean
        @Primary
        RctOAuthTokenGranter oAuthTokenGranter(RctOAuthTokenGranter rctOAuthTokenGranter) {
            var accessToken =
                    OAuthToken.of(
                            "xsrfToken:accessToken",
                            LocalDateTime.now().minus(Duration.ofHours(5)),
                            "scope");
            var refreshToken = OAuthRefreshableToken.of(accessToken, "xsrfToken:");
            rctOAuthTokenGranter = Mockito.spy(rctOAuthTokenGranter);
            doReturn(refreshToken).when(rctOAuthTokenGranter).grant(any(OAuthGrant.class));
            return rctOAuthTokenGranter;
        }

        @SpyBean
        @Qualifier("grpcResourceClient")
        ResourcePool grpcResourceClient;

        @PostConstruct
        void config() {
            // avoid to invoke grpc services
            Mockito.doReturn(true).when(grpcResourceClient).isResourceUrlProvider();
        }
    }

    @Autowired EventPublisher eventPublisher;

    @SpyBean
    @Qualifier("rctApiClient")
    ApiClient rctApiClient;

    @SpyBean TasksApi tasksApi;

    @SpyBean GrpcProjectIIManager grpcProjectIIManager;

    @SpyBean GrpcProjectTagManager grpcProjectTagManager;

    @SpyBean JobScheduler jobScheduler;

    @Autowired ExternalIntegrationManager gprcServiceExternalIntegrationManager;

    /** Testing {@link UpdateRctTaskStatusOnProjectReturnedToClient} */
    @SneakyThrows
    @Test
    void testUpdateTaskStatusVendorSubmittedIfWithoutTagOrderRCReport() {
        var projectId = "123005";
        var event = new ProjectReturnedToClientEvent();
        event.setProjectId(projectId);

        var taskId = RCT_TASK_ID + event.getProjectId();

        doReturn(createProject(projectId, ServiceType.EXTERIOR_UNDERWRITING, TimeZone.getDefault()))
                .when(grpcProjectIIManager)
                .findById(projectId);
        doReturn(null).when(rctApiClient).execute(any(Call.class));
        List<? extends ProjectTag> projectTags =
                Iterables.toList(
                        Iterables.transform(
                                List.of("Any Tag"),
                                t ->
                                        ProjectTag.of(
                                                ProjectTagMessage.newBuilder()
                                                        .setTitle(t)
                                                        .build())));
        doReturn(List.of())
                .when(grpcProjectTagManager)
                .findByProjectIdAndType(eq(projectId), any(ProjectTagType.class));
        doAnswer(arg -> projectTags)
                .when(grpcProjectTagManager)
                .findByProjectIdAndType(eq(projectId), eq(ProjectTagType.UNDERWRITING));

        eventPublisher.publish(event);

        var expectedOperation = new Operation().path("/TaskStatusId").op("replace").value(2425);
        awaitThenAssertUpdateTask(taskId, 1, List.of(expectedOperation));
    }

    /** Testing {@link UpdateRctTaskStatusOnProjectReturnedToClient} */
    @SneakyThrows
    @Test
    void testUpdateTaskStatusVendorSubmittedIfNoProjectTag() {
        var projectId = "123105";
        var event = new ProjectReturnedToClientEvent();
        event.setProjectId(projectId);

        var taskId = RCT_TASK_ID + event.getProjectId();

        doReturn(createProject(projectId, ServiceType.EXTERIOR_UNDERWRITING, TimeZone.getDefault()))
                .when(grpcProjectIIManager)
                .findById(projectId);
        doReturn(null).when(rctApiClient).execute(any(Call.class));
        doReturn(List.of())
                .when(grpcProjectTagManager)
                .findByProjectIdAndType(eq(projectId), any(ProjectTagType.class));

        eventPublisher.publish(event);

        var expectedOperation = new Operation().path("/TaskStatusId").op("replace").value(2425);
        awaitThenAssertUpdateTask(taskId, 1, List.of(expectedOperation));
    }

    private ProjectII createProject(String projectId, ServiceType serviceType, TimeZone timeZone) {
        var policy =
                PolicyMessage.newBuilder()
                        .setAddress(AddressMessage.newBuilder().setTimeZone(timeZone.getID()));
        return ProjectII.from(
                ProjectMessage.newBuilder()
                        .setId(projectId)
                        .setServiceType(serviceType)
                        .setPolicy(policy)
                        .build());
    }

    private void awaitThenAssertUpdateTask(
            String taskId, int invokeTimes, List<Operation> expectedOperations)
            throws ApiException {
        awaitThenAssertUpdateTask(
                taskId, invokeTimes, operations -> assertEquals(expectedOperations, operations));
    }

    private void awaitThenAssertUpdateTask(
            String taskId, int invokeTimes, Consumer<List<Operation>> assertOperations)
            throws ApiException {

        await().atMost(Duration.ofSeconds(5))
                .pollInterval(Duration.ofMillis(500))
                .untilAsserted(
                        () ->
                                verify(tasksApi)
                                        .updateTaskV2Call(
                                                eq(taskId),
                                                isNull(),
                                                isNull(),
                                                isNull(),
                                                anyList(),
                                                any(ApiCallback.class)));

        ArgumentCaptor<List<Operation>> argument = ArgumentCaptor.forClass(List.class);
        verify(tasksApi, times(invokeTimes))
                .updateTaskV2Call(
                        eq(taskId),
                        isNull(),
                        isNull(),
                        isNull(),
                        argument.capture(),
                        any(ApiCallback.class));

        var operations = argument.getValue();
        assertOperations.accept(operations);
    }

    @Test
    void testSetTaskAppointmentSetWithNotesOnInspectionScheduledTimeChanged() {
        var event = new InspectionScheduledTimeChanged();
        event.setProjectId("123019");
        event.setUpdatedTime(
                ZonedDateTime.parse("2024-05-24T13:15:30-05:00").toEpochSecond() * 1000);
        event.setScheduledTime(event.getUpdatedTime() + Duration.ofDays(7).toMillis());

        var notesAppend =
                "\r\n\r\n"
                        + "Inspection is scheduled for 05/31/2024, 02:15 PM\r\n"
                        + "updated at 05/24/2024, 02:15 PM";
        testSetTaskAppointmentSetWithNotesOnInspectionScheduledTimeChanged(event, notesAppend);
    }

    @Test
    void testSetTaskAppointmentSetWithNotesOnInspectionScheduledTimeChangedToNull() {
        var event = new InspectionScheduledTimeChanged();
        event.setProjectId("123019");
        event.setUpdatedTime(
                ZonedDateTime.parse("2024-05-24T13:15:30-05:00").toEpochSecond() * 1000);
        event.setScheduledTime(null);

        var notesAppend =
                "\r\n\r\n"
                        + "Scheduled inspection time has been removed\r\n"
                        + "updated at 05/24/2024, 02:15 PM";
        testSetTaskAppointmentSetWithNotesOnInspectionScheduledTimeChanged(event, notesAppend);
    }

    /** for test {@link UpdateRctTaskStatusOnInspectionScheduledTimeChanged} */
    @SneakyThrows
    void testSetTaskAppointmentSetWithNotesOnInspectionScheduledTimeChanged(
            InspectionScheduledTimeChanged event, String notesAppend) {
        var projectId = event.getProjectId();
        var taskId = RCT_TASK_ID + event.getProjectId();

        doReturn(
                        createProject(
                                projectId,
                                ServiceType.SCHEDULING_ONLY,
                                TimeZone.getTimeZone("America/New_York")))
                .when(grpcProjectIIManager)
                .findById(projectId);
        doReturn(null).when(rctApiClient).execute(any(Call.class));
        var taskDto = new TaskDto().id(taskId).notes("First Line.");
        doReturn(taskDto).when(tasksApi).getTaskById(eq(taskId), isNull(), isNull());

        eventPublisher.publish(event);

        // update task
        List<Operation> expectedOperations = Lists.newArrayList();
        expectedOperations.add(
                new Operation()
                        .path("/TaskStatusId")
                        .op("replace")
                        .value(RctTaskStatus.APPOINTMENT_SET.getCode()));
        var notes = "First Line." + notesAppend;
        expectedOperations.add(new Operation().path("/Notes").op("replace").value(notes));

        awaitThenAssertUpdateTask(taskId, 1, expectedOperations);
    }
}
