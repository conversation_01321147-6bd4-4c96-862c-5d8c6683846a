spring:
  jooq:
    sql-dialect: POSTGRES
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver

grpc:
  server:
    port: ${GRPC_SERVER_PORT:9898}
  client:
    projectIntegration:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}

rct:
  customer-enabled: true
  endpoint: https://localhost/96117978
  auth:
    id: username
    secret: password
  dataset:
    - id: Test A
      service-map:
        "[EXTERIOR]":
          forms:
            - templateId: 1545333116058741764
              if-supplemental-service-in: [ 'RC Report' ]
            - templateId: 1609203926552718156
            - templateId: 10000000012
              if-supplemental-service-in: [ 'Config Noise' ]
            - name: "Client Photos"
              templateId: 1694450801959674708
              recreate-if-exist: true

        "[WHITE_GLOVE]":
          forms:
            - templateId: 1545333116058741764
  app:
    dataset:
      id: 96117978
    detection:
      duration: P160D
      get-task-query:
        task-status-id-operator: NOTEQUAL
        task-status-id-values:
          - 2397 # Cancelled
          - 20 # Archived
          - 2671 # Declined
        page-size: 10
      cancel-task-query:
        task-status-id-operator: EQUAL
        task-status-id-values:
          - 2397 # cancelled
        page-size: 1
  client:
    request:
      connectTimeout: PT60S
      callTimeout: PT180S
      followRedirects: true
      retryOnConnectionFailure: true
  form:
    enabled: true
    dataset:
      - name: "Homeowners Data Collection Form"
        templateId: 1545333116058741764
        internal-template-key: "rct-homeowners-data-collection"
        section:
          - name: "Main Structure"
            templateId: 1545334610411981381
      - name: "Full Residential Form"
        templateId: 1609203926552718156
        internal-template-key: "rct-full-residential"
      - name: "Client Photos"
        templateId: 1694450801959674708
        internal-template-key: "rct-client-photos"
http:
  aws-lambda:
    enabled: true
    endpoint: "https://localhost/aws/"
    client-secret: 123456
  client:
    retry:
      maxAttempts: 5
      initialInterval: 2000
      multiplier: 2.0
      maxInterval: 10000

rct.app:
  rct-task:
    enabled-update-status: true
    update-status-vendor-submitted:
      # 3: EXTERIOR
      service-types: [ 3 ]
      without-project-tags: [ 'Order RC Report' ]
  job:
    retry:
      retry-count: 3
      retry-delay: PT5M
    close-project:
      enabled: true
    create-project:
      enabled: true
  user:
    robot-email: <EMAIL>
