<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bees360-rct</artifactId>
        <groupId>com.bees360</groupId>
        <version>${revision}${changelist}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>bees360-rct-app</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-grpc-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-integration-grpc-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-job-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-resource-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-resource-http-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-integration-grpc</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-integration-jooq</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>co.realms9</groupId>
            <artifactId>realms9-secretsmanger-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-monitor-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-rct-http-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-rct-auth</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-event</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-job</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-jooq</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-pipeline-grpc-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-integration-api</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
            <type>test-jar</type>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-api</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
            <type>test-jar</type>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-apolloconfig</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                    <mainClass>com.bees360.rct.RctApp</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
