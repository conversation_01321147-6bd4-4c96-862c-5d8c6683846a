package com.bees360.hover;

import com.bees360.api.ApiStatus;
import com.bees360.api.Message.ApiMessage;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.util.Iterables;

import lombok.RequiredArgsConstructor;

import org.springframework.context.annotation.Import;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.stream.Collectors;

@RestController
@RequestMapping("${http.project.endpoint:project}")
@RequiredArgsConstructor
@Import({ProtoHttpMessageConverterConfig.class, ApiExceptionHandler.class})
public class ProjectHoverEndpoint {

    private final ProjectHoverManager projectHoverManager;

    @GetMapping("{projectId:\\d+}/hover")
    public com.bees360.api.Message.ApiMessage getProjectHover(@PathVariable String projectId) {
        var hovers = projectHoverManager.findByProjectId(projectId);
        return ApiMessage.newBuilder()
                .setStatus(ApiStatus.OK.toMessage())
                .addProject(
                        ProjectMessage.newBuilder()
                                .setId(projectId)
                                .addAllHover(
                                        Iterables.toStream(hovers)
                                                .map(ProjectHover::toMessage)
                                                .collect(Collectors.toList())))
                .build();
    }

    @PutMapping("{projectId:\\d+}/hover")
    public ApiMessage upgradeHoverType(@PathVariable String projectId, @RequestParam String type) {
        projectHoverManager.upgradeType(projectId, type);
        return ApiMessage.newBuilder().setStatus(ApiStatus.OK.toMessage()).build();
    }
}
