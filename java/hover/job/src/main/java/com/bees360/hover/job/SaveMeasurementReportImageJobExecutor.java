package com.bees360.hover.job;

import com.bees360.api.InvalidArgumentException;
import com.bees360.firebase.FirebaseApi;
import com.bees360.firebase.domain.MeasurementReport;
import com.bees360.image.Image;
import com.bees360.image.ImageManager;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.Message.ImageMessage.Resource.Type;
import com.bees360.job.registry.SaveMeasurementReportImageJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.pipeline.PipelineService;
import com.bees360.util.Iterables;
import com.google.cloud.Timestamp;
import com.google.cloud.firestore.Firestore;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Map;

@Log4j2
public class SaveMeasurementReportImageJobExecutor
        extends AbstractJobExecutor<SaveMeasurementReportImageJob> {

    private static final String UPLOAD_HOVER_IMAGES_TASK_KEY = "upload_hover_images";

    private final ImageManager imageManager;
    private final PipelineService pipelineService;
    private final Firestore firestore;

    public SaveMeasurementReportImageJobExecutor(
            ImageManager imageManager, PipelineService pipelineService, Firestore firestore) {
        this.imageManager = imageManager;
        this.pipelineService = pipelineService;
        this.firestore = firestore;
    }

    @Override
    protected void handle(SaveMeasurementReportImageJob job) throws IOException {
        var projectId = job.getProjectId();
        var imageTags = job.getImages();
        log.info("Start processing the hover measurement job. {}", projectId);
        var images = imageManager.getImages(imageTags.keySet());
        MeasurementReport report = getMeasurementReportData(projectId, imageTags, images);
        try {
            FirebaseApi.exec(
                    () ->
                            firestore
                                    .collection(MeasurementReport.COLLECTION_NAME)
                                    .document()
                                    .set(report));
        } catch (RuntimeException e) {
            log.error("Failed to update firebase data. {}", projectId, e);
            setUploadImageTaskStatus(projectId, PipelineStatus.ERROR);
        }

        setUploadImageTaskStatus(projectId, PipelineStatus.ONGOING);
    }

    private MeasurementReport getMeasurementReportData(
            String projectId, Map<String, ImageTagEnum> imageTags, Iterable<? extends Image> images)
            throws IOException {
        var imageList = Iterables.toList(images);
        var reportImages = new ArrayList<MeasurementReport.Image>();
        for (Image image : imageList) {
            var reportImage = new MeasurementReport.Image();
            var resource =
                    Iterables.toStream(image.getResource())
                            .filter(r -> Type.LARGE.equals(r.getType()))
                            .findFirst()
                            .orElseThrow(
                                    () ->
                                            new IOException(
                                                    String.format(
                                                            "The large image of %s is not"
                                                                    + " generated.",
                                                            projectId)));
            reportImage.setUrl(resource.getUrl());
            reportImage.setDirection(imageTags.get(image.getId()).getDisplay());
            reportImages.add(reportImage);
        }
        return new MeasurementReport(projectId, reportImages, Timestamp.now());
    }

    private void setUploadImageTaskStatus(String pipelineId, PipelineStatus status) {
        var key = UPLOAD_HOVER_IMAGES_TASK_KEY;
        try {
            pipelineService.setTaskStatus(pipelineId, key, status);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Failed to set pipeline '{}' task '{}' to '{}'.", pipelineId, key, status, e);
        } catch (RuntimeException e) {
            log.error("Failed to set pipeline '{}' task '{}' to '{}'.", pipelineId, key, status, e);
        }
    }
}
