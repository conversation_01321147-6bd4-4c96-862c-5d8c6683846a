package com.bees360.hover;

import static com.bees360.hover.job.UploadFloorPlanJobExecutor.EXTRACT_FLOOR_PLAN_TASK_KEY;

import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;

import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.hover.job.config.JobConfig;
import com.bees360.image.ImageManager;
import com.bees360.image.ImageTagManager;
import com.bees360.image.util.TestImage;
import com.bees360.job.JobFuture;
import com.bees360.job.JobScheduler;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.UploadFloorPlanJob;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.image.ProjectImageManager;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.util.Iterables;
import com.bees360.util.ListenableFutures;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@SpringBootTest
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@TestPropertySource(
        properties = {
            "spring.config.location = classpath:application.yml",
            "hover.job.firestore.enabled=false",
        })
public class UploadFloorPlanJobExecutorTest {
    @Import({
        GrpcClientAutoConfiguration.class,
        RabbitApiConfig.class,
        RabbitEventPublisher.class,
        RabbitEventDispatcher.class,
        RabbitJobDispatcher.class,
        RabbitJobScheduler.class,
        JobConfig.class,
    })
    @Configuration
    static class Config {
        @Bean
        ImageManager imageManager() {
            return Mockito.mock(ImageManager.class);
        }

        @Bean
        ImageTagManager imageTagManager() {
            return Mockito.mock(ImageTagManager.class);
        }

        @Bean
        ProjectImageManager projectImageManager() {
            return Mockito.mock(ProjectImageManager.class);
        }

        @Bean
        public ResourcePool resourcePool() {
            return Mockito.mock(ResourcePool.class);
        }
    }

    private final JobScheduler jobScheduler;
    private final ProjectImageManager projectImageManager;
    private final ImageTagManager imageTagManager;
    private final ResourcePool resourcePool;

    private final String defaultUserId;

    @MockBean PipelineService pipelineService;

    public UploadFloorPlanJobExecutorTest(
            @Autowired JobScheduler jobScheduler,
            @Autowired ProjectImageManager projectImageManager,
            @Autowired ImageTagManager imageTagManager,
            @Autowired ResourcePool resourcePool) {
        this.jobScheduler = jobScheduler;
        this.projectImageManager = projectImageManager;
        this.imageTagManager = imageTagManager;
        this.resourcePool = resourcePool;
        this.defaultUserId = "10000";
    }

    @Test
    void uploadFloorPlanJobSuccessTest() {
        var projectId = "789";
        var resourceKey = "hover/floor_plan/abc.jpg";
        var buildingType = "Main Dwelling";
        var resourceName = "abc.jpg";
        var job = new UploadFloorPlanJob();
        job.setProjectId(projectId);
        job.setResourceKey(resourceKey);
        job.setBuildingType(buildingType);
        job.setResourceName(resourceName);

        Mockito.doReturn(ResourceMetadata.newBuilder().setETag("1234abcd").build())
                .when(resourcePool)
                .head(resourceKey);
        var imageId = "i1";
        Mockito.doReturn(List.of(TestImage.of(imageId, 1, Instant.now(), null, null)))
                .when(projectImageManager)
                .createProjectImage(eq(projectId), Mockito.any(), Mockito.anyString());

        JobFuture future = jobScheduler.schedule(JobPayloads.encode(job));
        ListenableFutures.getUnchecked(future);

        Mockito.verify(pipelineService, Mockito.times(1))
                .setTaskStatus(
                        eq(projectId),
                        eq(EXTRACT_FLOOR_PLAN_TASK_KEY),
                        eq(Message.PipelineStatus.DONE));
        Mockito.verify(projectImageManager, Mockito.times(1))
                .createProjectImage(eq(projectId), Mockito.any(), Mockito.anyString());

        Mockito.verify(imageTagManager, Mockito.times(1))
                .addAllImageTag(
                        (Map<String, Iterable<String>>)
                                argThat(
                                        map ->
                                                ((Map<String, Iterable<String>>) map).size() == 1
                                                        && Iterables.isNotEmpty(
                                                                ((Map<String, Iterable<String>>)
                                                                                map)
                                                                        .get(imageId))),
                        argThat(userId -> userId.equals(defaultUserId)));
    }

    @Test
    void uploadFloorPlanJobFailedTest() {
        var projectId = "123";
        var resourceKey = "hover/floor_plan/abc.jpg";
        var buildingType = "Main Dwelling";
        var resourceName = "abc.jpg";
        var job = new UploadFloorPlanJob();
        job.setProjectId(projectId);
        job.setResourceKey(resourceKey);
        job.setBuildingType(buildingType);
        job.setResourceName(resourceName);

        Mockito.doThrow(RuntimeException.class).when(resourcePool).head(resourceKey);

        JobFuture future = jobScheduler.schedule(JobPayloads.encode(job));

        Assertions.assertThrows(
                RuntimeException.class, () -> ListenableFutures.getUnchecked(future));
        Mockito.verify(pipelineService, Mockito.times(1))
                .setTaskStatus(
                        eq(projectId),
                        eq(EXTRACT_FLOOR_PLAN_TASK_KEY),
                        eq(Message.PipelineStatus.ERROR));
        Mockito.verify(projectImageManager, Mockito.never())
                .createProjectImage(Mockito.any(), Mockito.any(), Mockito.any());

        Mockito.verify(imageTagManager, Mockito.never())
                .addAllImageTag((Map<String, Iterable<String>>) Mockito.any(), Mockito.any());
    }

    @Test
    void uploadFloorPlanJobOutBuildingTest() {
        var projectId = "123";
        var resourceKey = "hover/floor_plan/abc.jpg";
        var buildingType = "Out Building";
        var resourceName = "abc.jpg";
        var job = new UploadFloorPlanJob();
        job.setProjectId(projectId);
        job.setResourceKey(resourceKey);
        job.setBuildingType(buildingType);
        job.setResourceName(resourceName);

        // failed
        Mockito.doThrow(RuntimeException.class).when(resourcePool).head(resourceKey);

        JobFuture future = jobScheduler.schedule(JobPayloads.encode(job));

        Assertions.assertThrows(
                RuntimeException.class, () -> ListenableFutures.getUnchecked(future));
        Mockito.verify(pipelineService, Mockito.never())
                .setTaskStatus(Mockito.any(), Mockito.any(), Mockito.any());

        // success
        Mockito.doReturn(ResourceMetadata.newBuilder().setETag("1234abcd").build())
                .when(resourcePool)
                .head(resourceKey);
        var imageId = "i1";
        Mockito.doReturn(List.of(TestImage.of(imageId, 1, Instant.now(), null, null)))
                .when(projectImageManager)
                .createProjectImage(eq(projectId), Mockito.any(), Mockito.anyString());

        JobFuture future2 = jobScheduler.schedule(JobPayloads.encode(job));
        ListenableFutures.getUnchecked(future2);

        Mockito.verify(pipelineService, Mockito.never())
                .setTaskStatus(Mockito.any(), Mockito.any(), Mockito.any());
    }
}
