package com.bees360.hover;

import com.bees360.api.Proto;
import com.bees360.project.Message.ProjectMessage.Hover;
import com.bees360.util.Functions;
import com.google.protobuf.BoolValue;

import jakarta.annotation.Nullable;

public interface ProjectHover extends Proto<Hover> {

    /** Hover job id.唯一 */
    String getJobId();

    /** Hover type */
    @Nullable
    String getType();

    /** Hover status */
    String getStatus();

    /** hover job是否由本公司的hover钱包创建 */
    @Nullable
    Boolean getCreatedByOwnWallet();

    @Override
    default Hover toMessage() {
        Hover.Builder builder = Hover.newBuilder();
        Functions.acceptIfNotNull(builder::setJobId, getJobId());
        Functions.acceptIfNotNull(builder::setType, getType());
        Functions.acceptIfNotNull(builder::setStatus, getStatus());
        Functions.acceptIfNotNull(builder::addAllUpgradeType, getUpgradeType());
        Functions.acceptIfNotNull(
                builder::setCreatedByOwnWallet,
                getCreatedByOwnWallet(),
                e -> BoolValue.newBuilder().setValue(e).build());
        return builder.build();
    }

    Iterable<String> getUpgradeType();
}
