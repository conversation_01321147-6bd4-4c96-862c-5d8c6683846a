package com.bees360.hover;

import com.bees360.estintel.FactorProvider;
import com.bees360.estintel.FactorValueList;
import com.bees360.estintel.FactorValueProvider;
import com.bees360.estintel.Message;
import com.bees360.estintel.NonBlockingLambdaExecutionManager;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.List;

@SpringBootTest(classes = EstintelProjectHoverManagerTest.class)
public class EstintelProjectHoverManagerTest {

    @MockBean ProjectHoverManager projectHoverManager;
    @MockBean NonBlockingLambdaExecutionManager nonBlockingLambdaExecutionManager;
    @MockBean FactorProvider factorProvider;
    @MockBean FactorValueProvider factorValueProvider;

    @Test
    void testSuccessfullyExecutedUpgradeType() {
        var testProjectId = "123";
        var testType = "test";
        Mockito.when(factorProvider.findByQuery(Mockito.any())).thenReturn(List.of());
        Mockito.when(factorValueProvider.findAndCountByQuery(Mockito.any()))
                .thenReturn(
                        FactorValueList.from(
                                Message.FactorValueList.newBuilder().setTotalCount(1).build()));
        Mockito.when(nonBlockingLambdaExecutionManager.triggerLambdaExecution(Mockito.any()))
                .thenReturn("true");

        var estintelProjectHoverManager =
                new EstintelProjectHoverManager(
                        projectHoverManager,
                        nonBlockingLambdaExecutionManager,
                        factorProvider,
                        factorValueProvider);

        estintelProjectHoverManager.upgradeType(testProjectId, testType);

        Mockito.verify(factorProvider, Mockito.times(1)).findByQuery(Mockito.any());
        Mockito.verify(factorValueProvider, Mockito.times(1)).findAndCountByQuery(Mockito.any());
        Mockito.verify(nonBlockingLambdaExecutionManager, Mockito.times(1))
                .triggerLambdaExecution(Mockito.any());
    }

    @Test
    void testFailedExecutedUpgradeType() {
        var testProjectId = "123";
        var testType = "test";
        Mockito.when(factorProvider.findByQuery(Mockito.any())).thenReturn(List.of());
        Mockito.when(factorValueProvider.findAndCountByQuery(Mockito.any()))
                .thenReturn(
                        FactorValueList.from(
                                Message.FactorValueList.newBuilder().setTotalCount(0).build()));
        Mockito.when(nonBlockingLambdaExecutionManager.triggerLambdaExecution(Mockito.any()))
                .thenReturn("true");

        var estintelProjectHoverManager =
                new EstintelProjectHoverManager(
                        projectHoverManager,
                        nonBlockingLambdaExecutionManager,
                        factorProvider,
                        factorValueProvider);

        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> estintelProjectHoverManager.upgradeType(testProjectId, testType));

        Mockito.verify(factorProvider, Mockito.times(1)).findByQuery(Mockito.any());
        Mockito.verify(factorValueProvider, Mockito.times(1)).findAndCountByQuery(Mockito.any());
        Mockito.verify(nonBlockingLambdaExecutionManager, Mockito.times(0))
                .triggerLambdaExecution(Mockito.any());
    }
}
