package com.bees360.hover;

import com.bees360.project.Message.ProjectMessage.Hover;

public class ProtoProjectHover {

    static ProjectHover from(Hover message) {
        return message == null || Hover.getDefaultInstance().equals(message)
                ? null
                : new ProjectHover() {
                    @Override
                    public String getJobId() {
                        return message.getJobId();
                    }

                    @Override
                    public String getType() {
                        return message.getType();
                    }

                    @Override
                    public String getStatus() {
                        return message.getStatus();
                    }

                    @Override
                    public Boolean getCreatedByOwnWallet() {
                        return message.hasCreatedByOwnWallet()
                                ? message.getCreatedByOwnWallet().getValue()
                                : null;
                    }

                    @Override
                    public Iterable<String> getUpgradeType() {
                        return message.getUpgradeTypeList();
                    }

                    @Override
                    public String getBuildingType() {
                        return message.getBuildingType();
                    }

                    @Override
                    public Integer getBuildingIndex() {
                        return message.hasBuildingIndex()
                                ? message.getBuildingIndex().getValue()
                                : null;
                    }
                };
    }
}
