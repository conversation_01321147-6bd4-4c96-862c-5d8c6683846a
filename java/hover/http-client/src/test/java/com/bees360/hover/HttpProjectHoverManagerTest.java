package com.bees360.hover;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.http.HttpClient;
import com.bees360.oauth.OAuthAutoRefreshToken;
import com.bees360.oauth.OAuthHttpClient;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.annotation.DirtiesContext;

import java.net.URI;

@DirtiesContext
@ApplicationAutoConfig
@SpringBootTest
public class HttpProjectHoverManagerTest {

    @Configuration
    static class Config {
        @MockBean public HttpClient httpClient;

        @MockBean public OAuthAutoRefreshToken oAuthAutoRefreshToken;

        @Bean
        public HttpProjectHoverManager projectHoverManager() {
            var uri = URI.create("local.beespilot/fn/hover/upgrade");
            return new HttpProjectHoverManager(
                    uri,
                    new OAuthHttpClient(httpClient, oAuthAutoRefreshToken),
                    Mockito.mock(ProjectHoverManager.class));
        }
    }

    @Autowired HttpClient httpClient;

    @Autowired ProjectHoverManager projectHoverManager;

    @Test
    void testUpgrade() {
        var projectId = RandomStringUtils.randomNumeric(4);
        var type = "Roof Only";
        projectHoverManager.upgradeType(projectId, type);
        Mockito.verify(httpClient, Mockito.times(1)).execute(Mockito.any(), Mockito.any());
    }
}
