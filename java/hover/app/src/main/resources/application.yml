spring:
  profiles:
    active: ${ENV}
    include: actuator
  mysql:
    jdbc:
      url: ************************************************************************************************************************************************************************
      username: root
      password: 123456
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver
  jooq:
    sql-dialect: POSTGRES

rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password

redis:
  client:
    host: redis
    port: 6379
    database: 0

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    password: ${BEES360_SECRET_KEY}

grpc:
  client:
    imageManager:
      address: static://bees360-image-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pipelineService:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectIIManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    contractManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    lambdaExecutionManager:
      address: static://bees360-estintel-lambda-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    nonBlockingLambdaExecutionManager:
      address: static://bees360-estintel-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    estintelManager:
      address: static://bees360-estintel-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    lambdaRunStatusManager:
      address: static://bees360-estintel-lambda-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    resourcePool:
      address: static://bees360-resource-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

project:
  image:
    group-type: GROUP_PROJECT

hover:
  measurement-report:
    image:
      tag:
        - FRONT, ELEVATION, OVERVIEW
        - FR, ELEVATION, OVERVIEW
        - RIGHT, ELEVATION, OVERVIEW
        - RR, ELEVATION, OVERVIEW
        - REAR, ELEVATION, OVERVIEW
        - RL, ELEVATION, OVERVIEW
        - LEFT, ELEVATION, OVERVIEW
        - FL, ELEVATION, OVERVIEW
  app:
    hover:
      job:
        whitelist:
          permitAll: false
          companyKeys: []

---
spring:
  config:
    activate:
      on-profile: default

firebase:
  firestore:
    emulator-host: firestore-emulator:8080
