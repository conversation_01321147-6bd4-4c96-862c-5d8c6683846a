package com.bees360.hover.listener;

import com.bees360.event.registry.BeespilotHoverJobChangedEvent;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.SaveProjectHoverJob;
import com.bees360.job.util.EventTriggeredJob;

import lombok.extern.log4j.Log4j2;

import java.time.Duration;

@Log4j2
public class UpdateHoverJobOnChanged extends EventTriggeredJob<BeespilotHoverJobChangedEvent> {
    private final Integer retryCount;
    private final Duration retryDelay;
    private final Float retryDelayIncreaseFactor;

    public UpdateHoverJobOnChanged(
            JobScheduler jobScheduler,
            Integer retryCount,
            Duration retryDelay,
            Float retryDelayIncreaseFactor) {
        super(jobScheduler);
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
        this.retryDelayIncreaseFactor = retryDelayIncreaseFactor;
        log.info("Created {}.", this);
    }

    @Override
    protected Job convert(BeespilotHoverJobChangedEvent beespilotHoverJobChangedEvent) {
        var saveProjectHoverJob =
                SaveProjectHoverJob.builder()
                        .projectId(beespilotHoverJobChangedEvent.getProjectId())
                        .deliverableId(beespilotHoverJobChangedEvent.getDeliverableId())
                        .hoverJobId(beespilotHoverJobChangedEvent.getHoverJobId())
                        .state(beespilotHoverJobChangedEvent.getState())
                        .stateUpdateTime(beespilotHoverJobChangedEvent.getStateUpdateTime())
                        .createdByBees360Wallet(
                                beespilotHoverJobChangedEvent.getCreatedByBees360Wallet())
                        .build();
        return RetryableJob.of(
                Job.ofPayload(saveProjectHoverJob),
                retryCount,
                retryDelay,
                retryDelayIncreaseFactor);
    }
}
