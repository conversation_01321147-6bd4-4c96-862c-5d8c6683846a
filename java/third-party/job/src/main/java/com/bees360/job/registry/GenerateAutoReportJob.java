package com.bees360.job.registry;

import com.bees360.event.registry.ReportFactorValueGenerated.FactorError;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

import javax.annotation.Nullable;

@JobPayload
@Getter
@Builder(setterPrefix = "set", builderMethodName = "newBuilder")
@ToString
public class GenerateAutoReportJob {

    /** project id */
    private final String projectId;

    private final String reportType;

    private final String factorValue;

    private final List<AddImageTag> addImageTags;

    private final List<FactorError> errors;

    private final String jobId;

    private final String createdAt;

    private final String createdBy;

    @ToString
    @Getter
    @Setter
    @Builder(builderMethodName = "newBuilder", setterPrefix = "set")
    @AllArgsConstructor
    public static class AddImageTag {
        /** image id */
        private String imageId;

        /** image tag */
        private List<AddTag> tags;
    }

    @ToString
    @Getter
    @Setter
    @Builder(builderMethodName = "newBuilder", setterPrefix = "set")
    @AllArgsConstructor
    public static class AddTag {
        /** tag id */
        private String tagId;

        /** tag attribute */
        private AddTagAttribute attribute;

        /**
         * The value may be empty; If it is not empty, it satisfies size=4, and the value range is
         * [0,1]. The first two values are the x,y of the annotation in the upper left corner of the
         * image, and the last two values are the x,y of the lower right corner.
         */
        @Nullable private Double[] bbox;
    }

    @ToString
    @Getter
    @Setter
    @Builder(builderMethodName = "newBuilder", setterPrefix = "set")
    @AllArgsConstructor
    public static class AddTagAttribute {
        /** tag attribute index */
        private int index;

        /** tag attribute source */
        private String source;
    }
}
