package com.bees360.thirdparty;

import com.bees360.http.HttpClient;
import com.bees360.status.Message.StatusMessage;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.net.URI;
import java.util.Optional;

/** ThirdPartyTypeEnum.REPORT_FACTOR 枚举类对应的 ThirdPartyManager 处理器，主要用来调用 ai 服务提供的第三方接口。 */
@Log4j2
public class ReportFactorThirdPartyManager extends HttpThirdPartyManager {
    public final String REQUEST_BODY = "request_body";

    private final URI uri;

    public ReportFactorThirdPartyManager(
            URI uri, HttpClient httpClient, String signatureHeader, String authToken) {
        super(httpClient, signatureHeader, authToken);
        this.uri = uri;
        log.info("Created {}(uri='{}', httpClient='{}').", this, this.uri, httpClient);
    }

    @Override
    public ThirdPartyTypeEnum getThirdPartyType() {
        return ThirdPartyTypeEnum.REPORT_FACTOR;
    }

    @Override
    protected void check(ThirdPartyQuery query) {
        super.check(query);
        var data =
                Optional.of(query.getData())
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                "ThirdPartyQuery check failed: ThirdPartyData"
                                                        + " is null."));
        getRequestBody(Iterables.toList(data));
    }

    @Override
    protected ThirdPartyStatus sendThirdParty(ThirdPartyQuery query) {
        var key = query.getKey();
        var data = query.getData();
        log.info("ReportFactorThirdPartyManager sendThirdParty start. key = '{}'", key);
        try {
            postJobs(Iterables.toList(data));
        } catch (IOException e) {
            throw new IllegalStateException(
                    "Failed to call job_scheduler service job interface:error message = "
                            + e.getMessage(),
                    e);
        }
        log.info("ReportFactorThirdPartyManager sendThirdParty end. key = '{}'", key);
        return ThirdPartyStatus.of(
                StatusMessage.Code.OK, "The interface call was successful.", null);
    }

    @Override
    URI getUri(Iterable<ThirdPartyQuery.ThirdPartyData> data) {
        return uri;
    }

    @Override
    String getRequestBody(Iterable<ThirdPartyQuery.ThirdPartyData> data) {
        return Iterables.toStream(data)
                .filter(d -> d.getName().equals(REQUEST_BODY))
                .findAny()
                .orElseThrow(
                        () ->
                                new IllegalArgumentException(
                                        String.format(
                                                "ThirdPartyQuery check failed: No such resource"
                                                        + " exists. name='%s'",
                                                REQUEST_BODY)))
                .getValue();
    }
}
