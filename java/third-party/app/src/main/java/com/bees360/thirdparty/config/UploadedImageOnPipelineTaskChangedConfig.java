package com.bees360.thirdparty.config;

import static com.bees360.thirdparty.listener.PipelineTaskTriggerUploadedImage2ThirdPartyJob.SKIP_COVER_ALL;

import static java.util.stream.Collectors.toSet;

import com.bees360.image.tag.ImageTag;
import com.bees360.image.tag.ImageTagDictProvider;
import com.bees360.job.JobScheduler;
import com.bees360.job.UploadImage2ThirdPartyJobExecutor;
import com.bees360.pipeline.Message;
import com.bees360.thirdparty.ThirdPartyImageTagGroup;
import com.bees360.thirdparty.listener.PipelineTaskTriggerUploadedImage2ThirdPartyJob;
import com.bees360.util.Iterables;

import lombok.Data;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@ConditionalOnProperty(
        prefix = "thirdparty.app.uploaded-image-on-pipeline-task",
        name = "enabled",
        havingValue = "true")
@Configuration
@Import({UploadImage2ThirdPartyJobExecutor.class})
public class UploadedImageOnPipelineTaskChangedConfig {
    @ConfigurationProperties(prefix = "thirdparty.app.uploaded-image-on-pipeline-task")
    @Configuration
    @Data
    @EnableConfigurationProperties
    public static class UploadedImageProperty {
        private List<PipelineTaskTagProperty> tasks;
        private Integer retryCount;
        private Duration retryDelay;
        private Float retryDelayIncreaseFactor;
    }

    @Data
    static class PipelineTaskTagProperty {
        private String taskKey;
        private String thirdPartyType;
        private Message.PipelineStatus status;
        private List<ImageTagGroup> tagGroups = new ArrayList<>();
        // 当task状态修改人 在{@code skipCoverUserId}中时，上传的图片不会覆盖上一次上传的照片，默认所以上上传照片都不覆盖。
        private List<String> skipCoverUserId = List.of(SKIP_COVER_ALL);
        private Integer minImageCount = 0;
    }

    @Data
    static class ImageTagGroup {
        private List<String> imageTagIds = new ArrayList<>();
        private List<String> excludeImageTagIds = new ArrayList<>();
        private List<String> imageTagCategory = new ArrayList<>();
        private List<String> excludeImageTagCategory = new ArrayList<>();
    }

    @Bean
    public PipelineTaskTriggerUploadedImage2ThirdPartyJob
            pipelineTaskTriggerUploadedImage2ThirdPartyJob(
                    JobScheduler jobScheduler,
                    UploadedImageProperty uploadedImageProperty,
                    ImageTagDictProvider imageTagDictProvider) {
        var map =
                uploadedImageProperty.getTasks().stream()
                        .collect(
                                Collectors.toMap(
                                        p -> Pair.of(p.getTaskKey(), p.getStatus()),
                                        g -> mapThirdPartyImageTagGroup(g, imageTagDictProvider)));
        return new PipelineTaskTriggerUploadedImage2ThirdPartyJob(
                jobScheduler,
                uploadedImageProperty.getRetryCount(),
                uploadedImageProperty.getRetryDelay(),
                uploadedImageProperty.getRetryDelayIncreaseFactor(),
                map);
    }

    private Pair<ThirdPartyImageTagGroup, List<String>> mapThirdPartyImageTagGroup(
            PipelineTaskTagProperty property, ImageTagDictProvider imageTagDictProvider) {
        var thirdPartyType = property.getThirdPartyType();
        var groups =
                property.getTagGroups().stream()
                        .map(g -> map(g, imageTagDictProvider))
                        .collect(Collectors.toList());
        Integer minImageCount = property.getMinImageCount();
        var group = new ThirdPartyImageTagGroup();
        group.setThirdPartyType(thirdPartyType);
        group.setMinImageCount(minImageCount);
        group.setTagGroups(
                groups.stream().flatMap(Collection::stream).collect(Collectors.toList()));
        return Pair.of(group, property.getSkipCoverUserId());
    }

    // TODO 应该需要支持通过GroupImageTag去搜索图片
    private List<ThirdPartyImageTagGroup.TagGroup> map(
            ImageTagGroup imageTagGroup, ImageTagDictProvider imageTagDictProvider) {
        var includeTags = imageTagGroup.getImageTagIds();
        var excludeTags = imageTagGroup.getExcludeImageTagIds();
        var tags = imageTagDictProvider.getAll();

        // grouping by category, Map<category, Set<tagId>>
        var category2TagIds =
                Iterables.toStream(tags)
                        .collect(
                                Collectors.groupingBy(
                                        ImageTag::getCategory,
                                        Collectors.mapping(ImageTag::getId, toSet())));
        // add exclude tagIds from excludeTagCategories
        Optional.ofNullable(imageTagGroup.getExcludeImageTagCategory())
                .ifPresent(
                        categories ->
                                categories.forEach(
                                        c ->
                                                excludeTags.addAll(
                                                        category2TagIds.getOrDefault(
                                                                c, Collections.emptySet()))));
        if (CollectionUtils.isEmpty(imageTagGroup.getImageTagCategory())) {
            var tagGroup =
                    ThirdPartyImageTagGroup.TagGroup.newBuilder()
                            .setImageTags(includeTags)
                            .setExcludeImageTags(excludeTags)
                            .build();
            return List.of(tagGroup);
        }
        List<Set<String>> tagIdsByCategory =
                imageTagGroup.getImageTagCategory().stream()
                        .map(
                                category ->
                                        category2TagIds.getOrDefault(
                                                category, Collections.emptySet()))
                        .collect(Collectors.toList());
        Iterable<? extends Iterable<String>> product = cartesianProduct(tagIdsByCategory);
        if (CollectionUtils.isEmpty(tagIdsByCategory)) {
            var tagGroup =
                    ThirdPartyImageTagGroup.TagGroup.newBuilder()
                            .setImageTags(includeTags)
                            .setExcludeImageTags(excludeTags)
                            .build();
            return List.of(tagGroup);
        }

        return StreamSupport.stream(product.spliterator(), false)
                .map(
                        t ->
                                ThirdPartyImageTagGroup.TagGroup.newBuilder()
                                        .setImageTags(
                                                concat(
                                                        new ArrayList<>(includeTags),
                                                        StreamSupport.stream(t.spliterator(), false)
                                                                .collect(Collectors.toList())))
                                        .setExcludeImageTags(excludeTags)
                                        .build())
                .collect(Collectors.toList());
    }

    /**
     * Computes the Cartesian product of a series of iterables.
     *
     * @param sets An iterable of iterables, each representing a set of elements.
     * @param <T> The type of elements in the sets.
     * @return An iterable of iterables, where each inner iterable represents a unique combination
     *     of elements, one from each set.
     */
    private <T> Iterable<? extends Iterable<T>> cartesianProduct(
            Iterable<? extends Iterable<T>> sets) {
        var origin = Iterables.toList(sets);
        return cartesianProduct(origin, origin.size());
    }

    /**
     * A helper method to compute the Cartesian product of a list of iterables.
     *
     * @param origin A list of iterables, each representing a set of elements.
     * @param index The current index in the list to process for Cartesian product.
     * @param <T> The type of elements in the sets.
     * @return An iterable of iterables, where each inner iterable represents a unique combination
     *     of elements, one from each set up to the specified index.
     */
    private <T> Iterable<? extends Iterable<T>> cartesianProduct(
            List<? extends Iterable<T>> origin, int index) {
        if (index == 0) {
            return Arrays.asList(Arrays.asList()); // Return a list containing an empty list
        }
        var currentSet = origin.get(index - 1);
        var preCartesianProduct = cartesianProduct(origin, index - 1);
        List<List<T>> res = new ArrayList<>();
        preCartesianProduct.forEach(
                pre -> {
                    currentSet.forEach(
                            cur -> {
                                var item =
                                        StreamSupport.stream(pre.spliterator(), false)
                                                .collect(Collectors.toList());
                                item.add(cur);
                                res.add(item);
                            });
                });
        return res;
    }

    private List<String> concat(List<String> imageTagEnums, List<String> tagEnums) {
        imageTagEnums.addAll(tagEnums);
        return imageTagEnums;
    }
}
