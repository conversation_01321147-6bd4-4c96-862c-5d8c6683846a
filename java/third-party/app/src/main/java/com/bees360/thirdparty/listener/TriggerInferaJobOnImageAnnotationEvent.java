package com.bees360.thirdparty.listener;

import com.bees360.event.registry.InferaImageAnnotationEvent;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.UpdateInferaAnnotationJob;
import com.bees360.job.registry.UpdateInferaAnnotationJob.InferaAnnotation;
import com.bees360.job.registry.UpdateInferaAnnotationJob.InferaImage;
import com.bees360.job.util.EventTriggeredJob;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
public class TriggerInferaJobOnImageAnnotationEvent
        extends EventTriggeredJob<InferaImageAnnotationEvent> {
    private final Integer retryCount;
    private final Duration retryDelay;
    private final Float retryDelayIncreaseFactor;

    public TriggerInferaJobOnImageAnnotationEvent(
            JobScheduler jobScheduler,
            @Nullable Integer retryCount,
            @Nullable Duration retryDelay,
            @Nullable Float retryDelayIncreaseFactor) {
        super(jobScheduler);
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
        this.retryDelayIncreaseFactor = retryDelayIncreaseFactor;
        log.info(
                "Created '{}(jobScheduler={}, retryCount={}, retryDelay={},"
                        + " retryDelayIncreaseFactor={})'",
                this,
                this.jobScheduler,
                this.retryCount,
                this.retryDelay,
                this.retryDelayIncreaseFactor);
    }

    @Override
    protected Job convert(InferaImageAnnotationEvent event) {
        var jobName = event.getJobName();
        var inferaImages = event.getImages();
        var images =
                inferaImages.stream()
                        .map(
                                image -> {
                                    var imageId = image.getImageId();
                                    var annotations = getAnnotations(image.getTags());
                                    return InferaImage.newBuilder()
                                            .setImageId(imageId)
                                            .setAnnotations(annotations)
                                            .build();
                                })
                        .collect(Collectors.toList());
        var job = new UpdateInferaAnnotationJob(jobName, images);
        return RetryableJob.of(
                Job.ofPayload(job), retryCount, retryDelay, retryDelayIncreaseFactor);
    }

    private List<InferaAnnotation> getAnnotations(
            List<InferaImageAnnotationEvent.InferaImageTag> imageTags) {
        if (CollectionUtils.isEmpty(imageTags)) {
            return Collections.emptyList();
        }
        return imageTags.stream()
                .map(
                        tag ->
                                InferaAnnotation.newBuilder()
                                        .setTagId(tag.getTagId())
                                        .setBbox(tag.getBbox())
                                        .setConfidenceLevel(tag.getConfidence())
                                        .build())
                .collect(Collectors.toList());
    }
}
