package com.bees360.thirdparty;

import com.bees360.api.ApiStatus;
import com.bees360.api.Message.ApiMessage;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.NoSuchElementException;
import java.util.stream.Collectors;

/** ThirdPartyJobStatusEndpoint */
@Log4j2
@RestController
@RequestMapping("${http.thirdparty.endpoint:thirdparty}/job-status")
@Import({ProtoHttpMessageConverterConfig.class, ApiExceptionHandler.class})
public class ThirdPartyJobStatusEndpoint {
    private final ThirdPartyJobStatusProvider thirdPartyJobStatusProvider;

    public ThirdPartyJobStatusEndpoint(ThirdPartyJobStatusProvider thirdPartyJobStatusProvider) {
        this.thirdPartyJobStatusProvider = thirdPartyJobStatusProvider;
        log.info(
                "created {}(ThirdPartyJobStatusManager='{}')",
                this,
                this.thirdPartyJobStatusProvider);
    }

    /**
     * Find status of a third party job.
     *
     * @param relatedId id of third party job
     * @param type job type
     * @return ApiMessage with a third party job status
     */
    @GetMapping(value = "/{relatedId:\\d+}")
    public ApiMessage findJobStatus(@PathVariable String relatedId, String type) {
        var response = ApiMessage.newBuilder().setStatus(ApiStatus.OK.toMessage());
        if (StringUtils.isNotBlank(type)) {
            var status = thirdPartyJobStatusProvider.findJobStatus(relatedId, type);
            if (null == status) {
                throw new NoSuchElementException(
                        String.format(
                                "The %s job status related id = %s not found.", type, relatedId));
            }
            return response.addThirdPartyJobStatus(status.toMessage()).build();
        }
        return response.addAllThirdPartyJobStatus(
                        Iterables.toList(
                                        thirdPartyJobStatusProvider.findJobStatusByRelatedId(
                                                relatedId))
                                .stream()
                                .map(ThirdPartyJobStatus::toMessage)
                                .collect(Collectors.toList()))
                .build();
    }
}
