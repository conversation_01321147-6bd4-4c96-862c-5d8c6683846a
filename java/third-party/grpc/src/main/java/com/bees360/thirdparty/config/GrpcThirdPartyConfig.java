package com.bees360.thirdparty.config;

import com.bees360.thirdparty.GrpcThirdPartyJobStatusProviderService;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
public class GrpcThirdPartyConfig {
    @Import({GrpcThirdPartyJobStatusProviderService.class})
    @Configuration
    @ConditionalOnProperty(
            prefix = "thirdparty.grpc.job-status-provider-service",
            name = "disabled",
            havingValue = "false",
            matchIfMissing = true)
    public static class GrpcThirdPartyJobStatusProviderServiceConfig {}
}
