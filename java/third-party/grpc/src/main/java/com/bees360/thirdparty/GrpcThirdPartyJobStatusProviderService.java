package com.bees360.thirdparty;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.thirdparty.Message.ThirdPartyJobStatusMessage;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.springframework.context.annotation.Import;

import java.util.Optional;

@Log4j2
@GrpcService
@Import({ExceptionTranslateInterceptor.class})
public class GrpcThirdPartyJobStatusProviderService
        extends ThirdPartyJobStatusProviderGrpc.ThirdPartyJobStatusProviderImplBase {
    private final ThirdPartyJobStatusManager thirdPartyJobStatusManager;

    public GrpcThirdPartyJobStatusProviderService(
            ThirdPartyJobStatusManager grpcThirdPartyJobStatusManager) {
        this.thirdPartyJobStatusManager = grpcThirdPartyJobStatusManager;
        log.info(
                "created '{}(ThirdPartyJobStatusManager={})'.",
                this,
                grpcThirdPartyJobStatusManager);
    }

    @Override
    public void findJobStatus(
            Message.FindThirdPartyJobStatusRequest request,
            StreamObserver<Message.ThirdPartyJobStatusMessage> responseObserver) {
        var jobStatus =
                thirdPartyJobStatusManager.findJobStatus(request.getRelatedId(), request.getType());
        responseObserver.onNext(
                Optional.ofNullable(jobStatus)
                        .map(ThirdPartyJobStatus::toMessage)
                        .orElse(ThirdPartyJobStatusMessage.getDefaultInstance()));
        responseObserver.onCompleted();
    }

    @Override
    public void findJobStatusByRelatedId(
            StringValue request,
            StreamObserver<Message.ThirdPartyJobStatusMessage> responseObserver) {
        var jobStatuses = thirdPartyJobStatusManager.findJobStatusByRelatedId(request.getValue());
        jobStatuses.forEach(s -> responseObserver.onNext(s.toMessage()));
        responseObserver.onCompleted();
    }
}
