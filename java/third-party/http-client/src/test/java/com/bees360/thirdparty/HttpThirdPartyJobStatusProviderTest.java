package com.bees360.thirdparty;

import com.bees360.api.ApiHttpClient;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.http.config.ApacheHttpClientConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

@DirtiesContext
@ApplicationAutoConfig
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class HttpThirdPartyJobStatusProviderTest extends AbstractThirdPartyJobStatusManagerTest {
    @Import({
        ApacheHttpClientConfig.class,
        ThirdPartyJobStatusEndpoint.class,
        InMemoryThirdPartyJobStatusManager.class,
        ApiHttpClient.class,
    })
    @Configuration
    static class Config {}

    public HttpThirdPartyJobStatusProviderTest(
            @LocalServerPort int port,
            @Value("${http.thirdparty.endpoint:thirdparty}/job-status") String endpoint,
            @Autowired ApiHttpClient httpClient,
            @Autowired ThirdPartyJobStatusManager thirdPartyJobStatusManager) {
        super(
                new HttpThirdPartyJobStatusProvider(
                        "localhost", port, endpoint, httpClient, thirdPartyJobStatusManager),
                thirdPartyJobStatusManager);
    }

    @Test
    public void saveAndFindJobStatusTest() {
        super.saveAndFindJobStatusTest();
    }

    @Test
    public void saveAndFindJobStatusByRelatedIdTest() {
        super.saveAndFindJobStatusByRelatedIdTest();
    }

    @Test
    public void saveAndUpdateJobStatusTest() {
        super.saveAndUpdateJobStatusTest();
    }

    @Test
    public void saveUnknownStatusTest() {
        super.saveUnknownStatusTest();
    }

    @Test
    public void saveUnknownTypeTest() {
        super.saveUnknownTypeTest();
    }
}
