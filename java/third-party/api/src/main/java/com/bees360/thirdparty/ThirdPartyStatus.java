package com.bees360.thirdparty;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.Proto;
import com.bees360.status.Message.StatusMessage;
import com.google.protobuf.ByteString;

import java.util.Objects;

import javax.annotation.Nullable;

public interface ThirdPartyStatus extends Proto<Message.ThirdPartyResponse> {
    StatusMessage getStatus();

    @Nullable
    static ThirdPartyStatus from(Message.ThirdPartyResponse message) {
        if (Objects.equals(Message.ThirdPartyResponse.getDefaultInstance(), message)) {
            return null;
        }
        return new ThirdPartyStatus() {
            @Override
            public StatusMessage getStatus() {
                return message.getStatus();
            }
        };
    }

    static ThirdPartyStatus of(StatusMessage.Code code, String description, String cause) {
        var statusBuilder = StatusMessage.newBuilder();
        acceptIfNotNull(statusBuilder::setCode, code);
        acceptIfNotNull(statusBuilder::setDescription, description);
        acceptIfNotNull(statusBuilder::setCause, cause, ByteString::copyFromUtf8);
        return of(statusBuilder.build());
    }

    static ThirdPartyStatus of(StatusMessage statusMessage) {
        var builder = Message.ThirdPartyResponse.newBuilder();
        acceptIfNotNull(builder::setStatus, statusMessage);
        return from(builder.build());
    }

    @Override
    default Message.ThirdPartyResponse toMessage() {
        var builder = Message.ThirdPartyResponse.newBuilder();
        acceptIfNotNull(builder::setStatus, getStatus());
        return builder.build();
    }
}
