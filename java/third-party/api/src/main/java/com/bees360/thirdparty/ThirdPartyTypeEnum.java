package com.bees360.thirdparty;

import java.util.Arrays;

import javax.annotation.Nullable;

public enum ThirdPartyTypeEnum {
    VALUE_360,
    INFERA,
    REPORT_FACTOR,
    ;

    @Nullable
    public static ThirdPartyTypeEnum from(String type) {
        return Arrays.stream(ThirdPartyTypeEnum.values())
                .filter(x -> x.name().equals(type))
                .findFirst()
                .orElse(null);
    }
}
