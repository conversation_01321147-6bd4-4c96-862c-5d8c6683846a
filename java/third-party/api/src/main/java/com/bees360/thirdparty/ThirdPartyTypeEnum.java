package com.bees360.thirdparty;

import jakarta.annotation.Nullable;

import java.util.Arrays;

public enum ThirdPartyTypeEnum {
    VALUE_360,
    INFERA,
    REPORT_FACTOR,
    ;

    @Nullable
    public static ThirdPartyTypeEnum from(String type) {
        return Arrays.stream(ThirdPartyTypeEnum.values())
                .filter(x -> x.name().equals(type))
                .findFirst()
                .orElse(null);
    }
}
