package com.bees360.activity;

import com.bees360.activity.Message.ActivityMessage;
import com.bees360.api.Message;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/** ActivityEndpoint */
@RestController
@Log4j2
public class ActivityEndpoint {
    private final ActivityManager activityManager;

    public ActivityEndpoint(final ActivityManager endpointActivityManager) {
        this.activityManager = endpointActivityManager;
        log.info("Created '{} (activityManager={})'", this, this.activityManager);
    }

    /**
     * 提交活动信息
     *
     * @param apiMessage 活动消息
     * @return 活动消息
     */
    @PostMapping(value = "activity")
    public @ResponseBody Message.ApiMessage submitActivity(
            @RequestBody Message.ApiMessage apiMessage) {
        if (apiMessage.getActivityCount() < 1) {
            throw new IllegalArgumentException("The activity cannot be empty");
        }
        List<ActivityMessage> messages = apiMessage.getActivityList();
        Iterable<String> ids =
                activityManager.submitActivities(
                        messages.stream()
                                .map(Activity::of)
                                .map(
                                        activity ->
                                                Optional.ofNullable(CurUserHolder.getUserId())
                                                        .map(activity::withCreatedBy)
                                                        .orElse(activity))
                                .collect(Collectors.toList()));
        return Message.ApiMessage.newBuilder()
                .addAllActivity(
                        Iterables.toStream(ids)
                                .map(id -> ActivityMessage.newBuilder().setId(id).build())
                                .collect(Collectors.toList()))
                .build();
    }

    /**
     * 获取活动列表
     *
     * @param query 活动查询条件
     * @return 活动列表
     */
    @GetMapping(value = "activity")
    public @ResponseBody Iterable<? extends Activity> getActivities(ActivityQuery query) {
        return activityManager.getActivities(query);
    }

    /**
     * 同步活动到指定目标
     *
     * @param activityId 活动ID
     * @param syncTo 同步目的地
     * @return 同步日志
     */
    @PostMapping("activity/{activityId}/sync-log/{syncTo}")
    public ActivitySyncLog sync(@PathVariable String activityId, @PathVariable String syncTo) {
        var createdByUserId = CurUserHolder.getUserId();
        return activityManager.sync(activityId, syncTo, createdByUserId);
    }

    /**
     * 查询指定活动信息
     *
     * @param activityId 活动ID
     * @return 活动信息
     */
    @GetMapping("activity/{activityId}")
    public Activity findById(@PathVariable String activityId) {
        return activityManager.getActivity(activityId);
    }

    /**
     * 删除指定ID的活动信息
     *
     * @param activityId 活动ID
     */
    @DeleteMapping("activity/{activityId}")
    public void deleteById(@PathVariable String activityId) {
        activityManager.deleteById(activityId);
    }

    /**
     * 允许活动展示在平台上
     *
     * @param activityId activity唯一编码
     * @param platform 平台名称(IO, AI, WEB, APP)
     */
    @PutMapping("activity/{activityId}/visibility/{platform}")
    public void addVisibility(@PathVariable String activityId, @PathVariable String platform) {
        activityManager.addVisibility(activityId, platform);
    }

    /**
     * 禁止活动展示在平台上
     *
     * @param activityId activity唯一编码
     * @param platform 平台名称(IO, AI, WEB, APP)
     */
    @DeleteMapping("activity/{activityId}/visibility/{platform}")
    public void removeVisibility(@PathVariable String activityId, @PathVariable String platform) {
        activityManager.removeVisibility(activityId, platform);
    }
}
