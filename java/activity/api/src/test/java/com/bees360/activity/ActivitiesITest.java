package com.bees360.activity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

import com.bees360.activity.Message.ActivityMessage;
import com.bees360.user.Message;
import com.google.common.collect.Lists;

import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.List;

public class ActivitiesITest extends BaseActivityUtil {

    private final Instant instant;

    public ActivitiesITest() {
        this.instant = Instant.now();
    }

    @Test
    public void testCreateProject() {
        long projectId = randomProjectId();
        Message.UserMessage createdBy = randomUser();
        Activity activity = Activities.createProject(projectId, createdBy.getId());
        assertActivity(
                activity,
                projectId,
                createdBy,
                projectId + "",
                1,
                ActivityMessage.EntityType.PROJECT.name(),
                ActivityMessage.ActionType.CREATE.name());
        try {
            activity.toText();
            fail();
        } catch (UnsupportedOperationException e) {
            // do nothing: success
        }
    }

    @Test
    public void testChangeProjectField() {
        long projectId = randomProjectId();
        Message.UserMessage createdBy = randomUser();
        ActivityMessage.Field field = randomField();
        Activity activity = Activities.changeProjectField(projectId, createdBy.getId(), field);
        assertActivity(
                activity,
                projectId,
                createdBy,
                projectId + "",
                1,
                ActivityMessage.EntityType.PROJECT.name(),
                ActivityMessage.ActionType.CHANGE.name());
        assertEquals(activity.getFiledType(), field.getType());
        assertEquals(activity.getFiledName(), field.getName());
        assertEquals(activity.getOldValue(), field.getOldValue());
        assertEquals(activity.getValue(), field.getValue());
        try {
            activity.toText();
            fail();
        } catch (UnsupportedOperationException e) {
            // do nothing: success
        }
    }

    @Test
    public void testChangeField() {
        long projectId = randomProjectId();
        Message.UserMessage createdBy = randomUser();
        ActivityMessage.Field field = randomField();
        ActivityMessage.Entity entity = randomEntity();
        Activity activity = Activities.changeField(projectId, createdBy.getId(), entity, field);
        assertActivity(
                activity,
                projectId,
                createdBy,
                entity.getId(),
                1,
                entity.getType(),
                ActivityMessage.ActionType.CHANGE.name());
        assertEquals(activity.getFiledType(), field.getType());
        assertEquals(activity.getFiledName(), field.getName());
        assertEquals(activity.getOldValue(), field.getOldValue());
        assertEquals(activity.getValue(), field.getValue());
        try {
            activity.toText();
        } catch (UnsupportedOperationException e) {
            // do nothing: success
        }
    }

    @Test
    public void testCreateImage() {
        int count = RandomUtils.nextInt();
        long projectId = randomProjectId();
        Message.UserMessage createdBy = randomUser();
        Activity activity = Activities.createImage(projectId, createdBy.getId(), count);
        assertActivity(
                activity,
                projectId,
                createdBy,
                "",
                count,
                ActivityMessage.EntityType.IMAGE.name(),
                ActivityMessage.ActionType.CREATE.name());
        assertNotNull(activity);
    }

    @Test
    public void testCreatePilot() {
        long projectId = randomProjectId();
        Message.UserMessage createdBy = randomUser();
        Message.UserMessage pilot = randomUser();
        Activity activity = Activities.assignedPilot(projectId, createdBy.getId(), pilot.getId());
        assertActivity(
                activity,
                projectId,
                createdBy,
                pilot.getId(),
                1,
                ActivityMessage.EntityType.PILOT.name(),
                ActivityMessage.ActionType.CREATE.name());
        try {
            activity.toText();
            fail();
        } catch (UnsupportedOperationException e) {
            // do nothing: success
        }
    }

    @Test
    public void testCancelPilot() {
        long projectId = randomProjectId();
        Message.UserMessage createdBy = randomUser();
        Message.UserMessage pilot = randomUser();
        Activity activity =
                Activities.cancelAssignedPilot(projectId, createdBy.getId(), pilot.getId());
        assertActivity(
                activity,
                projectId,
                createdBy,
                pilot.getId(),
                1,
                ActivityMessage.EntityType.PILOT.name(),
                ActivityMessage.ActionType.DELETE.name());
        try {
            activity.toText();
            fail();
        } catch (UnsupportedOperationException e) {
            // do nothing: success
        }
    }

    @Test
    public void testAcceptBatch() {
        long projectId = randomProjectId();
        Message.UserMessage pilot = randomUser();
        String batchNo = randomId();
        Activity activity = Activities.acceptBatch(projectId, pilot.getId(), batchNo);
        assertActivity(
                activity,
                projectId,
                pilot,
                batchNo,
                1,
                ActivityMessage.EntityType.BATCH.name(),
                ActivityMessage.ActionType.CREATE.name());
    }

    @Test
    public void testRejectBatch() {
        long projectId = randomProjectId();
        Message.UserMessage pilot = randomUser();
        String batchNo = randomId();
        Activity activity = Activities.rejectBatch(projectId, pilot.getId(), batchNo);
        assertActivity(
                activity,
                projectId,
                pilot,
                batchNo,
                1,
                ActivityMessage.EntityType.BATCH.name(),
                ActivityMessage.ActionType.DELETE.name());
        try {
            activity.toText();
            fail();
        } catch (UnsupportedOperationException e) {
            // do nothing: success
        }
    }

    @Test
    public void testSubscribePlnar() {
        long projectId = randomProjectId();
        Message.UserMessage pilot = randomUser();
        String plnarUrl = randomUrl();
        Activity activity = Activities.subscribePlnar(projectId, pilot.getId(), plnarUrl);
        assertActivity(
                activity,
                projectId,
                pilot,
                plnarUrl,
                1,
                ActivityMessage.EntityType.PLNAR.name(),
                ActivityMessage.ActionType.CREATE.name());
        try {
            activity.toText();
            fail();
        } catch (UnsupportedOperationException e) {
            // do nothing: success
        }
    }

    @Test
    public void testSubscribePlnarOnFailed() {
        long projectId = randomProjectId();
        Message.UserMessage pilot = randomUser();
        Activity activity = Activities.subscribePlnarOnFailed(projectId, pilot.getId());
        assertActivity(
                activity,
                projectId,
                pilot,
                "",
                1,
                ActivityMessage.EntityType.PLNAR.name(),
                ActivityMessage.ActionType.CREATE.name());
        try {
            activity.toText();
            fail();
        } catch (UnsupportedOperationException e) {
            // do nothing: success
        }
    }

    @Test
    public void testSubscribeHover() {
        long projectId = randomProjectId();
        Message.UserMessage createdBy = randomUser();
        String hoverJobId = randomId();
        Activity activity = Activities.subscribeHover(projectId, createdBy.getId(), hoverJobId);
        assertActivity(
                activity,
                projectId,
                createdBy,
                hoverJobId,
                1,
                ActivityMessage.EntityType.HOVER.name(),
                ActivityMessage.ActionType.CREATE.name());
        try {
            activity.toText();
            fail();
        } catch (UnsupportedOperationException e) {
            // do nothing: success
        }
    }

    @Test
    public void testSubscribeHoverOnFailed() {
        long projectId = randomProjectId();
        Message.UserMessage createdBy = randomUser();
        Activity activity = Activities.subscribeHoverOnFailed(projectId, createdBy.getId());
        assertActivity(
                activity,
                projectId,
                createdBy,
                "",
                1,
                ActivityMessage.EntityType.HOVER.name(),
                ActivityMessage.ActionType.CREATE.name());
        try {
            activity.toText();
            fail();
        } catch (UnsupportedOperationException e) {
            // do nothing: success
        }
    }

    @Test
    public void testChangeProjectTags() {
        List<String> tags = Lists.newArrayList("First Tag", "Second Tag", "Third Tag");
        final String ACTIVITY_TEXT = "Changed project tags to First Tag, Second Tag, Third Tag.";
        long projectId = randomProjectId();
        Message.UserMessage createdBy = randomUser();
        Activity activity = Activities.changeProjectTag(projectId, createdBy.getId(), tags);
        assertActivity(
                activity,
                projectId,
                createdBy,
                projectId + "",
                1,
                ActivityMessage.EntityType.PROJECT.name(),
                ActivityMessage.ActionType.CHANGE.name());

        assertEquals(activity.getFiledType(), ActivityMessage.FieldType.STRING.name());
        assertEquals(activity.getFiledName(), ActivityMessage.FieldName.TAG.name());
        assertEquals(activity.getOldValue(), "");
        assertEquals(
                activity.getValue(), StringUtils.join(tags, Activities.ACTIVITY_VALUE_DELIMITER));

        assertEquals(ACTIVITY_TEXT, activity.toText());
    }

    @Test
    public void testCommentActivityToText() {
        String activityId = randomId();
        Comment comment = randomComment();
        Activity activity = Activity.fromComment(activityId, comment);
        assertEquals(comment.getContent(), activity.toText());
    }

    @Test
    public void testActivityCommentToText() {
        Comment comment = randomComment();
        Activity baseActivity = BaseActivityUtil.randomActivity();
        var message = baseActivity.toMessage().toBuilder().setComment(comment.toMessage()).build();
        var activity = Activity.from(message);
        assertEquals(comment.getContent(), activity.toText());
    }

    private void assertActivity(
            Activity activity,
            long projectId,
            Message.UserMessage createBy,
            String entityId,
            int entityCount,
            String entityType,
            String actionType) {
        assertNotNull(activity);
        assertEquals(activity.getEntityId(), entityId);
        assertEquals(activity.getEntityType(), entityType);
        assertEquals(activity.getCreatedBy(), createBy.getId());
        assertEquals(activity.getProjectId(), projectId);
        assertFalse(activity.getCreatedAt().isBefore(instant));
        assertEquals(activity.getEntityCount(), entityCount);
        assertEquals(activity.getAction(), actionType);
    }
}
