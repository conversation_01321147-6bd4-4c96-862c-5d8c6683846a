package com.bees360.activity.impl;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityQuery;
import com.bees360.activity.ActivitySyncManager;
import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.activity.CommentQuery;
import com.bees360.user.Pilot;
import com.bees360.user.User;
import com.bees360.util.Iterables;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class InMemoryActivityCommentManager extends AbstractActivityManager
        implements CommentManager {

    /** key is {@link Activity#getId()} */
    private final Map<String, Activity> activityMap;
    /** key is {@link Comment#getId()} */
    private final Map<String, Comment> commentMap;

    public InMemoryActivityCommentManager(
            ActivitySyncManager activitySyncManager, InMemoryStorage inMemoryStorage) {
        super(activitySyncManager);
        this.activityMap = inMemoryStorage.activity();
        this.commentMap = inMemoryStorage.comment();
    }

    @Override
    public String submitActivity(Activity activity) {
        String id = generateRandomID();
        Activity entity = activity.withId(id);
        activityMap.put(id, entity);
        return id;
    }

    @Override
    public Activity getActivity(String id) {
        return activityMap.get(id);
    }

    @Override
    public Iterable<String> submitActivities(Iterable<? extends Activity> activities) {
        return Iterables.toStream(activities)
                .map(this::submitActivity)
                .collect(Collectors.toList());
    }

    private String generateRandomID() {
        return RandomStringUtils.randomAlphanumeric(16);
    }

    @Override
    public List<Activity> getActivities(ActivityQuery query) {
        return activityMap.values().stream()
                .filter(
                        e ->
                                query.getProjectId() == null
                                        || query.getProjectId() == 0
                                        || Objects.equals(query.getProjectId(), e.getProjectId()))
                .filter(
                        e ->
                                query.getProjectType() == null
                                        || query.getProjectType().equals(e.getProjectType()))
                .filter(e -> StringUtils.isBlank(query.getId()) || query.getId().equals(e.getId()))
                .filter(
                        e ->
                                StringUtils.isBlank(query.getCreatedBy())
                                        || query.getCreatedBy().equals(e.getCreatedBy()))
                .filter(
                        e ->
                                StringUtils.isBlank(query.getEntityId())
                                        || query.getEntityId().equals(e.getEntityId()))
                .filter(
                        e ->
                                Objects.isNull(query.getPilot())
                                        || equal(query.getPilot(), e.getPilot()))
                .filter(
                        e ->
                                StringUtils.isBlank(query.getEntityType())
                                        || query.getEntityType().equals(e.getEntityType()))
                .filter(
                        e ->
                                StringUtils.isBlank(query.getAction())
                                        || query.getAction().equals(e.getAction()))
                .filter(
                        e ->
                                StringUtils.isBlank(query.getSource())
                                        || Optional.ofNullable(e.getSource())
                                                .map(s -> s.matches(query.getSource()))
                                                .orElse(false))
                .filter(
                        e ->
                                StringUtils.isBlank(query.getValue())
                                        || Optional.ofNullable(e.getValue())
                                                .map(s -> s.matches(query.getValue()))
                                                .orElse(false))
                .filter(
                        e ->
                                StringUtils.isBlank(query.getFieldName())
                                        || Optional.ofNullable(e.getFiledName())
                                                .map(s -> s.equals(query.getFieldName()))
                                                .orElse(false))
                .filter(
                        e ->
                                Objects.isNull(query.getCreatedAtStart())
                                        || !e.getCreatedAt().isBefore(query.getCreatedAtStart()))
                .filter(
                        e ->
                                Objects.isNull(query.getCreatedAtEnd())
                                        || !e.getCreatedAt().isAfter(query.getCreatedAtEnd()))
                .filter(
                        e ->
                                StringUtils.isBlank(query.getPlatform())
                                        || e.getVisibility().contains(query.getPlatform()))
                .collect(Collectors.toList());
    }

    @Override
    public void addVisibility(String activityId, String platform) {
        Activity activity = getActivity(activityId);
        activity = Activity.of(activity.toMessage().toBuilder().addVisibility(platform).build());
        activityMap.put(activityId, activity);
    }

    @Override
    public void removeVisibility(String activityId, String platform) {
        Activity activity = getActivity(activityId);
        var visibility = activity.getVisibility();
        visibility.remove(platform);
        activity =
                Activity.of(
                        activity.toMessage().toBuilder()
                                .clearVisibility()
                                .addAllVisibility(visibility)
                                .build());
        activityMap.put(activityId, activity);
    }

    private boolean equal(Pilot query, Pilot actual) {
        User pilot = query.getPilotUser();
        User om = query.getOperationsManager();
        if (Objects.isNull(actual)) {
            return false;
        }
        var actualPilotId =
                Optional.ofNullable(actual.getPilotUser()).map(User::getId).orElse(null);
        var actualOmId =
                Optional.ofNullable(actual.getOperationsManager()).map(User::getId).orElse(null);
        return (pilot == null || Objects.equals(pilot.getId(), actualPilotId))
                && (om == null || Objects.equals(om.getId(), actualOmId));
    }

    @Override
    public String updateComment(Comment comment) {
        String commentId = comment.getId();
        activityMap.values().stream()
                .filter(activity -> activity.getEntityId().equals(comment.getId()))
                .findAny()
                .map(Activity::getId)
                .ifPresent(activityMap::remove);
        String activityId = generateRandomID();
        Comment commentEntity = comment.withId(commentId);
        commentMap.put(commentEntity.getId(), commentEntity);
        // generate a activity which is comment activity
        Activity activity = Activity.fromComment(activityId, commentEntity);
        activityMap.put(activityId, activity);
        return commentId;
    }

    @Override
    public String addComment(Comment comment) {
        String commentId = generateRandomID();
        String activityId = generateRandomID();
        Comment commentEntity = comment.withId(commentId);
        // generate a activity which is comment activity
        Activity activity = Activity.fromComment(activityId, commentEntity);
        commentMap.put(commentEntity.getId(), commentEntity);
        activityMap.put(activityId, activity);
        return commentId;
    }

    @Override
    public void deleteById(String id) {
        commentMap.remove(id);
        // 删除comment
        activityMap.values().stream()
                .filter(activity -> Objects.equals(id, activity.getEntityId()))
                .findAny()
                .ifPresent(a -> activityMap.remove(a.getId()));
        activityMap.remove(id);
    }

    @Override
    public Comment findById(String commentId) {
        return commentMap.get(commentId);
    }

    @Override
    public List<? extends Comment> getComments(CommentQuery query) {
        return commentMap.values().stream()
                .filter(
                        e ->
                                Objects.isNull(query.getProjectId())
                                        || Objects.equals(query.getProjectId(), e.getProjectId()))
                .filter(e -> StringUtils.isBlank(query.getId()) || query.getId().equals(e.getId()))
                .filter(
                        e ->
                                StringUtils.isBlank(query.getCreatedBy())
                                        || query.getCreatedBy().equals(e.getCreatedBy()))
                .filter(
                        e ->
                                StringUtils.isBlank(query.getSource())
                                        || Optional.ofNullable(e.getSource())
                                                .map(s -> s.matches(query.getSource()))
                                                .orElse(false))
                .collect(Collectors.toList());
    }
}
