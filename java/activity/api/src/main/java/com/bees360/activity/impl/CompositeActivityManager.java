package com.bees360.activity.impl;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivitySyncLog;
import com.bees360.activity.ActivitySyncManager;
import com.google.common.base.Preconditions;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.util.Map;

@Log4j2
public class CompositeActivityManager extends ForwardingActivityManager {

    private final ActivityManager activityManager;
    private final Map<String, ActivitySyncManager> syncActions;

    /**
     * @param activityManager 代理ActivityManager
     * @param syncActions 用于提供 {@code ActivityManager#sync} 方法的操作
     */
    public CompositeActivityManager(
            @NonNull ActivityManager activityManager,
            @NonNull Map<String, ActivitySyncManager> syncActions) {
        this.activityManager = activityManager;
        this.syncActions = syncActions;
        log.info(
                "{} created:{activityManager={},syncActions={}}",
                this,
                activityManager,
                syncActions);
    }

    public void addExecutor(@NonNull String syncTo, @NonNull ActivitySyncManager executor) {
        syncActions.put(syncTo, executor);
        log.info("{} add:{syncTo={},executor={}}", this, syncTo, executor);
    }

    @Override
    protected ActivityManager delegate() {
        return activityManager;
    }

    @Override
    public ActivitySyncLog sync(String activityId, String syncTo, String createdByUserId) {
        Preconditions.checkArgument(syncTo != null);
        var executor = syncActions.get(syncTo);
        if (executor == null) {
            String message = String.format("The syncTo %s isn't supported.", syncTo);
            throw new IllegalArgumentException(message);
        }

        var activity = getActivity(activityId);
        Preconditions.checkArgument(activity != null);
        return executor.getOrCreate(activity, syncTo, createdByUserId);
    }
}
