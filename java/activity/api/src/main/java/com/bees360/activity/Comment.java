package com.bees360.activity;

import com.bees360.activity.Message.CommentMessage;
import com.bees360.api.Entity;
import com.bees360.api.Proto;
import com.bees360.resource.Message.MetadataMessage;
import com.bees360.resource.ResourceMetadata;
import com.bees360.user.Message.UserMessage;
import com.bees360.user.User;
import com.bees360.util.DateTimes;
import com.bees360.util.Defaults;
import com.bees360.util.Iterables;
import com.bees360.util.Messages;
import com.google.protobuf.ByteString;
import com.google.protobuf.Timestamp;

import java.time.Instant;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

public interface Comment extends Proto<CommentMessage>, Entity {

    @Override
    default String getNamespace() {
        return "project/comment";
    }

    /**
     * 获取评论的主键ID
     *
     * @return 主键ID
     */
    String getId();

    /**
     * 评论所在的项目ID
     *
     * @return 项目ID
     */
    long getProjectId();

    /**
     * 评论的内容
     *
     * @return 评论的内容
     */
    String getContent();

    /**
     * 评论可以回复另外一条评论，这里返回的是被回复的评论主键ID
     *
     * @return 被回复的评论的主键ID
     */
    String getReplyTo();

    /**
     * 获取评论创建的时间
     *
     * @return 评论的创建时间
     */
    Instant getCreatedAt();

    /**
     * 获取评论的更新时间
     *
     * @return 评论的更新时间
     */
    Instant getUpdatedAt();

    /**
     * 获取评论的创建人的用户ID
     *
     * @return 评论创建人的用户ID
     */
    String getCreatedBy();

    default User getCreatedByUser() {
        return User.from(UserMessage.newBuilder().setId(getCreatedBy()).build());
    }

    /**
     * 获取获取评论来源
     *
     * <p>评论可能是在Web端产生，或者AI等网站上。 通过评论来源来决定是否在其它网站上显示。
     *
     * @return 评论来源
     */
    String getSource();

    String getContentType();

    Iterable<? extends Attachment> getAttachment();

    String getRichText();

    String getTopic();

    default ByteString toByteString() {
        return toMessage().toByteString();
    }

    default Comment withId(String id) {
        return from(toMessage().toBuilder().setId(id).build());
    }

    default Comment withCreatedBy(String id) {
        Message.CommentMessage message = toMessage();
        UserMessage userMessage = message.getCreatedBy();
        return from(
                message.toBuilder()
                        .setCreatedBy(userMessage.toBuilder().setId(id).build())
                        .build());
    }

    @Override
    default CommentMessage toMessage() {
        CommentMessage.Builder builder = CommentMessage.newBuilder().setProjectId(getProjectId());
        Optional.ofNullable(getId()).ifPresent(builder::setId);
        Optional.ofNullable(getId()).ifPresent(builder::setId);
        Optional.ofNullable(getContent()).ifPresent(builder::setContent);
        Optional.ofNullable(getReplyTo()).ifPresent(builder::setReplyTo);
        Optional.ofNullable(getCreatedBy())
                .ifPresent(
                        createdBy ->
                                builder.setCreatedBy(
                                        UserMessage.newBuilder().setId(getCreatedBy()).build()));
        Optional.ofNullable(getCreatedByUser())
                .ifPresent(createdBy -> builder.setCreatedBy(createdBy.toMessage()));

        Optional.ofNullable(getCreatedAt())
                .ifPresent(
                        instant ->
                                builder.setCreatedAt(
                                        Timestamp.newBuilder()
                                                .setNanos(instant.getNano())
                                                .setSeconds(instant.getEpochSecond())
                                                .build()));
        Optional.ofNullable(getUpdatedAt())
                .ifPresent(
                        instant ->
                                builder.setUpdatedAt(
                                        Timestamp.newBuilder()
                                                .setNanos(instant.getNano())
                                                .setSeconds(instant.getEpochSecond())
                                                .build()));
        Optional.ofNullable(getSource()).ifPresent(builder::setSource);
        Optional.ofNullable(getContentType()).ifPresent(builder::setContentType);
        Optional.ofNullable(getAttachment())
                .ifPresent(
                        attachments ->
                                builder.addAllAttachment(
                                        Iterables.transform(attachments, Attachment::toMessage)));
        Optional.ofNullable(getRichText()).ifPresent(builder::setRichText);
        Optional.ofNullable(getTopic()).ifPresent(builder::setTopic);

        return builder.build();
    }

    static Comment from(CommentMessage message) {
        if (CommentMessage.getDefaultInstance().equals(message)) {
            return null;
        }

        return new Comment() {
            @Override
            public String getId() {
                return message.getId();
            }

            @Override
            public long getProjectId() {
                return message.getProjectId();
            }

            @Override
            public String getContent() {
                return message.getContent();
            }

            @Override
            public String getReplyTo() {
                return message.getReplyTo();
            }

            @Override
            public String getCreatedBy() {
                return message.getCreatedBy().getId();
            }

            @Override
            public User getCreatedByUser() {
                return User.from(message.getCreatedBy());
            }

            @Override
            public Instant getCreatedAt() {
                return Instant.ofEpochSecond(
                        message.getCreatedAt().getSeconds(), message.getCreatedAt().getNanos());
            }

            @Override
            public Instant getUpdatedAt() {
                return Instant.ofEpochSecond(
                        message.getUpdatedAt().getSeconds(), message.getUpdatedAt().getNanos());
            }

            @Override
            public CommentMessage toMessage() {
                return message;
            }

            @Override
            public String getSource() {
                return message.getSource();
            }

            @Override
            public String getContentType() {
                String contentType = message.getContentType();
                return contentType.isEmpty() ? "text/plain" : contentType;
            }

            @Override
            public Iterable<? extends Attachment> getAttachment() {
                return message.getAttachmentList().stream()
                        .map(Attachment::from)
                        .collect(Collectors.toList());
            }

            @Override
            public String getRichText() {
                return Defaults.nullIfEmpty(message.getRichText());
            }

            @Override
            public String getTopic() {
                return Defaults.nullIfEmpty(message.getTopic());
            }
        };
    }

    static Comment from(final long projectId, final String createdBy, final String content) {
        return from(
                projectId,
                createdBy,
                content,
                DateTimes.toTimestamp(Instant.now()),
                DateTimes.toTimestamp(Instant.now()));
    }

    static Comment from(
            final long projectId,
            final String createdBy,
            final String content,
            Timestamp createdAt,
            Timestamp updateAt) {
        return from(
                CommentMessage.newBuilder()
                        .setProjectId(projectId)
                        .setCreatedBy(UserMessage.newBuilder().setId(createdBy).build())
                        .setContent(content)
                        .setCreatedAt(createdAt)
                        .setUpdatedAt(updateAt)
                        .build());
    }

    static Comment from(
            final long projectId,
            final User createdBy,
            final String content,
            Timestamp createdAt,
            Timestamp updateAt) {
        return from(
                CommentMessage.newBuilder()
                        .setProjectId(projectId)
                        .setCreatedBy(createdBy.toMessage())
                        .setContent(content)
                        .setCreatedAt(createdAt)
                        .setUpdatedAt(updateAt)
                        .build());
    }

    interface Attachment extends Proto<CommentMessage.Attachment> {
        @Nullable
        ResourceMetadata getMetadata();

        String getUrl();

        String getFilename();

        default Message.CommentMessage.Attachment toMessage() {
            var builder = Message.CommentMessage.Attachment.newBuilder();
            Optional.ofNullable(getMetadata())
                    .ifPresent(metadata -> builder.setMetadata(metadata.toMessage()));
            Optional.ofNullable(getFilename()).ifPresent(builder::setFilename);
            Optional.ofNullable(getUrl()).ifPresent(builder::setUrl);
            return builder.build();
        }

        @Nullable
        static ResourceMetadata getMetadata(MetadataMessage metadata) {
            if (MetadataMessage.getDefaultInstance().equals(metadata)) {
                return null;
            }
            return ResourceMetadata.from(metadata);
        }

        @Nullable
        static Attachment from(CommentMessage.Attachment message) {
            if (CommentMessage.Attachment.getDefaultInstance().equals(message)) {
                return null;
            }

            return new Attachment() {
                @Override
                public ResourceMetadata getMetadata() {
                    return Attachment.getMetadata(message.getMetadata());
                }

                @Override
                public String getUrl() {
                    return message.getUrl();
                }

                @Override
                public String getFilename() {
                    return message.getFilename();
                }

                @Override
                public String toString() {
                    return Messages.toShortDebugString(message);
                }
            };
        }
    }
}
