package com.bees360.activity.impl;

import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.activity.CommentQuery;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;

import jakarta.annotation.Nullable;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public class UserFillCommentManager extends ForwardingCommentManager {
    private final CommentManager commentManager;
    private final UserProvider userProvider;
    private final String defaultSource;

    public UserFillCommentManager(
            CommentManager commentManager,
            UserProvider userProvider,
            @Nullable String defaultSource) {
        this.commentManager = commentManager;
        this.userProvider = userProvider;
        this.defaultSource = defaultSource;
    }

    public UserFillCommentManager(CommentManager commentManager, UserProvider userProvider) {
        this(commentManager, userProvider, null);
    }

    @Override
    public Comment findById(String id) {
        return fillUser(super.findById(id));
    }

    @Override
    public List<? extends Comment> getComments(CommentQuery commentQuery) {
        List<? extends Comment> comments = super.getComments(commentQuery);
        Set<String> userIdSet =
                comments.stream()
                        .map(Comment::getCreatedBy)
                        .collect(Collectors.toUnmodifiableSet());

        Map<String, User> userMap =
                Iterables.toStream(userProvider.findUserById(userIdSet))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(User::getId, user -> user));

        return comments.stream()
                .map(comment -> fillUser(comment, userMap))
                .collect(Collectors.toList());
    }

    @Override
    protected CommentManager delegate() {
        return commentManager;
    }

    private Comment fillUser(Comment comment, Map<String, User> userMap) {
        if (comment == null || StringUtils.isBlank(comment.getCreatedBy())) {
            return comment;
        }

        // fill comment user
        com.bees360.activity.Message.CommentMessage.Builder commentMessage =
                comment.toMessage().toBuilder();
        Optional.ofNullable(userMap.get(comment.getCreatedBy()))
                .ifPresent(user -> commentMessage.setCreatedBy(user.toMessage()));
        return Comment.from(commentMessage.build());
    }

    private Comment fillUser(Comment comment) {
        if (comment == null || StringUtils.isBlank(comment.getCreatedBy())) {
            return comment;
        }

        // fill comment user
        com.bees360.activity.Message.CommentMessage.Builder commentMessage =
                comment.toMessage().toBuilder();
        Optional.ofNullable(userProvider.findUserById(comment.getCreatedBy()))
                .ifPresent(user -> commentMessage.setCreatedBy(user.toMessage()));
        return Comment.from(commentMessage.build());
    }

    @Override
    public String addComment(Comment comment) {
        // 默认后端生成的comment的来源为WEB, 如果是前端创建的activity应该主动设置该值，否则该值会被自动设置为WEB
        if (StringUtils.isBlank(comment.getSource()) && defaultSource != null) {
            comment =
                    Comment.from(comment.toMessage().toBuilder().setSource(defaultSource).build());
        }
        return super.addComment(comment);
    }
}
