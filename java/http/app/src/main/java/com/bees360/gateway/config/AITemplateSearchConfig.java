package com.bees360.gateway.config;

import com.bees360.GeneralEntity;
import com.bees360.es.ESApi;
import com.bees360.es.config.ESConfig;
import com.bees360.gateway.controller.template.PilotFeedbackTemplateSearchEndpoint;
import com.bees360.gateway.controller.template.PrefabTemplateSearchEndpoint;
import com.bees360.gateway.controller.template.ReportTemplateSearchEndpoint;
import com.bees360.gateway.controller.template.util.TemplateESRepository;
import com.bees360.repository.Repository;
import com.bees360.repository.SearchProvider;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@Import({
    ESConfig.class,
    ReportTemplateSearchEndpoint.class,
    PrefabTemplateSearchEndpoint.class,
    PilotFeedbackTemplateSearchEndpoint.class
})
public class AITemplateSearchConfig {

    @Bean
    public Repository<GeneralEntity> reportTemplateRepository(ESApi esApi) {
        return new TemplateESRepository(esApi, "report_template");
    }

    @Bean
    public Repository<GeneralEntity> prefabTemplateRepository(ESApi esApi) {
        return new TemplateESRepository(esApi, "prefab_template");
    }

    @Bean
    public SearchProvider pilotFeedbackTemplateSearchProvider(ESApi esApi) {
        return new TemplateESRepository(esApi, "pilot_feedback_template");
    }
}
