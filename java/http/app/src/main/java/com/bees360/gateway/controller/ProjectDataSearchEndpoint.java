package com.bees360.gateway.controller;

import com.bees360.gateway.ProjectIdSimilarProjectDataSearchProvider;
import com.bees360.repository.SearchProvider;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** ProjectDataSearchEndpoint */
@RestController
@RequestMapping("project")
public class ProjectDataSearchEndpoint extends AbstractSearchEndpoint {
    private final ProjectIdSimilarProjectDataSearchProvider
            projectIdSimilarProjectDataSearchProvider;

    public ProjectDataSearchEndpoint(
            SearchProvider projectDataSearchProvider,
            boolean withCustomizedAuth,
            ProjectIdSimilarProjectDataSearchProvider projectIdSimilarProjectDataSearchProvider) {
        super(projectDataSearchProvider, withCustomizedAuth);
        this.projectIdSimilarProjectDataSearchProvider = projectIdSimilarProjectDataSearchProvider;
    }

    /**
     * Searches for similar project data based on the provided project ID.
     *
     * @param projectId The ID of the project to search for similar data.
     * @return An object containing the search results.
     */
    @Operation(
            summary = "Search for similar project data",
            description =
                    "This endpoint searches for similar project data based on the provided project"
                            + " ID.")
    @GetMapping("/{projectId}/similar/search")
    public Object search(
            @Parameter(
                            name = "projectId",
                            description = "The ID of the project to search for similar data.",
                            required = true)
                    @PathVariable("projectId")
                    String projectId) {
        return projectIdSimilarProjectDataSearchProvider.searchSimilarProjectData(projectId);
    }
}
