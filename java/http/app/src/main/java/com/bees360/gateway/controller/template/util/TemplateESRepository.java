package com.bees360.gateway.controller.template.util;

import com.bees360.GeneralEntity;
import com.bees360.es.ESApi;
import com.bees360.es.ESCodecs.GsonEntityESCodec;
import com.bees360.es.ESRepository;
import com.google.gson.JsonObject;

import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RequestOptions;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.search.builder.SearchSourceBuilder;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

public class TemplateESRepository extends ESRepository<GeneralEntity> {

    private static final int MAX_HITS_SIZE = 1000;

    public TemplateESRepository(ESApi esApi, String namespace, String indexName) {
        super(esApi, GsonEntityESCodec.getInstance(GeneralEntity.class), namespace, indexName);
    }

    public TemplateESRepository(ESApi esApi, String namespace) {
        super(esApi, GsonEntityESCodec.getInstance(GeneralEntity.class), namespace);
    }

    @Override
    public Iterable<GeneralEntity> loadAll() {
        var request = new SearchRequest(namespace);
        var searchSource =
                new SearchSourceBuilder()
                        .query(QueryBuilders.termQuery("deleted", false))
                        .size(MAX_HITS_SIZE);
        request.source(searchSource);

        var response = esApi.apply(client -> client.search(request, RequestOptions.DEFAULT));
        return convertToESEntities(response);
    }

    private Iterable<GeneralEntity> convertToESEntities(SearchResponse response) {
        if (response.getHits() == null) {
            return Collections.emptyList();
        }
        return Arrays.stream(response.getHits().getHits())
                .map(hit -> decode(hit.getId(), hit.getSourceAsMap()))
                .collect(Collectors.toList());
    }

    @Override
    protected Map encode(GeneralEntity entity) {
        var body = gson.toJsonTree(entity.getContent());
        var object = body.getAsJsonObject();
        object.addProperty("id", entity.getId());
        return gson.fromJson(object, Map.class);
    }

    @Override
    protected GeneralEntity decode(String id, Map source) {
        var content = gson.toJsonTree(source);
        var object = new JsonObject();
        object.addProperty("id", id);
        object.add("content", content);
        return gson.fromJson(object, GeneralEntity.class);
    }
}
