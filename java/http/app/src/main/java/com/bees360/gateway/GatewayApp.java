package com.bees360.gateway;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.config.ActuatorSecurityConfig;
import com.bees360.gateway.config.AITemplateSearchConfig;
import com.bees360.gateway.config.ActivityConfig;
import com.bees360.gateway.config.AddressConfig;
import com.bees360.gateway.config.ApiKeyConfig;
import com.bees360.gateway.config.AttachmentConfig;
import com.bees360.gateway.config.ContractConfig;
import com.bees360.gateway.config.CustomerConfig;
import com.bees360.gateway.config.EstintelAirflowEndpointConfig;
import com.bees360.gateway.config.EstintelEndpointConfig;
import com.bees360.gateway.config.HttpSecurityConfig;
import com.bees360.gateway.config.ImageAnnotationConfig;
import com.bees360.gateway.config.ImageConfig;
import com.bees360.gateway.config.ImageTagConfig;
import com.bees360.gateway.config.ImageTagDictConfig;
import com.bees360.gateway.config.JettyExceptionHandler;
import com.bees360.gateway.config.MessageEndpointConfig;
import com.bees360.gateway.config.OpenApiConfig;
import com.bees360.gateway.config.PipelineConfig;
import com.bees360.gateway.config.ProjectConfig;
import com.bees360.gateway.config.ProjectHoverConfig;
import com.bees360.gateway.config.ProjectOptionsConfig;
import com.bees360.gateway.config.ReportConfig;
import com.bees360.gateway.config.RepositoryConfig;
import com.bees360.gateway.config.ResourceConfig;
import com.bees360.gateway.config.ScheduledJobConfig;
import com.bees360.gateway.config.SsoConfig;
import com.bees360.gateway.config.ThirdPartyConfig;
import com.bees360.gateway.config.TodoConfig;
import com.bees360.gateway.config.UserConfig;
import com.bees360.gateway.controller.FrontendConfigEndpoint;
import com.bees360.gateway.controller.SearchOptionEndpoint;
import com.bees360.grpc.config.GrpcClientConfig;
import com.bees360.http.config.CorsConfig;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import lombok.extern.log4j.Log4j2;

import org.springframework.context.annotation.Import;

@Log4j2
@Import({
    CorsConfig.class,
    HttpSecurityConfig.class,
    JettyExceptionHandler.class,
    RepositoryConfig.class,
    SsoConfig.class,
    UserConfig.class,
    TodoConfig.class,
    AddressConfig.class,
//    ResourceConfig.class,
    SearchOptionEndpoint.class,
    PipelineConfig.class,
    ActivityConfig.class,
    ApiKeyConfig.class,
    ProjectConfig.class,
    ImageConfig.class,
    ImageTagConfig.class,
    ImageAnnotationConfig.class,
    ImageTagDictConfig.class,
    CustomerConfig.class,
    ContractConfig.class,
    AITemplateSearchConfig.class,
    AttachmentConfig.class,
    ProjectHoverConfig.class,
    FrontendConfigEndpoint.class,
    ProjectOptionsConfig.class,
    ActuatorSecurityConfig.class,
    ScheduledJobConfig.class,
    ReportConfig.class,
    ThirdPartyConfig.class,
    GrpcClientConfig.class,
    MessageEndpointConfig.class,

    // estintel
    EstintelEndpointConfig.class,
    EstintelAirflowEndpointConfig.class,

    // openapi
    OpenApiConfig.class,
})
@ApplicationAutoConfig
@EnableEncryptableProperties
public class GatewayApp {

    public static void main(String[] args) {
        ExitableSpringApplication.run(GatewayApp.class, args);
    }
}
