log:
  level:
    org.springframework.security: debug

activity:
  config:
    default-source: WEB

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver
  jooq:
    sql-dialect: POSTGRES

rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password

es:
  host: elasticsearch
  port: 9200

http:
  image-api:
    endpoint: /v2/image
  user:
    endpoint: /user
  resource:
    endpoint: /resource
  todo:
    endpoint: /todo
  project:
    endpoint: /project
  pipeline-def:
    endpoint: /pipeline-def
  projectInvoice:
    endpoint: /project/invoice
  contract:
    endpoint: /contract
  client:
    apache:
      maxConnPerRoute: 16
      maxConnTotal: 64
      evictIdleConnectionsAfter: PT10M
      connectionTimeToLive: PT10M
      evictExpiredConnections: true
      redirectStrategy: always
      request:
        connectTimeout: PT15S
        connectionRequestTimeout: PT30S
        socketTimeout: PT15S
        redirectEnabled: true
        expectContinueEnabled: true
        normalizeUri: true
  cors:
    '[*]':
      allowed_origins: "*"
      allowed_headers: "*"
      allowed_methods: GET,HEAD,PUT,OPTIONS
      allow_credentials: true
      exposed_headers: Authorization,Access-Control-Allow-Origin,Access-Control-Allow-Credentials,X-Protobuf-Message
      max_age: 86400
  request:
    matchers:
      - method: "*"
        path: "/project/{projectId:\\d+}/**"
        accessRule: "@PAS.isProjectAccessible(#projectId)"
      - method: PUT
        path: /template/**
        accessRule: "hasAnyRole('ROLE_ADMIN')"
      - method: "*"
        path: "/project/statistic/**"
        accessRule: "hasAnyRole('ROLE_ADMIN')"
    access-security:
      companyStrategy:
        realms9: 2770
        forbiddenCompanyList: 2223

grpc:
  server:
    port: ${GRPC_SERVER_PORT:9811}
  client:

    activityManager:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    commentManager:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    customerManager:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    projectManager:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectStatisticService:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    userProvider:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    resourcePool:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    imageManager:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    projectContactManager:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    contractManager:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    userKeyProvider:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    attachmentManager:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    thirdPartyJobStatusProvider:
      address: 'static://localhost:${GRPC_SERVER_PORT:9811}'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    apiKeyManager:
      address: static://localhost:${GRPC_SERVER_PORT:9811}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
# This public key is for test only
auth:
  jwt:
    keyId: f45a691453a4bc0388931b029cfb95466f7b4dd8
    signingKey: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    verifierKey: |
      -----BEGIN CERTIFICATE-----
      MIIC/DCCAeSgAwIBAgIIOcybo4ESpmMwDQYJKoZIhvcNAQEFBQAwIDEeMBwGA1UE
      AxMVMTAzNzQxMjAwMzE3OTIwODg2ODQ1MCAXDTIxMDcyNzAzNDgxM1oYDzk5OTkx
      MjMxMjM1OTU5WjAgMR4wHAYDVQQDExUxMDM3NDEyMDAzMTc5MjA4ODY4NDUwggEi
      MA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDnpfrDqFClLoVqAg9GcM0sEmIR
      i7BP+gC8KautIhPyrScuujFHZf8MHP1AezjVd2gcWCi1pGTTNUbeZH2xet5qqP/X
      Gc2OsLFh9vAGOXQz1WtJWENv4ncazZu8uBeurVyJrNIaY0G3v3JYAUHKi/Wu0GCy
      t0bSwVYVlzH8IOwlZezuUmsoWMX56eI0CFrfA2WuGwvMJQzB7daN6XB68hGt+WFE
      18xXeJCKmyjhaFoY/KpWO1Z0g8/gLYLkPrC/oa6tNigENaK7aQmaPCJ3phwiDz2a
      WPNJ8evXMGVFIE5qUHG3aNEpA2Rru4pFA9+g3ZfjIu8VnQh1kLmL5dtw0x4pAgMB
      AAGjODA2MAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgeAMBYGA1UdJQEB/wQM
      MAoGCCsGAQUFBwMCMA0GCSqGSIb3DQEBBQUAA4IBAQCTlQLQOUEr5PHvG1Y9z7Hc
      +6MXFb7WxiRM7IcSEPA0BwkZM94BF5fojlHZ2ox335669wrGqGpkNLBqIMLuxw1b
      4N5wxeeS2M8fZeUjM7kaHT8ZE+27FzOY1NNbFn+dyUqonGM/1GlGL3hgOV4hkyzk
      3vderqQakqR7bYyUmjCgBcAvQahOpkJxX4/XgqzaDQBDGmhG1VOvyTJEyZtBX1GN
      nmsz9CzqClBgydf6bnjgm4Gh4CHI4YKL/7yGXAkk50JqYbhkAmlNcIbILryilTRu
      MZGX7oskns0vBqMtLmIPG5eH/KL3KqUS1KTxW33Ucca+WnmKQjNys2QKBVLR1Y+F
      -----END CERTIFICATE-----

  clients:
    - client_id: test_client_id
      client_secret: test_client_secret
      authorized_grant_types: password,refresh_token
      scope: bees360.ai
      access_token_validity_seconds: 3600
      refresh_token_validity_seconds: 604800

  saml:
    acs-endpoint: "http://localhost/saml2/sso"
    service-provider-id-base: "http://localhost/saml2/service-provider"
  oauth:
    redirect-endpoint: "http://localhost/oauth2/callback"

s3:
  clients:
    - endpoint: http://s3-primary
      key: local-identity
      secret: local-credential
      pathStyleAccess: true
      checksumValidationEnabled: false
    - endpoint: http://s3-secondary
      key: local-identity
      secret: local-credential
      pathStyleAccess: true
      checksumValidationEnabled: false

resource:
  s3:
    - client: s3-primary
      bucket: test-bucket
      url-expiration: PT1h
      url-stable-duration: PT15m
    - client: s3-secondary
      bucket: test-bucket
      url-expiration: PT1h
      url-stable-duration: PT15m
server:
  port: 0
