spring:
  profiles:
    active: ${ENV}
    include: actuator

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    password: ${BEES360_SECRET_KEY}

http:
  client:
    apache:
      maxConnPerRoute: 16
      maxConnTotal: 64
      evictIdleConnectionsAfter: PT10M
      connectionTimeToLive: PT10M
      evictExpiredConnections: true
      redirectStrategy: always
      request:
        connectTimeout: PT15S
        connectionRequestTimeout: PT120S
        socketTimeout: PT180S
        redirectEnabled: true
        expectContinueEnabled: true
        normalizeUri: true

rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password

redis:
  client:
    host: redis
    port: 6379
    database: 4

grpc:
  client:
    resourcePool:
      address: static://${GRPC_SERVICE_NAME:bees360-resource-app-grpc}:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectIIManager:
      address: static://${GRPC_SERVICE_NAME:bees360-project-app-grpc}:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectManager:
      address: static://${GRPC_SERVICE_NAME:bees360web-grpc}:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectIntegration:
      address: static://${GRPC_SERVICE_NAME:bees360-project-app-grpc}:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    reportManager:
      address: static://${GRPC_SERVICE_NAME:bees360-report-app-grpc}:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    userProvider:
      address: static://${GRPC_SERVICE_NAME:bees360web-grpc}:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

xactanalysis:
  app:
    document-regather:
      sftp: xactanalysis-sftp
    lookup-report-in-documents:
      regather:
        folders: OUT/Custom_Doc, OUT/Estimate
      custom-doc:
        enabled: true
        report-select-by-filename:
          # General Loss Report
          34:
            - filename: "GENERAL_LOSS.PDF"
          # Estimate Report
          11:
            - filename: "FINAL DRAFT WITH AGE LIFE AND CONDITION Report"
      estimate:
        enabled: true
        report-select-by-att-file:
          34:
            - filename: "GENERAL_LOSS.PDF"
          11:
            - description: "FINAL DRAFT WITH AGE LIFE AND CONDITION Report"
              file-type: PDF
    sftp:
      xactanalysis-sftp-api: xactanalysis-sftp
      xactanalysis-resource-pool: "sftp://xactanalysis-sftp/"
    user:
      robot-email: <EMAIL>

resource:
  sftp:
    - client: xactanalysis-sftp
      base-folder: "/"
