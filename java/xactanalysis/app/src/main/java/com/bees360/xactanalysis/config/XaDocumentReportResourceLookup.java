package com.bees360.xactanalysis.config;

import com.bees360.resource.Resource;
import com.bees360.resource.ResourceRepository;
import com.bees360.util.Iterables;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/** 通过 transaction id 和 report type 查找 report type 对应 resource。 */
@Log4j2
public class XaDocumentReportResourceLookup
        implements BiFunction<String, Set<String>, Map<String, Resource>> {

    private final ResourceRepository xaResourceRepository;
    private final String baseFolder;
    // 输入 transaction id，重新整理该transaction的文件位置，并返回文件所属目录。
    private final UnaryOperator<String> xaTransactionFileRegather;

    /** 在 Resource 中查找指定 report type的文件，并返回查找到的 report type 及其 resource。 */
    private final BiFunction<Resource, Set<String>, Map<String, Resource>> reportResourceLookup;

    // filename such as 05VW3LT.1DN1C.4123356928.20240303204700.ZIP, 其中len(20240303204700.ZIP) = 18.
    private static final int LEN_FILENAME_TIMESTAMP_PART = 18;

    public XaDocumentReportResourceLookup(
            @NonNull ResourceRepository xaResourceRepository,
            String baseFolder,
            UnaryOperator<String> xaTransactionFileRegather,
            BiFunction<Resource, Set<String>, Map<String, Resource>> reportResourceLookup) {
        this.xaResourceRepository = xaResourceRepository;
        this.baseFolder = StringUtils.endsWith(baseFolder, "/") ? baseFolder : baseFolder + "/";
        this.xaTransactionFileRegather = xaTransactionFileRegather;
        this.reportResourceLookup = reportResourceLookup;

        log.info(
                "Created"
                    + " {}(xaResourceRepository={},baseFolder={},xaTransactionFileRegather={},reportResourceLookup={})",
                this,
                xaResourceRepository,
                baseFolder,
                xaTransactionFileRegather,
                reportResourceLookup);
    }

    @Override
    public Map<String, Resource> apply(String transactionId, Set<String> reportTypes) {
        var folderNameWithTransactionIdPrefix =
                xaTransactionFileRegather.apply(transactionId) + transactionId;
        var keys = xaResourceRepository.keySet(baseFolder + folderNameWithTransactionIdPrefix);
        var filenamesSorted =
                Iterables.toStream(keys)
                        .sorted(
                                (v1, v2) ->
                                        StringUtils.compare(
                                                StringUtils.substring(
                                                        v2, LEN_FILENAME_TIMESTAMP_PART),
                                                StringUtils.substring(
                                                        v1, LEN_FILENAME_TIMESTAMP_PART)))
                        .collect(Collectors.toList());
        Map<String, Resource> result = new HashMap<>();
        var reportTypesLeft = new HashSet<>(reportTypes);
        for (var zipFilename : filenamesSorted) {
            log.info("Try to find resource {} found in {}.", reportTypes, zipFilename);
            var zipResource = xaResourceRepository.get(zipFilename);
            // Note: If you intend to cache the file here and read from the cache, you may not get
            // the latest updated file.
            reportResourceLookup
                    .apply(zipResource, reportTypesLeft)
                    .forEach(
                            (key, value) -> {
                                reportTypesLeft.remove(key);
                                result.put(key, value);
                            });
            if (reportTypesLeft.isEmpty()) {
                break;
            }
        }
        return result;
    }
}
