package com.bees360.project.util;

import com.bees360.project.Message.TimelineNode;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.claim.ClaimTypeEnum;
import com.bees360.project.inspection.Inspection;
import com.bees360.project.inspection.Structure;
import com.google.common.collect.ForwardingObject;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;

import java.time.Instant;
import java.time.LocalDate;

public abstract class ForwardingProjectManager extends ForwardingObject
        implements ProjectIIManager {
    @Override
    public Iterable<? extends ProjectII> findByStatus(
            @Nullable String startId, @Nullable Integer status, int countLimit) {
        return delegate().findByStatus(startId, status, countLimit);
    }

    @Override
    protected abstract ProjectIIManager delegate();

    @Override
    public ProjectII create(
            @NotNull UnderwritingCreation underwriting,
            boolean allowDuplication,
            String creationChannel) {
        return delegate().create(underwriting, allowDuplication, creationChannel);
    }

    @Override
    public ProjectII create(
            @NotNull ClaimCreation claim, boolean allowDuplication, String creationChannel) {
        return delegate().create(claim, allowDuplication, creationChannel);
    }

    @Override
    public boolean updateClaimNo(String projectId, String claimNo) {
        return delegate().updateClaimNo(projectId, claimNo);
    }

    @Override
    public boolean updateClaimType(String projectId, ClaimTypeEnum claimType) {
        return delegate().updateClaimType(projectId, claimType);
    }

    @Override
    public boolean updateServiceType(String projectId, ServiceTypeEnum serviceType) {
        return delegate().updateServiceType(projectId, serviceType);
    }

    @Override
    public boolean updateDateOfLoss(String projectId, LocalDate dateOfLoss) {
        return delegate().updateDateOfLoss(projectId, dateOfLoss);
    }

    @Override
    public boolean updateEstimateTotalPay(String projectId, double estimateTotalPay) {
        return delegate().updateEstimateTotalPay(projectId, estimateTotalPay);
    }

    @Override
    public Inspection createInspection(Inspection inspection) {
        return delegate().createInspection(inspection);
    }

    @Override
    public boolean updateInspectionNo(String projectId, String inspectionNo) {
        return delegate().updateInspectionNo(projectId, inspectionNo);
    }

    @Override
    public boolean updateInspectionAppointmentTime(
            String projectId, Instant inspectionAppointmentTime) {
        return delegate().updateInspectionAppointmentTime(projectId, inspectionAppointmentTime);
    }

    @Override
    public boolean updateDueDate(String projectId, LocalDate date) {
        return delegate().updateDueDate(projectId, date);
    }

    @Override
    public boolean updateScheduledTime(String projectId, Instant time) {
        return delegate().updateScheduledTime(projectId, time);
    }

    @Override
    public boolean updateInspectionStartTime(String projectId, Instant inspectionStartTime) {
        return delegate().updateInspectionStartTime(projectId, inspectionStartTime);
    }

    @Override
    public boolean updateInspectionCompletedTime(
            String projectId, Instant inspectionCompletedTime) {
        return delegate().updateInspectionCompletedTime(projectId, inspectionCompletedTime);
    }

    @Override
    public boolean updateInteriorDamageStatus(String projectId, boolean hasInteriorDamage) {
        return delegate().updateInteriorDamageStatus(projectId, hasInteriorDamage);
    }

    @Override
    public boolean updateStructures(String projectId, Iterable<? extends Structure> structures) {
        return delegate().updateStructures(projectId, structures);
    }

    @Override
    public String namespace() {
        return delegate().namespace();
    }

    @Override
    public ProjectII findById(String id) {
        return delegate().findById(id);
    }

    @Override
    public Iterable<? extends ProjectII> findAllById(Iterable<String> ids) {
        return delegate().findAllById(ids);
    }

    @Override
    public Iterable<? extends ProjectII> loadAll() {
        return delegate().loadAll();
    }

    @Override
    public boolean existsById(String id) {
        return delegate().existsById(id);
    }

    @Override
    public String save(ProjectII entity) {
        return delegate().save(entity);
    }

    @Override
    public Iterable<String> saveAll(Iterable<? extends ProjectII> entities) {
        return delegate().saveAll(entities);
    }

    @Override
    public void deleteById(String id) {
        delegate().deleteById(id);
    }

    @Override
    public void deleteAllById(Iterable<String> ids) {
        delegate().deleteAllById(ids);
    }

    @Override
    public boolean updateNumberOfOutBuildings(String projectId, Integer additionalStructure) {
        return delegate().updateNumberOfOutBuildings(projectId, additionalStructure);
    }

    @Override
    public boolean updateNumberOfInteriorRooms(String projectId, Integer interiorRoom) {
        return delegate().updateNumberOfInteriorRooms(projectId, interiorRoom);
    }

    @Override
    public Iterable<? extends ProjectII> findByTimelineQuery(
            Iterable<TimelineNode> nodes, Instant startTime, Instant endTime) {
        return delegate().findByTimelineQuery(nodes, startTime, endTime);
    }

    @Override
    public boolean updateProjectOperatingCompany(String projectId, String operatingCompany) {
        return delegate().updateProjectOperatingCompany(projectId, operatingCompany);
    }

    @Override
    public boolean updateProjectPolicyType(String projectId, String policyType) {
        return delegate().updateProjectPolicyType(projectId, policyType);
    }

    @Override
    public boolean setCustomerDivision(String projectId, String divisionId) {
        return delegate().setCustomerDivision(projectId, divisionId);
    }

    @Override
    public boolean updateProjectPolicyRenewal(String projectId, Boolean isRenewal) {
        return delegate().updateProjectPolicyRenewal(projectId, isRenewal);
    }
}
