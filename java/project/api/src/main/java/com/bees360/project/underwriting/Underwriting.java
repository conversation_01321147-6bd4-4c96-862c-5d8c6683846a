package com.bees360.project.underwriting;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.Entity;
import com.bees360.api.Proto;
import com.bees360.project.Message;
import com.bees360.project.ServiceTypeEnum;

import jakarta.annotation.Nullable;

public interface Underwriting extends Entity, Proto<Message.ProjectMessage.Underwriting> {

    @Nullable
    String getProjectId();

    /** 订阅服务类型 */
    ServiceTypeEnum getServiceType();

    Iterable<String> getSupplementalService();

    @Override
    default String getNamespace() {
        return "project/underwriting";
    }

    class UnderwritingBuilder {
        @lombok.Builder(
                builderClassName = "Builder",
                builderMethodName = "newBuilder",
                setterPrefix = "set")
        static Underwriting of(
                String id,
                String projectId,
                ServiceTypeEnum serviceType,
                Iterable<String> supplementalService) {
            return Underwriting.of(id, projectId, serviceType, supplementalService);
        }
    }

    static Underwriting of(
            String id,
            String projectId,
            ServiceTypeEnum serviceType,
            Iterable<String> supplementalService) {
        return new Underwriting() {

            @Nullable
            @Override
            public String getId() {
                return id;
            }

            @Override
            public String getProjectId() {
                return projectId;
            }

            @Override
            public ServiceTypeEnum getServiceType() {
                return serviceType;
            }

            @Override
            public Iterable<String> getSupplementalService() {
                return supplementalService;
            }
        };
    }

    static Underwriting from(final Message.ProjectMessage.Underwriting message) {
        return new Underwriting() {

            @Nullable
            @Override
            public String getId() {
                return null;
            }

            @Override
            public String getProjectId() {
                return null;
            }

            @Override
            public ServiceTypeEnum getServiceType() {
                return ServiceTypeEnum.valueOf(message.getServiceTypeValue());
            }

            @Override
            public Iterable<String> getSupplementalService() {
                return message.getSupplementalServiceList();
            }

            @Override
            public Message.ProjectMessage.Underwriting toMessage() {
                return message;
            }
        };
    }

    @Override
    default Message.ProjectMessage.Underwriting toMessage() {
        Message.ProjectMessage.Underwriting.Builder builder =
                Message.ProjectMessage.Underwriting.newBuilder();
        acceptIfNotNull(
                builder::setServiceType,
                getServiceType(),
                e -> Message.ServiceType.forNumber(e.getCode()));
        acceptIfNotNull(builder::addAllSupplementalService, getSupplementalService());
        return builder.build();
    }
}
