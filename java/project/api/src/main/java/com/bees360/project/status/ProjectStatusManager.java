package com.bees360.project.status;

import com.bees360.project.Message;

import jakarta.annotation.Nonnull;

import java.time.Instant;

public interface ProjectStatusManager extends ProjectStatusProvider {

    default boolean updateStatus(
            @Nonnull String projectId,
            @Nonnull Message.ProjectStatus status,
            @Nonnull String updatedBy) {
        return updateStatus(projectId, status, updatedBy, null, null);
    }

    default boolean updateStatus(
            @Nonnull String projectId,
            @Nonnull Message.ProjectStatus status,
            @Nonnull String updatedBy,
            Instant updatedAt) {
        return updateStatus(projectId, status, updatedBy, null, updatedAt);
    }

    default boolean updateStatusWithComment(
            @Nonnull String projectId,
            @Nonnull Message.ProjectStatus status,
            @Nonnull String updateBy,
            String comment) {
        return updateStatus(projectId, status, updateBy, comment, null);
    }

    /**
     * 更新项目状态, 若待更新状态触发时间大于项目当前状态触发时间则更新项目状态, 否则只记录该项目状态. 更新项目到相同的状态或者记录一条已存在的状态将会更新失败.
     *
     * @param projectId 项目Id
     * @param status 状态
     * @param updatedBy 操作用户
     * @param comment 留言
     * @param updatedAt 状态触发时间,若为空值则默认用当前时间
     * @return true 更新成功；false 更新失败
     */
    boolean updateStatus(
            @Nonnull String projectId,
            @Nonnull Message.ProjectStatus status,
            @Nonnull String updatedBy,
            String comment,
            Instant updatedAt);

    default boolean rollbackStatus(
            @Nonnull String projectId,
            @Nonnull Message.ProjectStatus status,
            @Nonnull String updatedBy,
            long version) {
        return rollbackStatus(projectId, status, updatedBy, null, version);
    }

    /**
     * 将project的状态回滚到指定的状态; 要求该状态至少触发过一次,会自动回滚到最新的一次,否则回滚失败; version字段必须对应当前状态的version,否则回滚失败.
     *
     * @param projectId 项目id
     * @param status 待回滚状态
     * @param updatedBy 操作者id
     * @param version 当前状态的时间戳
     * @return true 回滚成功; false 回滚失败
     */
    boolean rollbackStatus(
            @Nonnull String projectId,
            @Nonnull Message.ProjectStatus status,
            @Nonnull String updatedBy,
            String comment,
            long version);
}
