package com.bees360.project.state;

import com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage.ProjectStateChangeReasonType;

public interface ProjectStateChangeReasonManager extends ProjectStateChangeReasonProvider {

    ProjectStateChangeReason createStateChangedReason(
            String displayText, ProjectStateChangeReasonType type, String createdBy);

    void deleteStateChangeReasonByDisplayText(String displayText, String updatedBy);

    Iterable<? extends ProjectStateChangeReason> listStateChangeReasonByType(
            ProjectStateChangeReasonType type);
}
