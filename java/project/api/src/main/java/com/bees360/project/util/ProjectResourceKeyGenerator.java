package com.bees360.project.util;

import com.bees360.util.SecureTokens;

public class ProjectResourceKeyGenerator {
    private ProjectResourceKeyGenerator() {}

    public static String buildKeyWithProjectId(String originKey, String projectId) {
        return String.join("/", "project", projectId, originKey);
    }

    /**
     * 通过 project id 生成 一个随机的 key, 如果会传入的project是合法的项目id，则返回的key的长度不超过60个字符。
     *
     * @param projectId 项目ID
     * @return 带有 project 信息的随机key
     */
    public static String generateRandomKeyWithProjectId(String projectId) {
        var key = SecureTokens.generateRandomHexToken(16);
        return buildKeyWithProjectId(key, projectId);
    }
}
