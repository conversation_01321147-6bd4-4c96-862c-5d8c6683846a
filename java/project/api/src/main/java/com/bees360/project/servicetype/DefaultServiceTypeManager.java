package com.bees360.project.servicetype;

import com.bees360.project.state.ProjectStateChangeReason;
import com.bees360.project.state.StateChangeReasonGroupManager;
import com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage;
import com.google.common.collect.Iterables;

import org.apache.commons.lang3.StringUtils;

public class DefaultServiceTypeManager implements ServiceTypeManager {
    private static final String SERVICE_TYPE_STATE_CHANGE_REASON_GROUP_TYPE = "SERVICE_TYPE";

    private final StateChangeReasonGroupManager stateChangeReasonGroupManager;

    public DefaultServiceTypeManager(StateChangeReasonGroupManager stateChangeReasonGroupManager) {
        this.stateChangeReasonGroupManager = stateChangeReasonGroupManager;
    }

    @Override
    public ProjectStateChangeReason createStateChangeReason(
            String serviceType,
            String displayText,
            ProjectStateChangeReasonMessage.ProjectStateChangeReasonType type,
            String createdBy) {
        return stateChangeReasonGroupManager.create(
                serviceType,
                SERVICE_TYPE_STATE_CHANGE_REASON_GROUP_TYPE,
                displayText,
                type,
                createdBy);
    }

    @Override
    public Iterable<? extends ProjectStateChangeReason> findStateChangeReason(
            String serviceType, ProjectStateChangeReasonMessage.ProjectStateChangeReasonType type) {
        if (StringUtils.isEmpty(serviceType)) {
            return stateChangeReasonGroupManager.findByGroupAndType("", "", type);
        }
        // 针对关联上 ServiceType 的 type 类型原因, 比如 close 类型的原因
        var withServiceTypeReasons =
                stateChangeReasonGroupManager.findByGroupAndType(
                        serviceType, SERVICE_TYPE_STATE_CHANGE_REASON_GROUP_TYPE, type);
        if (Iterables.isEmpty(withServiceTypeReasons)) {
            // 针对未关联上 ServiceType 的 type 类型原因
            return stateChangeReasonGroupManager.findByGroupAndType(
                    "", SERVICE_TYPE_STATE_CHANGE_REASON_GROUP_TYPE, type);
        }
        return withServiceTypeReasons;
    }
}
