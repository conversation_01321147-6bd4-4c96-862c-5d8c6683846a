package com.bees360.project.claim;

import com.bees360.catastrophe.ClaimCatastrophe;
import com.google.common.collect.ForwardingObject;

import jakarta.annotation.Nullable;

import java.util.Map;

public abstract class ForwardingProjectCatastropheManager extends ForwardingObject
        implements ProjectCatastropheManager {

    protected final ProjectCatastropheManager projectCatastropheManager;

    protected ForwardingProjectCatastropheManager(
            ProjectCatastropheManager projectCatastropheManager) {
        this.projectCatastropheManager = projectCatastropheManager;
    }

    @Override
    public void addProjectCatastrophe(
            String projectId,
            String catSerialNumber,
            @Nullable Integer catLevel,
            @Nullable String customerId,
            @Nullable String addressState) {
        delegate()
                .addProjectCatastrophe(
                        projectId, catSerialNumber, catLevel, customerId, addressState);
    }

    @Override
    public boolean deleteByProjectId(String projectId) {
        return delegate().deleteByProjectId(projectId);
    }

    @Override
    public Iterable<? extends String> findProjectIdBySerialNum(String catSerialNumber) {
        return delegate().findProjectIdBySerialNum(catSerialNumber);
    }

    @Override
    public Map<String, ClaimCatastrophe> findByProjectIds(Iterable<String> projectIds) {
        return delegate().findByProjectIds(projectIds);
    }

    @Override
    public void setLevel(String catSerialNumber, Integer level) {
        delegate().setLevel(catSerialNumber, level);
    }

    @Override
    protected ProjectCatastropheManager delegate() {
        return projectCatastropheManager;
    }
}
