package com.bees360.project.report;

import com.bees360.report.Message.ReportMessage.Status;
import com.bees360.report.Report;

import jakarta.annotation.Nullable;

/**
 * Interface for providing project reports. This interface defines methods to find and retrieve
 * project reports based on different criteria.
 */
public interface ProjectReportProvider {

    /**
     * Find reports by project ID and report type
     *
     * @param projectId The ID of the project
     * @param reportType The type of report to find
     * @return An iterable of reports matching the criteria
     */
    default Iterable<? extends Report> find(String projectId, @Nullable String reportType) {
        return find(projectId, reportType, null);
    }

    /**
     * Find reports by project ID, report type and status
     *
     * @param projectId The ID of the project
     * @param reportType The type of report to find
     * @param status The status of reports to find
     * @return An iterable of reports matching all criteria
     */
    Iterable<? extends Report> find(
            String projectId, @Nullable String reportType, @Nullable Status status);

    /**
     * Find all reports for a given project
     *
     * @param projectId The ID of the project
     * @return An iterable of all reports associated with the project
     */
    default Iterable<? extends Report> findAll(String projectId) {
        return find(projectId, null, null);
    }

    /**
     * Find project IDs associated with a given report
     *
     * @param reportId The ID of the report
     * @return An iterable of project IDs linked to the report
     */
    Iterable<String> findProjectId(String reportId);

    /**
     * Find historical reports based on project ID, report type and status
     *
     * @param projectId The ID of the project
     * @param reportType The type of report to find
     * @param status The status of reports to find
     * @return An iterable of historical reports matching the criteria
     */
    Iterable<? extends Report> findInHistory(
            String projectId, @Nullable String reportType, @Nullable Status status);
}
