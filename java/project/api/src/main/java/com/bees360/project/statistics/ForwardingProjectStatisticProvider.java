package com.bees360.project.statistics;

import com.google.common.collect.ForwardingObject;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class ForwardingProjectStatisticProvider extends ForwardingObject
        implements ProjectStatisticProvider {

    protected final ProjectStatisticProvider projectStatisticProvider;

    public ForwardingProjectStatisticProvider(ProjectStatisticProvider projectStatisticProvider) {
        this.projectStatisticProvider = projectStatisticProvider;
        log.info("Created {}(projectStageStatisticsProvider={})", this, projectStatisticProvider);
    }

    @Override
    protected ProjectStatisticProvider delegate() {
        return projectStatisticProvider;
    }

    @Override
    public Iterable<ProjectStageStatistic> getCustomerStatistic(ProjectStatisticParams request) {
        return projectStatisticProvider.getCustomerStatistic(request);
    }

    @Override
    public Iterable<ProjectStageStatistic> getStageStatistic(ProjectStatisticParams request) {
        return projectStatisticProvider.getStageStatistic(request);
    }

    @Override
    public Iterable<ProjectStageStatistic> getCurrentStatistic(ProjectStatisticParams request) {
        return projectStatisticProvider.getCurrentStatistic(request);
    }
}
