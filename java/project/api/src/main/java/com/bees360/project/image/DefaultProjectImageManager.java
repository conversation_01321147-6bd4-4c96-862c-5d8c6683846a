package com.bees360.project.image;

import com.bees360.image.GroupImageSource;
import com.bees360.image.Image;
import com.bees360.image.ImageGroupManager;
import com.bees360.image.ImageSource;
import com.bees360.util.Iterables;

import java.util.List;
import java.util.stream.Collectors;

public class DefaultProjectImageManager extends DefaultProjectImageProvider
        implements ProjectImageManager {

    private final ImageGroupManager imageGroupManager;

    public DefaultProjectImageManager(ImageGroupManager imageGroupManager) {
        super(imageGroupManager);
        this.imageGroupManager = imageGroupManager;
    }

    @Override
    public Iterable<? extends Image> createProjectImage(
            String projectId, Iterable<? extends ImageSource> imageSources, String createdBy) {
        var groupImageSource =
                Iterables.toStream(imageSources)
                        .map(
                                imageSource ->
                                        GroupImageSource.of(
                                                imageSource,
                                                List.of(
                                                        GroupImageSource.Group.builder()
                                                                .groupKey(projectId)
                                                                .groupType(GROUP_PROJECT)
                                                                .build())))
                        .collect(Collectors.toList());
        return imageGroupManager.createGroupImage(groupImageSource, createdBy);
    }
}
