package com.bees360.pipeline;

import com.bees360.api.Proto;
import com.bees360.project.Message;

import jakarta.annotation.Nullable;

public interface ProjectPipelineConfig extends Proto<Message.ProjectPipelineConfigMessage> {
    String getName();

    String getInsuredBy();

    String getProcessedBy();

    Integer getServiceType();

    String getPipelineDefKey();

    String getUserId();

    @Nullable
    Boolean getPolicyRenewal();

    static ProjectPipelineConfig from(Message.ProjectPipelineConfigMessage message) {
        return new ProjectPipelineConfig() {
            @Override
            public String getName() {
                return message.getName();
            }

            @Override
            public String getInsuredBy() {
                return message.getInsuredBy();
            }

            @Override
            public String getProcessedBy() {
                return message.getProcessedBy();
            }

            @Override
            public Integer getServiceType() {
                return message.getServiceType();
            }

            @Override
            public String getPipelineDefKey() {
                return message.getPipelineDefKey();
            }

            @Override
            public String getUserId() {
                return message.getUserId();
            }

            @Override
            public Boolean getPolicyRenewal() {
                return message.hasPolicyRenewal() ? message.getPolicyRenewal().getValue() : null;
            }

            @Override
            public Message.ProjectPipelineConfigMessage toMessage() {
                return message;
            }
        };
    }
}
