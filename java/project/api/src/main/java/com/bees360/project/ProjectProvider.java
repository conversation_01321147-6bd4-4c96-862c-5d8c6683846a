package com.bees360.project;

import java.util.NoSuchElementException;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/6/5
 */
public interface ProjectProvider {

    default Project getProject(String projectId) throws NoSuchElementException {
        return Optional.ofNullable(findProjectById(projectId))
                .orElseThrow(
                        () ->
                                new NoSuchElementException(
                                        String.format(
                                                "Cannot find project with id '%s'", projectId)));
    }

    Project findProjectById(String projectId);

    Iterable<Project> getProject(ProjectQuery query);
}
