package com.bees360.project.invoice;

import com.bees360.invoice.Invoice;

public interface ProjectInvoiceManager extends ProjectInvoiceProvider {

    /**
     * this method can use to simply add a ClaimInvoice and if the project has an invoice, this
     * method can replace it to the new invoice
     *
     * @throws IllegalArgumentException The invoice that needs to be replaced has been paid
     * @deprecated 当前端不再依赖于 ClaimInvoice 表后这个方法要去掉，用 {@link #addInvoice}方法取代
     */
    @Deprecated(since = "refactor underwriting invoice")
    default ProjectInvoice addClaimInvoice(
            String projectId, String customerId, Invoice invoice, String fileKey) {
        return addInvoice(projectId, customerId, invoice, fileKey);
    }

    /**
     * this method can use to simply add an Invoice and if the project has an invoice, this method
     * can replace it to the new invoice
     *
     * @throws IllegalArgumentException The invoice that needs to be replaced has been paid
     */
    ProjectInvoice addInvoice(String projectId, String customerId, Invoice invoice, String fileKey);

    /**
     * update invoice set delete is true
     *
     * @param invoiceNo invoice no
     */
    boolean deleteByInvoiceNo(String invoiceNo);

    boolean finalizeInvoice(String invoiceNo);

    void updateInvoiceFile(String invoiceNo, String fileKey);
}
