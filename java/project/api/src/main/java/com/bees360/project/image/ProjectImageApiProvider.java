package com.bees360.project.image;

import com.bees360.image.ImageApiQueryResponse;

/** Interface for providing project image API functionalities. */
public interface ProjectImageApiProvider {

    /**
     * Provides paginated query based on the projectId condition.
     *
     * @param projectId the query condition
     * @return ImageApi list and other data.
     */
    ImageApiQueryResponse findByProjectId(String projectId);
}
