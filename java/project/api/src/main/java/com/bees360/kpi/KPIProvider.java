package com.bees360.kpi;

import java.time.Instant;
import java.util.function.Function;

public interface KPIProvider {
    Function<String, KPI> DEFAULT_INSTANCE =
            userId ->
                    KPI.from(
                            Message.KPIMessage.getDefaultInstance().toBuilder()
                                    .setUserId(userId)
                                    .build());

    KPI getKPISnapshot(Instant startTime, Instant endTime, String userId);

    Iterable<? extends KPI> getKPISeries(
            int periodStart, int periodEnd, Message.PeriodUnit unit, String userId);

    Iterable<? extends KPI> getKPISnapshot(Instant startTime, Instant endTime);
}
