package com.bees360.project.claim;

import com.bees360.project.ServiceTypeEnum;

import jakarta.validation.constraints.NotBlank;

import java.time.LocalDate;

@SuppressWarnings({"UnusedReturnValue"})
public interface ClaimManager {

    boolean updateClaimNo(@NotBlank String projectId, @NotBlank String claimNo);

    boolean updateClaimType(@NotBlank String projectId, ClaimTypeEnum claimType);

    boolean updateServiceType(@NotBlank String projectId, ServiceTypeEnum serviceType);

    boolean updateDateOfLoss(@NotBlank String projectId, LocalDate dateOfLoss);

    /**
     * 修改 project 针对保险理赔类型的估计总成本
     *
     * @param projectId project id
     * @param estimateTotalPay 保险理赔类型的估计总成本
     * @return 是否修改成功
     */
    boolean updateEstimateTotalPay(@NotBlank String projectId, double estimateTotalPay);
}
