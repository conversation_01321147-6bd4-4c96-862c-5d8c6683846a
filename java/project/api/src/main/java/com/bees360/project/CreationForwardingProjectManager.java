package com.bees360.project;

import com.bees360.project.claim.Claim;
import com.bees360.project.inspection.Inspection;
import com.bees360.project.underwriting.Underwriting;
import com.bees360.project.util.ForwardingProjectManager;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import javax.annotation.Nullable;

@Log4j2
public class CreationForwardingProjectManager extends ForwardingProjectManager {
    private final ProjectCreator projectCreator;
    private final ProjectIIManager projectIIManager;

    public CreationForwardingProjectManager(
            @NonNull ProjectIIManager projectIIManager, @NonNull ProjectCreator projectCreator) {
        this.projectIIManager = projectIIManager;
        this.projectCreator = projectCreator;
        log.info(
                "Created {}(projectIIManager={}, projectCreator={})",
                this,
                projectIIManager,
                projectCreator);
    }

    @Override
    protected ProjectIIManager delegate() {
        return projectIIManager;
    }

    @Override
    public ProjectII create(
            @Nullable String id,
            Claim claim,
            @Nullable Inspection inspection,
            String contractId,
            String policyId,
            String createdBy,
            boolean allowDuplication,
            boolean isTestCase) {
        return projectCreator.create(
                id,
                claim,
                inspection,
                contractId,
                policyId,
                createdBy,
                allowDuplication,
                isTestCase);
    }

    @Override
    public ProjectII create(
            @Nullable String id,
            Underwriting underwriting,
            @Nullable Inspection inspection,
            String contractId,
            String policyId,
            String createdBy,
            boolean allowDuplication,
            boolean isTestCase) {
        return projectCreator.create(
                id,
                underwriting,
                inspection,
                contractId,
                policyId,
                createdBy,
                allowDuplication,
                isTestCase);
    }

    @Override
    public ProjectII create(
            Underwriting underwriting,
            @Nullable Inspection inspection,
            String contractId,
            String policyId,
            String createdBy,
            boolean allowDuplication) {
        return projectCreator.create(
                underwriting, inspection, contractId, policyId, createdBy, allowDuplication);
    }

    @Override
    public ProjectII create(
            @Nullable String id,
            Claim claim,
            @Nullable Inspection inspection,
            String contractId,
            String policyId,
            String createdBy) {
        return projectCreator.create(id, claim, inspection, contractId, policyId, createdBy);
    }

    @Override
    public ProjectII create(
            Claim claim,
            @Nullable Inspection inspection,
            String contractId,
            String policyId,
            String createdBy) {
        return projectCreator.create(claim, inspection, contractId, policyId, createdBy);
    }

    @Override
    public ProjectII create(
            @Nullable String id,
            Underwriting underwriting,
            @Nullable Inspection inspection,
            String contractId,
            String policyId,
            String createdBy) {
        return projectCreator.create(id, underwriting, inspection, contractId, policyId, createdBy);
    }

    @Override
    public ProjectII create(
            Underwriting underwriting,
            @Nullable Inspection inspection,
            String contractId,
            String policyId,
            String createdBy) {
        return projectCreator.create(underwriting, inspection, contractId, policyId, createdBy);
    }

    @Override
    public ProjectII create(
            Claim claim,
            @Nullable Inspection inspection,
            String contractId,
            String policyId,
            String createdBy,
            boolean allowDuplication) {
        return projectCreator.create(
                claim, inspection, contractId, policyId, createdBy, allowDuplication);
    }

    @Override
    public ProjectII create(
            Claim claim,
            @Nullable Inspection inspection,
            String contractId,
            String policyId,
            String createdBy,
            boolean allowDuplication,
            boolean isTestCase) {
        return projectCreator.create(
                claim, inspection, contractId, policyId, createdBy, allowDuplication, isTestCase);
    }

    @Override
    public ProjectII create(
            Underwriting underwriting,
            @Nullable Inspection inspection,
            String contractId,
            String policyId,
            String createdBy,
            boolean allowDuplication,
            boolean isTestCase) {
        return projectCreator.create(
                underwriting,
                inspection,
                contractId,
                policyId,
                createdBy,
                allowDuplication,
                isTestCase);
    }

    @Override
    public ProjectII create(
            UnderwritingCreation underwriting, boolean allowDuplication, String creationChannel) {
        return projectCreator.create(underwriting, allowDuplication, creationChannel);
    }

    @Override
    public ProjectII create(ClaimCreation claim, boolean allowDuplication, String creationChannel) {
        return projectCreator.create(claim, allowDuplication, creationChannel);
    }
}
