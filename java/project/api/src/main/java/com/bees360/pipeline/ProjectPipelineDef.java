package com.bees360.pipeline;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.Proto;
import com.bees360.project.Message;
import com.bees360.util.DateTimes;
import com.google.protobuf.BoolValue;

import java.time.Instant;

import javax.annotation.Nullable;

public interface ProjectPipelineDef extends Proto<Message.ProjectPipelineDefMessage> {
    /** project pipeline-def config id. */
    String getId();

    /** insured by */
    String getInsuredBy();

    /** processed by */
    String getProcessedBy();

    /** service type enum */
    int getServiceType();

    /** pipeline def key */
    String getPipelineDefKey();

    /** pipeline def name */
    String getPipelineDefName();

    /** policy renewal */
    @Nullable
    Boolean getPolicyRenewal();

    /** 创建者 */
    String getCreatedBy();

    /** 创建时间 */
    Instant getCreatedAt();

    /** 更新者 */
    String getUpdatedBy();

    /** 更新时间 */
    Instant getUpdatedAt();

    static ProjectPipelineDef from(Message.ProjectPipelineDefMessage message) {
        var createdAt = DateTimes.toInstant(message.getCreatedAt());
        var updatedAt = DateTimes.toInstant(message.getUpdatedAt());
        return new ProjectPipelineDef() {
            @Override
            public String getId() {
                return message.getId();
            }

            @Override
            public String getInsuredBy() {
                return message.getInsuredBy();
            }

            @Override
            public String getProcessedBy() {
                return message.getProcessedBy();
            }

            @Override
            public int getServiceType() {
                return message.getServiceType();
            }

            @Override
            public String getPipelineDefKey() {
                return message.getPipelineDefKey();
            }

            @Override
            public String getPipelineDefName() {
                return message.getPipelineDefName();
            }

            @Override
            public Boolean getPolicyRenewal() {
                return message.hasPolicyRenewal() ? message.getPolicyRenewal().getValue() : null;
            }

            @Override
            public String getCreatedBy() {
                return message.getCreatedBy().getId();
            }

            @Override
            public Instant getCreatedAt() {
                return createdAt;
            }

            @Override
            public String getUpdatedBy() {
                return message.getUpdatedBy().getId();
            }

            @Override
            public Instant getUpdatedAt() {
                return updatedAt;
            }

            @Override
            public Message.ProjectPipelineDefMessage toMessage() {
                return message;
            }
        };
    }

    @Override
    default Message.ProjectPipelineDefMessage toMessage() {
        var builder = Message.ProjectPipelineDefMessage.newBuilder();
        acceptIfNotNull(builder::setId, getId());
        acceptIfNotNull(builder::setInsuredBy, getInsuredBy());
        acceptIfNotNull(builder::setProcessedBy, getProcessedBy());
        acceptIfNotNull(builder::setServiceType, getServiceType());
        acceptIfNotNull(builder::setPipelineDefKey, getPipelineDefKey());
        acceptIfNotNull(builder::setPipelineDefName, getPipelineDefName());
        acceptIfNotNull(
                builder::setCreatedBy,
                getCreatedBy(),
                id -> com.bees360.user.Message.UserMessage.newBuilder().setId(id).build());
        acceptIfNotNull(builder::setCreatedAt, getCreatedAt(), DateTimes::toTimestamp);
        acceptIfNotNull(
                builder::setUpdatedBy,
                getUpdatedBy(),
                id -> com.bees360.user.Message.UserMessage.newBuilder().setId(id).build());
        acceptIfNotNull(builder::setUpdatedAt, getUpdatedAt(), DateTimes::toTimestamp);
        acceptIfNotNull(builder::setPolicyRenewal, getPolicyRenewal(), BoolValue::of);
        return builder.build();
    }
}
