package com.bees360.project.group;

public interface ParentChildProjectManager extends ParentChildProjectProvider {

    /**
     * Add a parent-child project group. If parent id already exists, new projects will be added to
     * existed parent project and same projects will be ignored.
     *
     * @param parentId parent project id
     * @param childIds child project ids
     * @param createdBy user id.
     */
    void addChildProject(String parentId, Iterable<String> childIds, String createdBy);

    /**
     * Update a parent-child project group. This method will replace all child projects with
     * incoming arguments.
     *
     * @param parentId parent project id
     * @param childIds child project ids
     * @param updatedBy user id.
     */
    void replaceAllChildProject(String parentId, Iterable<String> childIds, String updatedBy);

    /**
     * Delete child projects in a parent-child project group.
     *
     * @param parentId parent project id
     * @param childIds child project ids
     * @param deletedBy user id.
     */
    void deleteChildProject(String parentId, Iterable<String> childIds, String deletedBy);
}
