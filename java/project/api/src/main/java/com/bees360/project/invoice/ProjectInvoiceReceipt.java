package com.bees360.project.invoice;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.Entity;
import com.bees360.api.Proto;
import com.bees360.project.Message;
import com.bees360.util.DateTimes;
import com.bees360.util.Defaults;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Objects;

import javax.annotation.Nullable;

public interface ProjectInvoiceReceipt
        extends Entity, Proto<Message.ProjectMessage.Invoice.Receipt> {

    String getInvoiceNo();

    @Nullable
    String getQbInvoiceNo();

    /** 付款日期 */
    @Nullable
    LocalDate getClearedDate();

    BigDecimal getAmountPaid();

    /** 收款单单号 */
    @Nullable
    String getCheckNo();

    static ProjectInvoiceReceipt from(Message.ProjectMessage.Invoice.Receipt message) {
        if (Message.ProjectMessage.Invoice.Receipt.getDefaultInstance().equals(message)
                || Objects.isNull(message)) {
            return null;
        }
        return new ProjectInvoiceReceipt() {

            @Nullable
            @Override
            public String getId() {
                return null;
            }

            @Override
            public String getInvoiceNo() {
                return Defaults.nullIfEmpty(message.getInvoiceNo());
            }

            @Override
            public String getQbInvoiceNo() {
                return Defaults.nullIfEmpty(message.getQbInvoiceNo());
            }

            @Override
            public LocalDate getClearedDate() {
                return DateTimes.toLocalDate(message.getClearedDate());
            }

            @Override
            public String getCheckNo() {
                return Defaults.nullIfEmpty(message.getCheckNo());
            }

            @Nullable
            @Override
            public BigDecimal getAmountPaid() {
                return new BigDecimal(String.valueOf(message.getAmountPaid()))
                        .setScale(2, RoundingMode.HALF_UP);
            }
        };
    }

    @Override
    default String getNamespace() {
        return "project/invoice/Receipt";
    }

    @Override
    default Message.ProjectMessage.Invoice.Receipt toMessage() {
        var builder = Message.ProjectMessage.Invoice.Receipt.newBuilder();
        acceptIfNotNull(builder::setInvoiceNo, getInvoiceNo());
        acceptIfNotNull(builder::setCheckNo, getCheckNo());
        acceptIfNotNull(builder::setQbInvoiceNo, getQbInvoiceNo());
        acceptIfNotNull(builder::setClearedDate, DateTimes.toProtoDate(getClearedDate()));
        acceptIfNotNull(builder::setAmountPaid, getAmountPaid(), BigDecimal::doubleValue);
        return builder.build();
    }
}
