package com.bees360.project.util;

import com.bees360.project.OperationTag;
import com.bees360.project.OperationTagProvider;
import com.bees360.repository.util.ForwardingProvider;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class ForwardingOperationTagProvider extends ForwardingProvider<OperationTag>
        implements OperationTagProvider {

    private final OperationTagProvider operationTagProvider;

    public ForwardingOperationTagProvider(@NonNull OperationTagProvider operationTagProvider) {
        this.operationTagProvider = operationTagProvider;
        log.info("Created {}(operationTagProvider={})", this, operationTagProvider);
    }

    @Override
    protected OperationTagProvider delegate() {
        return operationTagProvider;
    }
}
