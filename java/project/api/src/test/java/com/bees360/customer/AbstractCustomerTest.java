package com.bees360.customer;

import com.bees360.api.NotFoundException;
import com.bees360.customer.Message.CustomerAttributes.ServiceTypeAttributes;
import com.bees360.customer.Message.CustomerMessage;
import com.bees360.customer.Message.CustomerMessage.CustomerRole;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;
import com.google.protobuf.util.JsonFormat;

import lombok.SneakyThrows;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.skyscreamer.jsonassert.JSONAssert;

import java.time.Instant;
import java.util.List;

public class AbstractCustomerTest {

    protected CustomerManager customerManager;

    public AbstractCustomerTest(CustomerManager customerManager) {
        this.customerManager = customerManager;
    }

    /**
     * Randomly generate customer without id
     *
     * @return customer
     */
    protected Customer randomCustomer() {
        String name = RandomStringUtils.randomAlphabetic(8);
        String key = name;

        return Customer.of(
                CustomerMessage.newBuilder()
                        .setName(name)
                        .setLogo(RandomStringUtils.randomAlphabetic(8))
                        .setWebsite(RandomStringUtils.randomAlphabetic(8))
                        .setKey(key)
                        .setCreatedTime(DateTimes.toTimestamp(Instant.now()))
                        .addRole(CustomerRole.INSURANCE_CARRIER)
                        .build());
    }

    void testCreate() {
        Customer expected = randomCustomer();
        String id = customerManager.createCustomer(expected);
        var actual = customerManager.findById(id);
        assertCustomerEquals(expected, actual);
    }

    void testUpdate() {
        Customer customerToCreate = randomCustomer();
        String id = customerManager.createCustomer(customerToCreate);
        var customer = customerManager.findById(id);
        Assertions.assertNotNull(customer, "The customer should not be null.");

        String expectedName = RandomStringUtils.randomAlphabetic(8);
        String expectedKey = RandomStringUtils.randomAlphabetic(8);
        customerManager.updateCustomer(
                Customer.of(
                        customer.toMessage().toBuilder()
                                .setName(expectedName)
                                .setKey(expectedKey)
                                .build()));
        var actual = customerManager.findById(id);
        Assertions.assertNotNull(actual, "The customer should not be null.");
        Assertions.assertEquals(expectedName, actual.getName(), "The customer name should match.");
        Assertions.assertEquals(
                expectedKey, actual.getCompanyKey(), "The customer company key should match.");
    }

    void testFindById() {
        Customer expected = randomCustomer();
        String id = customerManager.createCustomer(expected);
        var actual = customerManager.findById(id);
        assertCustomerEquals(expected, actual);
    }

    void testFindByKey() {
        Customer expected = randomCustomer();
        String id = customerManager.createCustomer(expected);
        var actual = customerManager.findByKey(expected.getCompanyKey());
        assertCustomerEquals(expected, actual);
    }

    void testFindByName() {
        Customer expected = randomCustomer();
        String id = customerManager.createCustomer(expected);
        var actual = customerManager.findByName(expected.getName());
        assertCustomerEquals(expected, actual);
    }

    void testFindByRole() {
        var insurances = customerManager.findByRole(List.of(CustomerRole.INSURANCE_CARRIER));
        Assertions.assertNotNull(insurances);

        var all = customerManager.findByRole(null);
        Assertions.assertNotNull(all);
        Assertions.assertTrue(Iterables.toList(insurances).size() <= Iterables.toList(all).size());
    }

    @SneakyThrows
    void testUpdateAttribute() {
        Customer customer = randomCustomer();
        String id = customerManager.createCustomer(customer);

        var builder = Message.CustomerAttributes.newBuilder();
        builder.setServiceType(
                ServiceTypeAttributes.newBuilder().addAllSubscribe(List.of(3, 4, 5)).build());
        var attributeValue = JsonFormat.printer().print(builder);

        customerManager.updateAttribute(id, "service_type", attributeValue);

        var actual = customerManager.findById(id);
        Assertions.assertNotNull(actual, "The customer should not be null.");
        try {
            customerManager.updateAttribute(id, "single_field", attributeValue);
        } catch (NotFoundException e) {
            Assertions.assertEquals(
                    "Fail to update attributes: The attribute single_field is not found.",
                    e.getMessage());
        }
    }

    @SneakyThrows
    protected void assertCustomerEquals(Customer expected, Customer actual) {
        Assertions.assertNotNull(expected, "The expected customer should not be null.");
        Assertions.assertNotNull(actual, "The actual customer should not be null.");

        Assertions.assertEquals(
                expected.getName(), actual.getName(), "The customer name should match.");
        Assertions.assertEquals(
                expected.getCompanyKey(),
                actual.getCompanyKey(),
                "The customer company key should match.");
        Assertions.assertEquals(
                expected.getLogo(), actual.getLogo(), "The customer logo should match.");
        Assertions.assertEquals(
                expected.getWebsite(), actual.getWebsite(), "The customer website should match.");
        Assertions.assertTrue(
                !expected.getCreatedTime().isAfter(actual.getCreatedTime()),
                "The customer created time should be before time when created before.");
        JSONAssert.assertEquals(expected.getAttributes(), actual.getAttributes(), true);
        Assertions.assertEquals(
                Iterables.toList(expected.getRoles()).size(),
                Iterables.toList(actual.getRoles()).size(),
                "The customer roles should match.");
    }
}
