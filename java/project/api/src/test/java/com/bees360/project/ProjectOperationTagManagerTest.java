package com.bees360.project;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.project.util.ForwardingProjectOperationTagManager;
import com.bees360.util.Iterables;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;

public class ProjectOperationTagManagerTest extends ForwardingProjectOperationTagManager {

    public ProjectOperationTagManagerTest(ProjectOperationTagManager projectOperationTagManager) {
        super(projectOperationTagManager);
    }

    void updatedAndThenDeleted() {
        var projectId = RandomUtils.nextLong(1, 100000) + "";
        var tagId = "1";
        var updatedBy = RandomStringUtils.randomAlphanumeric(12);
        delegate().updateByProjectIdAndTagId(projectId, tagId, updatedBy);
        var tags = Iterables.toList(delegate().findByProjectId(projectId));
        assertEquals(1, tags.size());
        assertEquals(tagId, tags.get(0).getId());

        var newTagId = "2";
        delegate().updateByProjectIdAndTagId(projectId, newTagId, updatedBy);
        tags = Iterables.toList(delegate().findByProjectId(projectId));
        assertEquals(1, tags.size());
        assertEquals(newTagId, tags.get(0).getId());

        delegate().deleteByProjectIdAndTagId(projectId, newTagId, updatedBy);
        tags = Iterables.toList(delegate().findByProjectId(projectId));
        assertTrue(tags.isEmpty());
    }
}
