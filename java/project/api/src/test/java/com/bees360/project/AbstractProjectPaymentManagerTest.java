package com.bees360.project;

import com.bees360.util.Iterables;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class AbstractProjectPaymentManagerTest {
    abstract ProjectPaymentManager getProjectPaymentManager();

    public void testSetProjectPaymentThenFindByProjectIds() {
        var projectIds = generateRandomProjectIds();
        Map<String, Boolean> map = new HashMap<>();
        for (String projectId : projectIds) {
            map.put(projectId, RandomUtils.nextBoolean());
        }
        Instant timeBeforeSet = Instant.now();
        getProjectPaymentManager().setProjectsPaymentStatus(map);
        var payStatusByProjectIds =
                Iterables.toList(getProjectPaymentManager().findPaymentByProjectIds(projectIds));
        Assertions.assertEquals(map.size(), payStatusByProjectIds.size());
        for (Payment p : payStatusByProjectIds) {
            Assertions.assertEquals(p.isPaid(), map.get(p.getProjectId()));
            Assertions.assertTrue(p.getUpdatedAt().isAfter(timeBeforeSet));
        }
    }

    public void testSetProjectPaidThenFindByProjectIds() {
        var projectIds = generateRandomProjectIds();
        getProjectPaymentManager().setProjectsPaid(projectIds);
        var payStatusByProjectIds =
                Iterables.toList(getProjectPaymentManager().findPaymentByProjectIds(projectIds));
        Assertions.assertEquals(projectIds.size(), payStatusByProjectIds.size());
        for (Payment p : payStatusByProjectIds) {
            Assertions.assertTrue(p.isPaid());
        }
    }

    public void testSetProjectUnPaidThenFindByProjectIds() {
        var projectIds = generateRandomProjectIds();
        getProjectPaymentManager().setProjectsUnPaid(projectIds);
        var payStatusByProjectIds =
                Iterables.toList(getProjectPaymentManager().findPaymentByProjectIds(projectIds));
        Assertions.assertEquals(projectIds.size(), payStatusByProjectIds.size());
        for (Payment p : payStatusByProjectIds) {
            Assertions.assertFalse(p.isPaid());
        }
    }

    private List<String> generateRandomProjectIds() {
        List<String> projectIds = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            projectIds.add(RandomProjectUtil.getRandomId());
        }
        return projectIds;
    }
}
