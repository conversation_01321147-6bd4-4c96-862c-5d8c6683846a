package com.bees360.pilot.config;

import com.bees360.job.JobScheduler;
import com.bees360.pilot.FillPilotPilotFeedbackManager;
import com.bees360.pilot.SendJobPilotFeedbackManager;
import com.bees360.pilot.feedback.JooqPilotFeedbackRepository;
import com.bees360.pilot.feedback.PilotFeedbackManager;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.image.ProjectImageProvider;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PilotFeedbackManagerConfig {

    @Bean("pilotFeedbackManager")
    PilotFeedbackManager pilotFeedbackManager(
            JobScheduler jobScheduler,
            JooqPilotFeedbackRepository jooqPilotFeedbackRepository,
            ProjectIIRepository projectIIRepository,
            ProjectImageProvider projectImageProvider,
            @Value("${http.beespilot.image-score.feedback-by-pilot.enabled:false}")
                    Boolean enableFeedback) {
        var feedbackManager =
                new SendJobPilotFeedbackManager(jobScheduler, jooqPilotFeedbackRepository);
        return enableFeedback
                ? new FillPilotPilotFeedbackManager(
                        feedbackManager, projectIIRepository, projectImageProvider)
                : feedbackManager;
    }
}
