package com.bees360.project.listener;

import static com.bees360.event.registry.ThirdPartyCompletedEvent.CompletedStatus.SUCCESS;
import static com.bees360.project.report.DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE;
import static com.bees360.thirdparty.ThirdPartyDataTypeEnum.PDF;
import static com.bees360.thirdparty.ThirdPartyDataTypeEnum.XML;
import static com.bees360.thirdparty.ThirdPartyTypeEnum.VALUE_360;

import com.bees360.event.registry.ThirdPartyCompletedEvent;
import com.bees360.event.registry.ThirdPartyCompletedEvent.ThirdPartyResource;
import com.bees360.image.tag.ImageTagGroupType;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.SaveReportJob;
import com.bees360.job.util.EventTriggeredJobMultiple;
import com.bees360.report.ReportTypeEnum;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Log4j2
public class Value360CompletedEventTriggerJob
        extends EventTriggeredJobMultiple<ThirdPartyCompletedEvent> {

    public Value360CompletedEventTriggerJob(JobScheduler jobScheduler) {
        super(jobScheduler);
    }

    @Override
    protected Iterable<Job> convert(ThirdPartyCompletedEvent event) {
        String key = event.getKey();
        List<ThirdPartyResource> resources = event.getResources();
        return resources.stream()
                .filter(e -> XML.equals(e.getResourceType()) || PDF.equals(e.getResourceType()))
                .map(e -> build(key, e))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    protected boolean filter(ThirdPartyCompletedEvent event) {
        return VALUE_360.equals(event.getType()) && SUCCESS.equals(event.getStatus());
    }

    @Nullable
    private Job build(String key, ThirdPartyResource report) {
        // todo 这部分逻辑有待验证
        String reportKey = report.getResourceKey();
        SaveReportJob job =
                SaveReportJob.newBuilder()
                        .setReportType(ReportTypeEnum.NR.getKey())
                        .setReportKey(reportKey)
                        .setGroupKey(key)
                        .setGroupType(PROJECT_REPORT_GROUP_TYPE)
                        .setCreatedBy(ImageTagGroupType.BEES_AI.getValue())
                        .build();
        return RetryableJob.of(Job.ofPayload(job));
    }
}
