package com.bees360.project.listener;

import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.event.registry.ProjectCreatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.policy.Policy;
import com.bees360.project.ProjectIIManager;
import com.bees360.util.DateTimes;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Instant;
import java.util.function.BiFunction;

/** This class represents an event handler for adding a customer activity on project creation. */
@Log4j2
public class AddCustomerActivityOnProjectCreatedEvent
        extends AbstractNamedEventListener<ProjectCreatedEvent> {

    private final CommentManager commentManager;

    private final BiFunction<String, String, String> customerNoteProvider;

    private final ProjectIIManager projectIIManager;

    private static final String WEB_SOURCE = "WEB";

    public AddCustomerActivityOnProjectCreatedEvent(
            CommentManager commentManager,
            BiFunction<String, String, String> customerNoteProvider,
            ProjectIIManager projectIIManager) {
        this.commentManager = commentManager;
        this.customerNoteProvider = customerNoteProvider;
        this.projectIIManager = projectIIManager;
        log.info("Created {}.", this);
    }

    @Override
    public boolean handle(ProjectCreatedEvent event) throws IOException {
        var projectId = event.getProjectId();
        var project = projectIIManager.findById(projectId);
        var createdAt = Instant.now();
        var customerNote =
                getCustomerNote(
                        project.getPolicy(), project.getContract().getInsuredBy().getCompanyKey());

        if (StringUtils.isEmpty(customerNote)) {
            return true;
        }

        commentManager.addComment(
                Comment.from(
                        com.bees360.activity.Message.CommentMessage.newBuilder()
                                .setProjectId(Long.parseLong(project.getId()))
                                .setCreatedBy(project.getCreateBy().toMessage())
                                .setContent(customerNote)
                                .setCreatedAt(DateTimes.toTimestamp(createdAt))
                                .setSource(WEB_SOURCE)
                                .build()));

        return true;
    }

    private String getCustomerNote(Policy policy, String insuredBy) {
        var policyNo = policy.getPolicyNo();
        if (StringUtils.isEmpty(policyNo)) {
            return null;
        }

        return customerNoteProvider.apply(insuredBy, policyNo);
    }
}
