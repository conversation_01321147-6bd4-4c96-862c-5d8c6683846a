package com.bees360.project.listener;

import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.event.registry.ProjectCreatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.policy.Coverage;
import com.bees360.policy.Message;
import com.bees360.project.ProjectIIManager;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.Instant;
import java.util.List;

/** Create activity of policy coverages with a certain formatted style. */
@Log4j2
public class AddPolicyCoverageActivityOnProjectCreatedEvent
        extends AbstractNamedEventListener<ProjectCreatedEvent> {

    private final ProjectIIManager projectIIManager;

    private final CommentManager commentManager;

    private static final DecimalFormat decimalFormat = new DecimalFormat("###,###.00");

    private static final String WEB_SOURCE = "WEB";

    public AddPolicyCoverageActivityOnProjectCreatedEvent(
            ProjectIIManager projectIIManager, CommentManager commentManager) {
        this.projectIIManager = projectIIManager;
        this.commentManager = commentManager;
        log.info("Created {}(commentManager={}).", this, this.commentManager);
    }

    @Override
    public void handle(ProjectCreatedEvent event) throws IOException {
        var projectId = event.getProjectId();
        var project = projectIIManager.findById(projectId);
        var policy = project.getPolicy();
        var coverageList = Iterables.toList(policy.getCoverage());
        if (CollectionUtils.isEmpty(coverageList)) {
            return;
        }

        var content = createCommentContent(coverageList);
        commentManager.addComment(
                Comment.from(
                        com.bees360.activity.Message.CommentMessage.newBuilder()
                                .setProjectId(Long.parseLong(project.getId()))
                                .setCreatedBy(project.getCreateBy().toMessage())
                                .setContent(content)
                                .setCreatedAt(DateTimes.toTimestamp(Instant.now()))
                                .setSource(WEB_SOURCE)
                                .build()));
        log.info("Successfully add comment of policy coverage for project {}.", project.getId());
    }

    private String createCommentContent(List<? extends Coverage> coverages) {
        BigDecimal dwelling = null;
        BigDecimal otherStructure = null;
        BigDecimal contents = null;
        for (Coverage coverage : coverages) {
            if (StringUtils.equals(coverage.getType(), Message.CoverageType.DWELLING.name())) {
                dwelling = coverage.getAmount();
            } else if (StringUtils.equals(
                    coverage.getType(), Message.CoverageType.OTHER_STRUCTURE.name())) {
                otherStructure = coverage.getAmount();
            } else if (StringUtils.equals(
                    coverage.getType(), Message.CoverageType.CONTENT.name())) {
                contents = coverage.getAmount();
            }
        }

        return String.format(
                getCoverageComment(),
                formatNumber(dwelling),
                formatNumber(otherStructure),
                formatNumber(contents));
    }

    private String getCoverageComment() {
        return "Coverage:\n" + "Main dwelling: %s\n" + "Other Structure: %s\n" + "Contents: %s";
    }

    private String formatNumber(BigDecimal decimal) {
        return decimal == null ? "N/A" : "$" + decimalFormat.format(decimal);
    }
}
