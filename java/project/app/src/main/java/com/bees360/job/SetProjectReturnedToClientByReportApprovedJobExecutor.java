package com.bees360.job;

import com.bees360.job.registry.SetProjectReturnedToClientByReportApprovedJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.project.Message.ProjectStatus;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.report.Message.ReportMessage.Status;
import com.bees360.report.Report;
import com.bees360.report.ReportTypeEnum;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/** 在报告approved的时候，如果项目所需的报告都已经approved，则将项目的状态设置为returned to client。 */
@Log4j2
public class SetProjectReturnedToClientByReportApprovedJobExecutor
        extends AbstractJobExecutor<SetProjectReturnedToClientByReportApprovedJob> {

    private final ProjectReportProvider projectReportProvider;
    private final ProjectStatusManager projectStatusManager;
    /** 通过 projectId 查找该 project 所必须的报告列表。 */
    private final Function<String, Set<String>> findRequiredReportTypeByProjectId;

    private final Supplier<String> robotUserIdSupplier;

    public SetProjectReturnedToClientByReportApprovedJobExecutor(
            ProjectReportProvider projectReportProvider,
            ProjectStatusManager projectStatusManager,
            Function<String, Set<String>> findRequiredReportTypeByProjectId,
            Supplier<String> robotUserIdSupplier) {
        this.projectReportProvider = projectReportProvider;
        this.projectStatusManager = projectStatusManager;
        this.findRequiredReportTypeByProjectId = findRequiredReportTypeByProjectId;
        this.robotUserIdSupplier = robotUserIdSupplier;
        log.info(
                "Created"
                    + " {}(projectReportProvider={},projectStatusManager={},findRequiredReportTypeByProjectId={},robotUserIdSupplier={})",
                this,
                projectReportProvider,
                projectStatusManager,
                findRequiredReportTypeByProjectId,
                robotUserIdSupplier);
    }

    @Override
    protected void handle(SetProjectReturnedToClientByReportApprovedJob job) throws IOException {
        log.info("Executor job: {}", job);
        var projectId = job.getProjectId();
        var reportId = job.getReportId();
        var reportType = job.getReportType();
        var robotUserId = robotUserIdSupplier.get();
        if (isAllRequiredReportsApproved(projectId, reportId, reportType)) {
            log.info(
                    "Update status of project {} to RETURNED_TO_CLIENT because all required"
                            + " report has been approved.",
                    projectId);
            projectStatusManager.updateStatus(
                    projectId, ProjectStatus.RETURNED_TO_CLIENT, robotUserId);
        }
    }

    private boolean isAllRequiredReportsApproved(
            String projectId, String reportId, String reportType) {
        var requiredReportTypes = findRequiredReportTypeByProjectId.apply(projectId);
        if (requiredReportTypes == null) {
            log.debug(
                    "Project {} is unsupported when trying to approve report (id: {}, type: {}).",
                    projectId,
                    reportId,
                    reportType);
            return false;
        }
        if (!requiredReportTypes.contains(reportType)) {
            log.debug(
                    "Report type {} isn't included in required report types: {}.",
                    reportType,
                    requiredReportTypes);
            return false;
        }
        var approvedReports = projectReportProvider.find(projectId, null, Status.APPROVED);
        var approvedReportTypes =
                Iterables.toStream(approvedReports)
                        .map(this::getReportTypeKey)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
        var isAllRequiredReportApproved =
                CollectionUtils.containsAll(approvedReportTypes, requiredReportTypes);
        if (!isAllRequiredReportApproved) {
            log.info(
                    "Reports ({}) that have been approved do not meet the list of required reports"
                            + " ({}) in project {}.",
                    approvedReportTypes,
                    requiredReportTypes,
                    projectId);
        }
        return isAllRequiredReportApproved;
    }

    private String getReportTypeKey(Report report) {
        var reportCode = report.getType();
        var reportType = ReportTypeEnum.valueOf(Integer.valueOf(reportCode));
        if (reportType == null) {
            // logging error and then ignore this type.
            log.error(
                    "Report type {} of report {} not found in ReportTypeEnum({}).",
                    report.getType(),
                    report.getId(),
                    ReportTypeEnum.values(),
                    new IllegalStateException());
            return null;
        }
        return reportType.getKey();
    }
}
