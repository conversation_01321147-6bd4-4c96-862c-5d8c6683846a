package com.bees360.project.config;

import static com.bees360.job.ReportJobNames.GENERATE_DPS_JOB_NAME;
import static com.bees360.job.ReportJobNames.GENERATE_MERGE_PDF_REPORT;
import static com.bees360.job.ReportJobNames.GENERATE_MPS_JOB_NAME;
import static com.bees360.job.ReportJobNames.GENERATE_PDF_REPORT;
import static com.bees360.project.report.DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE;
import static com.bees360.report.Message.ReportMessage.Resource.Type.ORIGIN;
import static com.bees360.report.ReportTypeEnum.SOS;

import com.bees360.estintel.FactorValueProvider;
import com.bees360.event.EventDispatcher;
import com.bees360.event.EventListener;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.handlebar.HandlebarTemplateConfig;
import com.bees360.handlebar.HandlebarTemplateProperties;
import com.bees360.image.ImageTagEnum;
import com.bees360.job.HtmlToPdfJob;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.MergePdfJob;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.GenericCommandJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.MapInferaAnnotationJob;
import com.bees360.job.registry.ReportSplitScheduleJob;
import com.bees360.map.BasicMap;
import com.bees360.map.RedisMap;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.listener.ScheduleJobOnPipelineReady;
import com.bees360.project.report.MergeFSRAndHoverOnPipelineTaskReady;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.redis.config.RedissonConfig;
import com.bees360.report.Message.ReportMessage.Resource.Type;
import com.bees360.report.Report;
import com.bees360.util.Iterables;
import com.bees360.util.SecureTokens;
import com.google.common.base.Preconditions;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.function.TriFunction;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Configuration
@Log4j2
@Import({
    AutoRegisterEventListenerConfig.class,
    RedissonConfig.class,
    RabbitApiConfig.class,
    RabbitEventDispatcher.class,
    RabbitEventPublisher.class,
    RabbitJobScheduler.class,
    RabbitJobDispatcher.class,
    ReportMergeConfig.class,
    ReportJobDataProviderConfig.class,
    SystemUserConfig.class,
    HandlebarTemplateConfig.class
})
public class ScheduleJobOnPipelineReadyConfig {
    private static final String CLA_REPORT_TYPE = "CLA";
    private static final String FSR_RCE_REPORT_TYPE = "FSR_RCE";
    private static final String DPS_REPORT_TYPE = "37";
    private static final String MPS_REPORT_TYPE = "38";
    private static final String CHR_REPORT_TYPE = "CHR";
    private static final Integer RETRY_COUNT = 5;
    private static final Duration RETRY_DELAY = Duration.ofSeconds(5);
    private static final float RETRY_REFACTOR = 2.f;
    private static final String AI_USER = "10000";

    @Configuration
    @ConfigurationProperties(prefix = "redis.job2project")
    @Data
    static class Job2ProjectProperties {
        private Duration expiry;
        private String name;
    }

    @Bean
    BasicMap<String, String> jobKey2ProjectIdMap(
            RedissonClient redissonClient, Job2ProjectProperties properties) {
        return new RedisMap<>(redissonClient, properties.getName(), properties.getExpiry());
    }

    @Bean
    EventListener generateDpsOnPipelineReady(
            JobScheduler jobScheduler,
            EventDispatcher eventDispatcher,
            BasicMap<String, String> jobKey2ProjectIdMap,
            TriFunction<String, String, List<ImageTagEnum>, String> photoSheetJsonDataProvider,
            PipelineService pipelineService,
            @Value("${project.app.generate-job-with-created-by.enabled:false}")
                    boolean generateJobWithCreatedByEnabled,
            HandlebarTemplateProperties handlebarResourcePrefix) {
        var template = "static/DpsReportTemplate.html";
        var dpsTemplate = handlebarResourcePrefix.getDpsTemplatePath();
        if (StringUtils.isNotBlank(dpsTemplate)) {
            template = dpsTemplate;
        }
        var taskKey = "generate_dps";
        var reportType = DPS_REPORT_TYPE;
        return new ScheduleJobOnPipelineReady(
                jobScheduler,
                eventDispatcher,
                pipelineService,
                jobKey2ProjectIdMap,
                taskKey,
                GENERATE_DPS_JOB_NAME,
                generateJobWithCreatedByEnabled,
                generateReportJobByProjectIdAndCreatedByProvider(
                        reportType,
                        template,
                        GENERATE_DPS_JOB_NAME,
                        projectId ->
                                photoSheetJsonDataProvider.apply(
                                        reportType, projectId, List.of(ImageTagEnum.ROOF))));
    }

    @Bean
    EventListener generateMpsOnPipelineReady(
            JobScheduler jobScheduler,
            EventDispatcher eventDispatcher,
            BasicMap<String, String> jobKey2ProjectIdMap,
            TriFunction<String, String, List<ImageTagEnum>, String> photoSheetJsonDataProvider,
            PipelineService pipelineService,
            @Value("${project.app.generate-job-with-created-by.enabled:false}")
                    boolean generateJobWithCreatedByEnabled,
            HandlebarTemplateProperties handlebarResourcePrefix) {
        var template = "static/MpsReportTemplate.html";
        var mpsTemplate = handlebarResourcePrefix.getMpsTemplatePath();
        if (StringUtils.isNotBlank(mpsTemplate)) {
            template = mpsTemplate;
        }
        var taskKey = "generate_mps";
        var reportType = MPS_REPORT_TYPE;
        return new ScheduleJobOnPipelineReady(
                jobScheduler,
                eventDispatcher,
                pipelineService,
                jobKey2ProjectIdMap,
                taskKey,
                GENERATE_MPS_JOB_NAME,
                generateJobWithCreatedByEnabled,
                generateReportJobByProjectIdAndCreatedByProvider(
                        reportType,
                        template,
                        GENERATE_MPS_JOB_NAME,
                        projectId ->
                                photoSheetJsonDataProvider.apply(
                                        reportType,
                                        projectId,
                                        List.of(ImageTagEnum.ELEVATION, ImageTagEnum.INTERIOR))));
    }

    @Bean
    @ConditionalOnProperty(prefix = "report.chr.generate", value = "enable", havingValue = "true")
    EventListener generateChrOnPipelineReady(
            JobScheduler jobScheduler,
            EventDispatcher eventDispatcher,
            BasicMap<String, String> jobKey2ProjectIdMap,
            @Qualifier("reportJobDataProvider") Function<String, String> reportJobDataProvider,
            PipelineService pipelineService,
            @Value("${project.app.generate-job-with-created-by.enabled:false}")
                    boolean generateJobWithCreatedByEnabled) {
        var template = "static/ChrReportTemplate.html";
        var taskKey = "generate_chr";
        return new ScheduleJobOnPipelineReady(
                jobScheduler,
                eventDispatcher,
                pipelineService,
                jobKey2ProjectIdMap,
                taskKey,
                GENERATE_PDF_REPORT,
                generateJobWithCreatedByEnabled,
                generateReportJobByProjectIdAndCreatedByProvider(
                        CHR_REPORT_TYPE, template, GENERATE_PDF_REPORT, reportJobDataProvider));
    }

    @Bean
    EventListener generateCLAReportListener(
            JobScheduler jobScheduler,
            EventDispatcher eventDispatcher,
            BasicMap<String, String> jobKey2ProjectIdMap,
            ProjectReportProvider projectReportProvider,
            PipelineService pipelineService,
            ReportMergeConfig reportMergeConfig) {
        var taskKey = "generate_cla";
        var jobName = GENERATE_MERGE_PDF_REPORT;
        var mergeReportTypes = reportMergeConfig.getTypeConfig().get(CLA_REPORT_TYPE);
        Preconditions.checkArgument(
                !CollectionUtils.isEmpty(mergeReportTypes),
                "Merge cla report type should be provide");

        return new ScheduleJobOnPipelineReady(
                jobScheduler,
                eventDispatcher,
                pipelineService,
                jobKey2ProjectIdMap,
                taskKey,
                jobName,
                mergeReportPdfJobProvider(
                        CLA_REPORT_TYPE, projectReportProvider, mergeReportTypes));
    }

    @Bean
    EventListener generateFSRRCEReportListener(
            JobScheduler jobScheduler,
            EventDispatcher eventDispatcher,
            BasicMap<String, String> jobKey2ProjectIdMap,
            ProjectReportProvider projectReportProvider,
            PipelineService pipelineService,
            ReportMergeConfig reportMergeConfig) {
        var taskKey = "generate_fsr_rce";
        var jobName = GENERATE_MERGE_PDF_REPORT;
        var mergeReportTypes = reportMergeConfig.getTypeConfig().get(FSR_RCE_REPORT_TYPE);
        Preconditions.checkArgument(
                !CollectionUtils.isEmpty(mergeReportTypes),
                "Merge fsr_rce report type should be provide");

        return new ScheduleJobOnPipelineReady(
                jobScheduler,
                eventDispatcher,
                pipelineService,
                jobKey2ProjectIdMap,
                taskKey,
                jobName,
                mergeReportPdfJobProvider(
                        FSR_RCE_REPORT_TYPE, projectReportProvider, mergeReportTypes));
    }

    @Bean
    @ConditionalOnProperty(prefix = "report.sos.generate", value = "enable", havingValue = "true")
    EventListener generateSosOnPipelineReady(
            JobScheduler jobScheduler,
            EventDispatcher eventDispatcher,
            BasicMap<String, String> jobKey2ProjectIdMap,
            @Qualifier("reportJobDataProvider") Function<String, String> reportJobDataProvider,
            PipelineService pipelineService,
            @Value("${project.app.generate-job-with-created-by.enabled:false}")
                    boolean generateJobWithCreatedByEnabled) {
        var template = "static/UWSOSReportTemplate.html";
        var taskKey = "generate_sos";
        return new ScheduleJobOnPipelineReady(
                jobScheduler,
                eventDispatcher,
                pipelineService,
                jobKey2ProjectIdMap,
                taskKey,
                GENERATE_PDF_REPORT,
                generateJobWithCreatedByEnabled,
                generateReportJobByProjectIdAndCreatedByProvider(
                        SOS.getKey(), template, GENERATE_PDF_REPORT, reportJobDataProvider));
    }

    @Bean
    EventListener mapInferaAnnotation(
            JobScheduler jobScheduler,
            EventDispatcher eventDispatcher,
            BasicMap<String, String> jobKey2ProjectIdMap,
            PipelineService pipelineService) {
        var taskKey = "map_infera_annotation";
        return new ScheduleJobOnPipelineReady(
                jobScheduler,
                eventDispatcher,
                pipelineService,
                jobKey2ProjectIdMap,
                taskKey,
                JobPayloads.getJobName(MapInferaAnnotationJob.class),
                projectId -> {
                    var job = new MapInferaAnnotationJob(projectId);
                    return RetryableJob.of(
                            Job.ofPayload(job), RETRY_COUNT, RETRY_DELAY, RETRY_REFACTOR);
                });
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "report.merge.fsr-and-hover",
            value = "enable",
            havingValue = "true")
    EventListener mergeFSRAndHoverOnPipelineTaskReady(
            JobScheduler jobScheduler,
            ProjectReportProvider projectReportProvider,
            FactorValueProvider factorValueProvider,
            EventDispatcher eventDispatcher,
            PipelineService pipelineService,
            BasicMap<String, String> jobKey2ProjectIdMap,
            @Qualifier("systemUserSupplier") Supplier<String> systemUserSupplier) {
        return new MergeFSRAndHoverOnPipelineTaskReady(
                jobScheduler,
                projectReportProvider,
                factorValueProvider,
                eventDispatcher,
                pipelineService,
                jobKey2ProjectIdMap,
                systemUserSupplier);
    }

    private Function<String, Job> splitReportJobProvider(
            ProjectReportProvider projectReportProvider, String reportType, String jobName) {
        return (projectId) -> {
            var reportUrl = getReportUrl(projectId, projectReportProvider, reportType);
            var job =
                    ReportSplitScheduleJob.newBuilder()
                            .setProjectId(projectId)
                            .setReportUrl(reportUrl)
                            .setReportType(reportType)
                            .build();
            String jobId = String.join("-", "split_report", projectId, reportType);
            return RetryableJob.of(
                    JobPayloads.encode(jobName, jobId, job),
                    RETRY_COUNT,
                    RETRY_DELAY,
                    RETRY_REFACTOR);
        };
    }

    private Function<String, Job> mergeReportPdfJobProvider(
            String mergeReportType,
            ProjectReportProvider projectReportProvider,
            List<String> reportMergeTypes) {
        return (projectId) -> {
            var reports = projectReportProvider.findAll(projectId);
            var reportTypeMap =
                    Iterables.toStream(reports)
                            .collect(
                                    Collectors.toMap(
                                            Report::getType,
                                            r -> r,
                                            (k1, k2) ->
                                                    k1.getCreatedAt().isAfter(k2.getCreatedAt())
                                                            ? k1
                                                            : k2));
            var mergePdfUrls =
                    reportMergeTypes.stream()
                            .filter(reportTypeMap::containsKey)
                            .map(
                                    reportType -> {
                                        var report = reportTypeMap.get(reportType);
                                        var resourceUrls = report.getResourceUrl();
                                        return resourceUrls.containsKey(Type.COMPRESSED)
                                                ? resourceUrls.get(Type.COMPRESSED)
                                                : resourceUrls.get(Type.ORIGIN);
                                    })
                            .collect(Collectors.toList());

            var jobId = Instant.now().toEpochMilli() + SecureTokens.generateRandomHexToken();
            var pdfResourceKey = jobId + ".pdf";
            var mergePdfJob = MergePdfJob.getInstance(mergePdfUrls, pdfResourceKey);

            var job =
                    GenericCommandJob.getInstance(
                            Map.of(
                                    "groupKey",
                                    projectId,
                                    "groupType",
                                    PROJECT_REPORT_GROUP_TYPE,
                                    "reportType",
                                    mergeReportType,
                                    "reportKey",
                                    pdfResourceKey,
                                    "createdBy",
                                    AI_USER),
                            mergePdfJob.toMessage(),
                            GENERATE_MERGE_PDF_REPORT,
                            jobId);
            return RetryableJob.of(job, RETRY_COUNT, RETRY_DELAY, RETRY_REFACTOR);
        };
    }

    private BiFunction<String, String, Job> generateReportJobByProjectIdAndCreatedByProvider(
            String reportType,
            String htmlTemplateKey,
            String jobName,
            Function<String, String> reportJobDataProvider) {
        return (projectId, createdBy) -> {
            String pdfResourceKey =
                    Instant.now().toEpochMilli() + SecureTokens.generateRandomHexToken();
            String summaryResourceKey = String.join("-", pdfResourceKey, "summary");
            String jobId = String.join("-", "generate_report", projectId, reportType);
            var jsonData = reportJobDataProvider.apply(projectId);
            log.info("Successfully get project '{}' json '{}'.", projectId, jsonData);
            var htmlToPdfJob =
                    HtmlToPdfJob.getInstance(
                            jobId, htmlTemplateKey, pdfResourceKey, summaryResourceKey, jsonData);

            var job =
                    GenericCommandJob.getInstance(
                            Map.of(
                                    "projectId",
                                    projectId,
                                    "reportType",
                                    reportType,
                                    "reportKey",
                                    pdfResourceKey,
                                    "summary",
                                    summaryResourceKey,
                                    "htmlKey",
                                    htmlTemplateKey,
                                    "createdBy",
                                    Optional.ofNullable(createdBy).orElse(AI_USER)),
                            htmlToPdfJob.toMessage(),
                            jobName,
                            jobId);
            return RetryableJob.of(job, RETRY_COUNT, RETRY_DELAY, RETRY_REFACTOR);
        };
    }

    private String getReportUrl(
            String projectId, ProjectReportProvider projectReportProvider, String reportType) {
        var reports = Iterables.toList(projectReportProvider.find(projectId, reportType, null));
        return reports.stream()
                .findFirst()
                .map(Report::getResourceUrl)
                .map(urlMap -> urlMap.get(ORIGIN))
                .orElseThrow(
                        () ->
                                new IllegalStateException(
                                        String.format(
                                                "Except project '%s' report '%s' url but"
                                                        + " found null. ",
                                                projectId, reportType)));
    }
}
