package com.bees360.project.executor;

import com.bees360.beespilot.BeespilotProjectManager;
import com.bees360.job.registry.SetBeespilotDaysOldJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.project.ProjectDaysOldProvider;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.time.Instant;
import java.util.Optional;

@Log4j2
public class SetBeespilotDaysOldJobExecutor extends AbstractJobExecutor<SetBeespilotDaysOldJob> {
    private final ProjectDaysOldProvider projectDaysOldProvider;
    private final BeespilotProjectManager beespilotManager;

    public SetBeespilotDaysOldJobExecutor(
            ProjectDaysOldProvider projectDaysOldProvider,
            BeespilotProjectManager beespilotManager) {
        this.projectDaysOldProvider = projectDaysOldProvider;
        this.beespilotManager = beespilotManager;
        log.info(
                "Created '{}'(projectDaysOldProvider={}, beespilotManager={})",
                this,
                this.projectDaysOldProvider,
                this.beespilotManager);
    }

    @Override
    protected void handle(SetBeespilotDaysOldJob job) throws IOException {
        var projectId = job.getProjectId();
        var daysOld = projectDaysOldProvider.findDaysOld(projectId);
        var preDaysOld = daysOld.getPreDaysOld();
        var openedOrEffectiveAt =
                Optional.ofNullable(daysOld.getOpenedOrEffectiveAt())
                        .map(Instant::toEpochMilli)
                        .orElse(null);
        beespilotManager.setProjectDaysOld(projectId, preDaysOld, openedOrEffectiveAt);
        log.info("Successfully sync project days old by job 'projectId {}'", projectId);
    }
}
