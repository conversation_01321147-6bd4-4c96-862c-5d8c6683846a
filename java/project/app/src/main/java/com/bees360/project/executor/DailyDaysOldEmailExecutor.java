package com.bees360.project.executor;

import static com.bees360.project.Message.ProjectStatus.ASSIGNED_TO_PILOT;
import static com.bees360.project.Message.ProjectStatus.CUSTOMER_CONTACTED;
import static com.bees360.project.Message.ProjectStatus.PROJECT_CREATED;

import com.bees360.job.registry.SendDaysOldEmailJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.mail.MailSender;
import com.bees360.mail.util.MailMessageFactory;
import com.bees360.project.Message;
import com.bees360.project.ProjectDaysOldProvider;
import com.bees360.project.ProjectDaysOldQuery;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.util.ProjectEmailUtil;
import com.bees360.user.UserProvider;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.gson.Gson;
import com.google.protobuf.Int32Value;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.checkerframework.checker.nullness.qual.Nullable;

import java.io.IOException;
import java.time.Instant;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Log4j2
public class DailyDaysOldEmailExecutor extends AbstractJobExecutor<SendDaysOldEmailJob> {
    private final MailMessageFactory mailMessageFactory;
    private final MailSender mailSender;
    private final ProjectIIManager projectIIManager;
    private final ProjectDaysOldProvider projectDaysOldProvider;
    private final UserProvider userProvider;
    private final Supplier<String> projectContextSupplier;
    private final ProjectEmailUtil projectEmailUtil;

    private final List<Message.ProjectStatus> projectStatus =
            List.of(PROJECT_CREATED, CUSTOMER_CONTACTED, ASSIGNED_TO_PILOT);
    private final Gson gson = new Gson();
    private static final String TEMPLATE_KEY = "daily_days_old";

    public DailyDaysOldEmailExecutor(
            @NonNull MailMessageFactory mailMessageFactory,
            @NonNull MailSender mailSender,
            ProjectIIManager projectIIManager,
            ProjectDaysOldProvider projectDaysOldProvider,
            UserProvider userProvider,
            Supplier<String> projectContextSupplier,
            ProjectEmailUtil projectEmailUtil) {
        this.mailMessageFactory = mailMessageFactory;
        this.mailSender = mailSender;
        this.projectIIManager = projectIIManager;
        this.projectDaysOldProvider = projectDaysOldProvider;
        this.userProvider = userProvider;
        this.projectContextSupplier = projectContextSupplier;
        this.projectEmailUtil = projectEmailUtil;
        log.info("Created {}", this);
    }

    @Override
    protected void handle(SendDaysOldEmailJob job) throws IOException {
        log.info("Received job '{}'.", job);

        var customerKey = job.getCompanyKey();
        var projects = listProjectForEmail(customerKey, job.getDaysOld());
        projects.forEach(
                projectII ->
                        sendEmailForProject(
                                projectII, job.getRecipients(), job.getCc(), job.getTriggerTime()));
    }

    private Iterable<ProjectII> listProjectForEmail(String companyKey, List<Integer> daysOld) {
        var builder = Message.ProjectDaysOldRequest.newBuilder().addCompanyKey(companyKey);

        Set<String> projectIds = new HashSet<>();
        daysOld.forEach(
                d -> {
                    builder.setDaysOldStart(Int32Value.of(d));
                    builder.setDaysOldEnd(Int32Value.of(d));
                    Optional.ofNullable(
                                    projectDaysOldProvider.findProjectByDaysOldQuery(
                                            ProjectDaysOldQuery.from(builder.build())))
                            .map(Iterables::toList)
                            .ifPresent(projectIds::addAll);
                });
        return Iterables.toStream(projectIIManager.findAllById(projectIds))
                .filter(filterProjectNotScheduled())
                .collect(Collectors.toList());
    }

    private Predicate<ProjectII> filterProjectNotScheduled() {
        return p ->
                Objects.isNull(p.getInspectionScheduledTime())
                        && projectStatus.contains(p.getLatestStatus());
    }

    private void sendEmailForProject(
            ProjectII project, List<String> to, List<String> cc, Instant triggerTime) {

        var triggerDate = DateTimes.toLocalDate(triggerTime);

        var mailMap = projectEmailUtil.assembleProjectMailMap(project, triggerDate);
        var projectUrl = String.join("/", projectContextSupplier.get(), project.getId(), "profile");
        mailMap.put("projectUrl", projectUrl);
        var content = gson.toJson(mailMap);

        var mailMessage =
                mailMessageFactory.create(
                        to, cc, List.of(), TEMPLATE_KEY, content, Collections.emptyMap());
        Futures.addCallback(
                mailSender.send(mailMessage),
                new FutureCallback<>() {
                    @Override
                    public void onSuccess(@Nullable Void unused) {
                        log.info(
                                "Successfully send days old email for project {}.",
                                project.getId());
                    }

                    @Override
                    public void onFailure(@NonNull Throwable throwable) {
                        log.error(
                                "Failed to send days old email for project {}.",
                                project.getId(),
                                throwable);
                    }
                },
                MoreExecutors.directExecutor());
        log.info(
                "Finish sending days old email with project {} to recipient '{}' and cc '{}'.",
                project.getId(),
                to,
                cc);
    }
}
