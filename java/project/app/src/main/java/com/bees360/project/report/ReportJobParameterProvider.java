package com.bees360.project.report;

import com.bees360.report.ReportTypeEnum;
import com.bees360.report.job.ReportJobParameter;

/**
 * Interface for providing report job parameters. This interface defines methods to retrieve report
 * job parameters based on project ID and report type.
 */
interface ReportJobParameterProvider {

    /**
     * Gets the report job parameters for a specific project and report type.
     *
     * @param projectId The ID of the project
     * @param reportType The type of report to generate
     * @return The ReportJobParameter containing the parameters needed for the report job
     */
    ReportJobParameter get(String projectId, ReportTypeEnum reportType);
}
