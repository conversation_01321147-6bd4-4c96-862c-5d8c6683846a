package com.bees360.project.config;

import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.project.listener.SetPipelineTaskStatusOnInspectionScheduledTimeChanged;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    AutoRegisterEventListenerConfig.class,
    SetPipelineTaskStatusOnInspectionScheduledTimeChanged.class
})
@ConditionalOnProperty(
        prefix = "project.app.set-pipeline-task-on-inspection-scheduled-time-changed",
        name = "enabled",
        havingValue = "true",
        matchIfMissing = true)
@Configuration
public class SetPipelineTaskStatusOnInspectionScheduledTimeChangedConfig {}
