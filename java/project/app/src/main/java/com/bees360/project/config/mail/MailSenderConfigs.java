package com.bees360.project.config.mail;

import com.bees360.mail.MailSender;
import com.bees360.mail.MailSenderProvider;
import com.bees360.mail.config.JobMailSenderConfig;

import lombok.extern.log4j.Log4j2;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    JobMailSenderConfig.class,
})
@Configuration
@Log4j2
public class MailSenderConfigs {

    @Bean("backendSender")
    MailSender backendSender(MailSenderProvider mailSenderProvider) {
        return mailSenderProvider.get("backend-sender");
    }

    @Bean("claimMailSender")
    MailSender claimMailSender(MailSenderProvider mailSenderProvider) {
        return mailSenderProvider.get("claim-sender");
    }

    @Bean("clientMailSender")
    MailSender clientMailSender(MailSenderProvider mailSenderProvider) {
        var sender = mailSenderProvider.get("client-mail-sender");
        log.info("Created {} as clientMailSender.", sender);
        return sender;
    }

    @Bean("noReplyMailSender")
    MailSender noReplyMailSender(MailSenderProvider mailSenderProvider) {
        var sender = mailSenderProvider.get("no-reply-sender");
        log.info("Created {} as noReplyMailSender.", sender);
        return sender;
    }
}
