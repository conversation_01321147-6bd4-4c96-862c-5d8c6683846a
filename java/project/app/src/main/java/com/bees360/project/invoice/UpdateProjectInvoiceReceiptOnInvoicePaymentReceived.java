package com.bees360.project.invoice;

import com.bees360.api.NotFoundException;
import com.bees360.event.registry.InvoicePaymentReceived;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.Message;
import com.bees360.util.DateTimes;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.time.LocalDate;

@Log4j2
public class UpdateProjectInvoiceReceiptOnInvoicePaymentReceived
        extends AbstractNamedEventListener<InvoicePaymentReceived> {

    private final ProjectInvoiceReceiptManager projectInvoiceReceiptManager;

    public UpdateProjectInvoiceReceiptOnInvoicePaymentReceived(
            @NonNull ProjectInvoiceReceiptManager projectInvoiceReceiptManager) {
        this.projectInvoiceReceiptManager = projectInvoiceReceiptManager;
        log.info("Start listener {}", this.getClass().getName());
    }

    @Override
    public void handle(InvoicePaymentReceived event) throws IOException {
        String invoiceNo = event.getInvoiceNo();
        LocalDate clearedDate = event.getPaymentDate();
        Message.ProjectMessage.Invoice.Receipt.Builder builder =
                Message.ProjectMessage.Invoice.Receipt.newBuilder();
        event.getCheckNo().ifPresent(builder::setCheckNo);
        ProjectInvoiceReceipt projectInvoiceReceiptUpdater =
                ProjectInvoiceReceipt.from(
                        builder.setQbInvoiceNo(invoiceNo)
                                .setInvoiceNo(invoiceNo)
                                .setAmountPaid(event.getAmountPaid().doubleValue())
                                .setClearedDate(DateTimes.toProtoDate(clearedDate))
                                .build());
        try {
            projectInvoiceReceiptManager.updateReceipt(projectInvoiceReceiptUpdater);
        } catch (NotFoundException e) {
            log.info("Not a Bees360 invoice, ignored. {}", e.getMessage());
        }
        log.info("finish update invoice receipt, invoice number:{}", invoiceNo);
    }
}
