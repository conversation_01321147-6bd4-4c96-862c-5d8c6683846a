package com.bees360.project.config;

import com.bees360.job.CloseProjectJobExecutor;
import com.bees360.job.JobDispatcher;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.state.ProjectStateChangeReasonProvider;
import com.bees360.project.state.ProjectStateManager;

import lombok.Data;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Import({
    GrpcProjectStateManagerConfig.class,
})
@Configuration
@ConditionalOnProperty(prefix = "project.job.close-project", name = "enabled", havingValue = "true")
@EnableConfigurationProperties
public class CloseProjectJobConfig {

    @Data
    @Configuration
    @ConfigurationProperties(prefix = "project.job.close-project")
    static class CloseProjectProperties {
        private String underwritingCloseReason = "";
        private String claimCloseReason = "";
        private Map<ServiceTypeEnum, String> serviceTypeCloseReasonMapping = new HashMap<>();
    }

    @Bean
    public Function<String, String> projectServiceTypeCloseReasonKeyProvider(
            CloseProjectProperties closeProjectProperties,
            ProjectIIRepository projectIIRepository) {
        return projectId -> {
            var project = projectIIRepository.findById(projectId);
            var serviceType = project.getServiceType();
            var closeReasonMapping = closeProjectProperties.getServiceTypeCloseReasonMapping();
            var claimCloseReason = closeProjectProperties.getClaimCloseReason();
            var underwritingCloseReason = closeProjectProperties.getUnderwritingCloseReason();
            return closeReasonMapping.getOrDefault(
                    serviceType,
                    ProjectTypeEnum.CLAIM.equals(serviceType.getProjectType())
                            ? claimCloseReason
                            : underwritingCloseReason);
        };
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.request-cancel",
            name = "enable",
            havingValue = "false",
            matchIfMissing = true)
    public CloseProjectJobExecutor cancelProjectJobExecutor(
            JobDispatcher jobDispatcher,
            ProjectIIRepository projectIIRepository,
            @Qualifier("grpcProjectStateManager") ProjectStateManager mysqlProjectStateManager,
            ProjectStateChangeReasonProvider projectStateChangeReasonProvider,
            @Qualifier("projectServiceTypeCloseReasonKeyProvider")
                    Function<String, String> projectServiceTypeCloseReasonKeyProvider) {
        // TODO 此处使用grpc调用web中的stateManager, 重构后需要修改连接地址
        var cancelProjectJobExecutor =
                new CloseProjectJobExecutor(
                        projectIIRepository,
                        mysqlProjectStateManager,
                        projectStateChangeReasonProvider,
                        projectServiceTypeCloseReasonKeyProvider);
        jobDispatcher.enlist(cancelProjectJobExecutor);
        return cancelProjectJobExecutor;
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.request-cancel",
            name = "enable",
            havingValue = "true")
    public CloseProjectJobExecutor closeProjectJobExecutor(
            JobDispatcher jobDispatcher,
            ProjectIIRepository projectIIRepository,
            @Qualifier("grpcProjectStateManager") ProjectStateManager mysqlProjectStateManager,
            ProjectStateChangeReasonProvider projectStateChangeReasonProvider,
            @Qualifier("projectCloseReasonProvider")
                    Function<String, String> projectCloseReasonProvider) {
        // TODO 此处使用grpc调用web中的stateManager, 重构后需要修改连接地址
        var cancelProjectJobExecutor =
                new CloseProjectJobExecutor(
                        projectIIRepository,
                        mysqlProjectStateManager,
                        projectStateChangeReasonProvider,
                        projectCloseReasonProvider);
        jobDispatcher.enlist(cancelProjectJobExecutor);
        return cancelProjectJobExecutor;
    }
}
