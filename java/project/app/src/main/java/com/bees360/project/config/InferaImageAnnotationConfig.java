package com.bees360.project.config;

import static com.bees360.image.util.ImageResourceUtils.getImageETag;

import com.bees360.common.Message.LogicalOperator;
import com.bees360.common.Message.QuerySymbol;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.image.ImageApi;
import com.bees360.image.ImageApiProvider;
import com.bees360.image.ImageApiQuery;
import com.bees360.image.ImageTagCategoryEnum;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.Message.ImageMessage.Resource.Type;
import com.bees360.image.config.GrpcImageApiClientConfig;
import com.bees360.job.JobScheduler;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.image.JobProjectImageProcessManager;
import com.bees360.project.image.ProjectImageProcessManager;
import com.bees360.project.image.ProjectImageProcessWithSetTaskStatusManager;
import com.bees360.project.listener.SendInferaImageOnPipelineReady;
import com.bees360.thirdparty.config.GrpcThirdPartyClientConfig;
import com.bees360.util.Iterables;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import jakarta.annotation.Nullable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Import({
    AutoRegisterEventListenerConfig.class,
    AutoRegisterJobExecutorConfig.class,
    GrpcImageApiClientConfig.class,
    GrpcThirdPartyClientConfig.class,
})
@Configuration
@EnableConfigurationProperties
public class InferaImageAnnotationConfig {
    public static final Gson gson =
            new GsonBuilder()
                    .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
                    .create();

    @Component
    @ConfigurationProperties(prefix = "infera.image")
    @Data
    static class InferaImageAnnotationProperties {
        private Map<String, InferaImageFilter> serviceConfig = new HashMap<>();
        private Type type = Type.ORIGIN;
        private Integer retryCount;
        private Duration retryDelay;
        private Float retryDelayIncreaseFactor;
    }

    @Data
    public static class InferaImageFilter {
        private String pipelineTaskKey;
        private List<ImageFilter> imageFilter = new ArrayList<>();
    }

    @Data
    public static class ImageFilter {
        private List<ImageTagEnum> includeTags = new ArrayList<>();
        private List<ImageTagEnum> excludeTags = new ArrayList<>();
        private List<ImageTagCategoryEnum> includeTagCategories = new ArrayList<>();
        private List<ImageTagCategoryEnum> excludeTagCategories = new ArrayList<>();
    }

    @Bean
    public BiFunction<String, String, String> inferaImageAnnotationJsonStringProvider(
            BiFunction<String, String, InferaImageAnnotationEntity>
                    inferaImageAnnotationEntityProvider) {
        return (serviceKey, projectId) -> {
            var entity = inferaImageAnnotationEntityProvider.apply(serviceKey, projectId);
            return gson.toJson(entity);
        };
    }

    @Bean
    public BiFunction<String, String, InferaImageAnnotationEntity>
            inferaImageAnnotationEntityProvider(
                    InferaImageAnnotationProperties properties, ImageApiProvider imageApiProvider) {
        var serviceConfig = properties.getServiceConfig();
        var type = properties.getType();
        return (serviceKey, projectId) -> {
            var inferaImageFilter = serviceConfig.get(serviceKey);
            var imageFilter = inferaImageFilter.getImageFilter();
            var imageApis = getImageApis(imageApiProvider, projectId, imageFilter);
            var images =
                    Iterables.toStream(imageApis)
                            .map(
                                    image ->
                                            new InferaImageAnnotationEntity.InferaImage(
                                                    image.getId(), getImageETag(image, type)))
                            .collect(Collectors.toList());
            var entity = new InferaImageAnnotationEntity();
            entity.setCustomAttribute(Map.of("project_id", projectId));
            entity.setImages(images);
            return entity;
        };
    }

    @Bean("endpointProjectImageProcessManager")
    public ProjectImageProcessManager endpointProjectImageProcessManager(
            ProjectImageProcessManager jobProjectImageProcessManager,
            PipelineService pipelineService,
            InferaImageAnnotationProperties properties) {
        var pipelineTaskKeyByServiceKey =
                properties.getServiceConfig().entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Map.Entry::getKey, x -> x.getValue().getPipelineTaskKey()));
        return new ProjectImageProcessWithSetTaskStatusManager(
                jobProjectImageProcessManager, pipelineService, pipelineTaskKeyByServiceKey::get);
    }

    @Bean("jobProjectImageProcessManager")
    public ProjectImageProcessManager jobProjectImageProcessManager(
            JobScheduler jobScheduler,
            InferaImageAnnotationProperties properties,
            @Qualifier("inferaImageAnnotationJsonStringProvider")
                    BiFunction<String, String, String> inferaImageAnnotationJsonStringProvider) {
        return new JobProjectImageProcessManager(
                jobScheduler,
                inferaImageAnnotationJsonStringProvider,
                properties.getRetryCount(),
                properties.getRetryDelay(),
                properties.getRetryDelayIncreaseFactor());
    }

    @Bean
    public SendInferaImageOnPipelineReady sendInferaImageOnThreeDFinishListener(
            JobScheduler jobScheduler,
            PipelineService pipelineService,
            InferaImageAnnotationProperties properties,
            @Qualifier("inferaImageAnnotationEntityProvider")
                    BiFunction<String, String, InferaImageAnnotationEntity>
                            inferaImageAnnotationEntityProvider) {
        var serviceKeysByPipelineTaskKeyMap = new HashMap<String, List<String>>();
        properties
                .getServiceConfig()
                .forEach(
                        (serviceKey, serviceConfig) -> {
                            var pipelineTaskKey = serviceConfig.getPipelineTaskKey();
                            var serviceKeys =
                                    serviceKeysByPipelineTaskKeyMap.computeIfAbsent(
                                            pipelineTaskKey, (key) -> new ArrayList<>());
                            serviceKeys.add(serviceKey);
                        });
        return new SendInferaImageOnPipelineReady(
                jobScheduler,
                pipelineService,
                serviceKeysByPipelineTaskKeyMap,
                inferaImageAnnotationEntityProvider,
                properties.getRetryCount(),
                properties.getRetryDelay(),
                properties.getRetryDelayIncreaseFactor());
    }

    /**
     * 根据 image openapi 提供的查询接口进行图片信息的获取。 不使用 imageGroupManager 是因为 Image 的数据获取时需要调用 resource-app 进行
     * url 的字段填充，会造成不必要的资源浪费。
     *
     * @param imageApiProvider imageApiProvider
     * @param projectId project id
     * @param imageFilter image 过滤规则
     * @return imageApi list
     */
    private Iterable<? extends ImageApi> getImageApis(
            ImageApiProvider imageApiProvider, String projectId, List<ImageFilter> imageFilter) {
        String query = getImageApiQueryString(projectId, imageFilter);
        var imageByQuery = imageApiProvider.findImageByQuery(ImageApiQuery.of(query));
        return imageByQuery.getImage();
    }

    /**
     * get imageApiQuery method query json string
     *
     * @param projectId project id
     * @param imageFilter image 过滤规则
     * @return imageApiQuery method query json string
     */
    private String getImageApiQueryString(String projectId, List<ImageFilter> imageFilter) {
        var projectIdQuery = Map.of("projectId", Map.of(QuerySymbol.EQ, projectId));
        var allTagQueryMap =
                Map.of(
                        LogicalOperator.OR,
                        Iterables.toList(
                                Iterables.transform(imageFilter, this::getAllTagQueryMap)));
        var map = Map.of(LogicalOperator.AND, List.of(projectIdQuery, allTagQueryMap));
        return new Gson().toJson(map);
    }

    private Map getAllTagQueryMap(ImageFilter imageFilter) {
        var includeTags = imageFilter.getIncludeTags();
        var excludeTags = imageFilter.getExcludeTags();
        var includeTagCategories = imageFilter.getIncludeTagCategories();
        var excludeTagCategories = imageFilter.getExcludeTagCategories();

        var list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(includeTags)) {
            list.add(getTagQuery(includeTags, QuerySymbol.INC));
        }
        if (!CollectionUtils.isEmpty(excludeTags)) {
            list.add(getTagQuery(excludeTags, QuerySymbol.EXC));
        }
        if (!CollectionUtils.isEmpty(includeTagCategories)) {
            list.add(getTagCategoryQuery(includeTagCategories, QuerySymbol.INC));
        }
        if (!CollectionUtils.isEmpty(excludeTagCategories)) {
            list.add(getTagCategoryQuery(excludeTagCategories, QuerySymbol.EXC));
        }
        return Map.of(LogicalOperator.AND, list);
    }

    private Map getTagQuery(List<ImageTagEnum> tagEnumList, QuerySymbol querySymbol) {
        return Map.of(
                "tag",
                Map.of(
                        querySymbol,
                        Iterables.toList(
                                Iterables.transform(tagEnumList, ImageTagEnum::getDisplay))));
    }

    private Object getTagCategoryQuery(
            List<ImageTagCategoryEnum> tagCategoryEnumList, QuerySymbol querySymbol) {
        return Map.of(
                "category",
                Map.of(
                        querySymbol,
                        Iterables.toList(
                                Iterables.transform(
                                        tagCategoryEnumList, ImageTagCategoryEnum::getDisplay))));
    }

    /** 调用 Infera 服务的job请求时的请求实体定义 */
    @ToString
    @Getter
    @Setter
    public static class InferaImageAnnotationEntity {
        /** 附加信息 e.g. {"project_id":"123456"} */
        @Nullable private Map customAttribute;

        /** image list */
        @NonNull private List<InferaImage> images;

        @ToString
        @Getter
        @Setter
        @AllArgsConstructor
        public static class InferaImage {
            /** image id */
            private String imageId;
            /** image e_tag */
            private String eTag;
        }
    }
}
