package com.bees360.project.util;

import org.apache.commons.lang3.StringUtils;
import org.mozilla.javascript.Context;
import org.mozilla.javascript.EcmaError;
import org.mozilla.javascript.EvaluatorException;
import org.mozilla.javascript.Scriptable;
import org.mozilla.javascript.Undefined;

public class JSUtil {

    public static Object execute(String script, String jsonParameter) {
        if (StringUtils.isBlank(script)) {
            throw new IllegalArgumentException("The parameter script cannot be empty.");
        }
        if (StringUtils.isBlank(jsonParameter)) {
            throw new IllegalArgumentException("The parameter json string cannot be empty.");
        }
        Context cx = Context.enter();
        try {
            Scriptable scope = cx.initStandardObjects();
            cx.evaluateString(
                    scope,
                    "var parameter = JSON.parse('" + jsonParameter + "');",
                    "jsonParse",
                    1,
                    null);
            var result = cx.evaluateString(scope, script, "script", 1, null);
            if (result instanceof Undefined) {
                return null;
            }
            return cx.evaluateString(scope, script, "script", 1, null);
        } catch (EvaluatorException | EcmaError e) {
            throw new IllegalArgumentException(
                    String.format(
                            "Failed to execute JS script: script is %s jsonParameter is %s",
                            script, jsonParameter),
                    e);
        } finally {
            // 直结束当前线程，不对其它线程有影响，不存在并发问题
            Context.exit();
        }
    }
}
