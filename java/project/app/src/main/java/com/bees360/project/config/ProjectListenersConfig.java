package com.bees360.project.config;

import com.bees360.customer.CustomerManager;
import com.bees360.event.EventDispatcher;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.listener.AddContactOnProjectCreated;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

@Configuration
@Log4j2
public class ProjectListenersConfig {
    @Data
    public static class ContactEntity implements Contact {
        private String id;
        private String fullName;
        private String primaryEmail;
        private String primaryPhone;
        private List<String> otherEmail = new ArrayList<>();
        private List<String> otherPhone = new ArrayList<>();
        private String role;
        private List<ProjectTypeEnum> projectType = new ArrayList<>();
        private List<String> companyKey = new ArrayList<>();
        private String firstName;
        private String middleName;
        private String lastName;
        private boolean isPrimary;
    }

    @Data
    @ConfigurationProperties(prefix = "project.app.contact.assign-trigger")
    @EnableConfigurationProperties
    @RefreshScope
    static class ContactProperties {
        List<ContactEntity> contacts = new ArrayList<>();

        Map<ProjectTypeEnum, Map<String, List<Contact>>> projectTypeMap = new HashMap<>();

        @PostConstruct
        private void constructProjectTypeMap() {
            for (ContactEntity contact : this.contacts) {
                var projectTypes = contact.getProjectType();
                for (ProjectTypeEnum projectType : projectTypes) {
                    var contactCompanyMap = projectTypeMap.get(projectType);
                    if (contactCompanyMap == null) {
                        contactCompanyMap = new HashMap<>();
                    }

                    var companys = contact.getCompanyKey();
                    for (String company : companys) {

                        var contactList = contactCompanyMap.get(company);
                        if (contactList == null) {
                            contactList = new ArrayList<>();
                        }

                        contactList.add(contact);
                        contactCompanyMap.put(company, contactList);
                    }
                    projectTypeMap.put(projectType, contactCompanyMap);
                }
            }
            log.info("projectTypeMap reConstruct: {}", projectTypeMap);
        }
    }

    @ConditionalOnProperty(
            prefix = "project.app.contact.assign-trigger",
            name = "enable",
            havingValue = "true")
    static class AddContactOnProjectCreatedConfig {
        @Bean
        public AddContactOnProjectCreated addContactOnProjectCreated(
                ContactManager contactManager,
                CustomerManager customerManager,
                ContactProperties contactProperties,
                EventDispatcher eventDispatcher) {
            String opUserId = "10000";
            AddContactOnProjectCreated addContactOnProjectCreated =
                    new AddContactOnProjectCreated(
                            contactManager,
                            customerManager,
                            type -> contactProperties.getProjectTypeMap().get(type),
                            opUserId);
            eventDispatcher.enlist(addContactOnProjectCreated);
            return addContactOnProjectCreated;
        }
    }
}
