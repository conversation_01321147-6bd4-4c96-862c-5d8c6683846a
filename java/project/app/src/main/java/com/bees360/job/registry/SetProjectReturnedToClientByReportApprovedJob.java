package com.bees360.job.registry;

import lombok.Getter;
import lombok.ToString;

@Getter
@JobPayload
@ToString
public class SetProjectReturnedToClientByReportApprovedJob {

    private final String projectId;

    private final String reportId;

    private final String reportType;

    public SetProjectReturnedToClientByReportApprovedJob(
            String projectId, String reportId, String reportType) {
        this.projectId = projectId;
        this.reportId = reportId;
        this.reportType = reportType;
    }

    public static SetProjectReturnedToClientByReportApprovedJob of(
            String projectId, String reportId, String reportType) {
        return new SetProjectReturnedToClientByReportApprovedJob(projectId, reportId, reportType);
    }
}
