package com.bees360.project.listener;

import static com.bees360.image.util.AttributeMessageAdapter.jsonToAttribute;

import com.bees360.event.registry.ReportStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.image.Image;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.ImageTagProvider;
import com.bees360.image.tag.ImageTag;
import com.bees360.image.tag.ImageTagDictProvider;
import com.bees360.image.tag.ImageTagGroupType;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.image.ProjectImageProvider;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.Message;
import com.bees360.report.ReportTypeEnum;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/** 当approve FSR事件触发时统计outbuildings并插入project inspection */
@Log4j2
public class SetNumberOfOutBuildingsListener
        extends AbstractNamedEventListener<ReportStatusChanged> {

    private final ProjectIIManager inspectionManager;

    private final ImageTagDictProvider imageTagDictProvider;

    private final ProjectImageProvider projectImageProvider;

    private final ImageTagProvider imageTagProvider;

    private final ProjectReportProvider projectReportProvider;

    private static final String OUTBUILDINGS = "Outbuildings";

    public SetNumberOfOutBuildingsListener(
            ProjectIIManager inspectionManager,
            ImageTagDictProvider imageTagDictProvider,
            ProjectImageProvider projectImageProvider,
            ImageTagProvider imageTagProvider,
            ProjectReportProvider projectReportProvider) {
        this.inspectionManager = inspectionManager;
        this.imageTagDictProvider = imageTagDictProvider;
        this.projectImageProvider = projectImageProvider;
        this.imageTagProvider = imageTagProvider;
        this.projectReportProvider = projectReportProvider;
        log.info("Created {}.", this);
    }

    @Override
    public void handle(ReportStatusChanged event) throws IOException {
        if (Message.ReportMessage.Status.APPROVED.getNumber() != event.getReportStatus()
                || !ReportTypeEnum.FUR.getKey().equals(event.getReportType())) {
            return;
        }

        log.info("Set number of out buildings on received event: {}.", event);
        var reportId = event.getId();
        var projectIds = projectReportProvider.findProjectId(reportId);
        for (String projectId : projectIds) {
            var buildingTags = getOutbuildingTags();
            var outbuildings = countOutbuildings(projectId, buildingTags);
            inspectionManager.updateNumberOfOutBuildings(projectId, outbuildings);
            log.info(
                    "Successfully set project {} number of outbuildings to {}.",
                    projectId,
                    outbuildings);
        }
    }

    /** 采用building tag和 tag attribute index 排列组合的方式计算buildings数量 */
    private Integer countOutbuildings(String projectId, List<ImageTagEnum> buildingTags) {
        Set<Long> buildingCountSet = new HashSet<>();

        Set<String> buildingTagSet =
                buildingTags.stream()
                        .map(tag -> String.valueOf(tag.getCode()))
                        .collect(Collectors.toSet());

        var images = projectImageProvider.findByProjectId(projectId);
        var imageTagMap =
                imageTagProvider.findByImageIds(Iterables.transform(images, Image::getId));
        // 统计building tag和number tag的排列组合
        for (Iterable<? extends ImageTag> tags : imageTagMap.values()) {
            var x = 0;
            var y = 0;
            for (ImageTag tag : tags) {
                var tagId = tag.getId();
                if (buildingTagSet.contains(tagId)) {
                    x = Integer.parseInt(tagId);
                    y = jsonToAttribute(tag.getAttribute()).getIndex().getValue();
                }
            }

            if (x != 0) {
                buildingCountSet.add(((long) x << 16) + y);
            }
        }
        return buildingCountSet.size();
    }

    private List<ImageTagEnum> getOutbuildingTags() {
        return Iterables.toStream(
                        imageTagDictProvider.findByGroup(
                                OUTBUILDINGS, ImageTagGroupType.BEES_AI.getValue()))
                .map(tag -> ImageTagEnum.valueOf(Integer.parseInt(tag.getId())))
                .collect(Collectors.toList());
    }
}
