package com.bees360.todo;

import com.bees360.job.registry.TodoNotificationAddedJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.util.DateTimes;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;

@Log4j2
public class TodoNotificationAddedJobExecutor
        extends AbstractJobExecutor<TodoNotificationAddedJob> {
    private final NotificationTodoRepository todoRepository;

    public TodoNotificationAddedJobExecutor(NotificationTodoRepository todoRepository) {
        this.todoRepository = todoRepository;
        log.info("Created {}.", this);
    }

    @Override
    protected void handle(TodoNotificationAddedJob job) throws IOException {
        log.info("Received job {} for add todo notification.", job);
        var userList = job.getUserIds();
        if (userList == null || userList.isEmpty()) {
            return;
        }
        var notification =
                com.bees360.todo.Message.Notification.newBuilder()
                        .setMessage(job.getContent())
                        .setSenderId(job.getSenderId())
                        .setProjectId(job.getProjectId())
                        .build();
        var todo =
                Todo.from(
                        Message.TodoMessage.newBuilder()
                                .setNotification(notification)
                                .setCreatedAt(DateTimes.toTimestamp(job.getUpdatedAt()))
                                .build());
        for (String receiver : userList) {
            todoRepository.addTodo(receiver, todo);
        }
    }
}
