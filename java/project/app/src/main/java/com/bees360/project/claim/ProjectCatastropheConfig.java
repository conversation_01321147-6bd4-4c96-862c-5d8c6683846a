package com.bees360.project.claim;

import com.bees360.policy.PolicyManager;
import com.bees360.project.JooqProjectCatastropheManager;
import com.bees360.project.ProjectIIRepository;

import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ProjectCatastropheConfig {
    @Bean({"grpcProjectCatastropheManager", "projectCatastropheManager"})
    DefaultProjectCatastropheManager grpcProjectCatastropheManager(
            @Qualifier("jooqProjectCatastropheManager")
                    ProjectCatastropheManager jooqProjectCatastropheManager,
            @Qualifier("jooqProjectIIRepository") ProjectIIRepository projectIIRepository,
            @Qualifier("jooqFullPolicyManager") PolicyManager policyManager) {
        return new DefaultProjectCatastropheManager(
                jooqProjectCatastropheManager, projectIIRepository, policyManager);
    }

    @Bean("jooqProjectCatastropheManager")
    JooqProjectCatastropheManager jooqProjectCatastropheManager(DSLContext dslContext) {
        return new JooqProjectCatastropheManager(dslContext);
    }
}
