package com.bees360.project.config;

import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.listener.SetDeliveryTaskReadyOnCloseoutReportGenerated;
import com.bees360.report.ReportProvider;
import com.bees360.report.config.GrpcReportManagerClientConfig;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Configuration
@Import({
    AutoRegisterEventListenerConfig.class,
    GrpcReportManagerClientConfig.class,
})
@EnableConfigurationProperties
@ConditionalOnProperty(prefix = "project.app.close-out", name = "enabled", havingValue = "true")
public class ProjectCloseoutConfig {

    @Configuration
    @ConfigurationProperties(prefix = "project.app.close-out")
    @Setter
    @Getter
    static class ProjectCloseoutProperties {

        private Map<String, CustomerCloseoutProperties> customer = new HashMap<>();

        private Set<String> deliveryTask = new HashSet<>();

        @Getter
        @Setter
        static class CustomerCloseoutProperties {
            private Boolean enabled = false;
        }
    }

    @Bean
    public SetDeliveryTaskReadyOnCloseoutReportGenerated
            scheduleDeliveryJobOnCloseoutReportGenerated(
                    ReportProvider reportProvider,
                    PipelineService pipelineService,
                    ProjectCloseoutProperties properties) {
        return new SetDeliveryTaskReadyOnCloseoutReportGenerated(
                reportProvider, pipelineService, properties.getDeliveryTask());
    }
}
