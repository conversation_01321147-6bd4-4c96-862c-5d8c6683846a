package com.bees360.project.listener;

import com.bees360.event.registry.DispatchUnitAssignedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.pipeline.assign.TaskAssignRecord;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.repository.Provider;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;

@Log4j2
public class ChangedTaskOwnerOnDuChanged
        extends AbstractNamedEventListener<DispatchUnitAssignedEvent> {
    @NonNull private final PipelineService pipelineService;
    @NonNull private final Provider<ProjectII> projectIIProvider;
    @NonNull private final UserProvider userProvider;
    private static final String GS_USER_PREFIX = "GS:";
    public static final List<String> DU_CLAIM_TASK_KEY =
            List.of("assign_to_pilot_claims", "du_contact_insured_claims", "assign_to_pilot_wg");
    public static final List<String> DU_P4P_TASK_KEY =
            List.of("assign_to_pilot_p4p", "du_contact_insured_p4p");
    /** White Glove Task Keys */
    public static final List<String> DU_WG_TASK_KEY =
            List.of("assign_to_pilot_wg", "du_contact_insured_wg");

    public static final List<String> DU_OTHER_UW_ASK_KEY =
            List.of("assign_to_pilot", "du_contact_insured");

    public ChangedTaskOwnerOnDuChanged(
            @NonNull PipelineService pipelineService,
            @NonNull Provider<ProjectII> projectIIProvider,
            @NonNull UserProvider userProvider) {
        this.pipelineService = pipelineService;
        this.projectIIProvider = projectIIProvider;
        this.userProvider = userProvider;
        log.info(
                "Created '{}(pipelineService={}, projectIIProvider={}, userProvider={})'",
                this,
                this.pipelineService,
                this.projectIIProvider,
                this.userProvider);
    }

    @Override
    public boolean handle(DispatchUnitAssignedEvent event) throws IOException {
        var pipelineId = event.getProjectId();
        var ownerId = event.getDuUserId();
        // TODO 目前默认 DU 是google 用户，并且google用户在后台系统会存在 GS: 前缀
        if (ownerId != null && !StringUtils.startsWith(ownerId, GS_USER_PREFIX)) {
            ownerId = GS_USER_PREFIX + ownerId;
        }

        if (ownerId != null) {
            // check user existence
            var user = userProvider.getUser(ownerId);
            Preconditions.checkState(user != null, "Cannot find user with id '%s'", ownerId);
        }

        var projectII = projectIIProvider.get(pipelineId);
        List<String> keys;
        if (ProjectTypeEnum.CLAIM.equals(projectII.getProjectType())) {
            keys = DU_CLAIM_TASK_KEY;
        } else if (ServiceTypeEnum.PREMIUM_FOUR_POINT.equals(projectII.getServiceType())) {
            keys = DU_P4P_TASK_KEY;
        } else if (ServiceTypeEnum.WHITE_GLOVE.equals(projectII.getServiceType())) {
            keys = DU_WG_TASK_KEY;
        } else {
            keys = DU_OTHER_UW_ASK_KEY;
        }
        // auto change task owner when du changed.
        var finalOwnerId = ownerId;
        var operationUserId = event.getOperationUserId();
        var setTaskRequest =
                Iterables.transform(
                        keys,
                        key ->
                                TaskAssignRecord.buildTaskToBeAssigned(
                                        finalOwnerId, pipelineId, key));
        pipelineService.batchSetTaskOwner(
                setTaskRequest,
                Message.TaskAssignChannelEnum.UNDEFINED,
                event.getClass().getSimpleName(),
                operationUserId);
        return true;
    }
}
