package com.bees360.project.config;

import com.bees360.project.listener.CreateActivityOnProjectStatusEvent;
import com.bees360.project.listener.CreateRollbackActivityOnProjectStatusChanged;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;

@Import({
    CreateActivityOnProjectStatusEvent.class,
    CreateRollbackActivityOnProjectStatusChanged.class,
})
@EnableConfigurationProperties
@ConditionalOnProperty(name = "project.app.status.activity-listener.enable", havingValue = "true")
public class CreateActivityOnProjectStatusEventConfig {}
