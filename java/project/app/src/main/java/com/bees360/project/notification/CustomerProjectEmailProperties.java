package com.bees360.project.notification;

import com.bees360.project.notification.ProjectEmailRecipientSelector.RecipientSelector;

import lombok.Data;

import java.util.Collection;
import java.util.HashSet;

@Data
public class CustomerProjectEmailProperties {
    private Boolean subscribed;
    // template key for email
    private String templateKey;
    private boolean toSelectorOverrideDefault;
    private boolean ccSelectorOverrideDefault;
    private boolean bccSelectorOverrideDefault;
    // selector rules for email recipient
    private Collection<RecipientSelector> toSelector = new HashSet<>();
    // selector rules for email cc
    private Collection<RecipientSelector> ccSelector = new HashSet<>();
    // selector rules for email bcc
    private Collection<RecipientSelector> bccSelector = new HashSet<>();

    public boolean isSubscribed() {
        return Boolean.TRUE.equals(subscribed);
    }
}
