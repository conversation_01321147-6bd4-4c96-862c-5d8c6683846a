package com.bees360.project.config;

import com.alibaba.excel.util.StringUtils;
import com.bees360.api.InvalidArgumentException;
import com.bees360.apolloconfig.annotation.RefreshableConfigurationProperties;
import com.bees360.building.Message.BuildingType;
import com.bees360.customer.CustomerProvider;
import com.bees360.project.BuildingManager;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectPolicyManager;
import com.bees360.project.ValidationProjectPolicyManager;
import com.bees360.project.creator.PolicyValidationProjectCreationPreHandler;
import com.bees360.project.status.ProjectStatusProvider;
import com.pivovarit.function.ThrowingBiConsumer;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.function.TriFunction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Map;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.BiPredicate;
import java.util.function.Supplier;

@Log4j2
@Import({
    ProjectCustomerConfig.class,
    ProjectGlobalOptionsProperties.class,
})
@Configuration
@EnableConfigurationProperties
public class ProjectPolicyValidationConfig {

    private static final String MISSING_POLICY_TYPE = "MISSING_POLICY_TYPE";
    private static final String MISSING_PROPERTY_TYPE = "MISSING_PROPERTY_TYPE";
    private static final String MISSING_POLICY_TYPE_PROPERTY_TYPE =
            "MISSING_POLICY_TYPE_PROPERTY_TYPE";
    private static final String INVALID_POLICY_TYPE = "INVALID_POLICY_TYPE";
    private static final String INVALID_PROPERTY_TYPE = "INVALID_PROPERTY_TYPE";

    private TriFunction<String, String, Integer, Integer> policyTypePropertyTypeValidator(
            ProjectGlobalOptionsProperties properties,
            ProjectPolicyValidationProperties validationProperties) {

        ThrowingBiConsumer<String, String, InvalidArgumentException> throwingValidationBiConsumer =
                (creationChannel, errorCode) -> {
                    var message = validationProperties.getErrorMessage(creationChannel, errorCode);
                    throw new InvalidArgumentException(message);
                };

        return (creationChannel, policyType, propertyType) -> {
            log.debug(
                    "Validate policy type and property type with: creationChannel={},"
                            + " policyType={}, propertyType={}",
                    creationChannel,
                    policyType,
                    propertyType);
            var isPropertyTypeMissing =
                    propertyType == null
                            || propertyType == BuildingType.UNKNOWN_BUILDING_TYPE_VALUE;
            if (StringUtils.isEmpty(policyType) && isPropertyTypeMissing) {
                throwingValidationBiConsumer.accept(
                        creationChannel, MISSING_POLICY_TYPE_PROPERTY_TYPE);
            } else if (StringUtils.isEmpty(policyType)) {
                throwingValidationBiConsumer.accept(creationChannel, MISSING_POLICY_TYPE);
            } else if (isPropertyTypeMissing) {
                throwingValidationBiConsumer.accept(creationChannel, MISSING_PROPERTY_TYPE);
            }
            var policyTypeProperties = properties.getPolicyType();
            if (policyTypeProperties == null || !policyTypeProperties.containsKey(policyType)) {
                throwingValidationBiConsumer.accept(creationChannel, INVALID_POLICY_TYPE);
            }
            var propertyTypeProperties = policyTypeProperties.get(policyType);
            log.debug(
                    "PropertyTypeProperties({}) will be used to test policyType={} and"
                            + " propertyType={}.",
                    propertyTypeProperties,
                    policyType,
                    propertyType);
            if (propertyTypeProperties == null
                    || CollectionUtils.isEmpty(propertyTypeProperties.getPropertyType())) {
                throwingValidationBiConsumer.accept(creationChannel, INVALID_POLICY_TYPE);
            }
            if (!propertyTypeProperties.getPropertyType().contains(propertyType)) {
                throwingValidationBiConsumer.accept(creationChannel, INVALID_PROPERTY_TYPE);
            }
            return propertyType;
        };
    }

    @Data
    static class ProjectPolicyValidationProperties {
        private final String GLOBAL_CREATION_CHANNEL = "*";

        private boolean validatePolicyTypeAndPropertyTypeWhenUpdate = true;
        private Set<Integer> denyToUpdateIfStatusNotIn = Set.of();
        private Map<String, Map<String, String>> errorMessage =
                Map.of(GLOBAL_CREATION_CHANNEL, Map.of());

        public String getErrorMessage(String creationChannel, String errorCode) {
            var channelErrorMessage = errorMessage.get(creationChannel);
            if (channelErrorMessage != null && channelErrorMessage.containsKey(errorCode)) {
                return channelErrorMessage.get(errorCode);
            }
            return errorMessage.get(GLOBAL_CREATION_CHANNEL).getOrDefault(errorCode, "Invalid.");
        }
    }

    @Bean
    @RefreshableConfigurationProperties("project.app.policy.validation-policy-type")
    ProjectPolicyValidationProperties projectPolicyValidationProperties() {
        return new ProjectPolicyValidationProperties();
    }

    @Data
    static class CustomerFeatureSwitchProperties {
        private Map<String, Map<String, Boolean>> featureSwitch = Map.of();
    }

    @Bean
    @RefreshableConfigurationProperties("project.app.customer")
    CustomerFeatureSwitchProperties customerFeatureSwitchProperties() {
        return new CustomerFeatureSwitchProperties();
    }

    @Bean
    public PolicyValidationProjectCreationPreHandler policyValidationProjectCreationPreHandler(
            @Qualifier("customerFeaturePredicate")
                    BiPredicate<String, String> customerFeaturePredicate,
            CustomerProvider customerProvider,
            ProjectPolicyValidationProperties properties,
            ProjectGlobalOptionsProperties projectGlobalOptionsProperties) {
        final var featurePrefix = "valid-policy-type-property-type-in-";
        BiPredicate<String, String> insuredByPredicate =
                (insuredBy, creationChannel) -> {
                    var customer = customerProvider.findById(insuredBy);
                    var customerKey = customer.getCompanyKey();
                    return customerFeaturePredicate.test(customerKey, featurePrefix + "ALL")
                            || customerFeaturePredicate.test(
                                    customerKey, featurePrefix + creationChannel);
                };
        return new PolicyValidationProjectCreationPreHandler(
                policyTypePropertyTypeValidator(projectGlobalOptionsProperties, properties),
                insuredByPredicate);
    }

    @Bean("customerFeaturePredicate")
    public BiPredicate<String, String> customerFeaturePredicate(
            CustomerFeatureSwitchProperties customerFeatureSwitchProperties) {
        return (customerKey, featureName) -> {
            var customerSwitch =
                    customerFeatureSwitchProperties.getFeatureSwitch().get(customerKey);
            if (customerSwitch != null && customerSwitch.containsKey(featureName)) {
                return customerSwitch.get(featureName);
            }
            var allCustomerSwitch = customerFeatureSwitchProperties.getFeatureSwitch().get("*");
            if (allCustomerSwitch != null && allCustomerSwitch.containsKey(featureName)) {
                return allCustomerSwitch.get(featureName);
            }
            return false;
        };
    }

    @Bean
    ProjectPolicyManager projectPolicyManager(
            ProjectIIManager projectIIManager,
            BuildingManager buildingManager,
            ProjectStatusProvider projectStatusProvider,
            ProjectPolicyValidationProperties policyConfigProperties,
            ProjectGlobalOptionsProperties projectGlobalOptionsProperties) {

        Supplier<Set<Integer>> denyToUpdatePolicyTypeIfStatusNotIn =
                policyConfigProperties::getDenyToUpdateIfStatusNotIn;

        var validator =
                policyTypePropertyTypeValidator(
                        projectGlobalOptionsProperties, policyConfigProperties);

        BiFunction<String, Integer, Integer> typeValidator =
                (policyType, propertyType) -> {
                    if (policyConfigProperties.isValidatePolicyTypeAndPropertyTypeWhenUpdate()) {
                        return validator.apply("ANY", policyType, propertyType);
                    }
                    return propertyType;
                };

        return new ValidationProjectPolicyManager(
                projectIIManager,
                buildingManager,
                typeValidator,
                projectStatusProvider,
                denyToUpdatePolicyTypeIfStatusNotIn);
    }
}
