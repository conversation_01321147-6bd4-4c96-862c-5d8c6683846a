package com.bees360.project.config;

import com.bees360.event.autoconfig.EnableEventAutoRegister;
import com.bees360.job.AssembleProjectSummaryJobExecutor;
import com.bees360.job.JobScheduler;
import com.bees360.job.autoconfig.EnableJobAutoRegister;
import com.bees360.lambda.AwsLambdaClientConfig;
import com.bees360.lambda.LambdaFunction;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.project.summary.AssembleProjectSummaryOnReturned;
import com.bees360.project.summary.ProjectSummaryManager;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    AwsLambdaClientConfig.class,
})
@Configuration
@EnableEventAutoRegister
@EnableJobAutoRegister
@ConditionalOnProperty(
        prefix = "project.app.assemble-project-summary",
        name = "enabled",
        havingValue = "true")
public class AssembleProjectSummaryConfig {

    @Bean
    AssembleProjectSummaryOnReturned assembleProjectSummaryOnReturned(JobScheduler jobScheduler) {
        return new AssembleProjectSummaryOnReturned(jobScheduler);
    }

    @Bean
    AssembleProjectSummaryJobExecutor assembleProjectSummaryJobExecutor(
            LambdaFunction lambdaFunction,
            ProjectSummaryManager projectSummaryManager,
            ProjectIIRepository projectIIRepository,
            ProjectReportProvider projectReportProvider) {
        return new AssembleProjectSummaryJobExecutor(
                lambdaFunction, projectSummaryManager, projectIIRepository, projectReportProvider);
    }
}
