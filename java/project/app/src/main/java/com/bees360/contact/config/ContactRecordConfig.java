package com.bees360.contact.config;

import com.bees360.activity.ActivityManager;
import com.bees360.contact.ContactRecordManager;
import com.bees360.contact.DefaultContactRecordManager;
import com.bees360.contact.GrpcContactRecordService;
import com.bees360.contact.JooqContactRecordManager;
import com.bees360.contact.JooqContactRecordStatisticsProvider;
import com.bees360.contact.listener.AddContactRecordOnBeespilotConversationAdded;
import com.bees360.contact.listener.AddContactRecordOnPilotCallRecordAdded;
import com.bees360.contact.listener.AddContactRecordOnPilotFeedbackAdded;
import com.bees360.user.UserKeyProvider;
import com.bees360.user.UserProvider;
import com.bees360.user.config.GrpcUserKeyProviderConfig;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;

import java.util.function.UnaryOperator;

@Configuration
@Import({
    JooqContactRecordManagerConfig.class,
    JooqContactRecordStatisticsProvider.class,
    ContactRecordConfig.AddContactRecordOnPilotCallRecordAddedConfig.class,
    GrpcContactRecordService.class,
    GrpcUserKeyProviderConfig.class,
})
public class ContactRecordConfig {

    @Configuration
    public static class AddContactRecordOnPilotCallRecordAddedConfig {

        @Bean
        AddContactRecordOnPilotCallRecordAdded addContactRecordOnPilotCallRecordAdded(
                ContactRecordManager contactRecordManager,
                UserProvider userProvider,
                UserKeyProvider userKeyProvider) {
            var externalUserIdStandardizer =
                    externalUserIdStandardizer(userProvider, userKeyProvider);
            return new AddContactRecordOnPilotCallRecordAdded(
                    contactRecordManager, externalUserIdStandardizer);
        }

        private UnaryOperator<String> externalUserIdStandardizer(
                UserProvider userProvider, UserKeyProvider userKeyProvider) {
            return userId -> {
                var user = userKeyProvider.findUserByKey(userId);
                if (user != null) {
                    return user.getId();
                }
                user = userProvider.findUserById(userId);
                if (user != null) {
                    return user.getId();
                }
                return userId;
            };
        }
    }

    @Bean
    @Primary
    public DefaultContactRecordManager defaultContactRecordManager(
            JooqContactRecordManager jooqContactRecordManager,
            UserProvider userProvider,
            ActivityManager activityManager) {
        return new DefaultContactRecordManager(
                jooqContactRecordManager, activityManager, userProvider);
    }

    @Bean
    public AddContactRecordOnPilotFeedbackAdded addContactRecordOnPilotFeedbackAdded(
            JooqContactRecordManager jooqContactRecordManager) {
        return new AddContactRecordOnPilotFeedbackAdded(jooqContactRecordManager);
    }

    @Bean
    public AddContactRecordOnBeespilotConversationAdded
            addContactRecordOnBeespilotConversationAdded(
                    JooqContactRecordManager jooqContactRecordManager, UserProvider userProvider) {
        return new AddContactRecordOnBeespilotConversationAdded(
                jooqContactRecordManager, userProvider);
    }
}
