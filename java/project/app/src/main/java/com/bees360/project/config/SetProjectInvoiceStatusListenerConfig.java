package com.bees360.project.config;

import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.invoice.ProjectInvoiceManager;
import com.bees360.project.listener.SetProjectInvoiceFinalizedOnProjectInvoiceAdded;
import com.bees360.project.listener.SetProjectInvoiceStatusOnPipelineTaskReady;
import com.bees360.project.listener.SetProjectInvoiceStatusOnReportStatusChanged;
import com.bees360.project.report.ProjectReportProvider;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({
    SetProjectInvoiceFinalizedOnProjectInvoiceAdded.class,
    JooqProjectInvoiceRepositoryConfig.class,
    AutoRegisterEventListenerConfig.class,
})
@ConditionalOnProperty(
        prefix = "project.app.invoice",
        name = "enable-invoice-status",
        havingValue = "true")
public class SetProjectInvoiceStatusListenerConfig {

    @Bean
    public SetProjectInvoiceStatusOnPipelineTaskReady setInvoiceStatusOnPipelineTaskReady(
            ProjectInvoiceManager projectInvoiceManager,
            ProjectReportProvider projectReportProvider,
            PipelineService pipelineService) {
        return new SetProjectInvoiceStatusOnPipelineTaskReady(
                projectInvoiceManager, projectReportProvider, pipelineService);
    }

    @Bean
    public SetProjectInvoiceStatusOnReportStatusChanged setInvoiceStatusOnReportStatusChanged(
            ProjectInvoiceManager projectInvoiceManager,
            ProjectReportProvider projectReportProvider,
            PipelineService pipelineService) {
        return new SetProjectInvoiceStatusOnReportStatusChanged(
                projectInvoiceManager, projectReportProvider, pipelineService);
    }
}
