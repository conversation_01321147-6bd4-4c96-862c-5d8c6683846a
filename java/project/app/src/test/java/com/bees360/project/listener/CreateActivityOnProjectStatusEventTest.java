package com.bees360.project.listener;

import com.bees360.activity.ActivityManager;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.ProjectStatusHistoryInserted;
import com.bees360.project.RandomProjectUtil;

import org.apache.commons.lang3.RandomStringUtils;
import org.jmock.lib.concurrent.DeterministicScheduler;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

import java.time.Instant;

@SpringBootTest(properties = "grpc.server.port=0")
@DirtiesContext
public class CreateActivityOnProjectStatusEventTest {

    @Configuration
    @ApplicationAutoConfig
    @Import({CreateActivityOnProjectStatusEvent.class})
    static class Config {

        @Bean
        DeterministicScheduler deterministicScheduler() {
            return new DeterministicScheduler();
        }

        @Bean
        public InMemoryEventPublisher inMemoryEventPublisher(
                DeterministicScheduler deterministicScheduler) {
            return new InMemoryEventPublisher(deterministicScheduler);
        }
    }

    @Autowired private CreateActivityOnProjectStatusEvent listener;

    @Autowired private InMemoryEventPublisher publisher;

    @Autowired private DeterministicScheduler deterministicScheduler;

    @MockBean private ActivityManager activityManager;

    @Test
    void testCreateActivitySucceed() {
        publisher.enlist(listener);

        var event = new ProjectStatusHistoryInserted();
        event.setProjectId(RandomProjectUtil.getRandomId());
        event.setStatus(10);
        event.setUpdatedAt(Instant.now());
        event.setUpdatedBy(RandomStringUtils.randomAlphabetic(12));
        event.setComment(RandomStringUtils.randomAlphabetic(12));
        publisher.publish(event);
        deterministicScheduler.runUntilIdle();
        Mockito.verify(activityManager, Mockito.never()).submitActivity(Mockito.any());

        event.setStatus(30);
        publisher.publish(event);
        deterministicScheduler.runUntilIdle();
        Mockito.verify(activityManager, Mockito.times(1)).submitActivity(Mockito.any());
    }
}
