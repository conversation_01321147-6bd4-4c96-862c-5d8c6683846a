package com.bees360.event;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.event.registry.ProjectGroupUpgradedAddedEvent;
import com.bees360.project.ServiceTypeEnum;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.time.Instant;

class AddActivityOnProjectGroupUpgradedAddedTest {

    @Mock private ActivityManager activityManager;

    @Captor private ArgumentCaptor<Activity> activityCaptor;

    private AddActivityOnProjectGroupUpgradedAdded listener;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        listener = new AddActivityOnProjectGroupUpgradedAdded(activityManager);
        when(activityManager.submitActivity(any()))
                .thenReturn(RandomStringUtils.randomAlphanumeric(24));
    }

    @Test
    void testHandleEventSuccessfully() throws IOException {
        // Given
        String projectId = "11226";
        String createdBy = "1000074";
        String groupKey = "SERVICE_TYPE:13->9";
        String groupType = "GROUP_UPGRADED";
        String createdAt = Instant.now().toString();

        ProjectGroupUpgradedAddedEvent event =
                new ProjectGroupUpgradedAddedEvent(
                        projectId, groupKey, groupType, createdBy, createdAt);

        // When
        listener.handle(event);

        // Then
        verify(activityManager, times(1)).submitActivity(activityCaptor.capture());

        Activity capturedActivity = activityCaptor.getValue();
        Assertions.assertEquals(Long.parseLong(projectId), capturedActivity.getProjectId());
        Assertions.assertEquals(
                Message.ActivityMessage.ActionType.CHANGE.name(), capturedActivity.getAction());
        Assertions.assertEquals(createdBy, capturedActivity.getCreatedBy());
        Assertions.assertEquals(
                Message.ActivityMessage.EntityType.PROJECT.name(),
                capturedActivity.getEntityType());
        Assertions.assertEquals("SERVICE_TYPE", capturedActivity.getFiledName());

        // 验证服务类型值是否正确转换
        ServiceTypeEnum oldType = ServiceTypeEnum.LIMITED_EXTERIOR_UNDERWRITING; // code 13
        ServiceTypeEnum newType = ServiceTypeEnum.WHITE_GLOVE; // code 9
        Assertions.assertEquals(oldType.getName(), capturedActivity.getOldValue());
        Assertions.assertEquals(newType.getName(), capturedActivity.getValue());

        // 验证评论内容
        Assertions.assertNotNull(capturedActivity.getComment());
        Assertions.assertEquals("Reason: Upgrade", capturedActivity.getComment().getContent());
    }

    @Test
    void testHandleEventWithInvalidGroupKey() {
        // Given
        String projectId = "11226";
        String createdBy = "1000074";
        String invalidGroupKey = "INVALID_FORMAT";
        String groupType = "SERVICE_TYPE";
        String createdAt = Instant.now().toString();

        ProjectGroupUpgradedAddedEvent event =
                new ProjectGroupUpgradedAddedEvent(
                        projectId, invalidGroupKey, groupType, createdBy, createdAt);

        // When & Then
        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> {
                    listener.handle(event);
                });
    }

    @Test
    void testHandleEventWithInvalidServiceTypeCode() {
        // Given
        String projectId = "11226";
        String createdBy = "1000074";
        String invalidGroupKey = "SERVICE_TYPE:999->888"; // 不存在的服务类型代码
        String groupType = "SERVICE_TYPE";
        String createdAt = Instant.now().toString();

        ProjectGroupUpgradedAddedEvent event =
                new ProjectGroupUpgradedAddedEvent(
                        projectId, invalidGroupKey, groupType, createdBy, createdAt);

        // When & Then
        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> {
                    listener.handle(event);
                });
    }
}
