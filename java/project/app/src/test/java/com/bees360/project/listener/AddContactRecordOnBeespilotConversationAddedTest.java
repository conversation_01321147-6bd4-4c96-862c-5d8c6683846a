package com.bees360.project.listener;

import com.bees360.contact.ContactRecord;
import com.bees360.contact.ContactRecordManager;
import com.bees360.contact.listener.AddContactRecordOnBeespilotConversationAdded;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.BeespilotConversationEvent;
import com.bees360.user.Message;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.Executor;

@SpringBootTest
public class AddContactRecordOnBeespilotConversationAddedTest {
    @Configuration
    @Import({
        InMemoryEventPublisher.class,
        AddContactRecordOnBeespilotConversationAdded.class,
        AutoRegisterEventListenerConfig.class
    })
    static class Config {
        @MockBean ContactRecordManager contactRecordManager;

        @MockBean UserProvider userProvider;

        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @Autowired private EventPublisher eventPublisher;
    @Autowired private ContactRecordManager contactRecordManager;
    @Autowired private UserProvider userProvider;

    @Test
    void testAddContactRecordOnBeespilotConversationAdded() {
        var projectId = RandomStringUtils.randomNumeric(8);
        var content = RandomStringUtils.randomAlphabetic(6);
        var updatedAt = Instant.now().toEpochMilli();
        var createdBy = RandomStringUtils.randomNumeric(32);
        var fromUser = new BeespilotConversationEvent.User();
        fromUser.setId(RandomStringUtils.randomAlphanumeric(32));
        fromUser.setName("name");
        fromUser.setRole("Pilot");
        var toUser = new BeespilotConversationEvent.User();
        toUser.setId(RandomStringUtils.randomAlphanumeric(32));
        toUser.setName("name");
        toUser.setRole("Insured");

        var event = new BeespilotConversationEvent();
        event.setProjectId(projectId);
        // Email/CallResult/Message
        event.setType("CallResult");
        event.setContent(content);
        event.setFrom(fromUser);
        event.setTo(toUser);
        event.setUpdatedAt(updatedAt);
        event.setVisibility(List.of("IO", "AI"));

        Mockito.doReturn(User.from(Message.UserMessage.newBuilder().setId(createdBy).build()))
                .when(userProvider)
                .findUserById(fromUser.getId());

        eventPublisher.publish(event);

        ArgumentCaptor<ContactRecord> contactRecordArgumentCaptor =
                ArgumentCaptor.forClass(ContactRecord.class);
        Mockito.verify(contactRecordManager)
                .addContactRecord(contactRecordArgumentCaptor.capture());
        var actual = contactRecordArgumentCaptor.getValue();
        Assertions.assertEquals(projectId, actual.getProjectId());
        Assertions.assertEquals("PILOT", actual.getContactFrom());
        Assertions.assertEquals("INSURED", actual.getContactTo());
        Assertions.assertEquals(content, actual.getContent());
        Assertions.assertEquals(Instant.ofEpochMilli(updatedAt), actual.getCreatedAt());
        Assertions.assertEquals(createdBy, actual.getCreatedBy());
        Assertions.assertEquals(
                com.bees360.contact.Message.ContactRecordMessage.ContactMethod.PHONE_CALL.name(),
                actual.getContactMethod());
    }
}
