package com.bees360.project.listener;

import static com.bees360.pipeline.Message.PipelineStatus.DONE;
import static com.bees360.pipeline.Message.PipelineStatus.ERROR;
import static com.bees360.pipeline.TestUtils.convertToSavePipelineDefRequest;
import static com.bees360.pipeline.TestUtils.randomKey;
import static com.bees360.pipeline.TestUtils.randomPipelineDef;
import static com.bees360.pipeline.TestUtils.randomTaskDef;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.bees360.codec.UniversalCodec;
import com.bees360.estintel.FactorValueProvider;
import com.bees360.event.EventDispatcher;
import com.bees360.event.EventPublisher;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.image.ImageTagEnum;
import com.bees360.job.Job;
import com.bees360.job.JobDispatcher;
import com.bees360.job.JobScheduler;
import com.bees360.job.ReportJobNames;
import com.bees360.job.registry.MapInferaAnnotationJob;
import com.bees360.job.registry.SaveReportJob;
import com.bees360.job.util.AbstractAsyncJobExecutor;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.map.BasicMap;
import com.bees360.pipeline.InMemoryPipelineDefService;
import com.bees360.pipeline.InMemoryPipelineService;
import com.bees360.pipeline.InMemoryProjectPipelineConfigService;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineDef;
import com.bees360.pipeline.PipelineDefService;
import com.bees360.pipeline.PipelineService;
import com.bees360.pipeline.PipelineTask;
import com.bees360.pipeline.PipelineTaskDef;
import com.bees360.pipeline.ProjectPipelineConfigService;
import com.bees360.project.config.ScheduleJobOnPipelineReadyConfig;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.Message.ReportMessage.Status;
import com.bees360.report.Report;
import com.bees360.report.util.InMemoryReport;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.ByteString;

import lombok.SneakyThrows;

import org.apache.commons.lang3.function.TriFunction;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.util.concurrent.SettableListenableFuture;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/** 这个类测试会和ProjectApp测试类，暂时disable部分测试 */
@SpringBootTest
@DirtiesContext
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
public class ScheduleJobOnPipelineReadyTest {
    @Configuration
    @Import(
            value = {
                InMemoryProjectPipelineConfigService.class,
                ScheduleJobOnPipelineReadyConfig.class
            })
    static class Config {
        @Bean
        public PipelineDefService pipelineDefService() {
            return new InMemoryPipelineDefService();
        }

        @Bean
        public PipelineService pipelineService(PipelineDefService pipelineDefService) {
            return new InMemoryPipelineService(pipelineDefService);
        }

        @Bean
        @Primary
        ProjectReportProvider projectReportProvider() {
            return new ProjectReportProvider() {
                @Override
                public Iterable<? extends Report> find(
                        String projectId, String reportType, Status status) {
                    var report = new InMemoryReport();
                    report.setId(randomKey());
                    report.setResourceUrl(
                            Map.of(
                                    com.bees360.report.Message.ReportMessage.Resource.Type.ORIGIN,
                                    randomKey()));
                    report.setResourceKey(
                            Map.of(
                                    com.bees360.report.Message.ReportMessage.Resource.Type.ORIGIN,
                                    randomKey()));
                    return Set.of(report);
                }

                @Override
                public Iterable<? extends Report> findInHistory(
                        String projectId, String reportType, Status status) {
                    return null;
                }

                @Override
                public Iterable<String> findProjectId(String reportId) {
                    return Set.of(reportId);
                }
            };
        }

        @MockBean
        TriFunction<String, String, List<ImageTagEnum>, String> photoSheetJsonDataProvider;

        @MockBean(name = "reportJobDataProvider")
        Function<String, String> reportJobDataProvider;

        @MockBean(name = "projectMessageJsonProvider")
        Function<String, String> projectMessageJsonProvider;

        @MockBean(name = "factorValueProvider")
        FactorValueProvider factorValueProvider;
    }

    static class TestJobExecutor extends AbstractJobExecutor<SaveReportJob> {
        private final String jobName;

        private final boolean isFailed;

        public TestJobExecutor(String jobName, boolean isFailed) {
            this.jobName = jobName;
            this.isFailed = isFailed;
        }

        @Override
        protected void handle(SaveReportJob object) {
            if (isFailed) {
                throw new IllegalStateException();
            }
        }

        @Override
        public String getName() {
            return jobName;
        }
    }

    @Autowired private PipelineDefService pipelineDefService;
    @Autowired private PipelineService pipelineService;
    @Autowired private ProjectPipelineConfigService projectPipelineConfigService;
    @Autowired private EventPublisher eventPublisher;
    @Autowired private JobScheduler jobScheduler;
    @Autowired private JobDispatcher jobDispatcher;
    @Autowired private EventDispatcher eventDispatcher;
    @Autowired private BasicMap<String, String> jobKey2ProjectIdMap;

    @Test
    void testScheduleJobOnPipelineTaskReadyAndSetPipelineDoneOnJobComplete()
            throws InterruptedException {
        var job_name = randomKey();
        var taskDefKey = randomKey();
        var jobExecutor = new TestJobExecutor(job_name, false);
        jobDispatcher.enlist(jobExecutor);
        var projectId = randomKey();
        var eventListener =
                new ScheduleJobOnPipelineReady(
                        jobScheduler,
                        eventDispatcher,
                        pipelineService,
                        jobKey2ProjectIdMap,
                        taskDefKey,
                        job_name,
                        (id) -> Job.of(job_name, "test-" + id, ByteString.EMPTY));
        eventDispatcher.enlist(eventListener);

        createPipelineAndPublishTaskReady(taskDefKey, projectId);
        waitForExecute();
        Assertions.assertEquals(DONE, getTaskStatus(projectId));
    }

    @Test
    void testScheduleJobOnPipelineTaskReadyAndSetPipelineTaskErrorOnJobFailed() {
        var job_name = randomKey();
        var taskDefKey = randomKey();
        var jobExecutor = new TestJobExecutor(job_name, true);
        jobDispatcher.enlist(jobExecutor);
        var eventListener =
                new ScheduleJobOnPipelineReady(
                        jobScheduler,
                        eventDispatcher,
                        pipelineService,
                        jobKey2ProjectIdMap,
                        taskDefKey,
                        job_name,
                        (projectId) -> Job.of(job_name, "test-" + projectId, ByteString.EMPTY));
        eventDispatcher.enlist(eventListener);
        var projectId = randomKey();

        createPipelineAndPublishTaskReady(taskDefKey, projectId);
        waitForExecute();

        Assertions.assertEquals(ERROR, getTaskStatus(projectId));
    }

    @Test
    void testSetPipelineTaskErrorComment() {
        var job_name = randomKey();
        var taskDefKey = randomKey();
        var jobExecutor = new TestJobExecutor(job_name, true);
        jobDispatcher.enlist(jobExecutor);
        var comment = "Test task comment.";
        var eventListener =
                new ScheduleJobOnPipelineReady(
                        jobScheduler,
                        eventDispatcher,
                        pipelineService,
                        jobKey2ProjectIdMap,
                        taskDefKey,
                        job_name,
                        false,
                        (id, createdBy) -> Job.of(job_name, "test-" + id, ByteString.EMPTY),
                        comment);
        eventDispatcher.enlist(eventListener);
        var projectId = randomKey();

        createPipelineAndPublishTaskReady(taskDefKey, projectId);
        waitForExecute();

        Assertions.assertEquals(comment, getTask(projectId).getComment());
    }

    @Test
    @Disabled
    void testGenerateMpsOnPipelineTaskReady() {
        var job_name = ReportJobNames.GENERATE_MPS_JOB_NAME;
        var jobExecutor = new TestJobExecutor(job_name, false);
        jobDispatcher.enlist(jobExecutor);
        var taskDefKey = "generate_mps";
        var projectId = randomKey();
        createPipelineAndPublishTaskReady(taskDefKey, projectId);
        waitForExecute();

        Assertions.assertEquals(DONE, getTaskStatus(projectId));
    }

    @Test
    @Disabled
    void testGenerateDpsOnPipelineTaskReady() {
        var job_name = ReportJobNames.GENERATE_DPS_JOB_NAME;
        var jobExecutor = new TestJobExecutor(job_name, false);
        jobDispatcher.enlist(jobExecutor);
        var taskDefKey = "generate_dps";
        var projectId = randomKey();
        createPipelineAndPublishTaskReady(taskDefKey, projectId);
        waitForExecute();

        Assertions.assertEquals(DONE, getTaskStatus(projectId));
    }

    @Test
    @Disabled
    void testSplitMpsOnPipelineTaskReady() {
        var job_name = ReportJobNames.SPLIT_MPS_AND_UPLOADED_TO_XA;
        var jobExecutor = new TestJobExecutor(job_name, false);
        jobDispatcher.enlist(jobExecutor);
        var taskDefKey = "upload_mps_to_xa";
        var projectId = randomKey();
        createPipelineAndPublishTaskReady(taskDefKey, projectId);
        waitForExecute();

        Assertions.assertEquals(DONE, getTaskStatus(projectId));
    }

    @Test
    @Disabled
    void testSplitDpsOnPipelineTaskReady() {
        var job_name = ReportJobNames.SPLIT_DPS_AND_UPLOADED_TO_XA;
        var jobExecutor = new TestJobExecutor(job_name, false);
        jobDispatcher.enlist(jobExecutor);
        var taskDefKey = "upload_dps_to_xa";
        var projectId = randomKey();
        createPipelineAndPublishTaskReady(taskDefKey, projectId);
        waitForExecute();
        Assertions.assertEquals(DONE, getTaskStatus(projectId));
    }

    /** Temporarily disable ScheduleJobOnPipelineReadyTest. */
    @Test
    @Disabled
    void testGenerateSosOnPipelineTaskReady() {
        var job_name = ReportJobNames.GENERATE_PDF_REPORT;
        var jobExecutor = new TestJobExecutor(job_name, false);
        jobDispatcher.enlist(jobExecutor);
        var taskDefKey = "generate_sos";
        var projectId = randomKey();
        createPipelineAndPublishTaskReady(taskDefKey, projectId);
        waitForExecute();

        Assertions.assertEquals(DONE, getTaskStatus(projectId));
    }

    @Test
    @SneakyThrows
    @Disabled
    void testMapInferaAnnotation() {
        var taskDefKey = "map_infera_annotation";
        final SettableListenableFuture<MapInferaAnnotationJob> jobResult =
                new SettableListenableFuture<>();
        var jobExecutor =
                new AbstractAsyncJobExecutor<MapInferaAnnotationJob>() {
                    @Override
                    protected ListenableFuture<Void> accept(MapInferaAnnotationJob job) {
                        return Futures.submit(() -> setResult(job), MoreExecutors.directExecutor());
                    }

                    private void setResult(MapInferaAnnotationJob job) {
                        jobResult.set(job);
                    }
                };
        jobDispatcher.enlist(jobExecutor);
        var projectId = randomKey();
        createPipelineAndPublishTaskReady(taskDefKey, projectId);

        assertEquals(projectId, jobResult.get(20, TimeUnit.SECONDS).getProjectId());

        Message.PipelineStatus status = null;
        for (int i = 0; i < 20; i++) {
            status = getTaskStatus(projectId);
            if (status == DONE) {
                break;
            }
            Thread.sleep(100);
        }
        assertEquals(DONE, status);
    }

    @Test
    @Disabled
    void testMergeFSRAndHoverOnPipelineTaskReady() {
        var job_name = ReportJobNames.MERGE_FSR_HOVER_REPORT;
        var jobExecutor = new TestJobExecutor(job_name, false);
        jobDispatcher.enlist(jobExecutor);
        var taskDefKey = "merge_fsr_and_hover";
        var projectId = randomKey();
        createPipelineAndPublishTaskReady(taskDefKey, projectId);
        waitForExecute();

        Assertions.assertEquals(DONE, getTaskStatus(projectId));
    }

    private com.bees360.pipeline.Message.PipelineStatus getTaskStatus(String pipelineId) {
        return getTask(pipelineId).getStatus();
    }

    private PipelineTask getTask(String pipelineId) {
        var pipeline = pipelineService.findById(pipelineId);
        Assertions.assertNotNull(pipeline);
        var pipelineTask = Iterables.toList(pipeline.getTask());
        return pipelineTask.get(0);
    }

    private void createPipelineAndPublishTaskReady(String taskDefKey, String projectId) {
        var taskDef =
                PipelineTaskDef.from(
                        randomTaskDef().toMessage().toBuilder().setKey(taskDefKey).build());
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDef));
        pipelineDefService.savePipelineTaskDef(taskDef);
        pipelineDefService.create(convertToSavePipelineDefRequest(pipelineDef));

        pipelineService.createPipeline(projectId, pipelineDef.getKey());
        var event = new PipelineTaskChanged();
        var stage = new PipelineTaskChanged.State();
        event.setPipelineId(projectId);
        event.setTaskDefKey(taskDefKey);
        stage.setStatus(com.bees360.pipeline.Message.PipelineStatus.READY);
        event.setState(stage);
        eventPublisher.publish(
                "pipeline_task_changed.status_changed." + taskDefKey + ".ready",
                UniversalCodec.INSTANCE.encode(event));
    }

    private void waitForExecute() {
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }
    }
}
