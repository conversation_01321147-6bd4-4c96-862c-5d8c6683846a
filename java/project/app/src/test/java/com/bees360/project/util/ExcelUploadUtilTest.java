package com.bees360.project.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.bees360.project.util.annotation.DateTimeZone;
import com.bees360.project.util.easyexcel.ExcelUploadUtil;
import com.bees360.project.util.easyexcel.InstantConverter;
import com.bees360.project.util.easyexcel.LocalDateConverter;
import com.bees360.resource.InMemoryResourceRepository;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourceMetadata;

import lombok.Data;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.FileInputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

public class ExcelUploadUtilTest {

    @Data
    public static class TestCell {

        @ExcelProperty(value = "Project ID")
        private String projectId;

        @DateTimeFormat("dd/MM/yyyy HH:mm")
        @DateTimeZone("America/Chicago")
        @ExcelProperty(value = "date", converter = InstantConverter.class)
        private Instant date;

        @DateTimeFormat("dd/MM/yyyy")
        @ExcelProperty(value = "localDate", converter = LocalDateConverter.class)
        private LocalDate localDate;

        @NumberFormat("0.00")
        @ExcelProperty(value = "tax")
        private float tax;

        public TestCell(String projectId, Instant date, LocalDate localDate, float tax) {
            this.projectId = projectId;
            this.date = date;
            this.localDate = localDate;
            this.tax = tax;
        }

        public TestCell() {}
    }

    @Test
    public void excelUploadTest() throws URISyntaxException, IOException {
        var projectId = "1";
        var date = Instant.now().truncatedTo(ChronoUnit.MINUTES);
        var localDate = LocalDate.now();
        float tax = 1.1F;

        var pool = new InMemoryResourceRepository(new URI("/"));
        var util = new ExcelUploadUtil(pool);

        // upload excel file to resource pool
        util.writeThenUpload(
                "testExcel.xlsx",
                List.of(new TestCell(projectId, date, localDate, tax)),
                TestCell.class);
        // get from resource pool and test whether equal
        var testExcel = pool.get("testExcel.xlsx");
        var list = new ArrayList<TestCell>();
        EasyExcel.read(
                        testExcel.open(),
                        TestCell.class,
                        new AnalysisEventListener<TestCell>() {
                            @Override
                            public void invoke(TestCell data, AnalysisContext context) {
                                list.add(data);
                            }

                            public void doAfterAllAnalysed(AnalysisContext analysisContext) {}
                        })
                .sheet()
                .doRead();

        Assertions.assertEquals(1, list.size());
        var cell = list.get(0);
        Assertions.assertEquals(projectId, cell.getProjectId());
        Assertions.assertEquals(date.toEpochMilli(), cell.getDate().toEpochMilli());
        Assertions.assertEquals(localDate.toEpochDay(), cell.getLocalDate().toEpochDay());
        Assertions.assertEquals(tax, cell.getTax());
    }

    @Test
    public void testDuplicateGetInputStreamWhenUploaded()
            throws URISyntaxException, InterruptedException {
        var pool = new InMemoryResourceRepository(new URI("/"));
        var util = new ExcelUploadUtil(pool);

        var testExcel =
                util.writeThenUpload(
                        "testExcelDuplicate.xlsx",
                        List.of(new TestCell("1", Instant.now(), LocalDate.now(), 1.1F)),
                        TestCell.class);

        var m1 = testExcel.apply(ResourceMetadata::extractFrom);
        // 过一段时间，如果是相同的文件，则创建时间是相同的，如果不是相同文件则创建时间相差1s，计算的md5就会不一样
        Thread.sleep(1000);
        var m2 = testExcel.apply(ResourceMetadata::extractFrom);
        Assertions.assertEquals(m1.getContentMD5(), m2.getContentMD5());
        Assertions.assertEquals(m1.getContentLength(), m2.getContentLength());
        Assertions.assertEquals(m1.getETag(), m2.getETag());
        Assertions.assertEquals(m1.getLastModified(), m2.getLastModified());
    }

    @Test
    public void excelUploadWithTemplateTest() throws URISyntaxException, IOException {
        var projectId = "1";
        var date = Instant.now().truncatedTo(ChronoUnit.MINUTES);
        var localDate = LocalDate.now();
        float tax = 1.1F;

        var pool = new InMemoryResourceRepository(new URI("/"));
        var util = new ExcelUploadUtil(pool);

        var file = this.getClass().getResource("/testTemplate.xlsx").getFile();
        var r = Resource.of(new FileInputStream(file), ResourceMetadata.newBuilder().build());
        pool.put("templateKey.xlsx", r);

        // upload excel file to resource pool
        util.writeThenUpload(
                "testExcel.xlsx",
                "templateKey.xlsx",
                List.of(new TestCell(projectId, date, localDate, tax)),
                TestCell.class);
        // get from resource pool and test whether equal
        var testExcel = pool.get("testExcel.xlsx");
        var list = new ArrayList<TestCell>();
        EasyExcel.read(
                        testExcel.open(),
                        TestCell.class,
                        new AnalysisEventListener<TestCell>() {
                            @Override
                            public void invoke(TestCell data, AnalysisContext context) {
                                list.add(data);
                            }

                            public void doAfterAllAnalysed(AnalysisContext analysisContext) {}
                        })
                .sheet()
                .doRead();

        Assertions.assertEquals(1, list.size());
        var cell = list.get(0);
        Assertions.assertEquals(projectId, cell.getProjectId());
        Assertions.assertEquals(date.toEpochMilli(), cell.getDate().toEpochMilli());
        Assertions.assertEquals(localDate.toEpochDay(), cell.getLocalDate().toEpochDay());
        Assertions.assertEquals(tax, cell.getTax());
    }
}
