package com.bees360.project.notification;

import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

import com.bees360.building.Message;
import com.bees360.contract.Contract;
import com.bees360.customer.Customer;
import com.bees360.event.registry.ProjectCreatedEvent;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.policy.Policy;
import com.bees360.policy.PolicyManager;
import com.bees360.project.Building;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class NotedCommercialProjectCreatedNotifyTest {

    @Configuration
    @Import({})
    static class Config {}

    @MockitoBean private ProjectIIRepository projectIIRepository;
    @MockitoBean private PolicyManager policyManager;
    @MockitoBean private JobScheduler jobScheduler;

    private NotedCommercialProjectCreatedNotify notifier;
    private static final String templateKey = "commercial-project-created-template";
    private static final List<String> toRecipients =
            List.of("<EMAIL>", "<EMAIL>");
    private final Gson gson = new Gson();

    @BeforeEach
    void init() {
        notifier =
                new NotedCommercialProjectCreatedNotify(
                        jobScheduler, projectIIRepository, templateKey, toRecipients);
    }

    @Test
    void testFilterCommercialProject() {
        // prepare test data
        String projectId = "100001";

        // create mock project
        var project = Mockito.mock(ProjectII.class);
        var policy = Mockito.mock(Policy.class);
        when(policy.getType()).thenReturn("Commercial Property Insurance");
        when(project.getPolicy()).thenReturn(policy);
        when(project.getNote()).thenReturn("Test Note");
        when(projectIIRepository.findById(eq(projectId))).thenReturn(project);

        // create event
        ProjectCreatedEvent event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        // validate filter
        boolean result = notifier.filter(event);
        Assertions.assertTrue(result);
    }

    @Test
    void testFilterNonCommercialProject() {
        // prepare test data
        String projectId = "10001";

        var project = Mockito.mock(ProjectII.class);
        var policy = Mockito.mock(Policy.class);
        when(policy.getType()).thenReturn("Other Policy Type");
        when(projectIIRepository.findById(eq(projectId))).thenReturn(project);
        when(project.getNote()).thenReturn("Test Note");
        when(project.getPolicy()).thenReturn(policy);

        ProjectCreatedEvent event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        // validate filter
        boolean result = notifier.filter(event);
        Assertions.assertFalse(result);
    }

    @Test
    void testFilterNonNoteProject() {

        // prepare test data
        String projectId = "10001";
        var project = Mockito.mock(ProjectII.class);
        var policy = Mockito.mock(Policy.class);
        when(policy.getType()).thenReturn("Commercial Property Insurance");
        when(projectIIRepository.findById(eq(projectId))).thenReturn(project);
        when(project.getPolicy()).thenReturn(policy);

        ProjectCreatedEvent event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        // validate filter
        boolean result = notifier.filter(event);
        Assertions.assertFalse(result);
    }

    @Test
    void testConvertToJob() {
        // prepare test data
        String projectId = "10001";

        var project = Mockito.mock(ProjectII.class);
        var policy = Mockito.mock(Policy.class);
        var contract = Mockito.mock(Contract.class);
        var building = Mockito.mock(Building.class);
        when(building.getType()).thenReturn(Message.BuildingType.COMMERCIAL);
        when(policy.getBuilding()).thenReturn(building);
        when(project.getPolicy()).thenReturn(policy);
        when(project.getNote()).thenReturn("Test Note");
        when(project.getId()).thenReturn(projectId);
        when(project.getContract()).thenReturn(contract);
        when(contract.getInsuredBy())
                .thenReturn(
                        Customer.of(
                                com.bees360.customer.Message.CustomerMessage.newBuilder()
                                        .setKey("Test InsuredBy")
                                        .setName("Test InsuredBy")
                                        .build()));
        when(projectIIRepository.findById(eq(projectId))).thenReturn(project);

        ProjectCreatedEvent event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        // convert to job
        Job job = notifier.convert(event);
        Assertions.assertNotNull(job);

        // validate job
        var emailJob = gson.fromJson(job.getPayload().toStringUtf8(), JsonObject.class);
        Assertions.assertEquals(templateKey, emailJob.get("template_key").getAsString());
        List<String> actualList = new ArrayList<>();
        for (JsonElement elem : emailJob.get("to_recipients").getAsJsonArray()) {
            actualList.add(elem.getAsString());
        }
        Assertions.assertEquals(toRecipients, actualList);
        JsonObject expected =
                new Gson()
                        .fromJson(
                                String.format(
                                        """
                        {
                          "projectId" : "%s",
                          "insuredBy" : "Test InsuredBy",
                          "note" : "Test Note"
                        }
                        """,
                                        projectId),
                                JsonObject.class);

        JsonObject actual =
                new Gson().fromJson(emailJob.get("variables_json").getAsString(), JsonObject.class);
        Assertions.assertEquals(expected, actual);
        Assertions.assertEquals("backend-sender", emailJob.get("mail_sender").getAsString());
    }
}
