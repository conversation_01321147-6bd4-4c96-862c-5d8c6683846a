package com.bees360.project.config.mail;

import com.bees360.mail.MailSender;
import com.bees360.mail.MailSenderProvider;

/** {@code @SpyBean} 无法作用于lambda function实现或者匿名内部类 */
public class SpyableMailSenderProvider implements MailSenderProvider {

    private final MailSenderProvider mailSenderProvider;

    public SpyableMailSenderProvider(MailSenderProvider mailSenderProvider) {
        this.mailSenderProvider = mailSenderProvider;
    }

    @Override
    public MailSender get(String senderName) {
        return mailSenderProvider.get(senderName);
    }
}
