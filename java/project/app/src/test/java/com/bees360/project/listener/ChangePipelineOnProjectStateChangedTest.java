package com.bees360.project.listener;

import static com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE;
import static com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_OPEN;
import static com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_PAUSE;

import static org.mockito.ArgumentMatchers.eq;

import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.pipeline.PipelineService;
import com.bees360.pipeline.ProjectPipelineConfigService;
import com.bees360.project.Message;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.config.PipelineListenersConfig;
import com.bees360.project.state.AbstractProjectState;
import com.bees360.util.DateTimes;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.time.Instant;
import java.util.concurrent.Executor;
import java.util.stream.Stream;

@SpringBootTest
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
class ChangePipelineOnProjectStateChangedTest {

    @Configuration
    @Import({
        InMemoryEventPublisher.class,
        AutoRegisterEventListenerConfig.class,
        PipelineListenersConfig.class,
    })
    static class config {
        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @Autowired private EventPublisher eventPublisher;
    @MockBean private PipelineService pipelineService;
    @MockBean private ProjectPipelineConfigService projectPipelineConfigService;
    @MockBean private ProjectIIRepository projectIIRepository;

    private static Stream<Message.ProjectMessage.ProjectState.ProjectStateEnum> stateResource() {
        return Stream.of(PROJECT_OPEN, PROJECT_PAUSE);
    }

    @ParameterizedTest
    @MethodSource("stateResource")
    void testClosePipelineWhenProjectStateClosed(
            Message.ProjectMessage.ProjectState.ProjectStateEnum oldState) {
        var projectId = RandomUtils.nextLong();
        var changedBy = RandomStringUtils.randomAlphabetic(6);
        var updatedAt = Instant.now();
        ProjectStateChangedEvent event = new ProjectStateChangedEvent();
        event.setProjectId(projectId);
        event.setOldState(oldState);
        var currentState =
                AbstractProjectState.from(
                        Message.ProjectMessage.ProjectState.newBuilder()
                                .setState(PROJECT_CLOSE)
                                .setUpdatedAt(DateTimes.toTimestamp(updatedAt))
                                .setUpdatedBy(changedBy)
                                .build());
        event.setCurrentState(currentState);
        eventPublisher.publish(event);
        Mockito.verify(pipelineService).closePipeline(eq(String.valueOf(projectId)));
    }

    @ParameterizedTest
    @MethodSource("stateResource")
    void testRecoverPipelineWhenProjectStateReopened(
            Message.ProjectMessage.ProjectState.ProjectStateEnum stateEnum) {
        var projectId = RandomUtils.nextLong();
        var changedBy = RandomStringUtils.randomAlphabetic(6);
        var updatedAt = Instant.now();
        ProjectStateChangedEvent event = new ProjectStateChangedEvent();
        event.setProjectId(projectId);
        event.setOldState(PROJECT_CLOSE);
        var currentState =
                AbstractProjectState.from(
                        Message.ProjectMessage.ProjectState.newBuilder()
                                .setState(stateEnum)
                                .setUpdatedAt(DateTimes.toTimestamp(updatedAt))
                                .setUpdatedBy(changedBy)
                                .build());
        event.setCurrentState(currentState);
        eventPublisher.publish(event);
        Mockito.verify(pipelineService).recoverPipeline(eq(String.valueOf(projectId)));
    }
}
