package com.bees360.project.util;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;

public class JSUtilTest {

    @Test
    public void correctScriptAndParamShouldSuccess() {
        String jsonArrayString =
                "[{\"userId\":\"10000\",\"capacity\":1,\"difficulty\":[1,2]},{\"userId\":\"10001\",\"capacity\":2,\"difficulty\":[3,4]}]";
        var result = JSUtil.execute(getCorrectScript(), jsonArrayString);
        Assertions.assertEquals(4D, result);
    }

    @Test
    public void errorScriptShouldThrowException() {
        String jsonArrayString =
                "[{\"userId\":\"10000\",\"capacity\":1,\"difficulty\":[1,2]},{\"userId\":\"10001\",\"capacity\":2,\"difficulty\":[3,4]}]";
        String script = "var difficultySum = parameter.reduce(function(sum, obj)";

        Assertions.assertThrows(
                IllegalArgumentException.class, () -> JSUtil.execute(script, jsonArrayString));
    }

    @Test
    public void errorJsonParamShouldThrowException() {
        String jsonArrayString = "[{\"userId\":\"10000\",\"capacity\":";

        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> JSUtil.execute(getCorrectScript(), jsonArrayString));
    }

    @Test
    public void nullJsonParamShouldThrowException() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> JSUtil.execute("abc", null));
    }

    @Test
    public void nullScriptShouldThrowException() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> JSUtil.execute(null, "abc"));
    }

    @Test
    public void assignScriptNoReturnValueShouldReturnNull() {
        String script = "var totalDifficulty = 0;";
        String testData = "{\"userId\":\"10000\"}";
        var result = JSUtil.execute(script, testData);
        Assertions.assertNull(result);
    }

    @SneakyThrows
    private String getCorrectScript() {
        return IOUtils.resourceToString(
                "auto-assign-test-loss-function-script.js",
                StandardCharsets.UTF_8,
                JSUtilTest.class.getClassLoader());
    }
}
