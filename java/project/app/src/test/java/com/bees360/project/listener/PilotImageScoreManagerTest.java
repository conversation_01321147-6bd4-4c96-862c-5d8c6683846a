package com.bees360.project.listener;

import static com.bees360.event.registry.MongoCollectionChanged.Type.UPSERT;
import static com.bees360.pilot.util.ProjectPilotFeedbackUtil.getPilotIdFromProject;
import static com.bees360.project.member.RoleEnum.PILOT;
import static com.bees360.project.member.RoleEnum.PROCESSOR;

import static java.lang.Math.floor;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.beespilot.PilotImageScoreManager;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.ActivityChangedEvent;
import com.bees360.event.registry.PilotRatedEvent;
import com.bees360.event.registry.ProjectReturnedToClientEvent;
import com.bees360.pilot.ProjectPilotFeedback;
import com.bees360.pilot.feedback.PilotFeedback;
import com.bees360.pilot.feedback.PilotFeedbackManager;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.image.ProjectImageProvider;
import com.bees360.project.member.Member;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import com.bees360.util.Messages;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.function.Supplier;

@SpringBootTest
public class PilotImageScoreManagerTest {

    @Configuration
    @Import({
        InMemoryEventPublisher.class,
        AutoRegisterEventListenerConfig.class,
        RatePilotImageScoreOnActivityAdded.class,
        RatePilotImageScoreOnReturnToClient.class,
        RatePilotImageScoreOnManuallyRating.class,
    })
    static class config {
        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }

        @Bean
        Supplier<String> robotUserIdSupplier() {
            return () -> "10000";
        }
    }

    @MockBean public PilotImageScoreManager pilotImageScoreManager;
    @Autowired private EventPublisher eventPublisher;
    @Autowired private Supplier<String> robotUserIdSupplier;

    @MockBean private ProjectIIRepository projectIIRepository;
    @MockBean private PilotFeedbackManager pilotFeedbackManager;
    @MockBean private Function<ProjectPilotFeedback, Integer> imageFeedback2PilotScoreConverter;
    @MockBean private ProjectImageProvider projectImageProvider;

    @MockBean private UserProvider userProvider;
    @MockBean private ActivityManager activityManager;

    @Test
    public void setPilotImageScore() {
        var pilotId = RandomStringUtils.randomAlphanumeric(8);
        var createdBy = RandomStringUtils.randomAlphanumeric(8);
        var score = 0.6F;
        var projectId = RandomUtils.nextLong();
        var activty = from(pilotId, projectId, createdBy, score);
        var event = new ActivityChangedEvent();
        event.setType(UPSERT);
        event.setDocument(Messages.toJsonString(activty.toMessage()));
        eventPublisher.publish(event);
        Mockito.verify(pilotImageScoreManager)
                .setImageRatingScore(
                        Mockito.eq(String.valueOf(projectId)),
                        Mockito.eq(pilotId),
                        Mockito.eq(createdBy),
                        Mockito.eq(score));
    }

    @Test
    public void setPilotImageScoreOnReportApproved() {

        var projectId = String.valueOf(RandomUtils.nextLong());
        var status = com.bees360.project.Message.ProjectStatus.REVIEWER_APPROVED.getNumber();
        var event = new ProjectReturnedToClientEvent();
        event.setProjectId(projectId);
        event.setStatus(status);
        var randomProject = generateRandomProject(projectId);
        var pilotId = getPilotIdFromProject(randomProject);
        var pilot = com.bees360.user.Message.PilotMessage.newBuilder();
        pilot.setPilotUser(
                com.bees360.user.Message.UserMessage.newBuilder().setId(pilotId).build());
        var feedback = generateFeedback(projectId);
        Mockito.when(projectIIRepository.findById(projectId)).thenReturn(randomProject);
        Mockito.when(pilotFeedbackManager.findByProjectId(projectId)).thenReturn(feedback);
        Mockito.when(projectImageProvider.findPilotByImageId(Mockito.any()))
                .thenReturn(Map.of("83axo_0qEvocx1ad121", pilotId));
        var ratedBy = robotUserIdSupplier.get();
        var ratedByUser =
                User.from(com.bees360.user.Message.UserMessage.newBuilder().setId(ratedBy).build());
        Mockito.when(userProvider.findUserById(ratedBy)).thenReturn(ratedByUser);
        var feedbackCount = Iterables.toList(feedback.getFeedbacks()).size();
        var randomScore = RandomUtils.nextInt(0, 10) * -1;
        var totalScore = randomScore * feedbackCount;
        var floatScore = Math.max((float) floor((5 + totalScore / 10.0) * 2.0) / 2, 0.5f);
        // each feedback will have a score
        Mockito.when(imageFeedback2PilotScoreConverter.apply(Mockito.any()))
                .thenAnswer(e -> randomScore);
        eventPublisher.publish(event);
        Mockito.verify(pilotImageScoreManager)
                .setImageRatingScore(
                        Mockito.eq(projectId),
                        Mockito.eq(pilotId),
                        Mockito.eq(robotUserIdSupplier.get()),
                        Mockito.eq(floatScore));
        Mockito.verify(activityManager, Mockito.times(1)).submitActivity(Mockito.any());
    }

    @Test
    public void setPilotImageScoreOnManuallyRating() {
        var score = RandomUtils.nextFloat();
        var projectId = randomId();
        var pilotId = randomId();
        var ratedBy = randomId();
        var event = new PilotRatedEvent();
        event.setScore(score);
        event.setPilotId(pilotId);
        event.setRatedBy(ratedBy);
        event.setProjectId(projectId);
        var ratedByUser =
                User.from(com.bees360.user.Message.UserMessage.newBuilder().setId(ratedBy).build());
        Mockito.when(userProvider.findUserById(ratedBy)).thenReturn(ratedByUser);
        eventPublisher.publish(event);
        Mockito.verify(pilotImageScoreManager)
                .setImageRatingScore(
                        Mockito.eq(projectId),
                        Mockito.eq(pilotId),
                        Mockito.eq(ratedBy),
                        Mockito.eq(score));
        Mockito.verify(activityManager, Mockito.times(1)).submitActivity(Mockito.any());
    }

    private Activity from(String pilotId, long projectId, String createdBy, float score) {
        var userMsg = com.bees360.user.Message.UserMessage.newBuilder().setId(createdBy).build();
        var pilot = com.bees360.user.Message.PilotMessage.newBuilder();
        pilot.setPilotUser(userMsg.toBuilder().setId(pilotId));
        return Activity.from(
                Message.ActivityMessage.newBuilder()
                        .setAction(Message.ActivityMessage.ActionType.RATE.name())
                        .setProjectId(projectId)
                        .setCreatedBy(userMsg)
                        .setEntity(
                                Message.ActivityMessage.Entity.newBuilder()
                                        .setId(pilotId)
                                        .setType(Message.ActivityMessage.EntityType.PILOT.name())
                                        .setPilot(pilot)
                                        .build())
                        .setField(
                                Message.ActivityMessage.Field.newBuilder()
                                        .setName(Message.ActivityMessage.FieldName.SCORE.name())
                                        .setValue(String.valueOf(score))
                                        .setType(Message.ActivityMessage.FieldType.FLOAT.name())
                                        .build())
                        .build());
    }

    private ProjectII generateRandomProject(String projectId) {
        var pilotMember = new SimpleTestMember(randomUser(), PILOT.getValue());
        var reviewerMember = new SimpleTestMember(randomUser(), PROCESSOR.getValue());
        return ProjectII.from(
                com.bees360.project.Message.ProjectMessage.newBuilder()
                        .setId(projectId)
                        .addAllMember(List.of(pilotMember.toMessage(), reviewerMember.toMessage()))
                        .build());
    }

    private PilotFeedback generateFeedback(String projectId) {
        var feedback_1 =
                "{\n"
                    + " \"feedbackId\": 1, \n"
                    + " \"recommendation\": \"Elevation image missing\",\n"
                    + " \"yoursImages\": [\"83axo_0qEvocx1ad121\"],\n"
                    + " \"sampleImages\": [\"834Yo_0qEvocx1SZMO1.jpg\"],\n"
                    + " \"yoursImageList\":[{\"yoursImage\":\"83axo_0qEvocx1ad121.jpg\",\"yoursImageId\":\"83axo_0qEvocx1ad121\"}]"
                    + " \n"
                    + "}";
        var feedback_2 =
                "{\n"
                    + " \"feedbackId\": 2, \n"
                    + " \"recommendation\": \"Drone image missing\",\n"
                    + " \"yoursImages\": [\"83axo_0qEvocx1ad121\"],\n"
                    + " \"sampleImages\": [\"834Yo_0qEvocx1SZMO1.jpg\"],\n"
                    + " \"yoursImageList\":[{\"yoursImage\":\"83axo_0qEvocx1ad121.jpg\",\"yoursImageId\":\"83axo_0qEvocx1ad121\"}]"
                    + " \n"
                    + "}";
        return PilotFeedback.from(
                com.bees360.pilot.feedback.Message.PilotFeedbackMessage.newBuilder()
                        .setProjectId(projectId)
                        .addAllFeedback(List.of(feedback_1, feedback_2))
                        .build());
    }

    private static User randomUser() {
        var id = randomId();
        return User.from(
                com.bees360.user.Message.UserMessage.newBuilder()
                        .setId(id)
                        .setUid(Long.parseLong(id))
                        .build());
    }

    private static String randomId() {
        return String.valueOf(RandomUtils.nextLong());
    }

    @RequiredArgsConstructor
    @Getter
    static class SimpleTestMember implements Member {
        private final User user;
        private final String role;
    }
}
