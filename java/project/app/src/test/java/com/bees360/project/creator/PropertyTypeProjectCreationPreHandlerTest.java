package com.bees360.project.creator;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.bees360.building.Message.BuildingType;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.config.ProjectGlobalOptionsProperties;
import com.google.gson.Gson;
import com.google.protobuf.util.JsonFormat;

import lombok.SneakyThrows;

import org.junit.jupiter.api.Test;

class PropertyTypeProjectCreationPreHandlerTest {

    private PropertyTypeProjectCreationPreHandler handler = creationPreHandler();

    private static PropertyTypeProjectCreationPreHandler creationPreHandler() {
        var propertyJson =
                """
            {
                "policyType": {
                    "Commercial Property Insurance": {
                        "defaultPropertyType": 22,
                        "propertyType": [ 18, 19, 20, 21, 22 ]
                    }
                }
            }
            """;
        var properties = new Gson().from<PERSON>son(propertyJson, ProjectGlobalOptionsProperties.class);
        return new PropertyTypeProjectCreationPreHandler(BuildingType.OTHER, properties);
    }

    /** default building type for grpc request is RESIDENTIAL_SINGLE_FAMILY. */
    @Test
    void validateAndLoadData_expectUpdateBuildingTypePolicyTypeDefault() {
        var request =
                genRequest(
                        """
            {
                "policy": {
                    "type": "Commercial Property Insurance"
                }
            }
            """);
        handler.validateAndLoadData(request, null);
        assertEquals(
                BuildingType.RESIDENTIAL_SINGLE_FAMILY,
                request.getPolicy().getBuilding().getType());
    }

    @Test
    void validateAndLoadData_expectUpdateBuildingType() {
        var request =
                genRequest(
                        """
            {
                "policy": {
                    "type": "Commercial Property Insurance",
                    "building": { "type": 100 }
                }
            }
            """);
        handler.validateAndLoadData(request, null);
        assertEquals(BuildingType.GENERAL_COMMERCIAL, request.getPolicy().getBuilding().getType());
    }

    @Test
    void validateAndLoadData_expectNotUpdateBuildingType() {
        var request =
                genRequest(
                        """
            {
                "policy": {
                    "type": "Commercial Property Insurance",
                    "building": { "type": 21 }
                }
            }
            """);
        handler.validateAndLoadData(request, null);
        assertEquals(BuildingType.GAS_STATION, request.getPolicy().getBuilding().getType());
    }

    /** default building type for grpc request is RESIDENTIAL_SINGLE_FAMILY. */
    @Test
    void validateAndLoadData_expectNotUpdateBuildingTypeIfDefault() {
        var request =
                genRequest(
                        """
            {
                "policy": {
                    "building": {}
                }
            }
            """);
        handler.validateAndLoadData(request, null);
        assertEquals(
                BuildingType.RESIDENTIAL_SINGLE_FAMILY,
                request.getPolicy().getBuilding().getType());
    }

    /** default building type for grpc request is RESIDENTIAL_SINGLE_FAMILY. */
    @Test
    void validateAndLoadData_expectedDefaultValue() {
        var request =
                genRequest(
                        """
            {
                "policy": {
                    "type": "Fake policy type",
                     "building": { "type": 100 }
                }
            }
            """);
        handler.validateAndLoadData(request, null);
        assertEquals(BuildingType.OTHER, request.getPolicy().getBuilding().getType());
    }

    @SneakyThrows
    private BasicProjectCreationRequest genRequest(String requestJson) {
        var builder = ProjectMessage.newBuilder();
        JsonFormat.parser().merge(requestJson, builder);
        return new BasicProjectCreationRequest(ProjectCreationRequest.from(builder.build()));
    }
}
