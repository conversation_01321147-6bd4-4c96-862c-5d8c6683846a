package com.bees360.project.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyIterable;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import com.bees360.contract.Message;
import com.bees360.event.EventDispatcher;
import com.bees360.event.EventListener;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.ProjectCreate;
import com.bees360.project.Project;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.RandomProjectUtil;
import com.bees360.project.config.ProjectCustomerConfig;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagManager;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

import javax.annotation.PostConstruct;

@SpringBootTest(classes = AddHailTagOnProjectCreatedEventTest.Config.class)
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
public class AddHailTagOnProjectCreatedEventTest {

    @Import({
        ProjectCustomerConfig.class,
        AddHailTagOnProjectCreatedEvent.class,
        InMemoryEventPublisher.class,
    })
    @ImportAutoConfiguration(RefreshAutoConfiguration.class)
    @Configuration
    static class Config {
        @Bean("systemUserSupplier")
        public Supplier<String> systemUserSupplier(
                @Value("${bees360.system-user:10000}") String systemUser) {
            return () -> systemUser;
        }

        @Bean
        public Executor executor() {
            return MoreExecutors.directExecutor();
        }

        @Autowired private EventDispatcher eventPublisher;

        @Autowired private EventListener eventListener;

        @PostConstruct
        void enlist() {
            eventPublisher.enlist(eventListener);
        }
    }

    @MockBean private ProjectIIManager projectIIManager;

    @MockBean private ProjectTagManager projectTagManager;

    @Autowired private EventPublisher eventPublisher;

    private static final String title = "Hail report NEEDED";

    private static final String BEES360_COMPANY_ID = "1062";

    private static final String AI_USER_ID = "10000";

    @Test
    void testHailNeeded() {
        var project = randomProjectWithHailNote("Insurance Company");
        Mockito.when(projectIIManager.findById(any())).thenAnswer(e -> project);
        Mockito.when(
                        projectTagManager.findByTitle(
                                eq(title),
                                eq(BEES360_COMPANY_ID),
                                eq(com.bees360.project.tag.Message.ProjectTagType.CLAIM)))
                .thenAnswer(e -> List.of(randomHailTag()));
        var event = new ProjectCreate();
        event.setProject(Project.from(project.toMessage()));

        eventPublisher.publish(event);

        Mockito.verify(projectTagManager, Mockito.times(1))
                .addProjectTag(anyString(), anyIterable(), anyString(), anyString());
    }

    @Test
    void testAutoHailNeededFalse() {
        var project = randomProjectWithHailNote("No Hail Needed Company");
        Mockito.when(projectIIManager.findById(any())).thenAnswer(e -> project);
        Mockito.when(
                        projectTagManager.findByTitle(
                                eq(title),
                                eq(BEES360_COMPANY_ID),
                                eq(com.bees360.project.tag.Message.ProjectTagType.CLAIM)))
                .thenAnswer(e -> List.of(randomHailTag()));
        var event = new ProjectCreate();
        event.setProject(Project.from(project.toMessage()));

        eventPublisher.publish(event);

        Mockito.verify(projectTagManager, Mockito.never())
                .addProjectTag(any(), any(), any(), any());
    }

    private ProjectII randomProjectWithHailNote(String insuredBy) {
        var builder = RandomProjectUtil.randomProject().toMessage().toBuilder();
        builder.setServiceType(com.bees360.project.Message.ServiceType.EXPRESS_UNDERWRITING);
        builder.setNote("Loss Description:\n Hail\n");
        Optional.ofNullable(insuredBy)
                .ifPresent(
                        i ->
                                builder.setContract(
                                        Message.ContractMessage.newBuilder()
                                                .setInsuredBy(
                                                        com.bees360.customer.Message.CustomerMessage
                                                                .newBuilder()
                                                                .setKey(i)
                                                                .build())
                                                .build()));
        return ProjectII.from(builder.build());
    }

    private ProjectTag randomHailTag() {
        return ProjectTag.ProjectTagBuilder.newBuilder()
                .setId(RandomStringUtils.randomNumeric(4))
                .build();
    }
}
