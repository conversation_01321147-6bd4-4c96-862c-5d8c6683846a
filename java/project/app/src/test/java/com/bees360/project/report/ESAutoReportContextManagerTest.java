package com.bees360.project.report;

import static com.bees360.report.RandomReport.randomReportType;

import static org.junit.jupiter.api.Assertions.*;

import com.bees360.es.ESApi;
import com.bees360.project.RandomProjectUtil;
import com.bees360.util.Iterables;
import com.google.common.collect.Lists;
import com.google.gson.Gson;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest
@TestPropertySource(
        properties = {
            "spring.config.location = classpath:application-test.yml",
        })
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ESAutoReportContextManagerTest {

    @Configuration
    @Import({
        ESAutoReportContextManagerConfig.class,
    })
    static class Config {}

    private final Gson gson = ESAutoReportContextManager.gson;

    @Autowired private ESAutoReportContextManager esRepository;
    @Autowired private ESApi esApi;

    @Autowired
    private ESAutoReportContextManagerConfig.AutoReportContextEsRepositoryProperties properties;

    @Test
    void testSetVerificationPassedStatus() throws IOException {
        // Prepare test data
        var projectId = RandomProjectUtil.getRandomId();
        var jobId = RandomProjectUtil.getRandomId();
        var createdAt = Instant.now().toString();
        var reportType = randomReportType();
        String factorValue =
                loadJson("test_report_factor_value.json", this.getClass().getClassLoader());
        var errors =
                Lists.newArrayList(AutoReportContextError.of("source1", "reason1", "exception1"));
        var entity =
                AutoReportContext.of(
                        projectId, reportType, jobId, createdAt, factorValue, errors, false);

        // Execute
        esRepository.save(entity);
        assertFalse(esRepository.checkIfVerificationPassed(projectId, reportType));
        esRepository.setVerificationPassedStatus(projectId, reportType, true);
        assertTrue(esRepository.checkIfVerificationPassed(projectId, reportType));
        esRepository.setVerificationPassedStatus(projectId, reportType, false);
        assertFalse(esRepository.checkIfVerificationPassed(projectId, reportType));
    }

    @Test
    void testSaveAndFind() throws IOException {
        // Prepare test data
        var projectId = RandomProjectUtil.getRandomId();
        var jobId = RandomProjectUtil.getRandomId();
        var createdAt = Instant.now().toString();
        var reportType = randomReportType();
        String factorValue =
                loadJson("test_report_factor_value.json", this.getClass().getClassLoader());
        var errors =
                Lists.newArrayList(AutoReportContextError.of("source1", "reason1", "exception1"));

        var entity =
                AutoReportContext.of(
                        projectId, reportType, jobId, createdAt, factorValue, errors, false);

        // Execute
        String savedId = esRepository.save(entity);
        assertEquals(jobId, savedId);

        // Verify findById
        var savedEntity = esRepository.findById(jobId);
        assertNotNull(savedEntity);
        assertEquals(jobId, savedEntity.getJobId());
        assertEquals(jobId, savedEntity.getId());
        assertEquals(projectId, savedEntity.getProjectId());
        assertEquals(reportType, savedEntity.getReportType());
        assertEquals(createdAt, savedEntity.getCreatedAt());
        assertEquals(gson.toJson(factorValue), gson.toJson(savedEntity.getFactorValue()));
        assertEquals(1, Iterables.toList(savedEntity.getErrors()).size());

        // find by projectId and reportType
        var result = esRepository.find(projectId, reportType);
        assertEquals(gson.toJson(savedEntity), gson.toJson(result));
    }

    @Test
    void testAddError() throws IOException {
        // Prepare test data
        var projectId = RandomProjectUtil.getRandomId();
        var jobId = RandomProjectUtil.getRandomId();
        var createdAt = Instant.now().toString();
        var reportType = randomReportType();
        var errors =
                Lists.newArrayList(AutoReportContextError.of("source1", "reason1", "exception1"));

        var context =
                AutoReportContext.of(projectId, reportType, jobId, createdAt, null, errors, false);
        var savedId = esRepository.save(context);
        assertEquals(jobId, savedId);

        var newError = AutoReportContextError.of("source2", "reason2", "exception2");
        savedId = esRepository.addError(projectId, reportType, newError);
        assertEquals(jobId, savedId);

        var entity = esRepository.findById(jobId);
        assertNotNull(entity);
        assertEquals(jobId, entity.getJobId());
        assertEquals(jobId, entity.getId());
        assertEquals(projectId, entity.getProjectId());
        assertEquals(reportType, entity.getReportType());
        assertEquals(createdAt, entity.getCreatedAt());
        assertNull(entity.getFactorValue());
        assertEquals(2, Iterables.toList(entity.getErrors()).size());
        assertTrue(
                Iterables.toStream(entity.getErrors())
                        .map(AutoReportContextError::getReason)
                        .collect(Collectors.toSet())
                        .containsAll(List.of("reason1", "reason2")));
    }

    @Test
    void testSearchEntityWithInvalidQuery() {
        // Execute & Verify
        assertThrows(
                IllegalArgumentException.class,
                () -> esRepository.searchEntity(""),
                "Query condition cannot be null or empty");
    }

    @Test
    void testSearchEntityWithNoResults() {
        assertThrows(IllegalStateException.class, () -> esRepository.searchEntity("test-query"));
    }

    public static String loadJson(String jsonPath, ClassLoader classLoader) throws IOException {
        return IOUtils.resourceToString(jsonPath, StandardCharsets.UTF_8, classLoader);
    }
}
