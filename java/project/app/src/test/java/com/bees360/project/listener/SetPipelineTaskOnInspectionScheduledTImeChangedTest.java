package com.bees360.project.listener;

import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.InspectionScheduledTimeChanged;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.config.SetPipelineTaskStatusOnInspectionScheduledTimeChangedConfig;
import com.bees360.util.DateTimes;
import com.bees360.util.TimeZones;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.time.Instant;
import java.time.LocalDate;
import java.util.concurrent.Executor;

@SpringBootTest(properties = "grpc.server.port=0")
public class SetPipelineTaskOnInspectionScheduledTImeChangedTest {
    @Configuration
    @Import(
            value = {
                InMemoryEventPublisher.class,
                SetPipelineTaskStatusOnInspectionScheduledTimeChangedConfig.class
            })
    static class Config {
        @MockBean PipelineService pipelineService;

        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @Autowired private EventPublisher eventPublisher;
    @Autowired private PipelineService pipelineService;

    @Test
    void testSetPipelineTaskOnProject() {
        var pipelineId = RandomStringUtils.randomAlphabetic(6);
        var event = new InspectionScheduledTimeChanged();
        event.setScheduledTime(System.currentTimeMillis());
        event.setProjectId(pipelineId);
        eventPublisher.publish(event);
        Mockito.verify(pipelineService)
                .setTaskStatus(pipelineId, "assign_to_pilot_claims", Message.PipelineStatus.DONE);
        Mockito.verify(pipelineService)
                .setTaskStatus(pipelineId, "assign_to_pilot_p4p", Message.PipelineStatus.DONE);
        Mockito.verify(pipelineService, Mockito.times(0))
                .setTaskStatus(pipelineId, "confirm_show_up", Message.PipelineStatus.READY);
    }

    @Test
    void testSetPipelineTaskOnInspectionScheduledTimeChangedToNextDay() {
        var pipelineId = RandomStringUtils.randomAlphabetic(6);
        var event = new InspectionScheduledTimeChanged();
        event.setScheduledTime(
                LocalDate.now(TimeZones.DEFAULT_US_TIMEZONE_ID)
                        .plusDays(1)
                        .atStartOfDay()
                        .toInstant(DateTimes.DEFAULT_US_ZONE_ID.getRules().getOffset(Instant.now()))
                        .toEpochMilli());
        event.setProjectId(pipelineId);
        eventPublisher.publish(event);
        Mockito.verify(pipelineService)
                .setTaskStatus(pipelineId, "assign_to_pilot_claims", Message.PipelineStatus.DONE);
        Mockito.verify(pipelineService)
                .setTaskStatus(pipelineId, "assign_to_pilot_p4p", Message.PipelineStatus.DONE);
        Mockito.verify(pipelineService)
                .setTaskStatus(pipelineId, "confirm_show_up", Message.PipelineStatus.READY);
    }
}
