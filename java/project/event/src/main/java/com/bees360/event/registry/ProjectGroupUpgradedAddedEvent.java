package com.bees360.event.registry;

import lombok.Getter;

@Event
public class ProjectGroupUpgradedAddedEvent {

    @Getter private final String projectId;
    @Getter private final String groupKey;
    @Getter private final String groupType;
    @Getter private final String createdBy;
    @Getter private final String createdAt;

    public ProjectGroupUpgradedAddedEvent(
            String projectId,
            String groupKey,
            String groupType,
            String createdBy,
            String createdAt) {
        this.projectId = projectId;
        this.groupKey = groupKey;
        this.groupType = groupType;
        this.createdBy = createdBy;
        this.createdAt = createdAt;
    }
}
