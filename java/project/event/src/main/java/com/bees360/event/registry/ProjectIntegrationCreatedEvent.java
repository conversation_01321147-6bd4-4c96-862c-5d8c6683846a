package com.bees360.event.registry;

import com.bees360.codec.ProtoGsonDecoder;
import com.bees360.project.ExternalIntegration;
import com.google.gson.annotations.JsonAdapter;

import lombok.Data;

/** The integration created without projects. */
@Data
@Event
public class ProjectIntegrationCreatedEvent {

    @JsonAdapter(value = ProtoGsonDecoder.class)
    private ExternalIntegration projectIntegration;
}
