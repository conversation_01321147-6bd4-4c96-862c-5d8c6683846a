package com.bees360.event.registry;

import lombok.Getter;
import lombok.ToString;

@Event("project_parent_child_group_updated")
@ToString
public class ProjectParentChildGroupUpdatedEvent {

    @Getter private final String projectId;
    @Getter private final String groupKey;
    @Getter private final String groupType;
    @Getter private final String createdBy;
    @Getter private final String createdAt;
    @Getter private final boolean isDeleted;

    public ProjectParentChildGroupUpdatedEvent(
            String projectId,
            String groupKey,
            String groupType,
            String createdBy,
            String createdAt,
            boolean isDeleted) {
        this.projectId = projectId;
        this.groupKey = groupKey;
        this.groupType = groupType;
        this.createdBy = createdBy;
        this.createdAt = createdAt;
        this.isDeleted = isDeleted;
    }
}
