package com.bees360.event.registry;

import com.bees360.codec.ProtoGsonDecoder;
import com.bees360.project.Contact;
import com.google.gson.annotations.JsonAdapter;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.Instant;

@Event
@Getter
@Setter
@ToString
public class ContactChangedEvent {

    private String projectId;

    private String updatedBy;

    private Instant updatedAt;

    /** null if creation */
    @JsonAdapter(value = ProtoGsonDecoder.class)
    private Contact oldValue;

    /** null if deletion */
    @JsonAdapter(value = ProtoGsonDecoder.class)
    private Contact newValue;
}
