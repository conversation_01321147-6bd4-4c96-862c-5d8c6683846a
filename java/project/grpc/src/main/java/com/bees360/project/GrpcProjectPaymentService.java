package com.bees360.project;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.util.DateTimes;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Log4j2
@GrpcService
@Import({ExceptionTranslateInterceptor.class})
public class GrpcProjectPaymentService
        extends ProjectPaymentServiceGrpc.ProjectPaymentServiceImplBase {
    private final ProjectPaymentManager projectPaymentManager;

    public GrpcProjectPaymentService(ProjectPaymentManager projectPaymentManager) {
        this.projectPaymentManager = projectPaymentManager;
        log.info("Created {}(projectPaymentManager={})", this, projectPaymentManager);
    }

    @Override
    public StreamObserver<Message.ProjectPaymentStatusChangeRequest> updateProjectPaymentStatus(
            StreamObserver<Empty> responseObserver) {
        return new StreamObserver<Message.ProjectPaymentStatusChangeRequest>() {
            Map<String, Boolean> map = new HashMap<>();

            @Override
            public void onNext(Message.ProjectPaymentStatusChangeRequest request) {
                map.put(request.getProjectId(), request.getIsPaid());
            }

            @Override
            public void onError(Throwable t) {
                log.error("A error occurs when updateProjectPaymentStatus.", t);
            }

            @Override
            public void onCompleted() {
                projectPaymentManager.setProjectsPaymentStatus(map);
                responseObserver.onNext(Empty.getDefaultInstance());
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public StreamObserver<StringValue> findByProjectIds(
            StreamObserver<Message.ProjectMessage.Payment> responseObserver) {
        return new StreamObserver<StringValue>() {
            List<String> projectIds = new ArrayList<>();

            @Override
            public void onNext(StringValue value) {
                projectIds.add(value.getValue());
            }

            @Override
            public void onError(Throwable t) {
                log.error("A error occurs when findPaymentByProjectIds.", t);
            }

            @Override
            public void onCompleted() {
                var payStatusByProjectIds =
                        projectPaymentManager.findPaymentByProjectIds(projectIds);
                payStatusByProjectIds.forEach(
                        payment ->
                                responseObserver.onNext(
                                        Message.ProjectMessage.Payment.newBuilder()
                                                .setProjectId(
                                                        Objects.requireNonNull(
                                                                payment.getProjectId()))
                                                .setIsPaid(payment.isPaid())
                                                .setUpdatedAt(
                                                        DateTimes.toTimestamp(
                                                                payment.getUpdatedAt()))
                                                .build()));
                responseObserver.onCompleted();
            }
        };
    }
}
