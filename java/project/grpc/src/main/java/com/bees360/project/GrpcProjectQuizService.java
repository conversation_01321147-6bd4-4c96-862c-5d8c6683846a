package com.bees360.project;

import com.bees360.project.quiz.ProjectQuizManager;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

@GrpcService
@Log4j2
public class GrpcProjectQuizService extends ProjectQuizServiceGrpc.ProjectQuizServiceImplBase {
    private final ProjectQuizManager projectQuizManager;

    public GrpcProjectQuizService(ProjectQuizManager projectQuizManager) {
        this.projectQuizManager = projectQuizManager;
        log.info("Created {}(projectQuizManager={})", this, projectQuizManager);
    }

    @Override
    public void findByProjectId(
            StringValue request, StreamObserver<Message.ProjectQuizList> responseObserver) {
        var result = projectQuizManager.findByProjectId(request.getValue());
        var builder = Message.ProjectQuizList.newBuilder();
        result.forEach(quiz -> builder.addProjectQuiz(quiz.toMessage()));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
