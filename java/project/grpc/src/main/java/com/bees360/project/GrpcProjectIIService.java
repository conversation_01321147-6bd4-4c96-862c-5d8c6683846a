package com.bees360.project;

import com.bees360.attachment.BatchAttachment;
import com.bees360.common.Message.SetIntValueRequest;
import com.bees360.common.Message.SetStringValueRequest;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.hover.ProjectHoverManager;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.Message.ProjectMessage.Hover;
import com.bees360.project.Message.ProjectStatusMessage;
import com.bees360.project.Message.ProjectTimelineQuery;
import com.bees360.project.Message.StatusUpdateRequest;
import com.bees360.project.ProjectServiceGrpc.ProjectServiceImplBase;
import com.bees360.project.claim.Claim;
import com.bees360.project.claim.ClaimTypeEnum;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.inspection.Inspection;
import com.bees360.project.inspection.Structure;
import com.bees360.project.member.Member;
import com.bees360.project.member.MemberManager;
import com.bees360.project.member.RoleEnum;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.project.state.ProjectStateQuery;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.project.timeline.ProjectTimelineManager;
import com.bees360.project.underwriting.Underwriting;
import com.bees360.util.DateTimes;
import com.bees360.util.Defaults;
import com.bees360.util.Iterables;
import com.bees360.util.Null;
import com.google.common.collect.Maps;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;

import java.time.Instant;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Log4j2
@GrpcService
@Import({ExceptionTranslateInterceptor.class})
@RequiredArgsConstructor
public class GrpcProjectIIService extends ProjectServiceImplBase {

    private final ProjectIIManager grpcProjectManager;

    private final ProjectStatusManager jooqProjectStatusRepository;

    private final ProjectTimelineManager projectTimelineManager;

    private final MemberManager projectMemberManager;

    private final ProjectHoverManager projectHoverManager;

    private final ProjectStateManager projectStateManager;

    private final SimilarProjectProvider grpcSimilarProjectProvider;

    private final GenericProjectCreator genericProjectCreator;

    private final BatchProjectCreator batchProjectCreator;

    @Override
    public void get(StringValue request, StreamObserver<ProjectMessage> responseObserver) {
        var project = grpcProjectManager.findById(request.getValue());
        responseObserver.onNext(project.toMessage());
        responseObserver.onCompleted();
    }

    @Override
    public StreamObserver<StringValue> findById(StreamObserver<ProjectMessage> responseObserver) {
        return new StreamObserver<>() {

            @Override
            public void onNext(StringValue stringValue) {
                var projectMessage =
                        Optional.ofNullable(grpcProjectManager.findById(stringValue.getValue()))
                                .map(ProjectII::toMessage)
                                .orElse(ProjectMessage.getDefaultInstance());
                responseObserver.onNext(projectMessage);
            }

            @Override
            public void onError(Throwable throwable) {
                log.warn("Encountered error in get project", throwable);
            }

            @Override
            public void onCompleted() {
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public void createClaimProject(
            Message.CreateClaimProjectRequest request,
            StreamObserver<ProjectMessage> responseObserver) {
        log.info("Project create claim project {}", request);
        var project =
                grpcProjectManager.create(
                        Defaults.nullIfEmpty(request.getId()),
                        Claim.from(request.getClaim()),
                        Inspection.from(request.getInspection()),
                        request.getContractId(),
                        request.getPolicyId(),
                        request.getCreatedBy(),
                        request.getAllowDuplication(),
                        request.getIsTestCase());
        var projectMessage =
                Optional.ofNullable(project)
                        .map(ProjectII::toMessage)
                        .orElse(ProjectMessage.getDefaultInstance());
        responseObserver.onNext(projectMessage);
        responseObserver.onCompleted();
    }

    @Override
    public void createUnderwritingProject(
            Message.CreateUnderwritingProjectRequest request,
            StreamObserver<ProjectMessage> responseObserver) {
        log.info("Project create underwriting project {}", request);
        var project =
                grpcProjectManager.create(
                        Defaults.nullIfEmpty(request.getId()),
                        Underwriting.from(request.getUnderwriting()),
                        Inspection.from(request.getInspection()),
                        request.getContractId(),
                        request.getPolicyId(),
                        request.getCreatedBy(),
                        request.getAllowDuplication(),
                        request.getIsTestCase());
        var projectMessage =
                Optional.ofNullable(project)
                        .map(ProjectII::toMessage)
                        .orElse(ProjectMessage.getDefaultInstance());
        responseObserver.onNext(projectMessage);
        responseObserver.onCompleted();
    }

    @Override
    public void createInspection(
            Message.CreateInspectionRequest request,
            StreamObserver<ProjectMessage.Inspection> responseObserver) {
        log.info(
                "Project create inspection projectId {} request {}",
                request.getProjectId(),
                request);
        var message = Inspection.from(request.getInspection());
        var inspection =
                grpcProjectManager.createInspection(
                        Inspection.InspectionBuilder.newBuilder()
                                .setProjectId(request.getProjectId())
                                .setInspectionNo(message.getInspectionNo())
                                .setAppointmentTime(message.getAppointmentTime())
                                .setScheduledTime(message.getScheduledTime())
                                .setDueDate(message.getDueDate())
                                .setStartTime(message.getStartTime())
                                .setCompleteTime(message.getCompleteTime())
                                .build());
        responseObserver.onNext(
                Null.getIfNPEThenDefault(
                        inspection::toMessage, ProjectMessage.Inspection.getDefaultInstance()));
        responseObserver.onCompleted();
    }

    @Override
    public void updateClaimNo(
            com.bees360.common.Message.SetStringValueRequest request,
            StreamObserver<BoolValue> responseObserver) {
        log.info("Project update claimNo projectId {} request {}", request.getId(), request);
        responseObserver.onNext(
                BoolValue.of(
                        grpcProjectManager.updateClaimNo(
                                request.getId(), Defaults.nullIfEmpty(request.getValue()))));
        responseObserver.onCompleted();
    }

    @Override
    public void updateClaimType(
            Message.UpdateClaimTypeRequest request, StreamObserver<BoolValue> responseObserver) {
        log.info(
                "Project update claim type projectId {} request {}",
                request.getProjectId(),
                request);
        responseObserver.onNext(
                BoolValue.of(
                        grpcProjectManager.updateClaimType(
                                request.getProjectId(),
                                ClaimTypeEnum.valueOf(request.getClaimTypeValue()))));
        responseObserver.onCompleted();
    }

    @Override
    public void updateServiceType(
            Message.UpdateServiceTypeRequest request, StreamObserver<BoolValue> responseObserver) {
        log.info(
                "Project update service type projectId {} request {}",
                request.getProjectId(),
                request);
        responseObserver.onNext(
                BoolValue.of(
                        grpcProjectManager.updateServiceType(
                                request.getProjectId(),
                                ServiceTypeEnum.valueOf(request.getServiceTypeValue()))));
        responseObserver.onCompleted();
    }

    @Override
    public void updateDateOfLoss(
            com.bees360.common.Message.SetDateRequest request,
            StreamObserver<BoolValue> responseObserver) {
        log.info("Project update date of loss projectId {} request {}", request.getId(), request);
        responseObserver.onNext(
                BoolValue.of(
                        grpcProjectManager.updateDateOfLoss(
                                request.getId(), DateTimes.toLocalDate(request.getValue()))));
        responseObserver.onCompleted();
    }

    @Override
    public void updateInspectionNo(
            com.bees360.common.Message.SetStringValueRequest request,
            StreamObserver<BoolValue> responseObserver) {
        log.info("Project update inspectionNo projectId {} request {}", request.getId(), request);
        responseObserver.onNext(
                BoolValue.of(
                        grpcProjectManager.updateInspectionNo(
                                request.getId(), Defaults.nullIfEmpty(request.getValue()))));
        responseObserver.onCompleted();
    }

    @Override
    public void updateInspectionAppointmentTime(
            com.bees360.common.Message.SetTimeRequest request,
            StreamObserver<BoolValue> responseObserver) {
        log.info(
                "Project update inspection appointment time projectId {} request {}",
                request.getId(),
                request);
        responseObserver.onNext(
                BoolValue.of(
                        grpcProjectManager.updateInspectionAppointmentTime(
                                request.getId(), DateTimes.toInstant(request.getValue()))));
        responseObserver.onCompleted();
    }

    @Override
    public void updateInspectionScheduledTime(
            com.bees360.common.Message.SetTimeRequest request,
            StreamObserver<BoolValue> responseObserver) {
        log.info(
                "Update inspection scheduled time of project {} with request {}",
                request.getId(),
                request);
        responseObserver.onNext(
                BoolValue.of(
                        grpcProjectManager.updateScheduledTime(
                                request.getId(), DateTimes.toInstant(request.getValue()))));
        responseObserver.onCompleted();
    }

    @Override
    public void updateInspectionDueDate(
            com.bees360.common.Message.SetDateRequest request,
            StreamObserver<BoolValue> responseObserver) {
        log.info(
                "Update inspection due date of project {} with request {}",
                request.getId(),
                request);
        responseObserver.onNext(
                BoolValue.of(
                        grpcProjectManager.updateDueDate(
                                request.getId(), DateTimes.toLocalDate(request.getValue()))));
        responseObserver.onCompleted();
    }

    @Override
    public void updateInspectionStartTime(
            com.bees360.common.Message.SetTimeRequest request,
            StreamObserver<BoolValue> responseObserver) {
        log.info(
                "Project update inspection start time projectId {} request {}",
                request.getId(),
                request);
        responseObserver.onNext(
                BoolValue.of(
                        grpcProjectManager.updateInspectionStartTime(
                                request.getId(), DateTimes.toInstant(request.getValue()))));
        responseObserver.onCompleted();
    }

    @Override
    public void updateInspectionCompletedTime(
            com.bees360.common.Message.SetTimeRequest request,
            StreamObserver<BoolValue> responseObserver) {
        log.info(
                "Project update inspection completed time projectId {} request {}",
                request.getId(),
                request);
        responseObserver.onNext(
                BoolValue.of(
                        grpcProjectManager.updateInspectionCompletedTime(
                                request.getId(), DateTimes.toInstant(request.getValue()))));
        responseObserver.onCompleted();
    }

    @Override
    public void updateInteriorDamageStatus(
            com.bees360.common.Message.SetBoolValueRequest request,
            StreamObserver<BoolValue> responseObserver) {
        log.info(
                "update interior damage status projectId '{}', '{}'",
                request.getId(),
                request.getValue());

        responseObserver.onNext(
                BoolValue.of(
                        grpcProjectManager.updateInteriorDamageStatus(
                                request.getId(), request.getValue())));
        responseObserver.onCompleted();
    }

    @Override
    public void updateStructures(
            Message.UpdateStructuresRequest request, StreamObserver<BoolValue> responseObserver) {
        log.info(
                "update structures projectId '{}', structure '{}'",
                request.getProjectId(),
                request.getStructureList());
        var list =
                request.getStructureList().stream()
                        .map(e -> Structure.builder().name(e.getName()).build())
                        .collect(Collectors.toList());
        responseObserver.onNext(
                BoolValue.of(grpcProjectManager.updateStructures(request.getProjectId(), list)));
        responseObserver.onCompleted();
    }

    @Override
    public void updateStatus(
            StatusUpdateRequest request, StreamObserver<BoolValue> responseObserver) {
        var response =
                jooqProjectStatusRepository.updateStatus(
                        request.getProjectId(),
                        request.getStatus(),
                        request.getUpdatedBy(),
                        request.getComment(),
                        DateTimes.toInstant(request.getUpdatedAt()));
        responseObserver.onNext(BoolValue.of(response));
        responseObserver.onCompleted();
    }

    @Override
    public void rollbackStatus(
            StatusUpdateRequest request, StreamObserver<BoolValue> responseObserver) {
        var response =
                jooqProjectStatusRepository.rollbackStatus(
                        request.getProjectId(),
                        request.getStatus(),
                        request.getUpdatedBy(),
                        request.getComment(),
                        request.getVersion());
        responseObserver.onNext(BoolValue.of(response));
        responseObserver.onCompleted();
    }

    @Override
    public void getStatus(
            StringValue request, StreamObserver<ProjectStatusMessage> responseObserver) {
        var projectStatus = jooqProjectStatusRepository.getStatus(request.getValue());
        responseObserver.onNext(projectStatus.toMessage());
        responseObserver.onCompleted();
    }

    @Override
    public void getStatusHistory(
            StringValue request, StreamObserver<ProjectStatusMessage> responseObserver) {
        var projectStatuses = jooqProjectStatusRepository.getStatusHistory(request.getValue());
        projectStatuses.forEach(status -> responseObserver.onNext(status.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void updateNumberOfOutBuildings(
            SetIntValueRequest request, StreamObserver<BoolValue> responseObserver) {
        var response =
                grpcProjectManager.updateNumberOfOutBuildings(request.getId(), request.getValue());
        responseObserver.onNext(BoolValue.of(response));
        responseObserver.onCompleted();
    }

    @Override
    public void updateNumberOfInteriorRooms(
            SetIntValueRequest request, StreamObserver<BoolValue> responseObserver) {
        var response =
                grpcProjectManager.updateNumberOfInteriorRooms(request.getId(), request.getValue());
        responseObserver.onNext(BoolValue.of(response));
        responseObserver.onCompleted();
    }

    @Override
    public void findByTimelineQuery(
            ProjectTimelineQuery request, StreamObserver<ProjectMessage> responseObserver) {
        var ids =
                projectTimelineManager.findIdByTimelineQuery(
                        request.getNodeList(),
                        DateTimes.toInstant(request.getTimeStart()),
                        DateTimes.toInstant(request.getTimeEnd()));
        var response = grpcProjectManager.findAllById(ids);
        response.forEach(project -> responseObserver.onNext(project.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void findByStatusQuery(
            Message.ProjectStatusQuery request, StreamObserver<ProjectMessage> responseObserver) {
        Iterable<? extends ProjectII> projects =
                grpcProjectManager.findByStatus(
                        request.getStartId(), request.getStatus(), request.getCountLimit());
        projects.forEach(project -> responseObserver.onNext(project.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void setMember(
            Message.MemberRequest request, StreamObserver<BoolValue> responseObserver) {
        var result =
                projectMemberManager.setMember(
                        request.getProjectId(),
                        request.getUserId(),
                        RoleEnum.valueOf(request.getRole()),
                        request.getOpUserId(),
                        request.getVersion());
        responseObserver.onNext(BoolValue.newBuilder().setValue(result).build());
        responseObserver.onCompleted();
    }

    @Override
    public void removeMember(
            Message.MemberRequest request, StreamObserver<BoolValue> responseObserver) {
        log.info("Remove member {}", request);
        boolean result =
                projectMemberManager.removeMember(
                        request.getProjectId(),
                        RoleEnum.valueOf(request.getRole()),
                        request.getOpUserId(),
                        request.getVersion());
        responseObserver.onNext(BoolValue.of(result));
        responseObserver.onCompleted();
    }

    @Override
    public void delete(StringValue request, StreamObserver<Empty> responseObserver) {
        log.info("Delete project id '{}'", request);
        grpcProjectManager.deleteById(request.getValue());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void getProjectHover(StringValue request, StreamObserver<Hover> responseObserver) {
        var hovers = projectHoverManager.findByProjectId(request.getValue());
        Iterables.toStream(hovers).forEach(hover -> responseObserver.onNext(hover.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void upgradeProjectHover(
            SetStringValueRequest request, StreamObserver<BoolValue> responseObserver) {
        var result = projectHoverManager.upgradeType(request.getId(), request.getValue());
        responseObserver.onNext(BoolValue.of(result));
        responseObserver.onCompleted();
    }

    @Override
    public void changeProjectState(
            Message.ProjectStateChangeRequest request,
            StreamObserver<Message.ProjectOperationFeedback> responseObserver) {
        var projectState = request.getProjectState();
        var changeReason = projectState.getStateChangeReason().getId();
        var version =
                Optional.ofNullable(DateTimes.toInstant(projectState.getUpdatedAt()))
                        .map(Instant::toEpochMilli)
                        .orElse(null);
        var feedback =
                projectStateManager.changeProjectState(
                        request.getProjectId(),
                        projectState.getState(),
                        changeReason,
                        projectState.getUpdatedBy(),
                        projectState.getComment(),
                        version,
                        request.getChangeForce());
        responseObserver.onNext(feedback.toMessage());
        responseObserver.onCompleted();
    }

    @Override
    public void findProjectIdByProjectState(
            Message.ProjectStateListRequest request,
            StreamObserver<com.bees360.common.Message.StringValueList> responseObserver) {
        var stateQueryRequest = buildStateQueryRequest(request);
        var projectIds =
                projectStateManager.findProjectByProjectState(
                        request.getStateListList(), stateQueryRequest);
        responseObserver.onNext(
                com.bees360.common.Message.StringValueList.newBuilder()
                        .addAllValue(projectIds)
                        .build());
        responseObserver.onCompleted();
    }

    @Deprecated
    @Override
    public void findProjectByProjectState(
            Message.ProjectStateListRequest request, StreamObserver<StringValue> responseObserver) {
        var stateQueryRequest = buildStateQueryRequest(request);
        var projectIds =
                projectStateManager.findProjectByProjectState(
                        request.getStateListList(), stateQueryRequest);
        projectIds.forEach(id -> responseObserver.onNext(StringValue.of(id)));
        responseObserver.onCompleted();
    }

    /** message ProjectStateListRequest 转换成 ProjectStateQuery */
    private ProjectStateQuery buildStateQueryRequest(Message.ProjectStateListRequest request) {
        var stateQueryBuilder = ProjectStateQuery.ProjectStateQueryBuilder.newBuilder();
        stateQueryBuilder.setServiceTypes(request.getServiceTypeList());
        stateQueryBuilder.setTimeStart(DateTimes.toInstant(request.getTimeStart()));
        stateQueryBuilder.setTimeEnd(DateTimes.toInstant(request.getTimeEnd()));
        stateQueryBuilder.setProjectCreatedStart(
                DateTimes.toInstant(request.getProjectCreatedStart()));
        stateQueryBuilder.setProjectCreatedEnd(DateTimes.toInstant(request.getProjectCreatedEnd()));
        stateQueryBuilder.setCompanyId(request.getCompanyIdList());
        return stateQueryBuilder.build();
    }

    @Override
    public void findMemberByProjectIdAndRole(
            Message.ProjectIdListAndMemberRoleRequest request,
            StreamObserver<Message.ProjectIdAndMemberResponse> responseObserver) {
        var projectIdList =
                request.getProjectIdList().stream()
                        .map(String::toString)
                        .collect(Collectors.toList());

        if (StringUtils.isNotBlank(request.getRole())) {
            var res =
                    projectMemberManager.findMemberByProjectIdAndRole(
                            projectIdList, RoleEnum.valueOf(request.getRole()));
            res.forEach(
                    (k, v) -> {
                        var memberResponse =
                                Message.ProjectIdAndMemberResponse.newBuilder()
                                        .setProjectId(k)
                                        .addMember(v.toMessage())
                                        .build();
                        responseObserver.onNext(memberResponse);
                    });
        } else {
            var res = projectMemberManager.findByProjectIds(projectIdList);
            res.forEach(
                    (k, v) -> {
                        var memberList =
                                IterableUtils.toList(v).stream()
                                        .map(Member::toMessage)
                                        .collect(Collectors.toList());
                        var memberResponse =
                                Message.ProjectIdAndMemberResponse.newBuilder()
                                        .setProjectId(k)
                                        .addAllMember(memberList)
                                        .build();
                        responseObserver.onNext(memberResponse);
                    });
        }
        responseObserver.onCompleted();
    }

    @Override
    public void findProjectIdByRoleAndUserId(
            Message.UserIdAndMemberRoleRequest request,
            StreamObserver<StringValue> responseObserver) {
        var res =
                projectMemberManager.findProjectIdByRoleAndUserId(
                        request.getUserId(), RoleEnum.valueOf(request.getRole()));
        res.forEach(e -> responseObserver.onNext(StringValue.of(e)));
        responseObserver.onCompleted();
    }

    @Override
    public void getSimilarProjectsByProjectId(
            Message.SimilarProjectRequest request,
            StreamObserver<Message.SimilarProjectResponse> responseObserver) {
        var similarProjectsByProjectId =
                grpcSimilarProjectProvider.getSimilarProjectsByProjectId(
                        request.getProjectIdList(),
                        request.getIncludeSimilarTypeList(),
                        request.getExcludeSimilarTypeList(),
                        request.getIncludeNotDuplicate());
        similarProjectsByProjectId.forEach(
                (k, v) -> {
                    var similarMap =
                            Maps.transformValues(
                                    v,
                                    s ->
                                            Message.ProjectSimilarTypeList.newBuilder()
                                                    .addAllSimilarType(s)
                                                    .build());
                    var response =
                            Message.SimilarProjectResponse.newBuilder()
                                    .setProjectId(k)
                                    .putAllSimilarProject(similarMap)
                                    .build();
                    responseObserver.onNext(response);
                });
        responseObserver.onCompleted();
    }

    @Override
    public void findProjectIdByStateChangeReasonQuery(
            Message.ProjectStateChangeReasonQueryRequest request,
            StreamObserver<com.bees360.common.Message.StringValueList> responseObserver) {
        var stateQuery = stateChangeReasonQuery2StateQuery(request);
        var projectIds =
                projectStateManager.findProjectByStateChangeReason(
                        request.getChangeReasonQuery().getChangeReasonListList(), stateQuery);
        responseObserver.onNext(
                com.bees360.common.Message.StringValueList.newBuilder()
                        .addAllValue(projectIds)
                        .build());
        responseObserver.onCompleted();
    }

    @Deprecated
    @Override
    public void findProjectByStateChangeReasonQuery(
            Message.ProjectStateChangeReasonQueryRequest request,
            StreamObserver<StringValue> responseObserver) {
        var stateQuery = stateChangeReasonQuery2StateQuery(request);
        var projectIds =
                projectStateManager.findProjectByStateChangeReason(
                        request.getChangeReasonQuery().getChangeReasonListList(), stateQuery);
        projectIds.forEach(id -> responseObserver.onNext(StringValue.of(id)));
        responseObserver.onCompleted();
    }

    private ProjectStateQuery stateChangeReasonQuery2StateQuery(
            Message.ProjectStateChangeReasonQueryRequest request) {
        var stateQueryBuilder = ProjectStateQuery.ProjectStateQueryBuilder.newBuilder();
        stateQueryBuilder.setServiceTypes(request.getServiceTypeList());
        stateQueryBuilder.setTimeStart(
                DateTimes.toInstant(request.getChangeReasonQuery().getTimeStart()));
        stateQueryBuilder.setTimeEnd(
                DateTimes.toInstant(request.getChangeReasonQuery().getTimeEnd()));
        stateQueryBuilder.setProjectCreatedStart(
                DateTimes.toInstant(request.getProjectCreatedStart()));
        stateQueryBuilder.setProjectCreatedEnd(DateTimes.toInstant(request.getProjectCreatedEnd()));
        stateQueryBuilder.setCompanyId(request.getCompanyIdList());
        return stateQueryBuilder.build();
    }

    @Override
    public void updateOperatingCompany(
            SetStringValueRequest request, StreamObserver<BoolValue> responseObserver) {
        var result =
                grpcProjectManager.updateProjectOperatingCompany(
                        request.getId(), request.getValue());
        responseObserver.onNext(BoolValue.of(result));
        responseObserver.onCompleted();
    }

    @Override
    public void updateProjectPolicyType(
            SetStringValueRequest request, StreamObserver<BoolValue> responseObserver) {
        var result =
                grpcProjectManager.updateProjectPolicyType(request.getId(), request.getValue());
        responseObserver.onNext(BoolValue.of(result));
        responseObserver.onCompleted();
    }

    @Override
    public void batchConfirmDuplicatedProject(
            Message.BatchConfirmDuplicateProjectRequest request,
            StreamObserver<Empty> responseObserver) {
        var requestMap =
                request.getRequestList().stream()
                        .collect(
                                Collectors.groupingBy(
                                        Message.ConfirmDuplicateProjectRequest::getIsDuplicate));
        var projectIdForDuplicate =
                Maps.transformValues(
                        requestMap,
                        list ->
                                list.stream()
                                        .map(Message.ConfirmDuplicateProjectRequest::getProjectId)
                                        .collect(Collectors.toList()));
        grpcSimilarProjectProvider.batchUpdateProjectDuplicate(projectIdForDuplicate);
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void updateCustomerDivision(
            SetStringValueRequest request, StreamObserver<BoolValue> responseObserver) {
        var result = grpcProjectManager.setCustomerDivision(request.getId(), request.getValue());
        responseObserver.onNext(BoolValue.of(result));
        responseObserver.onCompleted();
    }

    @Override
    public void createProject(
            Message.CreateProjectRequest request, StreamObserver<ProjectMessage> responseObserver) {
        var project = ProjectCreationRequest.from(request.getProject());
        var attachment = BatchAttachment.from(request.getAttachment());
        var result =
                genericProjectCreator.create(
                        project,
                        request.getAllowDuplication(),
                        Defaults.nullIfEmpty(request.getCreationChannel()),
                        attachment);
        var projectMessage =
                Optional.ofNullable(result)
                        .map(ProjectII::toMessage)
                        .orElse(ProjectMessage.getDefaultInstance());
        responseObserver.onNext(projectMessage);
        responseObserver.onCompleted();
    }

    @Override
    public void createProjectBatch(
            Message.CreateProjectBatchRequest request,
            StreamObserver<Message.CreateProjectBatchResponse> responseObserver) {
        var projectList =
                Iterables.transform(request.getProjectList(), ProjectCreationRequest::from);
        var result =
                batchProjectCreator.createBatch(
                        request.getBatchId(),
                        request.getCreator(),
                        projectList,
                        request.getAllowDuplication(),
                        Defaults.nullIfEmpty(request.getCreationChannel()));

        var response =
                Message.CreateProjectBatchResponse.newBuilder()
                        .putAllResult(
                                result.entrySet().stream()
                                        .collect(
                                                Collectors.toMap(
                                                        Map.Entry::getKey,
                                                        e -> e.getValue().toMessage())))
                        .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updateEstimateTotalPay(
            com.bees360.common.Message.SetDoubleValueRequest request,
            StreamObserver<BoolValue> responseObserver) {
        log.info(
                "Project update estimateTotalPay. projectId {} request {}",
                request.getId(),
                request);
        var result = grpcProjectManager.updateEstimateTotalPay(request.getId(), request.getValue());
        responseObserver.onNext(BoolValue.of(result));
        responseObserver.onCompleted();
    }

    @Override
    public void updateProjectPolicyRenewal(
            Message.UpdateProjectPolicyRenewal request,
            StreamObserver<BoolValue> responseObserver) {
        var result =
                grpcProjectManager.updateProjectPolicyRenewal(
                        request.getProjectId(), request.getIsRenewal());
        responseObserver.onNext(BoolValue.of(result));
        responseObserver.onCompleted();
    }
}
