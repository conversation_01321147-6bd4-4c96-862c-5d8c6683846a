package com.bees360.project.inspection;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.project.Message.InspectionCodeMessage;
import com.bees360.project.Message.UpdateInspectionCodeRequest;
import com.bees360.project.ProjectInspectionCodeServiceGrpc.ProjectInspectionCodeServiceImplBase;
import com.bees360.util.DateTimes;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.springframework.context.annotation.Import;

import java.util.Optional;

@Log4j2
@GrpcService
@Import({ExceptionTranslateInterceptor.class})
public class GrpcProjectInspectionCodeService extends ProjectInspectionCodeServiceImplBase {

    private final InspectionCodeManager inspectionCodeManager;

    public GrpcProjectInspectionCodeService(@NonNull InspectionCodeManager inspectionCodeManager) {
        this.inspectionCodeManager = inspectionCodeManager;
        log.info("Created {}(inspectionCodeManager={})", this, inspectionCodeManager);
    }

    @Override
    public void getByProjectId(
            StringValue request, StreamObserver<InspectionCodeMessage> responseObserver) {
        var inspectionCode = inspectionCodeManager.getByProjectId(request.getValue());
        responseObserver.onNext(
                Optional.ofNullable(inspectionCode)
                        .map(InspectionCode::toMessage)
                        .orElse(InspectionCodeMessage.getDefaultInstance()));
        responseObserver.onCompleted();
    }

    @Override
    public void updateInspectionCode(
            UpdateInspectionCodeRequest request, StreamObserver<Empty> responseObserver) {
        var expireAt =
                Optional.ofNullable(request.getExpireAt()).map(DateTimes::toInstant).orElse(null);
        inspectionCodeManager.updateInspectionCode(
                request.getProjectId(),
                request.getInspectionCode(),
                request.getInspectionCodeLink(),
                expireAt);
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
