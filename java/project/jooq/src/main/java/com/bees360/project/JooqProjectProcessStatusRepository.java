package com.bees360.project;

import static com.bees360.jooq.persistent.project.tables.ProjectProcessStatus.PROJECT_PROCESS_STATUS;
import static com.bees360.jooq.persistent.project.tables.ProjectProcessStatusHistory.PROJECT_PROCESS_STATUS_HISTORY;

import com.bees360.project.status.ProjectProcessStatusManager;
import com.bees360.project.status.ProjectStatus;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.jooq.DSLContext;

@Log4j2
public class JooqProjectProcessStatusRepository implements ProjectProcessStatusManager {

    private final JooqAbstractProjectStatusRepository jooqAbstractProjectStatusRepository;

    public JooqProjectProcessStatusRepository(DSLContext dsl) {
        this.jooqAbstractProjectStatusRepository =
                new JooqAbstractProjectStatusRepository<>(
                        dsl, PROJECT_PROCESS_STATUS, PROJECT_PROCESS_STATUS_HISTORY);
        log.info(
                "Created {}(jooqAbstractProjectStatusRepository={}).",
                this,
                this.jooqAbstractProjectStatusRepository);
    }

    @Override
    public boolean updateProcessStatus(
            @NonNull String projectId,
            @NonNull Message.ProjectStatus status,
            @NonNull String updatedBy,
            String comment) {
        return jooqAbstractProjectStatusRepository.updateStatus(
                projectId, status, updatedBy, comment);
    }

    @Override
    public ProjectStatus getProcessStatus(String projectId) {
        return jooqAbstractProjectStatusRepository.getStatus(projectId);
    }

    @Override
    public Iterable<? extends ProjectStatus> getProcessStatusHistory(String projectId) {
        return jooqAbstractProjectStatusRepository.getStatusHistory(projectId);
    }
}
