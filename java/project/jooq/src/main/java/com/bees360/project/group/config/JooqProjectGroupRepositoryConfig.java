package com.bees360.project.group.config;

import com.bees360.jooq.config.JooqConfig;
import com.bees360.project.group.JooqHardDeleteProjectGroupRepository;
import com.bees360.project.group.JooqProjectGroupRepository;

import org.jooq.DSLContext;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({
    JooqConfig.class,
})
public class JooqProjectGroupRepositoryConfig {

    /**
     * 通过开关配置决定用哪个jooq实现, 如果后续需要软删除，set project.app.group.enable-hard-delete = false.
     *
     * <p>PS: 如果要把 project-app 的实现类改成软删除的实现类要慎重，可能会导致 urgent 分组的数据倾斜问题
     */
    @ConditionalOnProperty(
            prefix = "project.app.group",
            value = "enable-hard-delete",
            havingValue = "false",
            matchIfMissing = true)
    @Bean({"jooqProjectGroupRepository", "projectGroupRepository"})
    public JooqProjectGroupRepository jooqProjectGroupRepository(DSLContext dsl) {
        return new JooqProjectGroupRepository(dsl);
    }

    /**
     * 目前的 project_group 应用场景中，软删除全改为硬删除没有问题. 为了避免 urgent 分组数据软删除后导致的数据倾斜问题，
     * 代码上线后通过开关在project-app中注入硬删除的实现类: set project.app.group.enable-hard-delete = true
     */
    @ConditionalOnProperty(
            prefix = "project.app.group",
            value = "enable-hard-delete",
            havingValue = "true")
    @Bean({"jooqProjectGroupRepository", "projectGroupRepository"})
    public JooqHardDeleteProjectGroupRepository jooqHardDeleteProjectGroupRepository(
            DSLContext dsl) {
        return new JooqHardDeleteProjectGroupRepository(dsl);
    }
}
