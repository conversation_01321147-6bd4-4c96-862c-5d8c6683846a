package com.bees360.project;

import static com.bees360.jooq.persistent.project.tables.ProjectOperationTagEnum.PROJECT_OPERATION_TAG_ENUM;
import static com.bees360.jooq.persistent.project.tables.ProjectOperationTagProject.PROJECT_OPERATION_TAG_PROJECT;

import com.bees360.jooq.persistent.project.tables.records.ProjectOperationTagEnumRecord;
import com.bees360.repository.AbstractJooqProvider;

import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.RecordMapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public class JooqOperationTagManager
        extends AbstractJooqProvider<ProjectOperationTagEnumRecord, OperationTag>
        implements ProjectOperationTagManager, OperationTagProvider {

    public JooqOperationTagManager(DSLContext dsl) {
        super(
                PROJECT_OPERATION_TAG_ENUM,
                dsl,
                PROJECT_OPERATION_TAG_ENUM.ID,
                OperationTag.NAMESPACE);
    }

    @Override
    public Iterable<? extends OperationTag> findByProjectId(String projectId) {
        if (StringUtils.isEmpty(projectId)) {
            return List.of();
        }
        return dsl.select()
                .from(PROJECT_OPERATION_TAG_ENUM)
                .leftJoin(PROJECT_OPERATION_TAG_PROJECT)
                .on(
                        PROJECT_OPERATION_TAG_ENUM.ID.eq(
                                PROJECT_OPERATION_TAG_PROJECT.OPERATION_TAG_ID))
                .where(
                        PROJECT_OPERATION_TAG_PROJECT
                                .PROJECT_ID
                                .eq(projectId)
                                .and(PROJECT_OPERATION_TAG_PROJECT.DELETED.eq("")))
                .fetch(mapping());
    }

    private RecordMapper<Record, OperationTag> mapping() {
        return r -> new RecordOperationTag(r);
    }

    @Override
    public void deleteByProjectIdAndTagId(String projectId, String tagId, String updatedBy) {
        delete(projectId, tagId, null, updatedBy);
    }

    @Transactional
    @Override
    public void updateByProjectIdAndTagId(String projectId, String tagId, String updatedBy) {
        delete(projectId, null, tagId, updatedBy);
        dsl.insertInto(
                        PROJECT_OPERATION_TAG_PROJECT,
                        PROJECT_OPERATION_TAG_PROJECT.PROJECT_ID,
                        PROJECT_OPERATION_TAG_PROJECT.OPERATION_TAG_ID,
                        PROJECT_OPERATION_TAG_PROJECT.CREATED_BY,
                        PROJECT_OPERATION_TAG_PROJECT.UPDATED_BY)
                .values(projectId, tagId, updatedBy, updatedBy)
                .onConflict(
                        PROJECT_OPERATION_TAG_PROJECT.PROJECT_ID,
                        PROJECT_OPERATION_TAG_PROJECT.OPERATION_TAG_ID,
                        PROJECT_OPERATION_TAG_PROJECT.DELETED)
                .doNothing()
                .execute();
    }

    private void delete(String projectId, String tagId, String excludeTagId, String updatedBy) {
        var condition = PROJECT_OPERATION_TAG_PROJECT.PROJECT_ID.eq(projectId);
        if (tagId != null) {
            condition = condition.and(PROJECT_OPERATION_TAG_PROJECT.OPERATION_TAG_ID.eq(tagId));
        }
        if (excludeTagId != null) {
            condition =
                    condition.and(
                            PROJECT_OPERATION_TAG_PROJECT.OPERATION_TAG_ID.notEqual(excludeTagId));
        }
        dsl.update(PROJECT_OPERATION_TAG_PROJECT)
                .set(PROJECT_OPERATION_TAG_PROJECT.DELETED, PROJECT_OPERATION_TAG_PROJECT.ID)
                .set(PROJECT_OPERATION_TAG_PROJECT.UPDATED_BY, updatedBy)
                .where(condition)
                .execute();
    }

    @Override
    protected OperationTag map(ProjectOperationTagEnumRecord record) {
        return new RecordOperationTag(record);
    }
}
