package com.bees360.policy;

import static com.bees360.jooq.persistent.project.tables.Policy.POLICY;
import static com.bees360.jooq.persistent.project.tables.PolicyCoverage.POLICY_COVERAGE;
import static com.bees360.jooq.persistent.project.tables.PolicyType.POLICY_TYPE;
import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.address.Address;
import com.bees360.project.Building;

import jakarta.annotation.Nullable;

import org.jooq.Record;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class RecordPolicy implements Policy {

    private final Record record;

    private final List<Coverage> coverages;

    public RecordPolicy(Record record) {
        this.record = record;
        this.coverages = new ArrayList<>();
    }

    @Override
    public String getId() {
        return record.get(POLICY.ID);
    }

    @Override
    public String getPolicyNo() {
        return record.get(POLICY.POLICY_NO);
    }

    @Override
    public LocalDate getPolicyEffectiveDate() {
        return record.get(POLICY.POLICY_EFFECTIVE_DATE);
    }

    @Override
    public Address getAddress() {
        return Address.AddressBuilder.newBuilder().setId(record.get(POLICY.ADDRESS_ID)).build();
    }

    @Override
    public Building getBuilding() {
        return Building.BuildingBuilder.newBuilder().setId(record.get(POLICY.BUILDING_ID)).build();
    }

    @Nullable
    @Override
    public String getType() {
        return record.get(POLICY_TYPE.NAME);
    }

    @Nullable
    @Override
    public Iterable<? extends Coverage> getCoverage() {
        return coverages;
    }

    @Nullable
    @Override
    public Boolean isRenewal() {
        return record.get(POLICY.IS_RENEWAL);
    }

    public void addCoverage(Record record) {
        var builder = Message.PolicyMessage.CoverageMessage.newBuilder();
        acceptIfNotNull(builder::setType, record.get(POLICY_COVERAGE.TYPE));
        acceptIfNotNull(
                builder::setAmount, record.get(POLICY_COVERAGE.AMOUNT), BigDecimal::doubleValue);
        var coverage = Coverage.from(builder.build());
        if (Objects.nonNull(coverage)) {
            coverages.add(coverage);
        }
    }
}
