package com.bees360.project.information.config;

import com.bees360.jooq.config.JooqConfig;
import com.bees360.project.information.JooqProjectInformationRepository;

import org.jooq.DSLContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({JooqConfig.class})
public class JooqProjectMessageRepositoryConfig {

    @Bean
    public JooqProjectInformationRepository jooqProjectInformationRepository(DSLContext dsl) {
        return new JooqProjectInformationRepository(dsl);
    }
}
