package com.bees360.contract;

import static com.bees360.jooq.persistent.project.tables.ContractService.CONTRACT_SERVICE;

import com.google.common.base.Preconditions;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;

import java.util.Objects;
import java.util.Optional;

@Log4j2
public class JooqContractServiceRepository implements ContractServiceManager {

    private final DSLContext dsl;

    public JooqContractServiceRepository(DSLContext dsl) {
        this.dsl = dsl;
        log.info("Created {}(dsl={}).", this, this.dsl);
    }

    @Override
    public Iterable<? extends ContractServiceItem> listAll() {
        return dsl.select().from(CONTRACT_SERVICE).fetch(RecordContractServiceItem::new);
    }

    @Override
    public String addServiceItem(ContractServiceItem item) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(item.getDisplayName()),
                "Display name of contract service can not be empty.");
        Preconditions.checkArgument(
                Objects.nonNull(item.getUnitPrice()),
                "Default price of contract service can not be null.");
        var key = Optional.ofNullable(item.getKey()).orElse(item.getDisplayName());
        return dsl.insertInto(
                        CONTRACT_SERVICE,
                        CONTRACT_SERVICE.KEY,
                        CONTRACT_SERVICE.DEFAULT_PRICE,
                        CONTRACT_SERVICE.DISPLAY_NAME)
                .values(key, item.getUnitPrice(), item.getDisplayName())
                .onConflict(CONTRACT_SERVICE.KEY)
                .doNothing()
                .returning()
                .fetchOne(CONTRACT_SERVICE.KEY);
    }

    @Override
    public void deleteServiceItem(String key) {
        dsl.deleteFrom(CONTRACT_SERVICE).where(CONTRACT_SERVICE.KEY.eq(key)).execute();
    }

    @Override
    public void updateServiceItem(ContractServiceItem item) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(item.getDisplayName()),
                "Display name of contract service can not be empty.");
        Preconditions.checkArgument(
                Objects.nonNull(item.getUnitPrice()),
                "Default price of contract service can not be null.");
        dsl.update(CONTRACT_SERVICE)
                .set(CONTRACT_SERVICE.DEFAULT_PRICE, item.getUnitPrice())
                .set(CONTRACT_SERVICE.DISPLAY_NAME, item.getDisplayName())
                .where(CONTRACT_SERVICE.KEY.eq(item.getKey()))
                .execute();
    }
}
