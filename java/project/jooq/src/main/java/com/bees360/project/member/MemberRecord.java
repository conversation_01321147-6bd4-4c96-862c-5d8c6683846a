package com.bees360.project.member;

import com.bees360.user.User;

class MemberRecord implements Member {

    private final User user;
    private final String role;

    private MemberRecord(User user, String role) {
        this.user = user;
        this.role = role;
    }

    @Override
    public User getUser() {
        return user;
    }

    @Override
    public String getRole() {
        return role;
    }

    static MemberRecord from(User user, String role) {
        return new MemberRecord(user, role);
    }
}
