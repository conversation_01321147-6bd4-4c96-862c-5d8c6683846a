package com.bees360.project;

import static com.bees360.jooq.persistent.project.tables.ProjectInvoicePayment.PROJECT_INVOICE_PAYMENT;
import static com.bees360.jooq.persistent.project.tables.ProjectInvoiceReceipt.PROJECT_INVOICE_RECEIPT;

import com.bees360.api.NotFoundException;
import com.bees360.jooq.persistent.project.enums.PaymentSource;
import com.bees360.jooq.persistent.project.tables.records.ProjectInvoicePaymentRecord;
import com.bees360.jooq.persistent.project.tables.records.ProjectInvoiceReceiptRecord;
import com.bees360.jooq.util.DSLUtils;
import com.bees360.project.Message.ProjectMessage.Invoice.Receipt;
import com.bees360.project.Message.ProjectMessage.Invoice.Receipt.Builder;
import com.bees360.project.invoice.ProjectInvoice;
import com.bees360.project.invoice.ProjectInvoiceReceipt;
import com.bees360.project.invoice.ProjectInvoiceReceiptManager;
import com.bees360.util.DateTimes;
import com.bees360.util.Functions;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Log4j2
public class JooqProjectInvoiceReceiptManager implements ProjectInvoiceReceiptManager {

    private final DSLContext dsl;

    private final JooqProjectInvoiceManager projectInvoiceManager;

    public JooqProjectInvoiceReceiptManager(
            DSLContext dsl, JooqProjectInvoiceManager projectInvoiceManager) {
        this.dsl = dsl;
        this.projectInvoiceManager = projectInvoiceManager;
    }

    @Override
    public ProjectInvoiceReceipt findByInvoiceNo(String invoiceNo) {
        return select(PROJECT_INVOICE_RECEIPT.INVOICE_NO.eq(invoiceNo));
    }

    @Override
    public Iterable<ProjectInvoiceReceipt> findByQBInvoiceNo(Iterable<String> QBInvoiceNos) {
        return dsl
                .selectFrom(PROJECT_INVOICE_RECEIPT)
                .where(PROJECT_INVOICE_RECEIPT.QB_INVOICE_NO.in(Iterables.toSet(QBInvoiceNos)))
                .stream()
                .map(JooqProjectInvoiceReceiptManager::fromRecord)
                .collect(Collectors.toList());
    }

    @Override
    public void initializeReceipt(String invoiceNo) {
        dsl.insertInto(
                        PROJECT_INVOICE_RECEIPT,
                        PROJECT_INVOICE_RECEIPT.INVOICE_NO,
                        PROJECT_INVOICE_RECEIPT.AMOUNT_PAID)
                .values(invoiceNo, BigDecimal.ZERO)
                .onConflict(PROJECT_INVOICE_RECEIPT.INVOICE_NO)
                .doNothing()
                .execute();
    }

    @Override
    public void setQBInvoiceNo(String invoiceNo, String qbInvoiceNo) {
        dsl.update(PROJECT_INVOICE_RECEIPT)
                .set(PROJECT_INVOICE_RECEIPT.QB_INVOICE_NO, qbInvoiceNo)
                .where(PROJECT_INVOICE_RECEIPT.INVOICE_NO.eq(invoiceNo))
                .execute();
    }

    @Override
    public ProjectInvoiceReceipt findByQBInvoiceNo(String QBInvoiceNo) {
        return select(PROJECT_INVOICE_RECEIPT.QB_INVOICE_NO.eq(QBInvoiceNo));
    }

    private ProjectInvoiceReceipt select(Condition condition) {
        ProjectInvoiceReceiptRecord record =
                dsl.selectFrom(PROJECT_INVOICE_RECEIPT).where(condition).fetchOne();
        if (record == null) {
            return null;
        }
        return fromRecord(record);
    }

    @Transactional
    @Override
    public void updateReceipt(ProjectInvoiceReceipt projectInvoiceReceipt) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(projectInvoiceReceipt.getQbInvoiceNo()),
                "Invalid QB invoice number");
        Preconditions.checkNotNull(projectInvoiceReceipt.getAmountPaid());
        String qbInvoiceNo = projectInvoiceReceipt.getQbInvoiceNo();
        ProjectInvoiceReceiptRecord originalReceipt =
                dsl.selectFrom(PROJECT_INVOICE_RECEIPT)
                        .where(PROJECT_INVOICE_RECEIPT.QB_INVOICE_NO.eq(qbInvoiceNo))
                        .forUpdate()
                        .fetchOne();
        if (null == originalReceipt) {
            throw new NotFoundException(
                    String.format("Receipt not found with invoice no '%s'", qbInvoiceNo));
        }
        String invoiceNo = originalReceipt.getInvoiceNo();
        ProjectInvoice projectInvoice = projectInvoiceManager.findByInvoiceNo(invoiceNo, true);

        // 总共需要付款多少
        BigDecimal totalToBePaid = projectInvoice.getInvoice().getTotal();
        // 本次付款多少
        BigDecimal amountReceived = projectInvoiceReceipt.getAmountPaid();
        // 已经付了
        BigDecimal balance = originalReceipt.getAmountPaid();
        BigDecimal amountPaidSum = balance.add(amountReceived);

        int comparedResult = amountPaidSum.compareTo(totalToBePaid);

        log.info(
                "Update invoice {} receipt with data(total={}, amountReceived={}, paidAlready={}).",
                invoiceNo,
                totalToBePaid,
                amountReceived,
                balance);
        ProjectInvoiceReceiptRecord record = new ProjectInvoiceReceiptRecord();
        record.setInvoiceNo(invoiceNo);
        Functions.acceptIfNotNull(record::setCheckNo, projectInvoiceReceipt.getCheckNo());
        LocalDate clearedDate = projectInvoiceReceipt.getClearedDate();

        if (clearedDate != null && comparedResult >= 0) {
            record.setClearedDate(clearedDate);
        }
        record.setAmountPaid(amountPaidSum);
        dsl.executeUpdate(record);
    }

    @Override
    public void saveInvoicePayment(
            String paymentId,
            String source,
            Iterable<ProjectInvoiceReceipt> receipts,
            LocalDate paymentDate) {
        Preconditions.checkArgument(
                StringUtils.isNoneEmpty(paymentId),
                "Fail to save project invoice payment: payment id cannot be empty.");
        Preconditions.checkArgument(
                paymentDate != null,
                "Fail to save project invoice payment: payment date is illegal.");
        List<ProjectInvoicePaymentRecord> records = new ArrayList<>();
        receipts.forEach(
                item -> {
                    var record = new ProjectInvoicePaymentRecord();
                    record.setInvoiceNo(item.getInvoiceNo());
                    record.setPaymentId(paymentId);
                    record.setSource(PaymentSource.valueOf(source));
                    record.setIsDeleted(false);
                    record.setAmountPaid(item.getAmountPaid());
                    record.setPaymentDate(paymentDate);
                    records.add(record);
                });

        DSLUtils.upsert(
                dsl,
                PROJECT_INVOICE_PAYMENT,
                records,
                PROJECT_INVOICE_PAYMENT.PAYMENT_ID,
                PROJECT_INVOICE_PAYMENT.INVOICE_NO,
                PROJECT_INVOICE_PAYMENT.SOURCE);
    }

    @Override
    public void deleteInvoicePayment(String paymentId, String source) {
        dsl.update(PROJECT_INVOICE_PAYMENT)
                .set(PROJECT_INVOICE_PAYMENT.IS_DELETED, true)
                .where(PROJECT_INVOICE_PAYMENT.PAYMENT_ID.eq(paymentId))
                .and(PROJECT_INVOICE_PAYMENT.SOURCE.eq(PaymentSource.valueOf(source)))
                .execute();
    }

    private static ProjectInvoiceReceipt fromRecord(ProjectInvoiceReceiptRecord record) {
        Builder builder = Receipt.newBuilder();
        builder.setInvoiceNo(record.getInvoiceNo());
        Functions.acceptIfNotNull(builder::setQbInvoiceNo, record.getQbInvoiceNo());
        Functions.acceptIfNotNull(builder::setCheckNo, record.getCheckNo());
        Functions.acceptIfNotNull(
                builder::setClearedDate,
                Optional.ofNullable(record.getClearedDate())
                        .map(DateTimes::toProtoDate)
                        .orElse(null));
        builder.setAmountPaid(record.getAmountPaid().doubleValue());
        return ProjectInvoiceReceipt.from(builder.build());
    }
}
