package com.bees360.project;

import com.bees360.address.Address;
import com.bees360.api.Entity;
import com.bees360.attachment.BatchAttachment;
import com.bees360.building.JooqBuildingRepository;
import com.bees360.contract.Contract;
import com.bees360.customer.Division;
import com.bees360.policy.JooqPolicyRepository;
import com.bees360.policy.Policy;
import com.bees360.project.attachment.JooqProjectAttachmentRepository;
import com.bees360.project.claim.ProjectCatastropheManager;
import com.bees360.project.creator.ClaimClaimNoAndAddressDuplicationValidator;
import com.bees360.project.creator.UnderwritingInspectionNoAddressDuplicationValidator;
import com.bees360.project.member.JooqProjectMemberRepository;
import com.bees360.project.member.RoleEnum;
import com.google.common.collect.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/** This class for init project data in database. */
@Log4j2
public class JooqGenericProjectCreator implements GenericProjectCreator {

    private final CheckDuplicationProjectCreator projectCreator;
    private final JooqProjectIIRepository projectIIManager;
    private final JooqPolicyRepository policyManager;
    private final JooqBuildingRepository buildingManager;
    private final JooqProjectMemberRepository memberManager;
    private final JooqProjectCatastropheManager projectCatastropheManager;
    private final JooqProjectContactRepository contactManager;
    private final JooqExternalIntegrationManager externalIntegrationManager;
    private final JooqProjectAttachmentRepository projectAttachmentManager;

    public JooqGenericProjectCreator(
            DSLContext dsl,
            JooqProjectIIRepository projectIIManager,
            JooqPolicyRepository policyManager,
            JooqBuildingRepository buildingManager,
            JooqProjectMemberRepository memberManager,
            JooqProjectCatastropheManager projectCatastropheManager,
            JooqProjectContactRepository contactManager,
            JooqExternalIntegrationManager externalIntegrationManager,
            JooqProjectAttachmentRepository projectAttachmentManager) {
        this.projectIIManager = projectIIManager;
        this.policyManager = policyManager;
        this.buildingManager = buildingManager;
        this.memberManager = memberManager;
        this.projectCatastropheManager = projectCatastropheManager;
        this.contactManager = contactManager;
        this.externalIntegrationManager = externalIntegrationManager;
        this.projectAttachmentManager = projectAttachmentManager;

        var claimDuplicationValidator = new ClaimClaimNoAndAddressDuplicationValidator(dsl);
        var underwritingDuplicationValidator =
                new UnderwritingInspectionNoAddressDuplicationValidator(dsl);
        this.projectCreator =
                new CheckDuplicationProjectCreator(
                        projectIIManager,
                        claimDuplicationValidator,
                        underwritingDuplicationValidator);

        log.info("Created {}.", this);
    }

    @Transactional
    @Override
    public ProjectII create(
            ProjectCreationRequest project,
            boolean allowDuplication,
            String creationChannel,
            BatchAttachment attachment) {
        var policy = createPolicy(project.getPolicy());
        var originAddress = project.getPolicy().getAddress().getAddress();
        var result =
                ProjectTypeEnum.CLAIM == project.getProjectType()
                        ? createClaim(
                                project,
                                policy.getId(),
                                originAddress,
                                allowDuplication,
                                creationChannel)
                        : createUnderwriting(
                                project,
                                policy.getId(),
                                originAddress,
                                allowDuplication,
                                creationChannel);

        var projectId = result.getId();
        initExtraInfo(project, projectId);
        saveAttachment(projectId, attachment, project.getCreatedBy());
        saveExternalIntegration(project, result.getId());
        log.info("Finish create project {}.", project.toMessage());
        return result;
    }

    private ProjectII createClaim(
            ProjectCreationRequest project,
            String policyId,
            String originAddress,
            boolean allowDuplication,
            String creationChannel) {
        return projectCreator.create(
                BasicProjectCreation.ClaimCreationBuilder.newBuilder()
                        .setClaim(project.getClaim())
                        .setId(project.getProjectId())
                        .setContractId(project.getContract().getId())
                        .setPolicyId(policyId)
                        .setInspection(project.getInspection())
                        .setCreatedBy(project.getCreatedBy())
                        .setDivisionId(
                                Optional.ofNullable(project.getProjectDivision())
                                        .map(Division::getId)
                                        .orElse(null))
                        .setIsTestCase(project.isTestCase())
                        .setNote(project.getNote())
                        .setOriginAddress(originAddress)
                        .setMetadata(project.getMetadata())
                        .build(),
                allowDuplication,
                creationChannel);
    }

    private ProjectII createUnderwriting(
            ProjectCreationRequest project,
            String policyId,
            String originAddress,
            boolean allowDuplication,
            String creationChannel) {
        return projectCreator.create(
                BasicProjectCreation.UnderwritingCreationBuilder.newBuilder()
                        .setUnderwriting(project.getUnderwriting())
                        .setId(project.getProjectId())
                        .setContractId(project.getContract().getId())
                        .setPolicyId(policyId)
                        .setInspection(project.getInspection())
                        .setCreatedBy(project.getCreatedBy())
                        .setDivisionId(
                                Optional.ofNullable(project.getProjectDivision())
                                        .map(Division::getId)
                                        .orElse(null))
                        .setIsTestCase(project.isTestCase())
                        .setNote(project.getNote())
                        .setOriginAddress(originAddress)
                        .setMetadata(project.getMetadata())
                        .build(),
                allowDuplication,
                creationChannel);
    }

    private Policy createPolicy(Policy policy) {
        var address = policy.getAddress();
        var building = buildingManager.create(policy.getBuilding(), address.getId());
        return policyManager.create(
                policy.getPolicyNo(),
                policy.getPolicyEffectiveDate(),
                address.getId(),
                building.getId(),
                policy.getType(),
                policy.getCoverage(),
                policy.isRenewal());
    }

    private void initExtraInfo(ProjectCreationRequest project, String projectId) {
        var catastrophe = project.getCatastrophe();
        if (catastrophe != null) {
            var insuredBy =
                    Optional.ofNullable(project.getContract())
                            .map(Contract::getInsuredBy)
                            .map(Entity::getId)
                            .orElse(ProjectCatastropheManager.DEFAULT_CUSTOMER_ID);
            var state =
                    Optional.ofNullable(project.getPolicy())
                            .map(Policy::getAddress)
                            .map(Address::getState)
                            .orElse(ProjectCatastropheManager.DEFAULT_ADDRESS_STATE);
            projectCatastropheManager.addProjectCatastrophe(
                    projectId,
                    catastrophe.getSerialNumber(),
                    catastrophe.getLevel(),
                    insuredBy,
                    state);
        }

        if (StringUtils.isNoneEmpty(project.getOperatingCompany())) {
            projectIIManager.updateProjectOperatingCompany(
                    projectId, project.getOperatingCompany());
        }

        var createdBy = project.getCreatedBy();
        var member = project.getMember();
        if (!Iterables.isEmpty(member)) {
            member.forEach(
                    m ->
                            memberManager.setMember(
                                    projectId,
                                    m.getUser().getId(),
                                    RoleEnum.valueOf(m.getRole()),
                                    createdBy));
        } else {
            // 默认添加member creator
            memberManager.setMember(projectId, createdBy, RoleEnum.CREATOR, createdBy);
        }

        var contact = project.getContact();
        if (contact != null) {
            contact.forEach(c -> contactManager.addContact(projectId, c, createdBy));
        }
    }

    private void saveAttachment(String projectId, BatchAttachment attachment, String createdBy) {
        if (attachment == null) {
            return;
        }

        projectAttachmentManager.addAttachments(
                projectId, attachment.getAttachments(), createdBy, attachment.getDescription());
    }

    private void saveExternalIntegration(ProjectCreationRequest project, String projectId) {
        var integration = project.getIntegration();
        if (integration == null) {
            return;
        }

        var builder = integration.toMessage().toBuilder();
        var dataset = integration.getDataset();
        if (StringUtils.isEmpty(dataset)) {
            var insuredBy = project.getContract().getInsuredBy();
            builder.setDataset(Optional.ofNullable(insuredBy.getCompanyKey()).orElse(""));
        }
        builder.setProjectId(projectId);
        externalIntegrationManager.save(ExternalIntegration.from(builder.build()));
    }
}
