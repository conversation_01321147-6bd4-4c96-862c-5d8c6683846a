package com.bees360.project.state;

import static com.bees360.jooq.persistent.project.Tables.CONTRACT;
import static com.bees360.jooq.persistent.project.Tables.PROJECT;
import static com.bees360.jooq.persistent.project.Tables.PROJECT_CLAIM;
import static com.bees360.jooq.persistent.project.Tables.PROJECT_STATE;
import static com.bees360.jooq.persistent.project.Tables.PROJECT_STATE_CHANGE_REASON;
import static com.bees360.jooq.persistent.project.Tables.PROJECT_STATE_HISTORY;
import static com.bees360.jooq.persistent.project.Tables.PROJECT_UNDERWRITING;

import com.bees360.jooq.persistent.project.enums.ProjectStateEnum;
import com.bees360.project.Message;
import com.bees360.util.Defaults;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.jooq.Condition;
import org.jooq.DSLContext;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Log4j2
public class JooqProjectStateManager implements ProjectStateManager {
    private final DSLContext dsl;

    private final int COMMENT_MAX_LENGTH = 1000;

    private final Predicate<String> COMMENT_IS_VALID =
            comment -> comment == null || comment.length() <= COMMENT_MAX_LENGTH;

    public JooqProjectStateManager(DSLContext dsl) {
        this.dsl = dsl;
    }

    @Override
    public ProjectOperationFeedback changeProjectState(
            String projectId,
            Message.ProjectMessage.ProjectState.ProjectStateEnum state,
            String changeReason,
            String changedBy,
            String comment,
            Long version,
            boolean changeForce) {
        comment =
                Optional.ofNullable(comment)
                        .map(String::trim)
                        .map(Defaults::nullIfEmpty)
                        .orElse(null);
        Preconditions.checkArgument(
                COMMENT_IS_VALID.test(comment),
                "The comment length should not longer than %s for '%s'",
                COMMENT_MAX_LENGTH,
                comment);
        var stateValue = ProjectStateEnum.valueOf(state.name());
        var changeReasonId =
                dsl.select(PROJECT_STATE_CHANGE_REASON.ID)
                        .from(PROJECT_STATE_CHANGE_REASON)
                        .where(PROJECT_STATE_CHANGE_REASON.ID.eq(changeReason))
                        .fetchOne(PROJECT_STATE_CHANGE_REASON.ID);
        if (changeReasonId == null) {
            throw new IllegalArgumentException(
                    String.format("Change reason with id %s not existed", changeReason));
        }
        var condition = PROJECT_STATE.PROJECT_STATE_.ne(stateValue);

        var step =
                dsl.insertInto(
                                PROJECT_STATE,
                                PROJECT_STATE.PROJECT_ID,
                                PROJECT_STATE.PROJECT_STATE_,
                                PROJECT_STATE.LAST_STATE_CHANGE_REASON_ID,
                                PROJECT_STATE.UPDATED_BY,
                                PROJECT_STATE.COMMENT,
                                PROJECT_STATE.VERSION)
                        .values(projectId, stateValue, changeReasonId, changedBy, comment, version)
                        .onConflict(PROJECT_STATE.PROJECT_ID)
                        .doUpdate()
                        .set(PROJECT_STATE.PROJECT_STATE_, stateValue)
                        .set(PROJECT_STATE.LAST_STATE_CHANGE_REASON_ID, changeReasonId)
                        .set(PROJECT_STATE.UPDATED_BY, changedBy)
                        .set(PROJECT_STATE.COMMENT, comment);
        if (version != null) {
            condition =
                    condition.and(
                            PROJECT_STATE
                                    .VERSION
                                    .isNull()
                                    .or(PROJECT_STATE.VERSION.lessThan(version)));
            step = step.set(PROJECT_STATE.VERSION, version);
        }
        if (step.where(condition).execute() == 0) {
            throw new IllegalArgumentException(
                    String.format(
                            "Failed to update project '%s' state(%s, reasonId=%s, comment=%s)",
                            projectId, state, changeReason, comment));
        }
        return ProjectOperationFeedback.from(
                Message.ProjectOperationFeedback.newBuilder()
                        .setProjectId(projectId)
                        .setOperationFeedback(
                                Message.ProjectOperationFeedback.OperationFeedback.ALLOWED)
                        .build());
    }

    @Override
    public Iterable<String> findProjectByProjectState(
            Iterable<Message.ProjectMessage.ProjectState.ProjectStateEnum> state,
            ProjectStateQuery stateQuery) {
        if (com.google.common.collect.Iterables.isEmpty(state)) {
            throw new IllegalArgumentException(
                    "Cannot search projects with empty state parameter.");
        }
        var stateList =
                Iterables.toStream(state)
                        .map(stateEnum -> ProjectStateEnum.valueOf(stateEnum.name()))
                        .collect(Collectors.toList());
        var condition = PROJECT_STATE.PROJECT_STATE_.in(stateList);
        condition = buildCondition(condition, stateQuery);
        return dsl.select(PROJECT_STATE.PROJECT_ID)
                .from(PROJECT_STATE)
                .join(PROJECT)
                .on(PROJECT.ID.eq(PROJECT_STATE.PROJECT_ID))
                .leftOuterJoin(CONTRACT)
                .on(CONTRACT.ID.eq(PROJECT.CONTRACT_ID))
                .leftOuterJoin(PROJECT_CLAIM)
                .on(PROJECT_CLAIM.PROJECT_ID.eq(PROJECT.ID))
                .leftOuterJoin(PROJECT_UNDERWRITING)
                .on(PROJECT_UNDERWRITING.PROJECT_ID.eq(PROJECT.ID))
                .where(condition)
                .fetch(PROJECT_STATE.PROJECT_ID);
    }

    @Override
    public Iterable<String> findProjectByStateChangeReason(
            Iterable<String> changeReason, ProjectStateQuery stateQuery) {
        if (com.google.common.collect.Iterables.isEmpty(changeReason)) {
            return List.of();
        }
        var changeReasonList = Iterables.toList(changeReason);
        var condition = PROJECT_STATE_CHANGE_REASON.DISPLAY_TEXT.in(changeReasonList);
        condition = buildCondition(condition, stateQuery);
        return dsl.select(PROJECT.ID)
                .from(PROJECT)
                .join(PROJECT_STATE)
                .on(PROJECT.ID.eq(PROJECT_STATE.PROJECT_ID))
                .join(PROJECT_STATE_CHANGE_REASON)
                .on(PROJECT_STATE.LAST_STATE_CHANGE_REASON_ID.eq(PROJECT_STATE_CHANGE_REASON.ID))
                .leftOuterJoin(CONTRACT)
                .on(CONTRACT.ID.eq(PROJECT.CONTRACT_ID))
                .leftOuterJoin(PROJECT_CLAIM)
                .on(PROJECT_CLAIM.PROJECT_ID.eq(PROJECT.ID))
                .leftOuterJoin(PROJECT_UNDERWRITING)
                .on(PROJECT_UNDERWRITING.PROJECT_ID.eq(PROJECT.ID))
                .where(condition)
                .fetch(PROJECT.ID);
    }

    @Override
    public Iterable<? extends ProjectState> findStateHistoryByProjectId(String projectId) {
        return dsl.select()
                .from(PROJECT_STATE_HISTORY)
                .leftOuterJoin(PROJECT_STATE_CHANGE_REASON)
                .on(PROJECT_STATE_HISTORY.STATE_CHANGE_REASON_ID.eq(PROJECT_STATE_CHANGE_REASON.ID))
                .where(PROJECT_STATE_HISTORY.PROJECT_ID.eq(projectId))
                .fetch(JooqProjectStateHistory::new);
    }

    private Condition buildCondition(Condition condition, ProjectStateQuery stateQuery) {
        if (Objects.isNull(stateQuery)) {
            return condition;
        }
        var timeStart = stateQuery.getTimeStart();
        var timeEnd = stateQuery.getTimeEnd();
        var projectCreatedStart = stateQuery.getProjectCreatedStart();
        var projectCreatedEnd = stateQuery.getProjectCreatedEnd();
        var companyId = stateQuery.getCompanyId();
        var serviceType = stateQuery.getServiceType();
        if (Objects.nonNull(timeStart)) {
            condition = condition.and(PROJECT_STATE.UPDATED_AT.greaterOrEqual(timeStart));
        }
        if (Objects.nonNull(timeEnd)) {
            condition = condition.and(PROJECT_STATE.UPDATED_AT.lessOrEqual(timeEnd));
        }
        if (Objects.nonNull(projectCreatedStart)) {
            condition = condition.and(PROJECT.CREATED_AT.greaterOrEqual(projectCreatedStart));
        }
        if (Objects.nonNull(projectCreatedEnd)) {
            condition = condition.and(PROJECT.CREATED_AT.lessOrEqual(projectCreatedEnd));
        }
        if (Objects.nonNull(companyId)) {
            var ids = Iterables.toList(companyId);
            if (CollectionUtils.isNotEmpty(ids)) {
                condition =
                        condition.and(
                                CONTRACT.INSURED_BY.in(ids).or(CONTRACT.PROCESSED_BY.in(ids)));
            }
        }
        if (Objects.nonNull(serviceType)) {
            var types =
                    Iterables.toStream(serviceType)
                            .map(Message.ServiceType::getNumber)
                            .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(types)) {
                condition =
                        condition.and(
                                PROJECT_CLAIM
                                        .SERVICE_TYPE
                                        .in(types)
                                        .or(PROJECT_UNDERWRITING.SERVICE_TYPE.in(types)));
            }
        }
        return condition;
    }
}
