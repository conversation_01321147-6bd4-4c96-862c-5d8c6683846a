package com.bees360.project.member;

import com.bees360.user.User;
import com.bees360.user.UserKeyProvider;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.jooq.DSLContext;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Log4j2
public class UserFillProjectMemberManager extends JooqProjectMemberRepository {

    private final UserProvider userProvider;
    private final UserKeyProvider userKeyProvider;

    public UserFillProjectMemberManager(
            DSLContext dslContext, UserProvider userProvider, UserKeyProvider userKeyProvider) {
        super(dslContext);
        this.userProvider = userProvider;
        this.userKeyProvider = userKeyProvider;
        log.info(
                "Created '{}(userProvider={} userKeyProvider = {})'",
                this,
                this.userProvider,
                this.userKeyProvider);
    }

    @Override
    public Iterable<? extends Member> findMemberByProjectId(String projectId) {
        Iterable<? extends Member> member = super.findMemberByProjectId(projectId);
        return fillMembers(member);
    }

    @Override
    public Map<String, Iterable<? extends Member>> findByProjectIds(Iterable<String> projectIds) {
        var map = super.findByProjectIds(projectIds);
        Map<String, Iterable<? extends Member>> result = new HashMap<>();
        map.forEach(
                (k, v) -> {
                    var m = fillMembers(v);
                    result.put(k, m);
                });
        return result;
    }

    @Override
    public Map<String, ? extends Member> findMemberByProjectIdAndRole(
            Iterable<String> projectIds, RoleEnum role) {
        var map = super.findMemberByProjectIdAndRole(projectIds, role);

        var userIds =
                map.values().stream()
                        .map(Member::getUser)
                        .map(User::getId)
                        .collect(Collectors.toList());

        Set<String> userIdSet = new HashSet<>(userIds);
        var keyIdUserMap = userKeyProvider.findUsersByKeys(userIds, null);
        var userKey = keyIdUserMap.keySet();
        userIdSet.removeAll(userKey);
        var pgIdUserMap =
                Iterables.toStream(userProvider.findUserById(userIdSet))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(User::getId, user -> user));

        Map<String, Member> result = new HashMap<>();
        map.forEach(
                (k, v) -> {
                    var r = keyIdUserMap.get(v.getUser().getId());
                    var r2 = pgIdUserMap.get(v.getUser().getId());

                    MemberRecord record;
                    if (r != null) {
                        record = MemberRecord.from(r, v.getRole());
                    } else if (r2 != null) {
                        record = MemberRecord.from(r2, v.getRole());
                    } else {
                        record = MemberRecord.from(v.getUser(), v.getRole());
                    }
                    result.put(k, record);
                });
        return result;
    }

    private List<? extends Member> fillMembers(Iterable<? extends Member> member) {
        var memberList = Iterables.toList(member);
        var userIds =
                memberList.stream()
                        .map(Member::getUser)
                        .map(User::getId)
                        .collect(Collectors.toSet());

        Set<String> userIdSet = new HashSet<>(userIds);
        var keyIdUserMap = userKeyProvider.findUsersByKeys(userIds, null);
        var userKey = keyIdUserMap.keySet();
        userIdSet.removeAll(userKey);
        var pgIdUserMap =
                Iterables.toStream(userProvider.findUserById(userIdSet))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(User::getId, user -> user));

        return Iterables.toStream(member)
                .map(
                        v -> {
                            var r = keyIdUserMap.get(v.getUser().getId());
                            var r2 = pgIdUserMap.get(v.getUser().getId());

                            MemberRecord record;
                            if (r != null) {
                                record = MemberRecord.from(r, v.getRole());
                            } else if (r2 != null) {
                                record = MemberRecord.from(r2, v.getRole());
                            } else {
                                record = MemberRecord.from(v.getUser(), v.getRole());
                            }
                            return record;
                        })
                .collect(Collectors.toList());
    }
}
