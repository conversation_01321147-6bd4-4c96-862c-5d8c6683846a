package com.bees360.project;

import static com.bees360.jooq.persistent.project.Tables.CONTRACT;
import static com.bees360.jooq.persistent.project.Tables.CUSTOMER;
import static com.bees360.jooq.persistent.project.Tables.PROJECT;
import static com.bees360.jooq.persistent.project.Tables.PROJECT_CLAIM;
import static com.bees360.jooq.persistent.project.Tables.PROJECT_DAYS_OLD;
import static com.bees360.jooq.persistent.project.Tables.PROJECT_UNDERWRITING;

import com.bees360.util.Iterables;

import org.apache.commons.collections4.CollectionUtils;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.impl.DSL;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class JooqProjectDaysOldProvider implements ProjectDaysOldProvider {
    private final DSLContext dsl;

    private static final String COUNT_DAYS_OLD =
            "GREATEST(pre_days_old  + floor ((EXTRACT(EPOCH FROM(now())) - EXTRACT(EPOCH"
                    + " FROM(coalesce(opened_or_effective_at, now())))) / 86400), 0) ";

    public JooqProjectDaysOldProvider(DSLContext dsl) {
        this.dsl = dsl;
    }

    @Override
    public Iterable<String> findProjectByDaysOldQuery(ProjectDaysOldQuery daysOldQuery) {
        Condition condition = DSL.trueCondition();
        var daysOldStart = daysOldQuery.getDaysOldStart();
        var daysOldEnd = daysOldQuery.getDaysOldEnd();
        final String INSURED_CUSTOMER = "INSURED_CUSTOMER";
        final String PROCESSED_CUSTOMER = "PROCESSED_CUSTOMER";
        var insuredCustomer = CUSTOMER.as(INSURED_CUSTOMER);
        var processedCustomer = CUSTOMER.as(PROCESSED_CUSTOMER);
        var companyKey = daysOldQuery.getCompanyKey();
        if (CollectionUtils.isNotEmpty(companyKey)) {
            condition =
                    condition.and(
                            insuredCustomer
                                    .KEY
                                    .in(companyKey)
                                    .or(processedCustomer.KEY.in(companyKey)));
        }
        if (daysOldStart != null) {
            condition = condition.and(COUNT_DAYS_OLD + " >= " + daysOldStart);
        }
        if (daysOldEnd != null) {
            // 防止当daysOldStart = daysOldEnd时，查询出所有project
            var daysAfterDaysOldEnd = daysOldEnd + 1;
            condition = condition.and(COUNT_DAYS_OLD + " < " + daysAfterDaysOldEnd);
        }
        condition = buildDaysOldCondition(condition, daysOldQuery);
        return dsl.select(PROJECT_DAYS_OLD.PROJECT_ID)
                .from(PROJECT_DAYS_OLD)
                .join(PROJECT)
                .on(PROJECT_DAYS_OLD.PROJECT_ID.eq(PROJECT.ID))
                .join(CONTRACT)
                .on(PROJECT.CONTRACT_ID.eq(CONTRACT.ID))
                .leftOuterJoin(PROJECT_CLAIM)
                .on(PROJECT_CLAIM.PROJECT_ID.eq(PROJECT.ID))
                .leftOuterJoin(PROJECT_UNDERWRITING)
                .on(PROJECT_UNDERWRITING.PROJECT_ID.eq(PROJECT.ID))
                .leftOuterJoin(insuredCustomer)
                .on(insuredCustomer.ID.eq(CONTRACT.INSURED_BY))
                .leftOuterJoin(processedCustomer)
                .on(processedCustomer.ID.eq(CONTRACT.PROCESSED_BY))
                .where(condition)
                .fetch(PROJECT_DAYS_OLD.PROJECT_ID);
    }

    @Override
    public Map<String, Integer> findProjectDaysOld(Iterable<String> projectId) {
        if (com.google.common.collect.Iterables.isEmpty(projectId)) {
            return Map.of();
        }
        var ids = Iterables.toList(projectId);
        return dsl.select(
                        PROJECT_DAYS_OLD.PROJECT_ID,
                        PROJECT_DAYS_OLD.PRE_DAYS_OLD,
                        PROJECT_DAYS_OLD.OPENED_OR_EFFECTIVE_AT)
                .from(PROJECT_DAYS_OLD)
                .where(PROJECT_DAYS_OLD.PROJECT_ID.in(ids))
                .fetchMap(
                        PROJECT_DAYS_OLD.PROJECT_ID,
                        JooqProjectDaysOldProvider::countDaysOldFromRecord);
    }

    @Override
    public ProjectDaysOld findDaysOld(String projectId) {
        var condition = PROJECT_DAYS_OLD.PROJECT_ID.eq(projectId);
        return selectDaysOld(condition).fetchOne(RecordProjectDaysOld::new);
    }

    private SelectConditionStep<? extends Record> selectDaysOld(Condition condition) {
        return dsl.select(
                        PROJECT_DAYS_OLD.PROJECT_ID,
                        PROJECT_DAYS_OLD.PRE_DAYS_OLD,
                        PROJECT_DAYS_OLD.OPENED_OR_EFFECTIVE_AT)
                .from(PROJECT_DAYS_OLD)
                .where(condition);
    }

    public static Integer countDaysOldFromRecord(Record r) {
        var preDaysOld = r.get(PROJECT_DAYS_OLD.PRE_DAYS_OLD);
        if (preDaysOld == null) {
            return 0;
        }
        var recordOpenedAt = r.get(PROJECT_DAYS_OLD.OPENED_OR_EFFECTIVE_AT);
        // Current project state is PROJECT_OPEN when lastOpenedAt is not null,
        // should count the days old of the current state additionally.
        var currentOpenedDaysOld =
                Optional.ofNullable(recordOpenedAt)
                        .map(
                                o -> {
                                    var countValue =
                                            Duration.between(o.toInstant(), Instant.now()).toDays();
                                    // Avoid negative value.
                                    // e.g. policy effective date is after now.
                                    return Math.max(countValue, 0);
                                })
                        .orElse(0L);
        return preDaysOld + currentOpenedDaysOld.intValue();
    }

    private Condition buildDaysOldCondition(Condition condition, ProjectDaysOldQuery daysOldQuery) {
        if (daysOldQuery == null) {
            return condition;
        }
        var companyId = daysOldQuery.getCompanyId();
        var serviceType = daysOldQuery.getServiceType();
        var projectCreatedStart = daysOldQuery.getProjectCreatedStart();
        var projectCreatedEnd = daysOldQuery.getProjectCreatedEnd();
        if (Objects.nonNull(projectCreatedStart)) {
            condition = condition.and(PROJECT.CREATED_AT.greaterOrEqual(projectCreatedStart));
        }
        if (Objects.nonNull(projectCreatedEnd)) {
            condition = condition.and(PROJECT.CREATED_AT.lessOrEqual(projectCreatedEnd));
        }
        if (Objects.nonNull(companyId)) {
            var ids = Iterables.toList(companyId);
            if (CollectionUtils.isNotEmpty(ids)) {
                condition =
                        condition.and(
                                CONTRACT.INSURED_BY.in(ids).or(CONTRACT.PROCESSED_BY.in(ids)));
            }
        }
        if (Objects.nonNull(serviceType)) {
            var types =
                    Iterables.toStream(serviceType)
                            .map(Message.ServiceType::getNumber)
                            .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(types)) {
                condition =
                        condition.and(
                                PROJECT_CLAIM
                                        .SERVICE_TYPE
                                        .in(types)
                                        .or(PROJECT_UNDERWRITING.SERVICE_TYPE.in(types)));
            }
        }
        return condition;
    }
}
