package com.bees360.project;

import com.bees360.address.Address;
import com.bees360.attachment.config.JooqAttachmentRepositoryConfig;
import com.bees360.building.config.JooqBuildingRepositoryConfig;
import com.bees360.jooq.persistent.project.enums.ProjectIntegrationType;
import com.bees360.policy.PolicyRepository;
import com.bees360.policy.config.JooqPolicyRepositoryConfig;
import com.bees360.project.attachment.ProjectAttachmentManager;
import com.bees360.project.attachment.config.JooqProjectAttachmentRepositoryConfig;
import com.bees360.project.config.ProjectRepositoryConfig;
import com.bees360.project.member.JooqProjectMemberRepository;
import com.bees360.project.member.MemberManager;
import com.bees360.util.Iterables;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

@SpringBootTest
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class JooqGenericProjectCreatorTest extends AbstractProjectCreatorTest {

    @Configuration
    @Import({
        DataSourceAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
        JooqPolicyRepositoryConfig.class,
        JooqProjectMemberRepository.class,
        ProjectRepositoryConfig.class,
        JooqBuildingRepositoryConfig.class,
        JooqProjectCatastropheManager.class,
        JooqExternalIntegrationManager.class,
        JooqAttachmentRepositoryConfig.class,
        JooqProjectAttachmentRepositoryConfig.class,
        JooqGenericProjectCreator.class
    })
    static class Config {}

    @Autowired private PolicyRepository policyRepository;

    @Autowired private ContactManager contactManager;

    @Autowired private MemberManager memberManager;

    @Autowired private ProjectIIManager projectIIManager;

    @Autowired private ExternalIntegrationManager externalIntegrationManager;

    @Autowired private ProjectAttachmentManager projectAttachmentManager;

    public JooqGenericProjectCreatorTest(@Autowired GenericProjectCreator genericProjectCreator) {
        super(genericProjectCreator);
    }

    @ParameterizedTest()
    @ValueSource(ints = {1, 2})
    public void testCreateProject(int type) {
        var projectType = ProjectTypeEnum.valueOf(type);
        super.testCreateProject(projectType);
    }

    @ParameterizedTest()
    @ValueSource(ints = {1, 2})
    public void testCloneProject(int type) {
        var projectType = ProjectTypeEnum.valueOf(type);
        super.testCloneProject(projectType, "WEB");
    }

    @Override
    protected void assertEquals(ProjectII expected, ProjectII actual) {
        super.assertEquals(expected, actual);

        var projectId = actual.getId();
        var actualProject = projectIIManager.findById(projectId);
        Assertions.assertEquals(
                expected.getOperatingCompany(), actualProject.getOperatingCompany());

        var member = memberManager.findByProjectIds(List.of(projectId));
        Assertions.assertFalse(member.isEmpty());

        var integration =
                Iterables.toList(externalIntegrationManager.findAllByProjectId(projectId)).get(0);
        Assertions.assertNotNull(integration);
        Assertions.assertEquals(projectId, integration.getProjectId());

        var attachments = projectAttachmentManager.findByProjectId(projectId);
        Assertions.assertFalse(Iterables.isEmpty(attachments));
    }

    @Override
    protected ProjectCreationRequest randomProjectRequest(ProjectII project) {
        var builder = project.toMessage().toBuilder();
        builder.setPolicy(builder.getPolicyBuilder().setAddress(randomAddress().toMessage()));
        builder.addIntegration(
                Message.IntegrationMessage.newBuilder()
                        .setIntegrationType(ProjectIntegrationType.BATCH.getLiteral())
                        .setReferenceNumber(RandomStringUtils.randomAlphabetic(6))
                        .setSubReferenceNumber(RandomStringUtils.randomAlphabetic(4))
                        .build());
        return ProjectCreationRequest.from(builder.build());
    }

    private com.bees360.address.Address randomAddress() {
        return Address.AddressBuilder.newBuilder()
                .setId(RandomStringUtils.randomNumeric(4))
                .setAddress("233 El Trl Denton, Lewisville, TX 75052")
                .setCity("Lewisville")
                .setState("TX")
                .setZip("75052")
                .setIsGpsApproximate(true)
                .build();
    }
}
