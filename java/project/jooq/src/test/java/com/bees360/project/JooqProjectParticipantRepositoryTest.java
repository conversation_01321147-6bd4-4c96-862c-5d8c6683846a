package com.bees360.project;

import static com.bees360.jooq.persistent.project.Tables.PIPELINE;
import static com.bees360.jooq.persistent.project.Tables.PROJECT;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.jooq.persistent.project.Tables;
import com.bees360.project.config.ProjectRepositoryConfig;
import com.bees360.project.participant.ProjectParticipantProvider;
import com.bees360.user.InMemoryUserRepository;

import org.apache.commons.lang3.RandomUtils;
import org.jooq.DSLContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

@DirtiesContext
@SpringBootTest
@ApplicationAutoConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class JooqProjectParticipantRepositoryTest extends AbstractProjectParticipantTest {
    @Import({ProjectRepositoryConfig.class, InMemoryUserRepository.class})
    @Configuration
    static class Config {}

    @Autowired ProjectParticipantProvider participantManager;
    @Autowired DSLContext dsl;

    @Override
    ProjectParticipantProvider getParticipantManager() {
        return participantManager;
    }

    @Test
    public void testGetParticipant() {
        super.testGetParticipant();
    }

    @Override
    void addParticipant(String ownerId, String projectId) {
        dsl.insertInto(
                        PROJECT,
                        PROJECT.ID,
                        PROJECT.POLICY_ID,
                        PROJECT.PROJECT_TYPE,
                        PROJECT.CONTRACT_ID)
                .values(
                        projectId,
                        String.valueOf(RandomUtils.nextLong()),
                        RandomUtils.nextInt(),
                        String.valueOf(RandomUtils.nextLong()))
                .execute();
        var pipelineId =
                dsl.insertInto(
                                PIPELINE,
                                PIPELINE.EXTERNAL_ID,
                                PIPELINE.PIPELINE_DEF_ID,
                                PIPELINE.STAGE,
                                PIPELINE.STATUS)
                        .values(projectId, "1", 1, 1)
                        .returning(PIPELINE.ID)
                        .fetchOne(PIPELINE.ID);
        dsl.insertInto(
                        Tables.PIPELINE_TASK,
                        Tables.PIPELINE_TASK.PIPELINE_ID,
                        Tables.PIPELINE_TASK.TASK_DEF_ID,
                        Tables.PIPELINE_TASK.OWNER_ID)
                .values(pipelineId, "124", ownerId)
                .execute();
    }
}
