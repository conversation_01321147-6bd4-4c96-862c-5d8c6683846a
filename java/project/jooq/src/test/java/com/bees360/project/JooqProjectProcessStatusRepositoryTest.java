package com.bees360.project;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.project.Message.ProjectStatus;
import com.bees360.project.config.ProjectRepositoryConfig;
import com.bees360.util.Iterables;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.Arrays;
import java.util.Random;

@DirtiesContext
@SpringJUnitConfig
@SpringBootTest
public class JooqProjectProcessStatusRepositoryTest extends AbstractProjectProcessStatusTest {

    @ApplicationAutoConfig
    @Import({ProjectRepositoryConfig.class})
    @Configuration
    static class Config {}

    public JooqProjectProcessStatusRepositoryTest(
            @Autowired JooqProjectProcessStatusRepository projectProcessStatusManager) {
        super(projectProcessStatusManager);
    }

    @ParameterizedTest
    @ValueSource(ints = {10, 30, -99})
    void testUpdateAndGetStatus(int status) {
        super.testUpdateAndGetStatus(status);
    }

    @ParameterizedTest
    @ValueSource(ints = {10, 30, -99})
    void testUpdateStatusWithComment(int status) {
        super.updateStatusWithComment(status);
    }

    @Test
    void testFindLatestStatus() {
        var projectId = String.valueOf(new Random().nextInt(10000));
        var userId = RandomStringUtils.randomNumeric(10);

        Arrays.stream(ProjectStatus.values())
                .forEach(
                        s -> {
                            if (s != ProjectStatus.UNRECOGNIZED) {
                                projectProcessStatusManager.updateProcessStatus(
                                        projectId, s, userId);
                            }
                        });

        var statusList =
                Iterables.toList(projectProcessStatusManager.getProcessStatusHistory(projectId));
        Assertions.assertEquals(ProjectStatus.values().length - 1, statusList.size());
    }

    @Test
    void testGetStatusHistory() {
        super.getStatusHistory();
    }

    @Test
    void updateStatusWithSameCodeShouldFail() {
        super.updateStatusWithSameCodeShouldFail();
    }
}
