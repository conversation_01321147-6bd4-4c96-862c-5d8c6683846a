package com.bees360.project.group;

import static com.bees360.jooq.persistent.project.tables.ProjectGroup.PROJECT_GROUP;

import com.bees360.project.JooqProjectIIRepository;
import com.bees360.project.group.config.JooqProjectGroupRepositoryConfig;
import com.bees360.util.CollectionUtils;

import org.jooq.DSLContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;

@SpringBootTest(
        classes = JooqHardDeleteProjectGroupRepositoryTest.Config.class,
        properties = "ENABLE_HARD_DELETE=true")
public class JooqHardDeleteProjectGroupRepositoryTest extends AbstractProjectGroupManagerTest {

    @Configuration
    @Import({
        JooqProjectGroupRepositoryConfig.class,
        JooqProjectIIRepository.class,
    })
    static class Config {}

    public JooqHardDeleteProjectGroupRepositoryTest(
            @Autowired ProjectGroupManager projectGroupManager,
            @Autowired JooqProjectIIRepository projectCreator,
            @Autowired DSLContext dsl) {
        super(projectGroupManager, projectCreator);
        this.projectGroupManager = projectGroupManager;
        this.dsl = dsl;
    }

    private final ProjectGroupManager projectGroupManager;
    private final DSLContext dsl;

    @Test
    void testProjectGroupManagerBean() {
        Assertions.assertTrue(projectGroupManager instanceof JooqHardDeleteProjectGroupRepository);
    }

    @Test
    void testHardDeleteProjectsInGroup() {
        var groupKey = randomId();
        var groupType = randomType();
        var projectIds = List.of(randomProject(), randomProject());
        var createdBy = randomId();

        projectGroupManager.addProjectToGroup(groupKey, groupType, projectIds, createdBy);

        projectGroupManager.deleteProjectInGroup(groupKey, groupType, projectIds, createdBy);

        var projectGroupIds =
                dsl.select(PROJECT_GROUP.ID)
                        .from(PROJECT_GROUP)
                        .where(PROJECT_GROUP.GROUP_KEY.eq(groupKey))
                        .and(PROJECT_GROUP.GROUP_TYPE.eq(groupType))
                        .and(PROJECT_GROUP.PROJECT_ID.in(projectIds))
                        .fetch(PROJECT_GROUP.ID);
        Assertions.assertTrue(CollectionUtils.isEmpty(projectGroupIds));
    }

    @Test
    void testHardDeleteProjectGroup() {
        var groupKey = randomId();
        var groupType = randomType();
        var projectIds = List.of(randomProject(), randomProject());
        var createdBy = randomId();

        projectGroupManager.addProjectToGroup(groupKey, groupType, projectIds, createdBy);

        projectGroupManager.deleteProjectGroup(groupKey, groupType, createdBy);

        var projectGroupIds =
                dsl.select(PROJECT_GROUP.ID)
                        .from(PROJECT_GROUP)
                        .where(PROJECT_GROUP.GROUP_KEY.eq(groupKey))
                        .and(PROJECT_GROUP.GROUP_TYPE.eq(groupType))
                        .fetch(PROJECT_GROUP.ID);
        Assertions.assertTrue(CollectionUtils.isEmpty(projectGroupIds));
    }

    @Test
    void testAddGroupAndFind() {
        super.testAddGroupAndFind();
    }

    @Test
    void testUpdateGroup() {
        super.testUpdateGroup();
    }

    @Test
    void testDeleteProjectsInGroup() {
        super.testDeleteProjectsInGroup();
    }

    @Test
    void testFindByProjectId() {
        super.testFindByProjectId();
    }

    @Test
    void testDeleteProjectGroup() {
        super.testDeleteProjectGroup();
    }

    @Test
    void testAddGroupWithIllegalArgument() {
        super.testAddGroupWithIllegalArgument();
    }

    @Test
    void testReplaceGroupWithIllegalArgument() {
        super.testReplaceGroupWithIllegalArgument();
    }

    @Test
    void testAddGroupWithNotExistedProject() {
        super.testAddGroupWithNotExistedProject();
    }

    @Test
    void testAddGroupWithExistedInOtherGroupProject() {
        super.testAddGroupWithExistedInOtherGroupProject();
    }
}
