package com.bees360.project;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.project.information.ProjectInformationManager;
import com.bees360.project.information.config.JooqProjectMessageRepositoryConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

@DirtiesContext
@SpringBootTest
@ApplicationAutoConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class JooqProjectInformationRepositoryTest extends AbstractProjectInformationTest {

    @Configuration
    @Import({JooqProjectMessageRepositoryConfig.class})
    static class Config {}

    public JooqProjectInformationRepositoryTest(
            @Autowired ProjectInformationManager projectInformationManager) {
        super(projectInformationManager);
    }

    @Test
    public void testAddAndFindInfo() {
        super.testAddAndFindInfo();
    }

    @Test
    public void testFindInfoByQuery() {
        super.testFindInfoByQuery();
    }
}
