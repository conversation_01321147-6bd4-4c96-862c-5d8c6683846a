package com.bees360.project;

import static com.bees360.project.RandomProjectUtil.randomProject;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.project.Message.ProjectStatus;
import com.bees360.project.claim.Claim;
import com.bees360.project.config.ProjectRepositoryConfig;
import com.bees360.project.status.ProjectStatusStatisticProvider;
import com.bees360.util.Iterables;

import org.apache.commons.lang3.RandomStringUtils;
import org.jooq.DSLContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.Random;

@DirtiesContext
@SpringJUnitConfig
@SpringBootTest
public class JooqProjectStatusRepositoryTest extends AbstractProjectStatusTest {

    @ApplicationAutoConfig
    @Import({ProjectRepositoryConfig.class})
    @Configuration
    static class Config {}

    private final JooqProjectIIRepository projectIIRepository;

    @Autowired private DSLContext dsl;

    private final ProjectStatusStatisticProvider projectStatusRepository;

    public JooqProjectStatusRepositoryTest(
            @Autowired JooqProjectIIRepository projectIIRepository,
            @Autowired JooqProjectStatusRepository projectStatusManager) {
        super(projectStatusManager);
        this.projectStatusRepository = projectStatusManager;
        this.projectIIRepository = projectIIRepository;
    }

    @ParameterizedTest
    @ValueSource(ints = {10, 30, -99})
    void testUpdateAndGetStatus(int status) {
        super.testUpdateAndGetStatus(status);
    }

    @ParameterizedTest
    @ValueSource(ints = {10, 30, -99})
    void testUpdateStatusWithComment(int status) {
        super.updateStatusWithComment(status);
    }

    @Test
    void testFindLatestStatus() {
        var projectId = String.valueOf(new Random().nextInt(10000));
        var userId = RandomStringUtils.randomNumeric(10);

        Arrays.stream(ProjectStatus.values())
                .forEach(
                        s -> {
                            if (s != ProjectStatus.UNRECOGNIZED) {
                                projectStatusManager.updateStatus(projectId, s, userId);
                            }
                        });

        var statusList = Iterables.toList(projectStatusManager.getStatusHistory(projectId));
        Assertions.assertEquals(ProjectStatus.values().length - 1, statusList.size());
    }

    @Test
    void testCountProjectTurnAroundTime() {
        var startTime = Instant.now();
        var projectId = createProject();
        var userId = RandomStringUtils.randomNumeric(10);
        var days = new Random().nextInt(100);
        var updated_at = startTime.plus(Duration.ofDays(days)).plusSeconds(1L);

        var result =
                projectStatusManager.updateStatus(
                        projectId, ProjectStatus.IMAGE_UPLOADED, userId, updated_at);
        Assertions.assertTrue(result);

        var map =
                projectStatusRepository.countProjectTurnAroundTime(
                        ProjectTypeEnum.CLAIM,
                        startTime,
                        startTime.plus(Duration.ofDays(days + 1)),
                        ProjectStatus.PROJECT_CREATED,
                        ProjectStatus.IMAGE_UPLOADED);

        Assertions.assertEquals(days, map.get(projectId));
    }

    @Test
    void testListProjectIdByStatus() {
        var startTime = Instant.now();
        var projectId = createProject();
        var days = new Random().nextInt(10);
        var updated_at = startTime.plus(Duration.ofDays(days)).plusSeconds(1L);

        var result =
                projectStatusManager.updateStatus(
                        projectId,
                        ProjectStatus.RETURNED_TO_CLIENT,
                        RandomStringUtils.randomAlphabetic(12),
                        updated_at);
        Assertions.assertTrue(result);

        var list =
                projectStatusRepository.listProjectIdByStatus(
                        ProjectTypeEnum.CLAIM,
                        ProjectStatus.RETURNED_TO_CLIENT,
                        startTime,
                        startTime.plus(Duration.ofDays(days + 1)));
        Assertions.assertTrue(list.size() != 0);
    }

    private String createProject() {
        var project = randomProject();
        projectIIRepository.create(
                project.getId(),
                Claim.ClaimBuilder.newBuilder()
                        .setProjectId(project.getId())
                        .setClaimType(project.getClaimType())
                        .setClaimNo(project.getClaimNo())
                        .setServiceType(project.getServiceType())
                        .build(),
                null,
                project.getContract().getId(),
                project.getPolicy().getId(),
                project.getCreateBy().getId());
        return project.getId();
    }

    @Test
    void testGetStatusHistory() {
        super.getStatusHistory();
    }

    @Test
    void updateStatusWithSameCodeShouldFail() {
        super.updateStatusWithSameCodeShouldFail();
    }

    @Test
    void updateStatusWithOlderTimeShouldNotUpdateCurrentStatus() {
        super.updateStatusWithOlderTimeShouldNotUpdateCurrentStatus();
    }

    @Test
    void updateStatusWithNewerTimeShouldSucceed() {
        super.updateStatusWithNewerTimeShouldSucceed();
    }

    @Test
    void rollbackStatusWithRightVersionShouldSucceed() {
        super.rollbackStatusWithRightVersionShouldSucceed();
    }

    @Test
    void rollbackStatusWithOlderVersionShouldFail() {
        super.rollbackStatusWithOlderVersionShouldFail();
    }

    @Test
    void rollbackStatusWithNotExistedStatusShouldFail() {
        super.rollbackStatusWithNotExistedStatusShouldFail();
    }

    @Test
    void rollbackStatusWithSameStatusShouldFail() {
        super.rollbackStatusWithSameStatusShouldFail();
    }

    @Test
    void updateStatusWithSameRecordShouldFail() {
        super.updateStatusWithSameRecordShouldFail();
    }
}
