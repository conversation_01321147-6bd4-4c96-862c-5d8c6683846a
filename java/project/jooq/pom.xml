<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bees360-project</artifactId>
        <groupId>com.bees360</groupId>
        <version>${revision}${changelist}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>bees360-project-jooq</artifactId>
    <properties>
        <jooq-tables>
            address | address_building | airspace_status | building | change_reason_type | claim_catastrophe | claim_invoice | contact_record | contract | contract | contract_service | contract_service | contract_service_item | contract_status_type | customer | customer | customer_attribute | customer_division | customer_policy_type | customer_role | hover_job_status | hover_job_type | oauth_token | operations_manager | payment_source | pilot_feedback | pipeline | pipeline_task | pipeline_def | policy | policy | policy_coverage | policy_coverage | policy_type | policy_type | project | project | project_airspace | project_attachment | project_catastrophe | project_claim | project_claim | project_contact | project_days_old | project_group | project_hover | project_information | project_inspection | project_inspection_code | project_integration_type | project_invoice | project_invoice_file | project_invoice_file | project_invoice_item | project_invoice_payment | project_invoice_receipt | project_invoice_receipt | project_invoice_status | project_member | project_operating_company | project_operating_company | project_operation_tag_enum | project_operation_tag_project | project_participant | project_payment | project_pipeline_def_config | project_process_status | project_process_status_history | project_score | project_stage | project_stage_cycle_time | project_state | project_state_change_reason | project_state_enum | project_state_history | project_status | project_status | project_status_history | project_tag | project_tag_company | project_tag_project | project_timeline | project_timeline | project_underwriting | project_underwriting | similar_project | similar_type | state_change_reason_group | project_summary
        </jooq-tables>
        <jooq-package>com.bees360.jooq.persistent.project</jooq-package>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-api</artifactId>
            <version>${project.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-map-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-jooq</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-address-jooq</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-address-grpc-client</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-address-grpc</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-api</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-hover-jooq</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-boot</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-integration-jooq</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-attachment-jooq</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-attachment-api</artifactId>
            <version>${project.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.jooq</groupId>
                <artifactId>jooq-codegen-maven</artifactId>
                <executions>
                    <execution>
                        <id>convergence-project-enum</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generator>
                                <name>com.bees360.jooq.generator.CommonEnumGenerator</name>

                                <database>
                                    <!-- 数据库的基本信息 -->
                                    <name>org.jooq.meta.postgres.PostgresDatabase</name>
                                    <inputSchema>public</inputSchema>
                                    <includes>project_status_enum | project_status_ai_enum | project_cycle_time_type_enum | project_operation_tag_enum </includes>
                                    <excludes />
                                </database>
                                <generate>
                                    <deprecated>false</deprecated>
                                </generate>
                                <target>
                                    <!-- 自动生成的类的包名，以及路径 -->
                                    <packageName>com.bees360.project</packageName>
                                    <directory>target/generated-sources/project/jooq</directory>
                                </target>
                            </generator>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
