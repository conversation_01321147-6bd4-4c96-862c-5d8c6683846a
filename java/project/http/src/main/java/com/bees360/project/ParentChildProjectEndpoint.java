package com.bees360.project;

import com.bees360.api.ApiStatus;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.project.group.ParentChildProjectManager;
import com.bees360.user.User;

import org.springframework.context.annotation.Import;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/** ParentChildProjectEndpoint */
@RestController
@RequestMapping("/${http.project.endpoint:project}/parent_child")
@Import({ProtoHttpMessageConverterConfig.class, ApiExceptionHandler.class})
public class ParentChildProjectEndpoint {

    private final ParentChildProjectManager parentChildProjectManager;

    public ParentChildProjectEndpoint(ParentChildProjectManager parentChildProjectManager) {
        this.parentChildProjectManager = parentChildProjectManager;
    }

    /**
     * Add a parent-child project group.
     *
     * @param user operator
     * @param entity parent-child group entity
     * @return ApiMessage with OK status
     */
    @PostMapping("")
    public com.bees360.api.Message.ApiMessage addParentChildProject(
            @AuthenticationPrincipal User user,
            @RequestBody com.bees360.project.group.Message.ProjectGroupMessage entity) {
        parentChildProjectManager.addChildProject(
                entity.getKey(), entity.getProjectIdList(), user.getId());
        return com.bees360.api.Message.ApiMessage.newBuilder()
                .setStatus(ApiStatus.OK.toMessage())
                .build();
    }

    /**
     * Update a parent-child project group.
     *
     * @param user operator
     * @param parentId id of parent project
     * @param entity parent-child group entity for update
     * @return ApiMessage with OK status
     */
    @PutMapping("/{parentId:\\d+}")
    public com.bees360.api.Message.ApiMessage updateParentChildProject(
            @AuthenticationPrincipal User user,
            @PathVariable String parentId,
            @RequestBody com.bees360.project.group.Message.ProjectGroupMessage entity) {
        parentChildProjectManager.replaceAllChildProject(
                parentId, entity.getProjectIdList(), user.getId());
        return com.bees360.api.Message.ApiMessage.newBuilder()
                .setStatus(ApiStatus.OK.toMessage())
                .build();
    }

    /**
     * Delete a child project from a parent-child project group.
     *
     * @param user operator
     * @param parentId id of parent project
     * @param projectId id of child project to delete
     * @return ApiMessage with OK status
     */
    @DeleteMapping("/{parentId:\\d+}/{projectId:\\d+}")
    public com.bees360.api.Message.ApiMessage deleteChildProject(
            @AuthenticationPrincipal User user,
            @PathVariable String parentId,
            @PathVariable String projectId) {
        parentChildProjectManager.deleteChildProject(parentId, List.of(projectId), user.getId());
        return com.bees360.api.Message.ApiMessage.newBuilder()
                .setStatus(ApiStatus.OK.toMessage())
                .build();
    }
}
