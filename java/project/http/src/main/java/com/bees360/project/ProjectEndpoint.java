package com.bees360.project;

import com.bees360.api.ApiStatus;
import com.bees360.api.Message.ApiMessage;
import com.bees360.api.NotFoundException;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.pilot.feedback.Message.PilotFeedbackMessage;
import com.bees360.pilot.feedback.PilotFeedback;
import com.bees360.pilot.feedback.PilotFeedbackManager;
import com.bees360.project.Message.MemberRequest;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.claim.ProjectCatastropheManager;
import com.bees360.project.member.MemberManager;
import com.bees360.project.member.RoleEnum;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.user.User;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;

import jakarta.validation.Validator;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Log4j2
@RestController
@RequestMapping("${http.project.endpoint:project}")
@RequiredArgsConstructor
@Import({ProtoHttpMessageConverterConfig.class, ApiExceptionHandler.class})
public class ProjectEndpoint {

    private final ProjectManager endpointProjectManager;
    private final ProjectProvider endpointProjectProvider;
    private final ContactManager endpointProjectContactManager;
    private final MemberManager endpointProjectMemberManager;
    private final ProjectCatastropheManager endpointProjectCatastropheManager;
    private final PilotFeedbackManager pilotFeedbackManager;
    private final ProjectStateManager endpointProjectStateManager;

    private final ProjectPaymentManager endpointProjectPaymentManager;
    private static final int MAX_FULL_NAME_LENGTH = 255;
    private static final int MAX_EMAIL_LENGTH = 255;
    private static final int MAX_PHONE_LENGTH = 32;
    private static final int MAX_ROLE_LENGTH = 64;

    private final Validator validator;

    @GetMapping("/{projectId:\\d+}")
    public Project getProject(@PathVariable String projectId) {
        return endpointProjectProvider.findProjectById(projectId);
    }

    @PutMapping("/{projectId:\\d+}")
    public void updateProject(
            @PathVariable String projectId, @RequestBody ProjectMessage projectMessage) {
        ProjectMessage.Builder builder = projectMessage.toBuilder();
        builder.setId(projectId);
        Project project = Project.of(builder.build());
        validator.validate(project);
        endpointProjectManager.updateProject(project);
    }

    @PostMapping("/{projectId:\\d+}/contact")
    public ApiMessage addContact(
            @AuthenticationPrincipal User user,
            @PathVariable String projectId,
            @RequestBody ProjectMessage.Contact contact) {
        var contactEntity = Contact.of(contact);
        checkContact(contactEntity);
        var contactId =
                endpointProjectContactManager.addContact(projectId, contactEntity, user.getId());
        var contentMsg = contact.toBuilder().setId(contactId);
        return ApiMessage.newBuilder()
                .addProject(ProjectMessage.newBuilder().addContact(contentMsg).build())
                .build();
    }

    @PutMapping("/{projectId:\\d+}/contact/{contactId:\\d+}")
    public ApiMessage updateContact(
            @AuthenticationPrincipal User user,
            @PathVariable String contactId,
            @RequestBody ProjectMessage.Contact contact) {
        ApiMessage.Builder response = ApiMessage.newBuilder();
        var ensuredContact = contact.toBuilder().setId(contactId).build();
        var ensuredContactEntity = Contact.of(ensuredContact);
        checkContact(ensuredContactEntity);
        endpointProjectContactManager.updateContact(ensuredContactEntity, user.getId());
        response.addProject(ProjectMessage.newBuilder().addContact(ensuredContact)).build();
        return response.build();
    }

    @PutMapping("/{projectId:\\d+}/member")
    public void addMember(
            @AuthenticationPrincipal User user,
            @PathVariable String projectId,
            @RequestBody MemberRequest memberRequest) {
        checkMemberRequest(memberRequest);
        endpointProjectMemberManager.setMember(
                projectId,
                memberRequest.getUserId(),
                RoleEnum.valueOf(memberRequest.getRole()),
                user.getId(),
                memberRequest.getVersion());
    }

    @DeleteMapping("/{projectId:\\d+}/member")
    public void deleteMember(
            @AuthenticationPrincipal User user,
            @PathVariable String projectId,
            @RequestBody MemberRequest memberRequest) {
        checkMemberRequest(memberRequest);
        endpointProjectMemberManager.removeMember(
                projectId,
                RoleEnum.valueOf(memberRequest.getRole()),
                user.getId(),
                memberRequest.getVersion());
    }

    @PutMapping("{projectId:\\d+}/catastrophe")
    public void updateProjectCatastrophe(
            @AuthenticationPrincipal User user,
            @PathVariable String projectId,
            @RequestBody Message.SetCatastropheLevelRequest request) {
        var serialNumber = request.getSerialNumber();
        Integer level = null;
        // set the level when request has level parameter
        if (request.hasLevel()) {
            level = request.getLevel().getValue();
        }
        endpointProjectCatastropheManager.addProjectCatastrophe(projectId, serialNumber, level);
        log.info(
                "Project catastrophe updated, projectId: {}, catNumber: {}, catLevel: {},"
                        + " updatedBy: {}.",
                projectId,
                serialNumber,
                level,
                user.getId());
    }

    /**
     * Delete the project catastrophe relation by project id.
     *
     * @param user operating user
     * @param projectId project id
     */
    @DeleteMapping("{projectId:\\d+}/catastrophe")
    public void deleteProjectCatastrophe(
            @AuthenticationPrincipal User user, @PathVariable String projectId) {
        var successful = endpointProjectCatastropheManager.deleteByProjectId(projectId);
        if (!successful) {
            throw new NotFoundException(
                    String.format(
                            "Cannot delete project catastrophe with projectId %s: No such project"
                                    + " catastrophe exists.",
                            projectId));
        }
        log.info(
                "Project catastrophe deleted, projectId: {}, deletedBy: {}.",
                projectId,
                user.getId());
    }

    @GetMapping("{projectId:\\d+}/feedback")
    public ApiMessage getPilotFeedback(@PathVariable String projectId) {
        var feedback = pilotFeedbackManager.findByProjectId(projectId);
        var response = ApiMessage.newBuilder().setStatus(ApiStatus.OK.toMessage());
        if (feedback != null) {
            response.setPilotFeedback(feedback.toMessage());
        }

        return response.build();
    }

    @PostMapping("{projectId:\\d+}/feedback")
    public void savePilotFeedback(
            @AuthenticationPrincipal User user,
            @PathVariable String projectId,
            @RequestBody PilotFeedbackMessage request) {
        var feedback =
                request.toBuilder().setProjectId(projectId).setCreatedBy(user.getId()).build();
        pilotFeedbackManager.save(PilotFeedback.from(feedback));
    }

    @PutMapping("{projectId:\\d+}/state")
    public ApiMessage changeProjectState(
            @PathVariable long projectId,
            @RequestParam(value = "state") ProjectMessage.ProjectState.ProjectStateEnum stateEnum,
            @RequestParam(value = "change-reason") String changeReason,
            @RequestParam(required = false, defaultValue = "false", value = "change-force")
                    Boolean changeForce,
            @AuthenticationPrincipal User user) {
        return ApiMessage.newBuilder()
                .setProjectOperationFeedback(
                        endpointProjectStateManager
                                .changeProjectState(
                                        String.valueOf(projectId),
                                        stateEnum,
                                        changeReason,
                                        user.getId(),
                                        changeForce)
                                .toMessage())
                .build();
    }

    @PutMapping("{projectId:\\d+}/payment")
    public void changeProjectPayment(@PathVariable String projectId, @RequestParam Boolean isPaid) {
        endpointProjectPaymentManager.setProjectsPaymentStatus(Map.of(projectId, isPaid));
    }

    private void checkMemberRequest(MemberRequest member) {
        Preconditions.checkArgument(
                StringUtils.isNotEmpty(member.getUserId()), "Member userId can't be null.");
        Preconditions.checkArgument(
                StringUtils.isNotEmpty(member.getRole()), "Member role can't be null.");
        Preconditions.checkArgument(
                member.getRole().length() <= MAX_ROLE_LENGTH,
                String.format("Illegal contact role length for '%s'", member.getRole()));
    }

    private void checkContact(Contact contact) {
        Preconditions.checkNotNull(contact, "Contact message should be initialized.");
        Preconditions.checkArgument(
                StringUtils.isNotEmpty(contact.getFullName())
                        || StringUtils.isNoneEmpty(contact.getFirstName()),
                "Contact name can't be null.");
        checkFullName(contact.getFullName());
        Preconditions.checkArgument(
                !StringUtils.isEmpty(contact.getRole()), "Contact role can't be null.");
        checkRole(contact.getRole());
        Optional.ofNullable(contact.getPrimaryPhone()).ifPresent(this::checkPhone);
        Optional.ofNullable(contact.getPrimaryEmail()).ifPresent(this::checkEmail);
        Iterables.toStream(contact.getOtherPhone()).forEach(this::checkPhone);
        Iterables.toStream(contact.getOtherEmail()).forEach(this::checkEmail);
    }

    private void checkRole(String role) {
        Preconditions.checkArgument(
                role.length() <= MAX_ROLE_LENGTH,
                String.format("Illegal contact role length for '%s'", role));
    }

    private void checkFullName(String fullName) {
        Preconditions.checkArgument(
                fullName.length() <= MAX_FULL_NAME_LENGTH,
                String.format("Illegal contact full name length for '%s'", fullName));
    }

    private void checkEmail(String email) {
        Preconditions.checkArgument(
                email.length() <= MAX_EMAIL_LENGTH,
                String.format("Illegal email length for '%s'", email));
    }

    private void checkPhone(String phone) {
        Preconditions.checkArgument(
                phone.length() <= MAX_PHONE_LENGTH,
                String.format("Illegal phone length for '%s'", phone));
    }
}
