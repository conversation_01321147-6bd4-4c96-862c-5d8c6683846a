package com.bees360.project.servicetype;

import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.project.Message;
import com.bees360.project.state.ProjectStateChangeReason;
import com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage;
import com.bees360.user.User;

import lombok.RequiredArgsConstructor;

import org.springframework.context.annotation.Import;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/** ServiceTypeEndpoint */
@RestController
@RequestMapping("${http.project.endpoint:project}/service-type")
@RequiredArgsConstructor
@Import({ProtoHttpMessageConverterConfig.class, ApiExceptionHandler.class})
public class ServiceTypeEndpoint {
    private final ServiceTypeManager serviceTypeManager;

    /**
     * Create project state change reason
     *
     * @param user 用户信息
     * @param changeReasonMessage 变更原因信息, 如原因类型、原因 display text
     * @param serviceType 服务类型 code (比如 1 表示 QUICK_INSPECT), 用于指定创建的原因属于哪个 service type
     * @return 创建的项目状态变更原因
     */
    @PostMapping("{service-type}/state-change-reason")
    public ProjectStateChangeReason createServiceTypeStateChangeReason(
            @AuthenticationPrincipal User user,
            @RequestBody ProjectStateChangeReasonMessage changeReasonMessage,
            @PathVariable("service-type") String serviceType) {
        return serviceTypeManager.createStateChangeReason(
                serviceType,
                changeReasonMessage.getDisplayText(),
                changeReasonMessage.getType(),
                user.getId());
    }

    /**
     * Find project state change reason list by type and serviceType
     *
     * @param type change reason type
     * @param serviceType service type, such as {@link
     *     com.bees360.project.Message.ServiceType#FOUR_POINT_UNDERWRITING}
     * @return 项目状态变更原因列表
     */
    @GetMapping("/state-change-reason")
    public Iterable<? extends ProjectStateChangeReason> getServiceTypeStateChangeReason(
            @RequestParam(value = "type")
                    ProjectStateChangeReasonMessage.ProjectStateChangeReasonType type,
            @RequestParam(value = "service-type", required = false, defaultValue = "")
                    Message.ServiceType serviceType) {
        return serviceTypeManager.findStateChangeReason(getServiceType(serviceType), type);
    }

    private String getServiceType(Message.ServiceType serviceType) {
        return serviceType == null || Message.ServiceType.UNRECOGNIZED.equals(serviceType)
                ? ""
                : String.valueOf(serviceType.getNumber());
    }
}
