package com.bees360.project;

import com.bees360.grpc.GrpcApi;
import com.bees360.project.Message.CancellationRequest;
import com.bees360.project.state.ProjectRequestCancellationManager;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class GrpcProjectRequestCancellationManager implements ProjectRequestCancellationManager {

    private final GrpcApi<ProjectCancellationServiceGrpc.ProjectCancellationServiceBlockingStub>
            api;

    public GrpcProjectRequestCancellationManager(
            @NonNull
                    GrpcApi<ProjectCancellationServiceGrpc.ProjectCancellationServiceBlockingStub>
                            api) {
        this.api = api;
        log.info("Created {}.", this);
    }

    @Override
    public boolean requestCancel(
            @NonNull String userId,
            @NonNull String projectId,
            @NonNull Message.CancellationOptionEnum cancelOption,
            String comment) {
        CancellationRequest request =
                CancellationRequest.newBuilder()
                        .setCancelOption(cancelOption)
                        .setUserId(userId)
                        .setProjectId(projectId)
                        .setComment(comment)
                        .build();
        return api.apply(e -> e.requestCancel(request)).getValue();
    }
}
