package com.bees360.project.group;

import com.bees360.grpc.GrpcApi;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class GrpcProjectGroupManager implements ProjectGroupManager {

    private final GrpcApi<ProjectGroupServiceGrpc.ProjectGroupServiceBlockingStub> api;

    public GrpcProjectGroupManager(
            GrpcApi<ProjectGroupServiceGrpc.ProjectGroupServiceBlockingStub> api) {
        this.api = api;
        log.info("Created {}(api={}).", this, this.api);
    }

    @Override
    public void addProjectToGroup(
            String groupKey, String groupType, Iterable<String> projectIds, String createdBy) {
        var request =
                Message.AddGroupRequest.newBuilder()
                        .setGroupKey(groupKey)
                        .setGroupType(groupType)
                        .addAllProjectId(projectIds)
                        .setCreatedBy(createdBy)
                        .build();
        api.apply(e -> e.add(request));
    }

    @Override
    public void replaceAllProjectInGroup(
            String groupKey, String groupType, Iterable<String> projectIds, String updatedBy) {
        var request =
                Message.UpdateGroupRequest.newBuilder()
                        .setGroupKey(groupKey)
                        .setGroupType(groupType)
                        .addAllProjectId(projectIds)
                        .setUpdatedBy(updatedBy)
                        .build();
        api.apply(e -> e.update(request));
    }

    @Override
    public void deleteProjectInGroup(
            String groupKey, String groupType, Iterable<String> projectIds, String deletedBy) {
        var request =
                Message.UpdateGroupRequest.newBuilder()
                        .setGroupKey(groupKey)
                        .setGroupType(groupType)
                        .addAllProjectId(projectIds)
                        .setUpdatedBy(deletedBy)
                        .build();
        api.apply(e -> e.deleteProjects(request));
    }

    @Override
    public void deleteProjectGroup(String groupKey, String groupType, String deletedBy) {
        var request =
                Message.UpdateGroupRequest.newBuilder()
                        .setGroupKey(groupKey)
                        .setGroupType(groupType)
                        .setUpdatedBy(deletedBy)
                        .build();
        api.apply(e -> e.delete(request));
    }

    @Override
    public ProjectGroup findByProjectId(String projectId, String groupType) {
        var request =
                Message.GroupQueryRequest.newBuilder()
                        .setProjectId(projectId)
                        .setGroupType(groupType)
                        .build();
        var result = api.apply(e -> e.findByProjectId(request));
        return ProjectGroup.from(result);
    }

    @Override
    public ProjectGroup findByGroupKey(String groupKey, String groupType) {
        var request =
                Message.GroupQueryRequest.newBuilder()
                        .setGroupKey(groupKey)
                        .setGroupType(groupType)
                        .build();
        var result = api.apply(e -> e.findByGroupKey(request));
        return ProjectGroup.from(result);
    }
}
