package com.bees360.project;

import com.bees360.grpc.GrpcApi;
import com.bees360.grpc.ResponseListFutureStreamObserver;
import com.bees360.util.ListenableFutures;
import com.google.protobuf.StringValue;

import jakarta.annotation.Nonnull;

import lombok.extern.log4j.Log4j2;

import java.util.Map;
import java.util.stream.Collectors;

@Log4j2
public class GrpcProjectDaysOldProvider implements ProjectDaysOldProvider {

    private final GrpcApi<ProjectDaysOldServiceGrpc.ProjectDaysOldServiceBlockingStub> api;
    private final ProjectDaysOldServiceGrpc.ProjectDaysOldServiceStub asyncApi;

    public GrpcProjectDaysOldProvider(
            @Nonnull GrpcApi<ProjectDaysOldServiceGrpc.ProjectDaysOldServiceBlockingStub> api,
            @Nonnull ProjectDaysOldServiceGrpc.ProjectDaysOldServiceStub asyncApi) {
        this.api = api;
        this.asyncApi = asyncApi;
        log.info("Created {}(api={}, asyncApi={})", this, api, asyncApi);
    }

    @Override
    public Iterable<String> findProjectByDaysOldQuery(ProjectDaysOldQuery daysOldQuery) {
        var apply = api.apply(e -> e.findProjectIdByDaysOld(daysOldQuery.toMessage()));
        return apply.getValueList();
    }

    @Override
    public Map<String, Integer> findProjectDaysOld(Iterable<String> projectId) {
        if (com.google.common.collect.Iterables.isEmpty(projectId)) {
            return Map.of();
        }
        var response =
                new ResponseListFutureStreamObserver<
                        Message.ProjectDaysOldResponse, Map.Entry<String, Integer>>(
                        message -> Map.entry(message.getProjectId(), message.getDaysOld()));
        var request = asyncApi.findProjectDaysOld(response);
        projectId.forEach(id -> request.onNext(StringValue.of(id)));
        request.onCompleted();
        return ListenableFutures.getUnchecked(response).stream()
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Override
    public ProjectDaysOld findDaysOld(String projectId) {
        throw new UnsupportedOperationException("Cannot find raw days old: unsupported operation.");
    }
}
