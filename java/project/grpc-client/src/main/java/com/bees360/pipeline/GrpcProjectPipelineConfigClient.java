package com.bees360.pipeline;

import com.bees360.grpc.GrpcApi;
import com.bees360.project.Message;
import com.bees360.project.ProjectPipelineConfigServiceGrpc;
import com.google.protobuf.BoolValue;
import com.google.protobuf.StringValue;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import java.util.Optional;

@Log4j2
public class GrpcProjectPipelineConfigClient implements ProjectPipelineConfigService {
    public GrpcProjectPipelineConfigClient(
            GrpcApi<ProjectPipelineConfigServiceGrpc.ProjectPipelineConfigServiceBlockingStub>
                    grpcApi) {
        this.grpcApi = grpcApi;
    }

    private final GrpcApi<ProjectPipelineConfigServiceGrpc.ProjectPipelineConfigServiceBlockingStub>
            grpcApi;

    @Override
    public ProjectPipelineDef create(ProjectPipelineConfig config) {
        var r = grpcApi.apply(api -> api.create(config.toMessage()));
        return ProjectPipelineDef.from(r);
    }

    @Override
    public String findPipelineDefKey(
            @Nullable String insuredBy,
            @Nullable String processedBy,
            int serviceType,
            Boolean policyRenewal) {
        var builder = Message.GetPipelineDefRequest.newBuilder();
        Optional.ofNullable(insuredBy).ifPresent(builder::setInsuredBy);
        Optional.ofNullable(processedBy).ifPresent(builder::setProcessedBy);
        Optional.ofNullable(policyRenewal).map(BoolValue::of).ifPresent(builder::setPolicyRenewal);
        builder.setServiceType(serviceType);
        var r = grpcApi.apply(api -> api.findPipelineDef(builder.build()));
        return r.getValue();
    }

    @Override
    public ProjectPipelineDefList findAndCountByQuery(ProjectPipelineDefQuery query) {
        var r = grpcApi.apply(api -> api.findAndCountByQuery(query.toMessage()));
        return ProjectPipelineDefList.from(r);
    }

    @Override
    public void update(UpdateProjectPipelineConfig config) {
        grpcApi.apply(api -> api.update(config.toMessage()));
    }

    @Override
    public void deleteById(String id) {
        grpcApi.apply(api -> api.deleteById(StringValue.of(id)));
    }
}
