package com.bees360.contract;

import com.bees360.grpc.GrpcApi;
import com.bees360.util.Iterables;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

public class GrpcContractServiceManager implements ContractServiceManager {

    private final GrpcApi<ContractServiceGrpc.ContractServiceBlockingStub> api;

    public GrpcContractServiceManager(
            GrpcApi<ContractServiceGrpc.ContractServiceBlockingStub> api) {
        this.api = api;
    }

    @Override
    public Iterable<? extends ContractServiceItem> listAll() {
        var result = api.apply(e -> e.listServiceItem(Empty.getDefaultInstance()));
        return api.get(() -> Iterables.transformAndToList(result, ContractServiceItem::from));
    }

    @Override
    public String addServiceItem(ContractServiceItem item) {
        return api.apply(e -> e.addServiceItem(item.toMessage())).getValue();
    }

    @Override
    public void deleteServiceItem(String key) {
        api.apply(e -> e.deleteServiceItem(StringValue.of(key)));
    }

    @Override
    public void updateServiceItem(ContractServiceItem item) {
        api.apply(e -> e.updateServiceItem(item.toMessage()));
    }
}
