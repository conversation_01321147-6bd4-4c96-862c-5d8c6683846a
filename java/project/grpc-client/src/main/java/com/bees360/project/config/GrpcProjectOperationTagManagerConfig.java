package com.bees360.project.config;

import com.bees360.project.GrpcProjectOperationTagManager;
import com.bees360.project.ProjectOperationTagManager;
import com.bees360.project.ProjectOperationTagServiceGrpc.ProjectOperationTagServiceBlockingStub;
import com.bees360.project.ProjectOperationTagServiceGrpc.ProjectOperationTagServiceStub;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrpcProjectOperationTagManagerConfig {

    @GrpcClient("projectOperationTagManager")
    private ProjectOperationTagServiceBlockingStub blockingStub;

    @GrpcClient("projectOperationTagManager")
    private ProjectOperationTagServiceStub stub;

    @Bean(name = {"grpcOperationTagProvider", "grpcProjectOperationTagManager"})
    public ProjectOperationTagManager grpcProjectOperationTagManager() {
        return new GrpcProjectOperationTagManager(blockingStub, stub);
    }
}
