package com.bees360.project;

import com.bees360.grpc.GrpcApi;
import com.bees360.project.member.Member;
import com.bees360.project.member.MemberManager;
import com.bees360.project.member.RoleEnum;
import com.bees360.util.Functions;
import com.bees360.util.Iterables;
import com.google.protobuf.StringValue;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Log4j2
public class GrpcProjectMemberClient implements MemberManager {

    private final GrpcApi<ProjectServiceGrpc.ProjectServiceBlockingStub> grpcApi;

    public GrpcProjectMemberClient(ProjectServiceGrpc.ProjectServiceBlockingStub grpcApi) {
        this.grpcApi = GrpcApi.of(grpcApi);
        log.info("Created {}.", this);
    }

    @Override
    public boolean setMember(
            String projectId,
            String userId,
            RoleEnum role,
            String opUserId,
            @Nullable Long version) {
        var builder = Message.MemberRequest.newBuilder();
        builder.setProjectId(projectId)
                .setUserId(userId)
                .setRole(role.getValue())
                .setOpUserId(opUserId);
        Optional.ofNullable(version).ifPresent(builder::setVersion);
        return grpcApi.apply(api -> api.setMember(builder.build())).getValue();
    }

    @Override
    public boolean removeMember(
            String projectId, RoleEnum role, String opUserId, @Nullable Long version) {
        var builder = Message.MemberRequest.newBuilder();
        builder.setProjectId(projectId).setRole(role.getValue()).setOpUserId(opUserId);
        Optional.ofNullable(version).ifPresent(builder::setVersion);
        return grpcApi.apply(api -> api.removeMember(builder.build())).getValue();
    }

    @Override
    public Iterable<String> findProjectIdByRoleAndUserId(String userId, RoleEnum role) {
        var builder =
                Message.UserIdAndMemberRoleRequest.newBuilder()
                        .setUserId(userId)
                        .setRole(role.getValue());
        var apply = grpcApi.apply(s -> s.findProjectIdByRoleAndUserId(builder.build()));
        return grpcApi.get(
                () -> Iterables.toList(Iterables.transformAndToList(apply, StringValue::getValue)));
    }

    @Override
    public Map<String, Iterable<? extends Member>> findByProjectIds(Iterable<String> projectIds) {
        var builder =
                Message.ProjectIdListAndMemberRoleRequest.newBuilder().addAllProjectId(projectIds);
        var apply = grpcApi.apply(s -> s.findMemberByProjectIdAndRole(builder.build()));

        Map<String, Iterable<? extends Member>> responseMap = new LinkedHashMap<>();
        Iterables.toStream(grpcApi.get(() -> Iterables.iterate(apply)))
                .forEach(
                        e -> {
                            var members =
                                    e.getMemberList().stream()
                                            .map(ProtoProjectMember::from)
                                            .collect(Collectors.toList());
                            responseMap.put(e.getProjectId(), members);
                        });
        return responseMap;
    }

    @Override
    public Map<String, ? extends Member> findMemberByProjectIdAndRole(
            Iterable<String> projectIds, RoleEnum role) {
        var builder =
                Message.ProjectIdListAndMemberRoleRequest.newBuilder().addAllProjectId(projectIds);
        Functions.acceptIfNotNull(builder::setRole, role, RoleEnum::getValue);
        var apply = grpcApi.apply(s -> s.findMemberByProjectIdAndRole(builder.build()));

        Map<String, Member> responseMap = new LinkedHashMap<>();
        Iterables.toStream(grpcApi.get(() -> Iterables.iterate(apply)))
                .forEach(
                        e -> {
                            if (CollectionUtils.isNotEmpty(e.getMemberList())) {
                                var member = ProtoProjectMember.from(e.getMember(0));
                                responseMap.put(e.getProjectId(), member);
                            }
                        });
        return responseMap;
    }
}
