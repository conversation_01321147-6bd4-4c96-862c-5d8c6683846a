package com.bees360.project.config;

import com.bees360.grpc.GrpcApi;
import com.bees360.project.GrpcProjectClient;
import com.bees360.project.ProjectServiceGrpc.ProjectServiceBlockingStub;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2021/4/1
 */
@Configuration
public class GrpcProjectClientConfig {

    @GrpcClient("projectManager")
    private ProjectServiceBlockingStub projectServiceBlockingStub;

    @Bean
    public GrpcProjectClient grpcProjectClient() {
        return new GrpcProjectClient(GrpcApi.of(projectServiceBlockingStub));
    }
}
