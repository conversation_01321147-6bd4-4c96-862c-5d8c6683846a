package com.bees360.project.config;

import com.bees360.grpc.GrpcApi;
import com.bees360.project.GrpcProjectStatusManager;
import com.bees360.project.ProjectServiceGrpc.ProjectServiceBlockingStub;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrpcProjectStatusManagerConfig {

    @GrpcClient("projectStatusManager")
    private ProjectServiceBlockingStub blockingStub;

    @Bean
    public GrpcProjectStatusManager grpcProjectStatusManager() {
        return new GrpcProjectStatusManager(GrpcApi.of(blockingStub));
    }
}
