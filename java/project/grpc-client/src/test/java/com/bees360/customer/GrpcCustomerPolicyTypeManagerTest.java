package com.bees360.customer;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.customer.config.GrpcCustomerPolicyTypeManagerConfig;
import com.bees360.customer.config.JooqCustomerPolicyTypeManagerConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

@ApplicationAutoConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
@DirtiesContext
@SpringBootTest(properties = "GRPC_SERVER_PORT=9322")
public class GrpcCustomerPolicyTypeManagerTest extends AbstractCustomerPolicyTypeManagerTest {
    @ApplicationAutoConfig
    @Configuration
    @Import({
        JooqCustomerPolicyTypeManagerConfig.class,
        GrpcCustomerPolicyTypeManagerConfig.class,
        GrpcCustomerService.class
    })
    static class Config {
        @MockBean CustomerManager customerManager;
        @MockBean DivisionManager divisionManager;

        @Bean
        CustomerPolicyTypeManager customerPolicyTypeManager(
                JooqCustomerPolicyTypeManager jooqCustomerPolicyTypeManager) {
            return jooqCustomerPolicyTypeManager;
        }
    }

    public GrpcCustomerPolicyTypeManagerTest(
            @Autowired CustomerPolicyTypeManager grpcCustomerPolicyTypeManager) {
        super(grpcCustomerPolicyTypeManager);
    }

    @Test
    @Override
    public void testCreateCustomerPolicyType() {
        super.testCreateCustomerPolicyType();
    }

    @Test
    @Override
    public void testCreateCustomerPolicyTypeUseExistedPolicyType() {
        super.testCreateCustomerPolicyTypeUseExistedPolicyType();
    }

    @Test
    @Override
    public void testListCustomerPolicyType() {
        super.testListCustomerPolicyType();
    }
}
