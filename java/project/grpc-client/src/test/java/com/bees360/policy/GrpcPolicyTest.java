package com.bees360.policy;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.customer.CustomerManager;
import com.bees360.customer.CustomerPolicyTypeManager;
import com.bees360.customer.DivisionManager;
import com.bees360.customer.GrpcCustomerService;
import com.bees360.customer.JooqCustomerPolicyTypeManager;
import com.bees360.customer.config.GrpcCustomerPolicyTypeManagerConfig;
import com.bees360.customer.config.JooqCustomerPolicyTypeManagerConfig;
import com.bees360.policy.config.GrpcPolicyMangerConfig;
import com.bees360.policy.config.JooqPolicyRepositoryConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

@ApplicationAutoConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
@DirtiesContext
@SpringBootTest(properties = "GRPC_SERVER_PORT=9805")
public class GrpcPolicyTest extends AbstractPolicyTest {

    @Import({
        JooqPolicyRepositoryConfig.class,
        JooqCustomerPolicyTypeManagerConfig.class,
        GrpcPolicyMangerConfig.class,
        GrpcCustomerPolicyTypeManagerConfig.class,
        GrpcCustomerService.class,
    })
    @Configuration
    static class Config {
        @MockBean CustomerManager customerManager;
        @MockBean DivisionManager divisionManager;

        @Bean
        public GrpcPolicyService grpcPolicyService(PolicyManager jooqPolicyRepository) {
            return new GrpcPolicyService(jooqPolicyRepository);
        }

        @Bean
        CustomerPolicyTypeManager customerPolicyTypeManager(
                JooqCustomerPolicyTypeManager jooqCustomerPolicyTypeManager) {
            return jooqCustomerPolicyTypeManager;
        }
    }

    public GrpcPolicyTest(
            @Autowired PolicyManager grpcPolicyManager,
            @Autowired CustomerPolicyTypeManager grpcCustomerPolicyTypeManager) {
        super(grpcPolicyManager, grpcCustomerPolicyTypeManager);
    }

    @Test
    public void create() {
        super.create();
    }

    @Test
    public void findById() {
        super.findById();
    }

    @Test
    public void update() {
        super.update();
    }

    @Test
    public void findByUnknownId() {
        super.findByUnknownId();
    }

    @Test
    public void hasNullField() {
        super.hasNullField();
    }

    @Test
    public void findByPolicyNo() {
        super.findByPolicyNo();
    }
}
