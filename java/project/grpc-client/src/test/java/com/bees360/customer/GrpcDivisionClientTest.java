package com.bees360.customer;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.customer.config.GrpcDivisionClientConfig;
import com.bees360.customer.config.JooqDivisionRepositoryConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

@ApplicationAutoConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
@DirtiesContext
@SpringBootTest(properties = "GRPC_SERVER_PORT=9321")
public class GrpcDivisionClientTest extends AbstractDivisionTest {

    @Configuration
    @Import({
        JooqDivisionRepositoryConfig.class,
        GrpcDivisionClientConfig.class,
        GrpcCustomerService.class
    })
    static class Config {
        @MockBean CustomerManager customerManager;
        @MockBean CustomerPolicyTypeManager customerPolicyTypeManager;

        @Bean
        DivisionManager grpcDivisionManager(JooqDivisionRepository jooqDivisionRepository) {
            return jooqDivisionRepository;
        }
    }

    public GrpcDivisionClientTest(@Autowired GrpcDivisionClient grpcDivisionClient) {
        super(grpcDivisionClient);
    }

    @Test
    void testCreate() {
        super.testCreate();
    }

    @Test
    void testUpdate() {
        super.testUpdate();
    }

    @Test
    void testFindByKey() {
        super.testFindByKey();
    }

    @Test
    void testFindByName() {
        super.testFindByName();
    }
}
