package com.bees360.project;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.building.Message.BuildingType;
import com.bees360.hover.ProjectHoverManager;
import com.bees360.project.config.GrpcProjectPolicyManagerConfig;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.member.MemberManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.project.timeline.ProjectTimelineManager;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;

@ApplicationAutoConfig
@DirtiesContext
@SpringBootTest(
        classes = {
            GrpcProjectPolicyManagerTest.Config.class,
        },
        properties = "GRPC_SERVER_PORT=9812")
@ActiveProfiles("GrpcProjectPolicyManagerTest")
public class GrpcProjectPolicyManagerTest {

    @Import({
        GrpcProjectPolicyManagerConfig.class,
    })
    @Configuration
    static class Config {

        @Bean
        ProjectPolicyManager mockProjectPolicyManager() {
            return Mockito.mock(ProjectPolicyManager.class);
        }

        @Bean
        public GrpcProjectIIService grpcProjectService() {
            return new GrpcProjectIIService(
                    Mockito.mock(ProjectIIManager.class),
                    Mockito.mock(ProjectStatusManager.class),
                    Mockito.mock(ProjectTimelineManager.class),
                    Mockito.mock(MemberManager.class),
                    Mockito.mock(ProjectHoverManager.class),
                    Mockito.mock(ProjectStateManager.class),
                    Mockito.mock(SimilarProjectProvider.class),
                    Mockito.mock(GenericProjectCreator.class),
                    Mockito.mock(BatchProjectCreator.class),
                    mockProjectPolicyManager());
        }
    }

    @Autowired GrpcProjectPolicyManager grpcProjectPolicyManager;

    @Autowired ProjectPolicyManager mockProjectPolicyManager;

    @Test
    void testUpdatePolicyTypeAndPropertyType() {
        final var projectId = "1000126";
        final var policyType = "Commercial Property Insurance";
        final var propertyType = BuildingType.GAS_STATION.getNumber();
        grpcProjectPolicyManager.updatePolicyTypeAndPropertyType(
                projectId, policyType, propertyType);

        Mockito.verify(mockProjectPolicyManager)
                .updatePolicyTypeAndPropertyType(eq(projectId), eq(policyType), eq(propertyType));
    }

    @Test
    void testUpdatePolicyTypeAndPropertyType_nullPropertyType() {
        final var projectId = "1000126";
        final var policyType = "Commercial Property Insurance";
        grpcProjectPolicyManager.updatePolicyTypeAndPropertyType(projectId, policyType, null);

        Mockito.verify(mockProjectPolicyManager)
                .updatePolicyTypeAndPropertyType(eq(projectId), eq(policyType), isNull());
    }
}
