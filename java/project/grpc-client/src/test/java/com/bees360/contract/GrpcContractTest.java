package com.bees360.contract;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.contract.config.GrpcContractManagerConfig;
import com.bees360.contract.config.GrpcContractServiceManagerConfig;
import com.bees360.contract.config.JooqContractRepositoryConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

@ApplicationAutoConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
@DirtiesContext
@SpringBootTest(properties = "GRPC_SERVER_PORT=9806")
public class GrpcContractTest extends AbstractContractTest {

    @Import({
        JooqContractRepositoryConfig.class,
        GrpcContractManagerConfig.class,
        GrpcContractServiceManagerConfig.class,
    })
    @Configuration
    static class Config {

        @Bean
        public GrpcContractService grpcContractService(
                ContractManager jooqContractRepository,
                ContractServiceManager jooqContractServiceRepository) {
            return new GrpcContractService(jooqContractRepository, jooqContractServiceRepository);
        }
    }

    @Autowired private ContractServiceManager jooqContractServiceRepository;

    public GrpcContractTest(
            @Autowired ContractManager grpcContractManager,
            @Autowired ContractServiceManager grpcContractServiceManager) {
        super(grpcContractManager, grpcContractServiceManager);
    }

    @Override
    void generateServiceItem(Iterable<? extends ContractServiceItem> items) {
        items.forEach(jooqContractServiceRepository::addServiceItem);
    }

    @Test
    public void create() {
        super.create();
    }

    @Test
    public void findById() {
        super.findById();
    }

    @Test
    public void findByUnknownId() {
        super.findByUnknownId();
    }

    @Test
    public void findByCompanyId() {
        super.findByCompanyId();
    }

    @Test
    public void save() {
        super.save();
    }

    @Test
    public void update() {
        super.update();
    }

    @Test
    public void loadAll() {
        super.loadAll();
    }

    @Test
    public void addService() {
        super.addService();
    }

    @Test
    public void deleteService() {
        super.deleteService();
    }

    @Test
    public void updateService() {
        super.updateService();
    }

    @Test
    public void updateAttachment() {
        super.updateAttachment();
    }

    @Test
    public void findByQuery() {
        super.findByQuery();
    }

    @Test
    public void testUpdateContractStatusWithForceShouldSucceed() {
        super.testUpdateContractStatusWithForceShouldSucceed();
    }

    @Test
    public void testFindInactiveByCompanyIdShouldBeNull() {
        super.testFindInactiveByCompanyIdShouldBeNull();
    }
}
