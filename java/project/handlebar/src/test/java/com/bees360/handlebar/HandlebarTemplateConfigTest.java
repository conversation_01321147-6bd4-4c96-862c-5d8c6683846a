package com.bees360.handlebar;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

/**
 * test HandlebarTemplateConfig Configuraton is loaded correctly
 * and test HandlebarTemplateProperties is correctly injected
 */
@SpringBootTest(classes = {
        HandlebarTemplateConfigTest.TestConfig.class
})
@TestPropertySource(properties = {
        "handlebar.template.enabled=true",
        "handlebar.template.resource-prefix=test-prefix/",
        "handlebar.template.invoice-template-name=TestInvoiceTemplate.html",
        "handlebar.template.dps-template-name=TestDpsTemplate.html",
        "handlebar.template.mps-template-name=TestMpsTemplate.html"
})
public class HandlebarTemplateConfigTest {

    @Autowired
    private ApplicationContext context;

    @Autowired
    private HandlebarTemplateProperties templateProperties;

    @Test
    public void testConfigLoaded() {
        // Test if the HandlebarTemplateConfig class is loaded correctly
        assertThat(context.getBean(HandlebarTemplateConfig.class)).isNotNull();
    }

    @Test
    public void testPropertiesInjected() {
        // Test if the HandlebarTemplateProperties class is injected correctly
        assertThat(templateProperties).isNotNull();
    }

    @Test
    public void testPropertyValuesLoaded() {
        // Test if the custom property values are loaded correctly
        assertThat(templateProperties.isEnabled()).isTrue();
        assertThat(templateProperties.getResourcePrefix()).isEqualTo("test-prefix/");
        assertThat(templateProperties.getInvoiceTemplateName()).isEqualTo("TestInvoiceTemplate.html");
        assertThat(templateProperties.getDpsTemplateName()).isEqualTo("TestDpsTemplate.html");
        assertThat(templateProperties.getMpsTemplateName()).isEqualTo("TestMpsTemplate.html");
    }

    @Test
    public void testTemplatePathMethods() {
        // Test if the getTemplatePath methods return the correct paths
        assertThat(templateProperties.getInvoiceTemplatePath()).isEqualTo("test-prefix/TestInvoiceTemplate.html");
        assertThat(templateProperties.getDpsTemplatePath()).isEqualTo("test-prefix/TestDpsTemplate.html");
        assertThat(templateProperties.getMpsTemplatePath()).isEqualTo("test-prefix/TestMpsTemplate.html");
    }

    @Test
    public void testDefaultValues() {
        // Create a default HandlebarTemplateProperties instance and verify default values
        HandlebarTemplateProperties defaultProps = new HandlebarTemplateProperties();

        assertThat(defaultProps.isEnabled()).isFalse();
        // Resource prefix now contains environment variable placeholder
        assertThat(defaultProps.getResourcePrefix()).isEqualTo("cdn://private/${ENV:local}/");
        assertThat(defaultProps.getInvoiceTemplateName()).isEqualTo("NewInvoiceReportTemplate.html");
        assertThat(defaultProps.getDpsTemplateName()).isEqualTo("DpsReportTemplate.html");
        assertThat(defaultProps.getMpsTemplateName()).isEqualTo("MpsReportTemplate.html");

        // Template paths will contain the unresolved placeholder
        assertThat(defaultProps.getInvoiceTemplatePath()).isEqualTo("cdn://private/${ENV:local}/NewInvoiceReportTemplate.html");
        assertThat(defaultProps.getDpsTemplatePath()).isEqualTo("cdn://private/${ENV:local}/DpsReportTemplate.html");
        assertThat(defaultProps.getMpsTemplatePath()).isEqualTo("cdn://private/${ENV:local}/MpsReportTemplate.html");
    }

    @Test
    public void testEnvironmentVariableResolution() {
        // We need to set a system property to simulate an environment variable
        System.setProperty("ENV", "test-env");
        
        try {
            // Create Spring context with explicit property source for testing
            new ApplicationContextRunner()
                    .withPropertyValues("ENV=test-env") // This is key - explicitly setting property
                    .withBean(HandlebarTemplateProperties.class) // Manually register the bean
                    .withBean(HandlebarTemplateConfig.class) // Manually register config
                    .run(context -> {
                        HandlebarTemplateProperties properties = context.getBean(HandlebarTemplateProperties.class);
                        // Check that path is correctly constructed with resolved env variable
                        assertThat(properties.getResourcePrefix()).contains("test-env");
                        // Validate the full path
                        assertThat(properties.getInvoiceTemplatePath())
                                .isEqualTo("cdn://private/test-env/NewInvoiceReportTemplate.html");
                    });
        } finally {
            // Clean up system property
            System.clearProperty("ENV");
        }
    }

    @Test
    public void testEnvironmentVariableDefaultValue() {
        // Make sure ENV is not set
        System.clearProperty("ENV");
        
        // Create Spring context without the ENV property
        new ApplicationContextRunner()
                .withBean(HandlebarTemplateProperties.class) // Manually register the bean 
                .withBean(HandlebarTemplateConfig.class) // Manually register config
                .run(context -> {
                    HandlebarTemplateProperties properties = context.getBean(HandlebarTemplateProperties.class);
                    // Should use the default value 'local'
                    assertThat(properties.getResourcePrefix()).contains("local");
                    // Validate the full path
                    assertThat(properties.getInvoiceTemplatePath())
                            .isEqualTo("cdn://private/local/NewInvoiceReportTemplate.html");
                });
    }

    @Configuration
    @EnableAutoConfiguration
    @Import(HandlebarTemplateConfig.class)
    static class TestConfig {
        // Test configuration that imports HandlebarTemplateConfig with auto-configuration
    }
}
