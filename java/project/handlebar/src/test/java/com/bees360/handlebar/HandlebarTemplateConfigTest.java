package com.bees360.handlebar;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

/**
 * 测试HandlebarTemplateConfig配置是否正确加载
 * 以及HandlebarTemplateProperties是否正确注入
 */
@SpringBootTest(classes = {
        HandlebarTemplateConfigTest.TestConfig.class
})
@TestPropertySource(properties = {
        "handlebar.template.enabled=true",
        "handlebar.template.resource-prefix=test-prefix/",
        "handlebar.template.invoice-template=TestInvoiceTemplate.html",
        "handlebar.template.dps-template=TestDpsTemplate.html",
        "handlebar.template.mps-template=TestMpsTemplate.html"
})
public class HandlebarTemplateConfigTest {

    @Autowired
    private ApplicationContext context;

    @Autowired
    private HandlebarTemplateProperties templateProperties;

    @Test
    public void testConfigLoaded() {
        // 测试配置类是否成功加载
        assertThat(context.getBean(HandlebarTemplateConfig.class)).isNotNull();
    }

    @Test
    public void testPropertiesInjected() {
        // 测试属性类是否被成功注入
        assertThat(templateProperties).isNotNull();
    }

    @Test
    public void testPropertyValuesLoaded() {
        // 测试自定义属性值是否正确加载
        assertThat(templateProperties.isEnabled()).isTrue();
        assertThat(templateProperties.getResourcePrefix()).isEqualTo("test-prefix/");
        assertThat(templateProperties.getInvoiceTemplate()).isEqualTo("TestInvoiceTemplate.html");
        assertThat(templateProperties.getDpsTemplate()).isEqualTo("TestDpsTemplate.html");
        assertThat(templateProperties.getMpsTemplate()).isEqualTo("TestMpsTemplate.html");
    }

    @Test
    public void testTemplatePathMethods() {
        // 测试获取完整路径的方法
        assertThat(templateProperties.getInvoiceTemplatePath()).isEqualTo("test-prefix/TestInvoiceTemplate.html");
        assertThat(templateProperties.getDpsTemplatePath()).isEqualTo("test-prefix/TestDpsTemplate.html");
        assertThat(templateProperties.getMpsTemplatePath()).isEqualTo("test-prefix/TestMpsTemplate.html");
    }

    @Test
    public void testDefaultValues() {
        // 创建一个默认的HandlebarTemplateProperties实例，验证默认值
        HandlebarTemplateProperties defaultProps = new HandlebarTemplateProperties();

        assertThat(defaultProps.isEnabled()).isTrue();
        assertThat(defaultProps.getResourcePrefix()).isEqualTo("");
        assertThat(defaultProps.getInvoiceTemplate()).isEqualTo("NewInvoiceReportTemplate.html");
        assertThat(defaultProps.getDpsTemplate()).isEqualTo("DpsReportTemplate.html");
        assertThat(defaultProps.getMpsTemplate()).isEqualTo("MpsReportTemplate.html");

        // 当resourcePrefix为空时，应该使用static/前缀
        assertThat(defaultProps.getInvoiceTemplatePath()).isEqualTo("NewInvoiceReportTemplate.html");
        assertThat(defaultProps.getDpsTemplatePath()).isEqualTo("DpsReportTemplate.html");
        assertThat(defaultProps.getMpsTemplatePath()).isEqualTo("MpsReportTemplate.html");
    }

    @Configuration
    @EnableAutoConfiguration
    @Import(HandlebarTemplateConfig.class)
    static class TestConfig {
        // Test configuration that imports HandlebarTemplateConfig with auto-configuration
    }
}