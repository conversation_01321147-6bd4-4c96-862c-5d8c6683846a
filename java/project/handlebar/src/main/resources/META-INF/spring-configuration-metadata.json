{"groups": [{"name": "handlebar.template", "type": "com.bees360.handlebar.HandlebarTemplateProperties", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties"}], "properties": [{"name": "handlebar.template.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable handlebar template configuration.", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties", "defaultValue": false}, {"name": "handlebar.template.resource-prefix", "type": "java.lang.String", "description": "Template resource prefix path with environment variable support. Default uses CDN path with ENV variable (e.g. 'cdn://private/${ENV:local}/'). Can be overridden in configuration files.", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties", "defaultValue": "static/"}, {"name": "handlebar.template.invoice-template-name", "type": "java.lang.String", "description": "Invoice template file name.", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties", "defaultValue": "NewInvoiceReportTemplate.html"}, {"name": "handlebar.template.dps-template-name", "type": "java.lang.String", "description": "DPS report template file name.", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties", "defaultValue": "DpsReportTemplate.html"}, {"name": "handlebar.template.mps-template-name", "type": "java.lang.String", "description": "MPS report template file name.", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties", "defaultValue": "MpsReportTemplate.html"}], "hints": []}