{"groups": [{"name": "handlebar.template", "type": "com.bees360.handlebar.HandlebarTemplateProperties", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties", "description": "Handlebars template configuration properties."}], "properties": [{"name": "handlebar.template.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties", "description": "Whether to enable Handlebars template configuration.", "defaultValue": true}, {"name": "handlebar.template.resource-prefix", "type": "java.lang.String", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties", "description": "Template resource prefix path, e.g., 'static/' or 'https://cdn.example.com/templates/'.", "defaultValue": ""}, {"name": "handlebar.template.invoice-template", "type": "java.lang.String", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties", "description": "Invoice template name.", "defaultValue": "NewInvoiceReportTemplate.html"}, {"name": "handlebar.template.dps-template", "type": "java.lang.String", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties", "description": "DPS report template name.", "defaultValue": "DpsReportTemplate.html"}, {"name": "handlebar.template.mps-template", "type": "java.lang.String", "sourceType": "com.bees360.handlebar.HandlebarTemplateProperties", "description": "MPS report template name.", "defaultValue": "MpsReportTemplate.html"}]}