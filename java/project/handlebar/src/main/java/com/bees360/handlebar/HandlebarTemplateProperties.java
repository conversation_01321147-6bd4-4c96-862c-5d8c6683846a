package com.bees360.handlebar;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/** Handlebars template configuration properties class that provides unified template path configuration management */
@Data
@Validated
@ConfigurationProperties(prefix = "handlebar.template")
public class HandlebarTemplateProperties {

    /** Whether to enable Handlebars template configuration. Default: false */
    private boolean enabled = false;

    /**
     * 模板资源前缀路径，支持环境变量
     * 例如: "static/" 或 "cdn://private/${ENV:local}/"
     * 默认值可通过配置文件设置
     */
    private String resourcePrefix = "static/";

    /** Invoice template name. Default: NewInvoiceReportTemplate.html */
    @NotBlank private String invoiceTemplateName = "NewInvoiceReportTemplate.html";

    /** DPS report template name. Default: DpsReportTemplate.html */
    @NotBlank private String dpsTemplateName = "DpsReportTemplate.html";

    /** MPS report template name. Default: MpsReportTemplate.html */
    @NotBlank private String mpsTemplateName = "MpsReportTemplate.html";

    // Convenience methods that return complete template paths

    /**
     * Get the complete invoice template path
     *
     * @return Complete template path
     */
    public String getInvoiceTemplatePath() {
        return buildTemplatePath(invoiceTemplateName);
    }

    /**
     * Get the complete DPS template path
     *
     * @return Complete template path
     */
    public String getDpsTemplatePath() {
        return buildTemplatePath(dpsTemplateName);
    }

    /**
     * Get the complete MPS template path
     *
     * @return Complete template path
     */
    public String getMpsTemplatePath() {
        return buildTemplatePath(mpsTemplateName);
    }

    /**
     * Build the complete template path
     *
     * @param templateName Template name
     * @return Complete template path
     */
    private String buildTemplatePath(String templateName) {
        if (resourcePrefix == null || resourcePrefix.isEmpty()) {
            return "static/" + templateName;
        }
        return resourcePrefix + templateName;
    }
}
