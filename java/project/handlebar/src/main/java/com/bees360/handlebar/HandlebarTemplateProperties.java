package com.bees360.handlebar;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/** Handlebars模板配置属性类 提供统一的模板路径配置管理 */
@Data
@Validated
@ConfigurationProperties(prefix = "handlebar.template")
public class HandlebarTemplateProperties {

    /** 是否启用Handlebars模板配置 默认: false */
    private boolean enabled = false;

    /** 模板资源前缀路径 例如: "static/" 或 "https://cdn.example.com/templates/" */
    private String resourcePrefix = "static/";

    /** 发票模板名称 默认: NewInvoiceReportTemplate.html */
    @NotBlank private String invoiceTemplate = "NewInvoiceReportTemplate.html";

    /** DPS报告模板名称 默认: DpsReportTemplate.html */
    @NotBlank private String dpsTemplate = "DpsReportTemplate.html";

    /** MPS报告模板名称 默认: MpsReportTemplate.html */
    @NotBlank private String mpsTemplate = "MpsReportTemplate.html";

    // 便利方法，返回完整的模板路径

    /**
     * 获取完整的发票模板路径
     *
     * @return 完整的模板路径
     */
    public String getInvoiceTemplatePath() {
        return buildTemplatePath(invoiceTemplate);
    }

    /**
     * 获取完整的DPS模板路径
     *
     * @return 完整的模板路径
     */
    public String getDpsTemplatePath() {
        return buildTemplatePath(dpsTemplate);
    }

    /**
     * 获取完整的MPS模板路径
     *
     * @return 完整的模板路径
     */
    public String getMpsTemplatePath() {
        return buildTemplatePath(mpsTemplate);
    }

    /**
     * 构建完整的模板路径
     *
     * @param templateName 模板名称
     * @return 完整的模板路径
     */
    private String buildTemplatePath(String templateName) {
        if (resourcePrefix == null || resourcePrefix.isEmpty()) {
            return templateName;
        }

        if (resourcePrefix.endsWith("/")) {
            return resourcePrefix + templateName;
        }
        return resourcePrefix + '/' + templateName;
    }
}
