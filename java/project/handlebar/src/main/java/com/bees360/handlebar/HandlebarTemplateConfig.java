package com.bees360.handlebar;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/** Handlebars模板配置类 启用HandlebarTemplateProperties配置 可通过 handlebar.template.enabled 属性控制是否启用（默认启用） */
@Configuration
@EnableConfigurationProperties(HandlebarTemplateProperties.class)
@ConditionalOnProperty(prefix = "handlebar.template", name = "enabled", havingValue = "true")
public class HandlebarTemplateConfig {
    // Spring Boot will automatically register HandlebarTemplateProperties as a Bean
    // Other modules can directly inject and use it
}
