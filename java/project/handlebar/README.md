# Handlebars Template Configuration

这个模块提供了统一的 Handlebars 模板配置管理功能。

## 功能特性

- 统一的模板路径配置管理
- 支持配置开关控制是否启用
- 提供便利方法获取完整的模板路径
- 支持自定义模板资源前缀

## 配置属性

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `handlebar.template.enabled` | Boolean | `true` | 是否启用 Handlebars 模板配置 |
| `handlebar.template.resource-prefix` | String | `""` | 模板资源前缀路径 |
| `handlebar.template.invoice-template` | String | `NewInvoiceReportTemplate.html` | 发票模板名称 |
| `handlebar.template.dps-template` | String | `DpsReportTemplate.html` | DPS 报告模板名称 |
| `handlebar.template.mps-template` | String | `MpsReportTemplate.html` | MPS 报告模板名称 |

## 使用方式

### 1. 启用配置（默认行为）

```yaml
# application.yml
handlebar:
  template:
    enabled: true  # 可以省略，默认为 true
    resource-prefix: "static/"
    invoice-template: "CustomInvoiceTemplate.html"
    dps-template: "CustomDpsTemplate.html"
    mps-template: "CustomMpsTemplate.html"
```

### 2. 禁用配置

```yaml
# application.yml
handlebar:
  template:
    enabled: false  # 禁用整个 Handlebars 模板配置
```

### 3. 在代码中使用

```java
@Service
public class ReportService {
    
    @Autowired
    private HandlebarTemplateProperties templateProperties;
    
    public void generateInvoiceReport() {
        // 获取完整的模板路径
        String templatePath = templateProperties.getInvoiceTemplatePath();
        // 使用模板路径生成报告...
    }
}
```

### 4. 条件性注入

```java
@Service
@ConditionalOnProperty(prefix = "handlebar.template", name = "enabled", havingValue = "true")
public class HandlebarReportService {
    // 只有当配置启用时，这个服务才会被创建
}
```

## 配置开关的作用

- **启用时** (`handlebar.template.enabled=true` 或未设置)：
  - `HandlebarTemplateConfig` 配置类会被加载
  - `HandlebarTemplateProperties` bean 会被注册到 Spring 容器中
  - 其他模块可以正常注入和使用模板配置

- **禁用时** (`handlebar.template.enabled=false`)：
  - `HandlebarTemplateConfig` 配置类不会被加载
  - `HandlebarTemplateProperties` bean 不会被注册
  - 依赖此配置的其他组件也不会被创建

## 测试

运行测试来验证配置功能：

```bash
mvn test
```

测试包括：
- 配置正确加载的测试
- 属性值正确注入的测试
- 模板路径方法的测试
- 默认值的测试
- 配置开关功能的测试
