package com.bees360.contract;

import com.bees360.api.ApiHttpClient;
import com.bees360.attachment.Attachment;
import com.bees360.http.util.URIs;
import com.bees360.util.Iterables;
import com.google.gson.Gson;

import jakarta.annotation.Nullable;

import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.assertj.core.util.Lists;

import java.util.HashMap;
import java.util.Map;

public class TestHttpContractClient implements ContractManager {
    private final String host;
    private final String endpoint;
    private final ApiHttpClient httpClient;

    private static final Gson gson = new Gson();

    public TestHttpContractClient(String host, String endpoint, ApiHttpClient httpClient) {
        this.host = host;
        this.endpoint = endpoint;
        this.httpClient = httpClient;
    }

    @Override
    public Contract create(String insuredById, String processedById) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Contract findByCompanyId(@Nullable String insuredById, @Nullable String processedById) {
        Map<String, Object> map = new HashMap<>();
        if (insuredById != null) {
            map.put("insuredId", insuredById);
        }
        if (processedById != null) {
            map.put("processedId", processedById);
        }
        var request = URIs.paramsHttpGet(host + endpoint, map);
        var apiMessage = httpClient.execute(request);
        if (apiMessage.getContractCount() == 0) {
            return null;
        }
        return Contract.from(apiMessage.getContract(0));
    }

    @Override
    public Iterable<? extends Contract> findByQuery(Message.ContractQuery contractQuery) {
        var request = new HttpPost(host + endpoint + "/list");
        request.setEntity(ApiHttpClient.protobufEntity(contractQuery));

        var result = httpClient.execute(request);
        if (result.getContractCount() == 0) {
            return Lists.emptyList();
        }
        return Iterables.transform(result.getContractList(), Contract::from);
    }

    @Override
    public String namespace() {
        return "contract";
    }

    @Override
    public Contract findById(String id) {
        var request = new HttpGet(host + endpoint + "/" + id);
        var result = httpClient.execute(request);
        if (result.getContractCount() == 0) {
            return null;
        }
        return Contract.from(result.getContract(0));
    }

    @Override
    public Iterable<? extends Contract> findAllById(Iterable<String> ids) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Iterable<? extends Contract> loadAll() {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean existsById(String id) {
        throw new UnsupportedOperationException();
    }

    @Override
    public String save(Contract entity) {
        var request = new HttpPost(host + endpoint);
        request.setEntity(ApiHttpClient.protobufEntity(entity.toMessage()));
        var result = httpClient.execute(request);
        if (result.getContractCount() == 0) {
            return null;
        }
        return Contract.from(result.getContract(0)).getId();
    }

    @Override
    public Contract update(
            String id,
            Iterable<? extends ContractServiceItem> addItems,
            Iterable<? extends ContractServiceItem> removedItems) {
        var request = new HttpPut(host + endpoint + "/" + id);
        var updateBody =
                Message.UpdateContractRequest.newBuilder()
                        .setContractId(id)
                        .addAllAddServiceItem(
                                Iterables.transform(addItems, ContractServiceItem::toMessage))
                        .addAllRemovedServiceItem(
                                Iterables.transform(removedItems, ContractServiceItem::toMessage))
                        .build();
        request.setEntity(ApiHttpClient.protobufEntity(updateBody));
        var result = httpClient.execute(request);
        if (result.getContractCount() == 0) {
            return null;
        }
        return Contract.from(result.getContract(0));
    }

    @Override
    public boolean updateStatus(
            String id, Message.ContractMessage.ContractStatus status, boolean force) {
        var uri = host + endpoint + "/" + id + "/status?";
        var params = "status=" + status.name() + "&force=" + force;
        var request = new HttpPut(uri + params);
        var result = httpClient.execute(request);
        if (result.getStatus().getCodeValue()
                == com.bees360.status.Message.StatusMessage.Code.FAILED_PRECONDITION_VALUE) {
            throw new IllegalArgumentException();
        }

        return true;
    }

    @Override
    public boolean updateAttachment(String contractId, Iterable<String> attachmentIds) {
        return false;
    }

    public boolean uploadAttachment(String id, Iterable<? extends Attachment> attachments) {
        var uri = host + endpoint + "/" + id + "/attachment";
        var request = new HttpPost(uri);
        var list = Iterables.transform(attachments, Attachment::toMessage);
        var entity = com.bees360.api.Message.ApiMessage.newBuilder().addAllAttachment(list).build();
        request.setEntity(ApiHttpClient.protobufEntity(entity));
        var result = httpClient.execute(request);
        if (result.getStatus().getCodeValue()
                != com.bees360.status.Message.StatusMessage.Code.OK_VALUE) {
            throw new IllegalArgumentException();
        }

        return true;
    }

    @Override
    public void deleteById(String id) {
        var request = new HttpDelete(host + endpoint + "/" + id);
        var result = httpClient.execute(request);
        if (result.getStatus().getCodeValue() != 0) {
            throw new IllegalStateException();
        }
    }

    @Override
    public Iterable<String> saveAll(Iterable<? extends Contract> entities) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void deleteAllById(Iterable<String> ids) {
        throw new UnsupportedOperationException();
    }
}
