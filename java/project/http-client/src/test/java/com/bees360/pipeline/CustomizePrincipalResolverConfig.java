package com.bees360.pipeline;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class CustomizePrincipalResolverConfig {
    public static final String TEST_USER_ID = "testUserId";

    @Bean
    HandlerMethodArgumentResolver defaultUserResolver() {
        return new HandlerMethodArgumentResolver() {
            @Override
            public boolean supportsParameter(MethodParameter methodParameter) {
                return methodParameter.getParameterType().isAssignableFrom(String.class);
            }

            @Override
            public Object resolveArgument(
                    MethodParameter methodParameter,
                    ModelAndViewContainer modelAndViewContainer,
                    NativeWebRequest nativeWebRequest,
                    WebDataBinderFactory webDataBinderFactory)
                    throws Exception {
                return TEST_USER_ID;
            }
        };
    }

    @Bean
    CustomizePrincipalResolver customizePrincipalResolver(
            HandlerMethodArgumentResolver defaultUserResolver) {
        return new CustomizePrincipalResolver(defaultUserResolver);
    }

    class CustomizePrincipalResolver implements WebMvcConfigurer {
        private final HandlerMethodArgumentResolver customizeResolver;

        public CustomizePrincipalResolver(HandlerMethodArgumentResolver customizeResolver) {
            this.customizeResolver = customizeResolver;
        }

        @Override
        public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
            // put customized resolver at first
            argumentResolvers.add(0, customizeResolver);
        }
    }
}
