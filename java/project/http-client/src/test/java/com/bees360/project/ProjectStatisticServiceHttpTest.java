package com.bees360.project;

import com.bees360.api.ApiHttpClient;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.http.ApacheHttpClient;
import com.bees360.project.statistics.HttpProjectStatisticProvider;
import com.bees360.project.statistics.MockProjectStatisticProvider;
import com.bees360.project.statistics.ProjectStatisticEndpoint;
import com.bees360.project.statistics.ProjectStatisticProvider;
import com.bees360.project.statistics.TestMockProjectStatisticProvider;

import lombok.extern.log4j.Log4j2;

import org.apache.http.impl.client.HttpClients;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.net.URI;

@Log4j2
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@SpringJUnitConfig
@ApplicationAutoConfig(
        exclude = {
            SecurityAutoConfiguration.class,
        })
@ActiveProfiles("test")
class ProjectStatisticServiceHttpTest extends TestMockProjectStatisticProvider {

    @Import({
        ProjectStatisticEndpoint.class,
    })
    @Configuration
    static class Config {

        @Bean
        ProjectStatisticProvider endpointProjectStageStatisticsProvider() {
            return new MockProjectStatisticProvider();
        }
    }

    public ProjectStatisticServiceHttpTest(@Autowired TestRestTemplate testRestTemplate) {
        super(createHttpProjectStageStatisticProvider(testRestTemplate));
        log.info("Created {}(projectStatisticProvider={})", this, projectStatisticProvider);
    }

    private static HttpProjectStatisticProvider createHttpProjectStageStatisticProvider(
            TestRestTemplate testRestTemplate) {
        var uriContext = URI.create((testRestTemplate.getRootUri() + "/project"));
        var httpClient = ApiHttpClient.of(new ApacheHttpClient(HttpClients.createDefault()));
        return new HttpProjectStatisticProvider(uriContext, httpClient);
    }

    @Test
    public void getCustomerStatistic() {
        super.getCustomerStatistic();
    }

    @Test
    public void getStageStatistic() {
        super.getStageStatistic();
    }

    @Test
    public void getCurrentStatistic() {
        super.getCurrentStatistic();
    }
}
