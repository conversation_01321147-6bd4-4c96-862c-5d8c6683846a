package com.bees360.project;

import com.bees360.api.ApiHttpClient;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.HttpClient;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.project.state.ChangeReasonFromSpecifiedGroupTypeProvider;
import com.bees360.project.state.ProjectStateChangeReasonEndpoint;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.util.Iterables;

import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.List;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@SpringJUnitConfig
@DirtiesContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class HttpChangeReasonFromCategoryProviderTest {
    @Import({
        ApacheHttpClientConfig.class,
        ApiExceptionHandler.class,
        ApacheHttpClientConfig.class,
        ProtoHttpMessageConverterConfig.class,
        ProjectStateChangeReasonEndpoint.class,
    })
    @Configuration
    @ApplicationAutoConfig(
            exclude = {SecurityAutoConfiguration.class, GrpcServerFactoryAutoConfiguration.class})
    static class Config {}

    @MockBean public ProjectStateChangeReasonManager projectStateChangeReasonManager;

    @MockBean
    public ChangeReasonFromSpecifiedGroupTypeProvider changeReasonFromSpecifiedGroupTypeProvider;

    private final HttpChangeReasonFromCategoryProvider httpChangeReasonFromCategoryProvider;

    public HttpChangeReasonFromCategoryProviderTest(
            @Autowired HttpClient httpClient,
            @LocalServerPort int port,
            @Value("${http.project.endpoint}") String endpoint) {
        this.httpChangeReasonFromCategoryProvider =
                new HttpChangeReasonFromCategoryProvider(
                        "http://localhost:" + port, endpoint, new ApiHttpClient(httpClient));
    }

    @Test
    public void testListReasonCategory() {
        var reasonCategory = List.of("Denied On Location", "Client Cancelled");
        Mockito.doReturn(reasonCategory)
                .when(changeReasonFromSpecifiedGroupTypeProvider)
                .listReasonGroup();
        var httpReturnedReasonCategory =
                Iterables.toList(httpChangeReasonFromCategoryProvider.listReasonGroup());
        Assertions.assertTrue(
                CollectionUtils.isEqualCollection(reasonCategory, httpReturnedReasonCategory));
    }
}
