package com.bees360.kpi;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.impl.ForwardingActivityManager;
import com.bees360.api.ApiHttpClient;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.HttpClient;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.kpi.config.KPIProviderConfig;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@SpringJUnitConfig
@DirtiesContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class HttpKPIProviderTest extends AbstractKPIProviderTest {
    @Import({
        OperationManagerKPIEndpoint.class,
        ApacheHttpClientConfig.class,
        ApiExceptionHandler.class,
        ApacheHttpClientConfig.class,
        ProtoHttpMessageConverterConfig.class,
        KPIProviderConfig.class
    })
    @ApplicationAutoConfig(exclude = {SecurityAutoConfiguration.class})
    @Configuration
    static class Config {

        @Bean
        ActivityManager activityManager() {
            return new ForwardingActivityManager() {
                @Override
                protected ActivityManager delegate() {
                    return activityManager;
                }
            };
        }

        public static void main(final String[] args) {}
    }

    static ActivityManager activityManager = getActivityManager();

    public HttpKPIProviderTest(
            @Autowired ActivityManager activityManager,
            @Autowired HttpClient httpClient,
            @Value("${http.kpi.operations-manager.endpoint}") String endpoint,
            @LocalServerPort int port) {
        super(
                activityManager,
                new HttpKPIProviderClient(
                        "http://localhost:" + port, endpoint, new ApiHttpClient(httpClient)));
    }

    @BeforeEach
    public void clean() {
        activityManager = getActivityManager();
    }

    @Test
    public void testGetKPISnapshot() {
        super.testGetKPISnapshot();
    }

    @Test
    public void testGetKPISeriesNumbersWithWeek() {
        super.testGetKPISeriesNumbersWithWeek();
    }

    @Test
    public void testGetKPISeriesNumbersWithMonth() {
        super.testGetKPISeriesNumbersWithMonth();
    }

    @Test
    public void testGetOperationsManagerKPIWithImageQuality() {
        super.testGetOperationsManagerKPIWithImageQuality();
    }

    @Test
    public void testGetOperationsManagerKPIWithTurnAround() {
        super.testGetOperationsManagerKPIWithTurnAround();
    }

    @Test
    public void testGetOperationsManagerKPI() {
        super.testGetOperationsManagerKPI();
    }
}
