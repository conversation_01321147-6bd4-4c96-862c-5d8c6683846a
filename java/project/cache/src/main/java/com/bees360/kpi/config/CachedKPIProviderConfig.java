package com.bees360.kpi.config;

import com.bees360.kpi.CachedKPIProvider;
import com.bees360.kpi.KPIProvider;
import com.bees360.kpi.KPIProviderRedisMap;
import com.bees360.kpi.Message;
import com.bees360.redis.config.RedissonConfig;
import com.bees360.util.TimeZones;

import lombok.Data;

import org.redisson.api.RedissonClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Configuration
@Import({KPIProviderConfig.class, RedissonConfig.class})
public class CachedKPIProviderConfig {
    @Configuration
    @EnableConfigurationProperties
    @ConfigurationProperties(prefix = "redis")
    @Data
    static class Properties {
        @Data
        static class KPIProperties {
            private Duration expiry;
            private String prefix;
            private Message.KPINamespace namespace;
        }

        private List<KPIProperties> kpi;
    }

    @Bean
    @Primary
    Map<Message.KPINamespace, KPIProvider> cachedKpiProviderRedisMap(
            RedissonClient redisClient,
            Map<Message.KPINamespace, KPIProvider> kpiProviderMap,
            Properties properties) {
        Map<Message.KPINamespace, Properties.KPIProperties> propertiesMap =
                properties.getKpi().stream()
                        .collect(
                                Collectors.toMap(
                                        Properties.KPIProperties::getNamespace,
                                        Function.identity()));
        Map<Message.KPINamespace, KPIProvider> map = new HashMap<>();
        for (Map.Entry<Message.KPINamespace, KPIProvider> entry : kpiProviderMap.entrySet()) {
            map.put(
                    entry.getKey(),
                    cachedKPIProvider(
                            redisClient, entry.getValue(), propertiesMap.get(entry.getKey())));
        }

        return map;
    }

    private KPIProvider cachedKPIProvider(
            RedissonClient redisClient,
            KPIProvider kpiProvider,
            Properties.KPIProperties kpiProperties) {
        if (kpiProperties != null) {
            return new CachedKPIProvider(
                    new KPIProviderRedisMap(
                            redisClient,
                            kpiProperties.getPrefix(),
                            kpiProvider,
                            kpiProperties.getExpiry()),
                    TimeZones.DEFAULT_US_TIMEZONE_ID);
        }

        return kpiProvider;
    }
}
