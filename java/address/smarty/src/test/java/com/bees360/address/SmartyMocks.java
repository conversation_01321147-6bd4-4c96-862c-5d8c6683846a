package com.bees360.address;

import static org.mockito.Mockito.doAnswer;

import com.smartystreets.api.SmartySerializer;
import com.smartystreets.api.us_street.Candidate;
import com.smartystreets.api.us_street.Client;
import com.smartystreets.api.us_street.Lookup;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.mockito.ArgumentMatchers;

import java.nio.charset.StandardCharsets;
import java.util.List;

public final class SmartyMocks {

    private static final SmartySerializer smartySerializer = new SmartySerializer();

    private static Address ADDRESS_1 =
            Address.AddressBuilder.newBuilder()
                    .setLat(32.941673)
                    .setLng(-97.397682)
                    .setAddress("11820 Toppell Trl, Haslet, TX 76052")
                    .setStreetAddress("11820 Toppell Trl")
                    .setStreetNumber("11820")
                    .setRoute("Toppell Trl")
                    .setCity("Haslet")
                    .setCounty("Tarrant")
                    .setState("TX")
                    .setCountry("US")
                    .setZip("76052")
                    .build();

    private static Address ADDRESS_ZIP_LENGTH_4 =
            Address.AddressBuilder.newBuilder()
                    .setLat(40.247562)
                    .setLng(-74.055756)
                    .setAddress("29 Stonehenge Dr, Ocean, NJ 07712")
                    .setStreetAddress("29 Stonehenge Dr")
                    .setStreetNumber("29")
                    .setRoute("Stonehenge Dr")
                    .setCity("Ocean")
                    .setCounty("Monmouth")
                    .setState("NJ")
                    .setCountry("US")
                    .setZip("07712")
                    .build();

    static Address mockAddress1(Client smartyClient) {
        var candidateJson = "smarty_11820_toppell_trl_haslet_tx_7605.json";
        return mockAddress(smartyClient, candidateJson, ADDRESS_1);
    }

    static Address mockAddressWithZipLength4(Client smartyClient) {
        var candidateJson = "smarty_address_29_STONEHENGE_DR_OCEAN_TOWNSHIP_NJ_7712.json";
        return mockAddress(smartyClient, candidateJson, ADDRESS_ZIP_LENGTH_4);
    }

    static Address mockNullResult(Client smartyClient) {
        mockAddressWithZipLength4(smartyClient);
        return ADDRESS_1;
    }

    @SneakyThrows
    private static Address mockAddress(Client smartyClient, String resourceName, Address address) {
        doAnswer(
                        inv -> {
                            var lookup = inv.getArgument(0, Lookup.class);
                            if (!isLookupEqAddress(lookup, address)) {
                                return null;
                            }
                            lookup.setResult(getCandidate(resourceName));
                            return null;
                        })
                .when(smartyClient)
                .send(ArgumentMatchers.any(Lookup.class));
        return address;
    }

    private static boolean isLookupEqAddress(Lookup lookup, Address address) {
        if (StringUtils.equalsIgnoreCase(lookup.getStreet(), address.getAddress())) {
            return true;
        }
        return StringUtils.equalsIgnoreCase(lookup.getZipCode(), address.getZip())
                && StringUtils.equals(lookup.getCity(), address.getCity())
                && StringUtils.equals(lookup.getState(), address.getState());
    }

    @SneakyThrows
    private static List<Candidate> getCandidate(String name) {
        var json =
                IOUtils.resourceToString(
                        name, StandardCharsets.UTF_8, SmartyMocks.class.getClassLoader());
        var candidates =
                smartySerializer.deserialize(
                        json.getBytes(StandardCharsets.UTF_8), Candidate[].class);
        if (candidates == null) {
            return List.of();
        }
        return List.of(candidates);
    }
}
