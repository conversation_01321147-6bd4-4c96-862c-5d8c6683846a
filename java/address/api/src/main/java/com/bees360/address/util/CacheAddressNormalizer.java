package com.bees360.address.util;

import com.bees360.address.Address;
import com.bees360.address.AddressNormalizer;
import com.bees360.address.AddressRepository;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.function.Supplier;

@Log4j2
public class CacheAddressNormalizer implements AddressNormalizer {

    private final AddressNormalizer normalizer;
    private final AddressRepository cacheRepository;

    public CacheAddressNormalizer(
            @NonNull AddressNormalizer normalizer, @NonNull AddressRepository cacheRepository) {
        this.normalizer = normalizer;
        this.cacheRepository = cacheRepository;
        log.info("Created {}(normalizer={},cacheRepository={})", this, normalizer, cacheRepository);
    }

    @Override
    public Address normalize(Address address) {
        if (address == null) {
            return null;
        }
        return saveIfNotExist(address.getAddress(), () -> normalizer.normalize(address));
    }

    @Override
    public Address normalize(String address) {
        return saveIfNotExist(address, () -> normalizer.normalize(address));
    }

    private Address saveIfNotExist(String address, Supplier<Address> addressSupplier) {
        if (StringUtils.isEmpty(address)) {
            return null;
        }
        var exist = cacheRepository.findByAddress(address);
        if (exist != null) {
            return exist;
        }
        var normalized = addressSupplier.get();
        if (normalized == null) {
            return null;
        }
        var id = cacheRepository.save(normalized);
        return Address.from(normalized.toMessage().toBuilder().setId(id).build());
    }
}
