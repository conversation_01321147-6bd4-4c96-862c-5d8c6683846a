spring:
  jooq:
    sql-dialect: POSTGRES
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver

  main:
    web-application-type: none

grpc:
  server:
    port: 9898
  client:
    addressManager:
      address: static://bees360-address-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

hazardHub:
  api: https://api.hazardhub.com/v1/
  token: Token token=FZuWzsmXq7k4sfxzxrMw
  retry:
    maxAttempts: 3
    initialInterval: 1000
    multiplier: 2.0
    maxInterval: 30000

address:
  time-zone:
    zone-id-map:
      Alaska: America/Anchorage
      Atlantic: America/St_Thomas
      Eastern: America/New_York
      Central: America/Chicago
      Hawaii: Pacific/Honolulu
      Mountain: America/Denver
      Pacific: America/Los_Angeles
      Samoa: Pacific/Pago_Pago

rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password

redis:
  client:
    host: redis
    port: 6379
    database: 0

state-time-zone-map:
  AL: America/Chicago
  AR: America/Chicago
  IL: America/Chicago
  IA: America/Chicago
  LA: America/Chicago
  MN: America/Chicago
  MS: America/Chicago
  MO: America/Chicago
  OK: America/Chicago
  WI: America/Chicago
  ME: America/New_York
  VT: America/New_York
  NH: America/New_York
  MA: America/New_York
  RI: America/New_York
  CT: America/New_York
  NY: America/New_York
  NJ: America/New_York
  DE: America/New_York
  MD: America/New_York
  DC: America/New_York
  PA: America/New_York
  OH: America/New_York
  WV: America/New_York
  VA: America/New_York
  NC: America/New_York
  SC: America/New_York
  GA: America/New_York
  CO: America/Denver
  MT: America/Denver
  NM: America/Denver
  UT: America/Denver
  WY: America/Denver
  CA: America/Los_Angeles
  NV: America/Los_Angeles
  WA: America/Los_Angeles
  AK: America/Anchorage
  HI: Pacific/Honolulu
