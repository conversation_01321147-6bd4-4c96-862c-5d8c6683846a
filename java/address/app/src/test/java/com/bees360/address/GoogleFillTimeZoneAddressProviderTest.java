package com.bees360.address;

import com.bees360.address.config.Config;
import com.bees360.address.config.JooqAddressRepositoryConfig;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;
import java.util.TimeZone;
import java.util.function.UnaryOperator;

@Import({
    DataSourceAutoConfiguration.class,
    DataSourceTransactionManagerAutoConfiguration.class,
})
@SpringBootTest(classes = {Config.class, JooqAddressRepositoryConfig.class})
@ActiveProfiles("test")
public class GoogleFillTimeZoneAddressProviderTest {

    private final AddressProvider googleFillTimeZoneAddressProvider;
    private final AddressRepository addressRepository;
    private final Map<String, String> stateTimeZoneMap;

    GoogleFillTimeZoneAddressProviderTest(
            @Autowired UnaryOperator<AddressProvider> googleFillTimeZoneAddressProviderCustomize,
            @Autowired AddressRepository addressRepository,
            @Autowired Map<String, String> stateTimeZoneMap) {
        this.googleFillTimeZoneAddressProvider =
                googleFillTimeZoneAddressProviderCustomize.apply(addressRepository);
        this.addressRepository = addressRepository;
        this.stateTimeZoneMap = stateTimeZoneMap;
    }

    static Address ADDRESS =
            Address.AddressBuilder.newBuilder()
                    .setLat(32.9416154)
                    .setLng(-97.3977666)
                    .setAddress("11820 Toppell Trl, Haslet, TX 76052")
                    .setStreetNumber("11820")
                    .setRoute("Toppell Trl")
                    .setCity("Haslet")
                    .setCounty("Tarrant County")
                    .setState("TX")
                    .setCountry("US")
                    .setZip("76052")
                    .build();

    static Address ADDRESS2 =
            Address.AddressBuilder.newBuilder()
                    .setLat(32.9416154)
                    .setLng(-97.3977666)
                    .setAddress("11820 Toppell Trl, Haslet, TX 76052")
                    .setStreetNumber("11820")
                    .setRoute("Toppell Trl")
                    .setCity("Haslet")
                    .setCounty("Tarrant County")
                    .setState("TEST")
                    .setCountry("US")
                    .setZip("76052")
                    .build();

    @Disabled
    @Test
    public void testGoogleFillTimeZoneAddressProviderApi() {
        var addressId = addressRepository.save(GoogleFillTimeZoneAddressProviderTest.ADDRESS2);
        Address address = googleFillTimeZoneAddressProvider.findById(addressId);
        Assertions.assertEquals("America/Chicago", address.getTimeZone().getID());
        Assertions.assertEquals(
                "America/Chicago", addressRepository.findById(addressId).getTimeZone().getID());
        addressRepository.deleteById(addressId);
    }

    @Test
    public void testGoogleFillTimeZoneAddressProviderMockApi() {
        var addressId = addressRepository.save(ADDRESS2);
        var googleGeoApi = Mockito.mock(GoogleGeoApi.class);

        Mockito.when(googleGeoApi.apply(Mockito.any()))
                .thenReturn(TimeZone.getTimeZone("America/Chicago"));

        GoogleFillTimeZoneAddressProvider googleFillTimeZoneAddressProviderMock =
                new GoogleFillTimeZoneAddressProvider(
                        addressRepository, googleGeoApi, addressRepository, stateTimeZoneMap);

        Address address = googleFillTimeZoneAddressProviderMock.findById(addressId);
        Assertions.assertEquals("America/Chicago", address.getTimeZone().getID());
        addressRepository.deleteById(addressId);
    }

    @Test
    public void testGoogleFillTimeZoneAddressProviderMockApiReturnNull() {
        var addressId = addressRepository.save(ADDRESS2);
        var googleGeoApi = Mockito.mock(GoogleGeoApi.class);

        Mockito.when(googleGeoApi.apply(Mockito.any())).thenReturn(null);

        GoogleFillTimeZoneAddressProvider googleFillTimeZoneAddressProviderMock =
                new GoogleFillTimeZoneAddressProvider(
                        addressRepository, googleGeoApi, addressRepository, stateTimeZoneMap);

        Address address = googleFillTimeZoneAddressProviderMock.findById(addressId);
        Assertions.assertNull(address.getTimeZone());
        addressRepository.deleteById(addressId);
    }
}
