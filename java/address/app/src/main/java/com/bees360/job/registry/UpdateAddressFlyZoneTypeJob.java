package com.bees360.job.registry;

import com.bees360.address.Address;
import com.bees360.codec.ProtoGsonDecoder;
import com.google.gson.annotations.JsonAdapter;

import lombok.Data;
import lombok.NonNull;

@Data
@JobPayload
public class UpdateAddressFlyZoneTypeJob {

    @JsonAdapter(value = ProtoGsonDecoder.class)
    private Address address;

    public UpdateAddressFlyZoneTypeJob(@NonNull Address address) {
        this.address = address;
    }
}
