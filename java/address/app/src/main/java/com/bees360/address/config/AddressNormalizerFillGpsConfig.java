package com.bees360.address.config;

import static com.bees360.util.Defaults.isNullOrZero;

import com.bees360.address.Address;
import com.bees360.address.AddressGpsProvider;
import com.bees360.address.AddressNormalizer;
import com.bees360.address.GoogleAddressGpsProvider;
import com.bees360.address.GoogleGeoApi;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Log4j2
@Configuration
public class AddressNormalizerFillGpsConfig {

    @Bean
    @ConditionalOnBean(GoogleGeoApi.class)
    public AddressGpsProvider googleAddressGpsProvider(
            GoogleGeoApi googleGeoApi,
            @Value("${google-map.country-value:US}") String countryValue) {
        return new GoogleAddressGpsProvider(googleGeoApi, countryValue);
    }

    // using conditional on property to allow to disable gps customizer.
    @Bean
    @ConditionalOnBean(AddressGpsProvider.class)
    @ConditionalOnProperty(
            prefix = "address.app.normalizer.customize",
            name = "gps",
            havingValue = "true",
            matchIfMissing = true)
    AddressNormalizerCustomize addressNormalizerCustomizeGps(
            AddressGpsProvider addressGpsProvider) {

        return normalizer -> {
            log.info("Customize {} to fill gps with {}", normalizer, addressGpsProvider);
            return new AddressNormalizer() {

                @Override
                public Address normalize(Address address) {
                    return fillAddressGps(normalizer.normalize(address));
                }

                @Override
                public Address normalize(String address) {
                    return fillAddressGps(normalizer.normalize(address));
                }

                private Address fillAddressGps(Address address) {
                    if (address == null) {
                        return address;
                    }
                    if (isNullOrZero(address.getLng()) || isNullOrZero(address.getLat())) {
                        // fill address gps
                        address = addressGpsProvider.fillAddressGps(address);
                        log.info("Fill gps of address to '{}'", address.toMessage());
                    }
                    return address;
                }
            };
        };
    }
}
