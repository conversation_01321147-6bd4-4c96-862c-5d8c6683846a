package com.bees360.address.config;

import com.bees360.address.Address;
import com.bees360.address.AddressNormalizer;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.util.Optional;
import java.util.Set;
import java.util.function.BinaryOperator;

@Log4j2
@Configuration
public class AddressNormalizerStateConfig {

    enum USStateEnum {
        AL("Alabama", "AL", "Ala."),
        AK("Alaska", "AK", "Alaska"),
        AZ("Arizona", "AZ", "Ariz."),
        AR("Arkansas", "AR", "Ark."),
        CA("California", "CA", "Calif."),
        CO("Colorado", "CO", "Colo."),
        CT("Connecticut", "CT", "Conn."),
        DE("Delaware", "DE", "Del."),
        FL("Florida", "FL", "Fla."),
        GA("Georgia", "GA", "Ga."),
        HI("Hawaii", "HI", "Hawaii"),
        ID("Idaho", "ID", "Idaho"),
        IL("Illinois", "IL", "Ill."),
        IN("Indiana", "IN", "Ind."),
        IA("Iowa", "IA", "Iowa"),
        KS("Kansas", "KS", "Kans."),
        KY("Kentucky", "KY", "Ky."),
        LA("Louisiana", "LA", "La."),
        ME("Maine", "ME", "Maine"),
        MD("Maryland", "MD", "Md."),
        MA("Massachusetts", "MA", "Mass."),
        MI("Michigan", "MI", "Mich."),
        MN("Minnesota", "MN", "Minn."),
        MS("Mississippi", "MS", "Miss."),
        MO("Missouri", "MO", "Mo."),
        MT("Montana", "MT", "Mont."),
        NE("Nebraska", "NE", Set.of("Neb.", "Nebr.")),
        NV("Nevada", "NV", "Nev."),
        NH("New Hampshire", "NH", "N.H."),
        NJ("New Jersey", "NJ", "N.J."),
        NM("New Mexico", "NM", "N.Mex."),
        NY("New York", "NY", "N.Y."),
        NC("North Carolina", "NC", "N.C."),
        ND("North Dakota", "ND", "N.Dak."),
        OH("Ohio", "OH", "Ohio"),
        OK("Oklahoma", "OK", "Okla."),
        OR("Oregon", "OR", Set.of("Ore.", "Oreg.")),
        PA("Pennsylvania", "PA", "Pa."),
        RI("Rhode Island", "RI", "R.I."),
        SC("South Carolina", "SC", "S.C."),
        SD("South Dakota", "SD", "S.Dak."),
        TN("Tennessee", "TN", "Tenn."),
        TX("Texas", "TX", "Tex."),
        UT("Utah", "UT", "Utah"),
        VT("Vermont", "VT", "Vt."),
        VA("Virginia", "VA", "Va."),
        WA("Washington", "WA", "Wash."),
        WV("West Virginia", "WV", "W.Va."),
        WI("Wisconsin", "WI", Set.of("Wis.", "Wisc.")),
        WY("Wyoming", "WY", "Wyo.");

        @Getter private final String name;
        @Getter private final String uspsAbbr;
        @Getter private final Set<String> traditionalAbbr;

        USStateEnum(String name, String uspsAbbr, String traditionalAbbr) {
            this(name, uspsAbbr, Set.of(traditionalAbbr));
        }

        USStateEnum(String name, String uspsAbbr, Set<String> traditionalAbbr) {
            this.name = name;
            this.uspsAbbr = uspsAbbr;
            this.traditionalAbbr = traditionalAbbr;
        }

        public static USStateEnum lockUp(String state) {
            for (var s : USStateEnum.values()) {
                if (StringUtils.equalsAnyIgnoreCase(state, s.getName(), s.getUspsAbbr())) {
                    return s;
                }
                if (s.getTraditionalAbbr().stream().anyMatch(e -> StringUtils.equals(state, e))) {
                    return s;
                }
            }
            return null;
        }
    }

    @Bean
    BinaryOperator<String> countryStateNormalizer() {
        return (country, state) -> {
            if (!StringUtils.equalsAnyIgnoreCase(country, null, "", "US", "USA")) {
                return state;
            }
            return Optional.ofNullable(USStateEnum.lockUp(state))
                    .map(USStateEnum::getUspsAbbr)
                    .orElse(state);
        };
    }

    // make sure to normalize the state before the customizer since it may work for other
    // normalizers.
    // using conditional on property to allow to disable timezone customizer.
    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    @ConditionalOnProperty(
            prefix = "address.app.normalizer.customize",
            name = "state",
            havingValue = "true",
            matchIfMissing = true)
    AddressNormalizerCustomize addressNormalizerCustomizeState(
            @Qualifier("countryStateNormalizer") BinaryOperator<String> countryStateNormalizer) {

        return normalizer -> {
            log.info("Customize {} to normalize state with {}", normalizer, countryStateNormalizer);
            return new AddressNormalizer() {

                @Override
                public Address normalize(Address address) {
                    return normalizeState(normalizer.normalize(address));
                }

                @Override
                public Address normalize(String address) {
                    return normalizeState(normalizer.normalize(address));
                }

                private Address normalizeState(Address address) {
                    if (address == null) {
                        return null;
                    }
                    var state =
                            countryStateNormalizer.apply(address.getCountry(), address.getState());
                    return Address.from(address.toMessage().toBuilder().setState(state).build());
                }
            };
        };
    }
}
