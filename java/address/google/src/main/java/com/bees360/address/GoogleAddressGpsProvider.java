package com.bees360.address;

import static com.bees360.util.Defaults.emptyIfNull;
import static com.bees360.util.Functions.acceptIfNotNull;
import static com.google.maps.model.ComponentFilter.country;

import com.bees360.api.common.Gps;
import com.google.common.base.Preconditions;
import com.google.maps.GeocodingApi;
import com.google.maps.model.ComponentFilter;
import com.google.maps.model.GeocodingResult;
import com.google.maps.model.LatLng;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

@Log4j2
public class GoogleAddressGpsProvider implements AddressGpsProvider {
    private final GoogleGeoApi api;
    private static final String streetAddressComponent = "street_address";
    private final String countryValue;

    public GoogleAddressGpsProvider(GoogleGeoApi api, String countryValue) {
        this.api = api;
        this.countryValue = countryValue;
        log.info(
                "Created {}(streetAddressComponent={},countryValue={})",
                this,
                streetAddressComponent,
                countryValue);
    }

    @Override
    public Gps getZipcodeGpsByAddress(Address address) {
        log.info("find address gps by google address '{}'", address);
        Preconditions.checkArgument(
                StringUtils.isNotEmpty(address.getZip()),
                String.format("Zipcode in address %s requires not null.", address));
        GeocodingResult results = getAddressGpsFromAddress(address);

        if (results == null) {
            throw new NoSuchElementException(
                    String.format("Address %s gps not found by Google.", address));
        }
        LatLng latLng = results.geometry.location;
        return Gps.of(latLng.lat, latLng.lng);
    }

    private GeocodingResult getAddressGpsFromAddress(Address address) {
        // find address by full address
        var resultByFullAddress =
                getAddressGpsFromGoogle(address.getAddress(), a -> isZipCodeSame(a, address));

        // find address by zipcode
        if (resultByFullAddress == null) {
            resultByFullAddress =
                    getAddressGpsFromGoogle(
                            null,
                            null,
                            null,
                            address.getZip(),
                            address.getCountry(),
                            a -> isZipCodeSame(a, address));
        }
        return resultByFullAddress;
    }

    private GeocodingResult getAddressGpsFromGoogle(
            String fullAddress, Function<GeocodingResult, Boolean> filter) {
        GeocodingResult[] results;
        results = api.apply(api -> GeocodingApi.geocode(api, fullAddress).await());

        if (results == null) {
            log.info("Find nothing from address :{} by google.", fullAddress);
            return null;
        }
        return Arrays.stream(results).filter(filter::apply).findFirst().orElse(null);
    }

    private GeocodingResult getAddressGpsFromGoogle(
            String streetAddress,
            String city,
            String state,
            String zipcode,
            String country,
            Function<GeocodingResult, Boolean> filter) {
        // build full address
        var addressStr =
                emptyIfNull(streetAddress)
                        + ", "
                        + emptyIfNull(city)
                        + ", "
                        + emptyIfNull(state)
                        + " "
                        + emptyIfNull(zipcode)
                        + ", "
                        + emptyIfNull(country);
        return getAddressGpsFromGoogle(addressStr, filter);
    }

    private boolean isZipCodeSame(GeocodingResult result, Address address) {
        var addressAccessor = new GoogleAddressAccessor(result);
        var matcher = GoogleAddressMatcher.builder().zipcode(address.getZip()).build();
        return matcher.match(addressAccessor);
    }

    /**
     * 使用Google geocoding
     * component查询address的gps信息，并填充至address中的gps。当结果中的city，state，zipcode均与address相同时，认为gps是准确的，其他情况认为gps是不准确的。
     *
     * <p>1.使用完整component查询Google address，记为results1。
     * 2.在results1中筛选出city，state，zipcode都匹配的数据并设置gps，此时gps为准确的。
     * 2.若1无法筛选出数据，在results1中筛选出city，state匹配的数据并设置gps，此时认为gps为不准确的。之后的所有gps都认为是不准确的。
     * 3.若2无法筛选出数据，判断address中是否有zipcode，若有，则以zipcode为component，否则以city，state为component，查询出Google
     * address，记为results2。 4.在result2中筛选出city，state匹配的数据并设置gps。
     * 5.若4中无法筛选出数据，在results2中筛选出state匹配的数据并设置gps。 6.若仍无法筛选出数据，返回原有address。
     *
     * @param address 待填充gps的address。
     * @return true：gps信息填充成功。false：未查询到符合条件的gps信息。
     */
    @Override
    public Address fillAddressGps(Address address) {
        var city = address.getCity();
        var state = address.getState();
        var zipcode = address.getZip();
        var googleResults = getGoogleAddressFromGoogleComponents(address);
        log.info("get Google address :{} by full components.", googleResults);

        var cityStateMatcher = GoogleAddressMatcher.builder().city(city).state(state).build();
        var result =
                fillAddressWithMatcher(
                        googleResults,
                        address,
                        GoogleAddressMatcher.builder()
                                .state(state)
                                .city(city)
                                .zipcode(zipcode)
                                .build());
        if (result != null) {
            return result;
        }
        result = fillAddressWithMatcher(googleResults, address, cityStateMatcher);
        if (result != null) {
            return result;
        }
        List<ComponentFilter> filters = new ArrayList<>();
        if (StringUtils.isNotEmpty(address.getZip())) {
            filters.add(ComponentFilter.postalCode(address.getZip()));
        } else {
            acceptIfNotNull(filters::add, address.getCity(), ComponentFilter::locality);
            acceptIfNotNull(filters::add, address.getState(), ComponentFilter::administrativeArea);
        }
        googleResults = getGoogleAddressFromGoogleComponents(filters);
        log.info("get Google address :{} by filters :{}", googleResults, filters);
        result = fillAddressWithMatcher(googleResults, address, cityStateMatcher);
        if (result != null) {
            return result;
        }
        result =
                fillAddressWithMatcher(
                        googleResults,
                        address,
                        GoogleAddressMatcher.builder().state(state).build());
        return result == null ? address : result;
    }

    /**
     * 在results中根据matcher筛选出与address匹配的Google address，并把Google address的gps信息填充至address中。
     * 有多个符合条件的结果时会填充第一个Google address的gps信息。
     *
     * @param results 用于筛选啊gps的Google address
     * @param address 待填充的address信息。
     * @param matcher Google address与待填充address的匹配规则。
     * @return 填充完成后的address信息。null表示未查询到对应的Google address。
     */
    @Nullable
    private Address fillAddressWithMatcher(
            List<GoogleAddressAccessor> results, Address address, GoogleAddressMatcher matcher) {
        var resultFound = results.stream().filter(matcher::match).findFirst().orElse(null);
        if (resultFound == null) {
            return null;
        }
        return fillAddressGpsWithIsApproximate(address, resultFound.getGps(), true);
    }

    private List<GoogleAddressAccessor> getGoogleAddressFromGoogleComponents(
            List<ComponentFilter> filters) {
        var results =
                api.apply(
                        api1 ->
                                GeocodingApi.newRequest(api1)
                                        .components(filters.toArray(ComponentFilter[]::new))
                                        .await());
        log.info(
                "get results :{} from google geocode components from filter :{}", results, filters);
        return Arrays.stream(results).map(GoogleAddressAccessor::new).collect(Collectors.toList());
    }

    private List<GoogleAddressAccessor> getGoogleAddressFromGoogleComponents(Address address) {
        List<ComponentFilter> filters = new ArrayList<>();
        acceptIfNotNull(
                filters::add,
                address.getAddress(),
                a -> new ComponentFilter(streetAddressComponent, a));
        acceptIfNotNull(filters::add, address.getCity(), ComponentFilter::locality);
        acceptIfNotNull(filters::add, address.getState(), ComponentFilter::administrativeArea);
        filters.add(country(countryValue));
        return getGoogleAddressFromGoogleComponents(filters);
    }

    private Address fillAddressGpsWithIsApproximate(
            Address address, Gps gps, Boolean isApproximate) {
        return Address.from(
                address.toMessage().toBuilder()
                        .setLat(gps.getLatitude())
                        .setLng(gps.getLongitude())
                        .setIsGpsApproximate(isApproximate)
                        .build());
    }
}
