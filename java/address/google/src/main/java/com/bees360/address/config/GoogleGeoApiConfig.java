package com.bees360.address.config;

import com.bees360.address.GoogleGeoApi;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "google-map", name = "apiKey")
public class GoogleGeoApiConfig {

    @Bean
    GoogleGeoApi googleGeoApi(@Value("${google-map.apiKey}") String apiKey) {
        return new GoogleGeoApi(apiKey);
    }
}
