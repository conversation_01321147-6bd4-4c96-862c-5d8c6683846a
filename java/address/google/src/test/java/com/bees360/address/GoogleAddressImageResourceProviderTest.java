package com.bees360.address;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.address.Address.AddressBuilder;
import com.google.common.collect.Iterables;

import lombok.SneakyThrows;

import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.util.function.Function;

@Disabled
class GoogleAddressImageResourceProviderTest {

    @TempDir File tempDir;

    private final GoogleGeoApis googleGeoApis =
            new GoogleGeoApis(new File("src/test/resources/google-map-geo-api-key.txt"));

    @Test
    void testGetGpsAddressImages() {
        testGetAddressImages(gpsAddressProvider(), "gps-address-image.png");
    }

    @Test
    void testGetLocationAddressImages() {
        testGetAddressImages(locationAddressProvider(), "location-address-image.png");
    }

    @SneakyThrows
    void testGetAddressImages(Function<String, Address> addressProvider, String imageFileName) {
        var api = googleGeoApis.createGeoApi();
        var provider = new GoogleAddressImageProvider(addressProvider, api);
        var images = provider.getAddressImages("1");
        var resource = Iterables.getFirst(images, null);
        assertNotNull(resource);

        var resultFile = new File(tempDir, imageFileName);
        resource.accept(input -> FileUtils.copyInputStreamToFile(input, resultFile));
        assertTrue(
                FileUtils.contentEquals(
                        resultFile, new File("src/test/resources/" + imageFileName)));
    }

    private Function<String, Address> gpsAddressProvider() {
        return (addressId) ->
                AddressBuilder.newBuilder()
                        .setLat(32.55853923771652)
                        .setLng(-97.12706372062844)
                        .build();
    }

    private Function<String, Address> locationAddressProvider() {
        return (addressId) ->
                AddressBuilder.newBuilder()
                        .setAddress("604 S Willow St, Mansfield, TX 76063, USA")
                        .build();
    }
}
