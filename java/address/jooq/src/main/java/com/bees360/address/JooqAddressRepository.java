package com.bees360.address;

import static com.bees360.jooq.persistent.address.Tables.ADDRESS_AIRSPACE;
import static com.bees360.jooq.persistent.address.tables.Address.ADDRESS;
import static com.bees360.jooq.persistent.address.tables.AddressFlyzone.ADDRESS_FLYZONE;
import static com.bees360.jooq.persistent.address.tables.AddressHiveLocation.ADDRESS_HIVE_LOCATION;
import static com.bees360.jooq.persistent.address.tables.HiveLocation.HIVE_LOCATION;

import com.bees360.address.Message.AddressMessage.FlyZoneType;
import com.bees360.jooq.persistent.address.tables.records.AddressRecord;
import com.bees360.jooq.util.DSLUtils;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.RecordMapper;
import org.jooq.SelectOnConditionStep;
import org.springframework.transaction.annotation.Transactional;

import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Log4j2
@Transactional(rollbackFor = Exception.class)
public class JooqAddressRepository implements AddressRepository {

    private final String realm;

    private final DSLContext dsl;

    public JooqAddressRepository(DSLContext dsl) {
        this(dsl, null);
    }

    public JooqAddressRepository(DSLContext dsl, String realm) {
        this.dsl = dsl;
        this.realm = realm;
        log.info("Created {}(dsl={},realm={})", this, dsl, realm);
    }

    @Override
    public String namespace() {
        return realm;
    }

    protected RecordMapper<Record, Address> mapping() {
        return r -> {
            var hiveLocationDistance =
                    JooqHiveLocationDistance.builder()
                            .zipcode(r.get(HIVE_LOCATION.ZIPCODE))
                            .city(r.get(HIVE_LOCATION.CITY))
                            .state(r.get(HIVE_LOCATION.STATE))
                            .distanceMiles(r.get(ADDRESS_HIVE_LOCATION.DISTANCE_MILES))
                            .addressId(r.get(ADDRESS.ID))
                            .build();
            var flyZoneType =
                    Optional.ofNullable(r.get(ADDRESS_FLYZONE.FLYZONE_TYPE))
                            .map(FlyZoneType::forNumber)
                            .orElse(FlyZoneType.NONE);
            var timeZone =
                    Optional.ofNullable(r.get(ADDRESS.TIME_ZONE))
                            .map(TimeZone::getTimeZone)
                            .orElse(null);
            var airspace = r.get(ADDRESS_AIRSPACE.ID) == null ? null : new JooqAirspace(r);
            return Address.AddressBuilder.newBuilder()
                    .setId(r.get(ADDRESS.ID))
                    .setLat(r.get(ADDRESS.LAT))
                    .setLng(r.get(ADDRESS.LNG))
                    .setAddress(r.get(ADDRESS.ADDRESS_))
                    .setStreetNumber(r.get(ADDRESS.STREET_NUMBER))
                    .setRoute(r.get(ADDRESS.ROUTE))
                    .setCity(r.get(ADDRESS.CITY))
                    .setCounty(r.get(ADDRESS.COUNTY))
                    .setState(r.get(ADDRESS.STATE))
                    .setCountry(r.get(ADDRESS.COUNTRY))
                    .setZip(r.get(ADDRESS.ZIP))
                    .setStreetAddress(r.get(ADDRESS.STREET_ADDRESS))
                    .setTimeZone(timeZone)
                    .setCreatedAt(r.get(ADDRESS.CREATED_AT))
                    .setUpdatedAt(r.get(ADDRESS.UPDATED_AT))
                    .setHiveDistance(hiveLocationDistance)
                    .setFlyZoneType(flyZoneType)
                    .setIsGpsApproximate(r.get(ADDRESS.IS_GPS_APPROXIMATE))
                    .setAirspace(airspace)
                    .build();
        };
    }

    @Override
    public void updateAddressTimeZoneById(String id, TimeZone timeZone) {
        dsl.update(ADDRESS)
                .set(ADDRESS.TIME_ZONE, timeZone.getID())
                .where(ADDRESS.ID.eq(id))
                .execute();
    }

    @Override
    public Address findById(String id) {
        return selectFromAddress().where(ADDRESS.ID.eq(id)).fetchOne(mapping());
    }

    @Override
    public Address findByAddress(String address) {
        return selectFromAddress().where(ADDRESS.ADDRESS_.eq(address)).fetchOne(mapping());
    }

    protected SelectOnConditionStep<Record> selectFromAddress() {
        return dsl.select()
                .from(ADDRESS)
                .leftJoin(ADDRESS_HIVE_LOCATION)
                .on(ADDRESS.ID.eq(ADDRESS_HIVE_LOCATION.ADDRESS_ID))
                .leftJoin(HIVE_LOCATION)
                .on(ADDRESS_HIVE_LOCATION.HIVE_LOCATION_ID.eq(HIVE_LOCATION.ID))
                .leftJoin(ADDRESS_FLYZONE)
                .on(ADDRESS.ID.eq(ADDRESS_FLYZONE.ADDRESS_ID))
                .leftJoin(ADDRESS_AIRSPACE)
                .on(ADDRESS_AIRSPACE.ADDRESS_ID.eq(ADDRESS.ID));
    }

    @Override
    public Iterable<? extends Address> loadAll() {
        return null;
    }

    @Override
    public boolean existsById(String id) {
        return false;
    }

    @Override
    public String save(Address entity) {
        Iterable<String> ids = saveAll(List.of(entity));
        Iterator<String> itr = ids.iterator();
        return itr.hasNext() ? itr.next() : null;
    }

    @Override
    public Iterable<String> saveAll(Iterable<? extends Address> entities) {
        List<AddressRecord> recordList =
                StreamSupport.stream(entities.spliterator(), false)
                        .map(
                                address -> {
                                    AddressRecord rec = new AddressRecord();
                                    rec.setLat(address.getLat());
                                    rec.setLng(address.getLng());
                                    rec.setAddress(address.getAddress());
                                    rec.setStreetNumber(address.getStreetNumber());
                                    rec.setRoute(address.getRoute());
                                    rec.setCity(address.getCity());
                                    rec.setCounty(address.getCounty());
                                    rec.setState(address.getState());
                                    rec.setCountry(address.getCountry());
                                    rec.setZip(address.getZip());
                                    rec.setStreetAddress(address.getStreetAddress());
                                    rec.setTimeZone(
                                            Optional.ofNullable(address.getTimeZone())
                                                    .map(TimeZone::getID)
                                                    .orElse(null));
                                    rec.setCreatedAt(address.getCreatedAt());
                                    rec.setUpdatedAt(address.getUpdatedAt());
                                    rec.setIsGpsApproximate(address.isGpsApproximate());
                                    rec.setIsVerified(true);
                                    return rec;
                                })
                        .collect(Collectors.toList());
        return DSLUtils.upsert(dsl, ADDRESS, recordList, ADDRESS.ADDRESS_).stream()
                .map(AddressRecord::getId)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteById(String id) {
        dsl.delete(ADDRESS).where(ADDRESS.ID.eq(id)).execute();
    }

    @Override
    public void deleteAllById(Iterable<String> ids) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Address normalize(Address address) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Address normalize(String address) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Iterable<? extends Address> findAllById(Iterable<String> ids) {
        return selectFromAddress().where(ADDRESS.ID.in(Iterables.toSet(ids))).fetch(mapping());
    }
}
