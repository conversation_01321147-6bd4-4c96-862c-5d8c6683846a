package com.bees360.address.config;

import com.bees360.address.AddressFlyZoneTypeManager;
import com.bees360.address.AddressHiveLocationProvider;
import com.bees360.address.JooqAddressFlyZoneTypeManager;
import com.bees360.address.JooqAddressHiveLocationProvider;
import com.bees360.address.JooqAddressRepository;
import com.bees360.address.JooqVerifiedAddressRepository;
import com.bees360.jooq.config.JooqConfig;

import org.jooq.DSLContext;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({JooqConfig.class})
public class JooqAddressRepositoryConfig {

    @Bean("jooqAddressRepository")
    @ConditionalOnProperty(
            value = "address.jooq.repository.filter-verified",
            havingValue = "false",
            matchIfMissing = true)
    JooqAddressRepository jooqAddressRepository(DSLContext dslContext) {
        return new JooqAddressRepository(dslContext);
    }

    @Bean("jooqAddressRepository")
    @ConditionalOnProperty(value = "address.jooq.repository.filter-verified", havingValue = "true")
    JooqAddressRepository jooqVerifiedAddressRepository(DSLContext dslContext) {
        return new JooqVerifiedAddressRepository(dslContext);
    }

    @Bean
    AddressHiveLocationProvider addressHiveLocationProvider(DSLContext dslContext) {
        return new JooqAddressHiveLocationProvider(dslContext);
    }

    @Bean
    AddressFlyZoneTypeManager addressFlyZoneTypeManager(DSLContext dslContext) {
        return new JooqAddressFlyZoneTypeManager(dslContext);
    }
}
