package com.bees360.address;

import com.bees360.jooq.config.JooqConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@SpringBootTest(classes = JooqAddressFlyZoneTypeManagerTest.Config.class)
public class JooqAddressFlyZoneTypeManagerTest extends AddressFlyZoneTypeManagerTest {

    @Import({
        JooqConfig.class,
        DataSourceAutoConfiguration.class,
        TransactionAutoConfiguration.class,
        JooqAddressFlyZoneTypeManager.class,
    })
    @Configuration
    public static class Config {}

    @Autowired
    public JooqAddressFlyZoneTypeManagerTest(AddressFlyZoneTypeManager addressFlyZoneTypeManager) {
        super(addressFlyZoneTypeManager);
    }

    @Test
    public void testUpdateThenFind() {
        super.updateThenFind();
    }

    @Test
    public void testNotFound() {
        super.findNonExisted();
    }
}
