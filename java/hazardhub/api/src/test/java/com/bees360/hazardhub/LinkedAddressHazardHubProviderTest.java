package com.bees360.hazardhub;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class LinkedAddressHazardHubProviderTest {
    @Mock private AddressHazardHubProvider primaryAddressHazardHubProvider;
    @Mock private AddressHazardHubProvider secondaryAddressHazardHubProvider;
    private LinkedAddressHazardHubProvider linkedAddressHazardHubProvider;
    private AddressHazardHub addressHazardHub;

    @BeforeEach
    void init() {
        linkedAddressHazardHubProvider =
                new LinkedAddressHazardHubProvider(
                        primaryAddressHazardHubProvider, secondaryAddressHazardHubProvider);

        addressHazardHub =
                AddressHazardHub.builder()
                        .addressId(getRandomString())
                        .risksJson(getRandomString())
                        .protectionCode(getRandomString())
                        .enhancedPropertyJson(getRandomString())
                        .replacementCostsJson(getRandomString())
                        .build();
    }

    @Test
    void testFindByPrimaryProvider() {
        when(primaryAddressHazardHubProvider.findLatestByAddressId(any()))
                .thenReturn(addressHazardHub);
        AddressHazardHub actual =
                linkedAddressHazardHubProvider.findLatestByAddressId(getRandomString());
        Assertions.assertEquals(addressHazardHub, actual);
    }

    @Test
    void testFindBySecondaryProvider() {
        when(secondaryAddressHazardHubProvider.findLatestByAddressId(any()))
                .thenReturn(addressHazardHub);
        AddressHazardHub actual =
                linkedAddressHazardHubProvider.findLatestByAddressId(getRandomString());
        Assertions.assertEquals(addressHazardHub, actual);
    }

    private String getRandomString() {
        return RandomStringUtils.randomAlphabetic(10);
    }
}
