package com.bees360.webhook;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.event.EventPublisher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.event.util.ForwardingSplitEventPublisher;
import com.bees360.event.util.JsonConvertEventPublisher;
import com.bees360.rabbit.RabbitApi;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.webhook.config.Bees360Config;
import com.bees360.webhook.config.EventConverterConfig;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Map;
import java.util.function.Function;

@ApplicationAutoConfig
@EnableEncryptableProperties
@Import({Bees360Config.class})
@Configuration
public class WebhookApp {

    public static void main(final String[] args) {
        ExitableSpringApplication.run(WebhookApp.class, args);
    }

    @Import({RabbitApiConfig.class, EventConverterConfig.class})
    @Configuration
    @ConditionalOnProperty(
            prefix = "webhook.app.rabbit",
            name = "enabled",
            havingValue = "true",
            matchIfMissing = true)
    static class config {
        @Bean(name = {"eventPublisher"})
        @ConditionalOnBean(name = "eventName2JsonConverter")
        public EventPublisher jsonConverterEventPublisher(
                @Autowired @Qualifier("eventName2JsonConverter")
                        Map<String, Function<String, String>> eventName2JsonConverter,
                RabbitApi rabbitApi) {
            RabbitEventPublisher rabbitEventPublisher = new RabbitEventPublisher(rabbitApi);
            var jsonConverterEventPublisher =
                    new JsonConvertEventPublisher(rabbitEventPublisher, eventName2JsonConverter);
            return new ForwardingSplitEventPublisher(jsonConverterEventPublisher);
        }

        @Bean
        @ConditionalOnMissingBean(name = "eventPublisher")
        public EventPublisher eventPublisher(RabbitApi rabbitApi) {
            RabbitEventPublisher rabbitEventPublisher = new RabbitEventPublisher(rabbitApi);
            return new ForwardingSplitEventPublisher(rabbitEventPublisher);
        }
    }
}
