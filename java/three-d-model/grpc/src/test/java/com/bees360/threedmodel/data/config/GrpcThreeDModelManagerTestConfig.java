package com.bees360.threedmodel.data.config;

import com.bees360.threedmodel.GrpcThreeDModelService;
import com.bees360.threedmodel.config.GrpcThreeDModelClientConfig;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    GrpcServerAutoConfiguration.class,
    GrpcServerFactoryAutoConfiguration.class,
    GrpcClientAutoConfiguration.class,
    GrpcThreeDModelService.class,
    GrpcThreeDModelClientConfig.class,
})
@Configuration
public class GrpcThreeDModelManagerTestConfig {}
