package com.bees360.threedmodel.data;

import com.bees360.job.registry.ThreeDDataJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.bees360.status.Message;

import lombok.extern.log4j.Log4j2;

import java.util.Objects;
import java.util.function.Function;

/** 3D数据存储job，创建model记录，从maplist中获取modelName以及modelImage记录 */
@Log4j2
public class ThreeDDataJobExecutor extends AbstractJobExecutor<ThreeDDataJob> {

    private final ResourcePool resourcePool;

    private final ThreeDModelDataManager threeDModelDataManager;

    private final Function<Resource, String> modelNameProvider;

    private final Function<String, String> groupKeyProvider;

    private final Function<String, String> mapListKeyProvider;

    public ThreeDDataJobExecutor(
            ResourcePool resourcePool,
            ThreeDModelDataManager threeDModelDataManager,
            Function<Resource, String> modelNameProvider,
            Function<String, String> groupKeyProvider,
            Function<String, String> mapListKeyProvider) {
        this.resourcePool = resourcePool;
        this.threeDModelDataManager = threeDModelDataManager;
        this.modelNameProvider = modelNameProvider;
        this.groupKeyProvider = groupKeyProvider;
        this.mapListKeyProvider = mapListKeyProvider;
        log.info("Created ThreeDDataJobExecutor");
    }

    @Override
    protected void handle(ThreeDDataJob job) {
        var jobStatus = job.getStatus();
        if (Objects.equals(jobStatus.getCode(), Message.StatusMessage.Code.CANCELLED)) {
            log.info("3D job is stopped.jobId {}", job.getJobId());
            return;
        }
        if (!Objects.equals(jobStatus.getCode(), Message.StatusMessage.Code.OK)) {
            log.warn("3d job is failed.jobId {}", job.getJobId());
            return;
        }
        log.info("Processing 3d data, jobId: {}", job.getJobId());
        var jobId = job.getJobId();
        var groupKey = groupKeyProvider.apply(jobId);
        // 获取modelName
        String mapListKey = mapListKeyProvider.apply(jobId);
        var mapListResource = resourcePool.get(mapListKey);
        var modelName = modelNameProvider.apply(mapListResource);
        // 创建model记录
        var modelId = saveModel(groupKey, modelName);
        // 获取modelImage记录
        var modelImages = MapListConverter.getModelImage(modelId, mapListResource);
        // 数据存入modelImage表
        threeDModelDataManager.saveModelImage(modelImages);
        log.info("3d data processing finished, jobId: {}", jobId);
    }

    private String saveModel(String grouKey, String modelName) {
        Model model = Model.of(null, modelName, grouKey, null, 0, null);
        log.info("creating model, groupKey: {}", grouKey);
        return threeDModelDataManager.saveModel(model);
    }
}
