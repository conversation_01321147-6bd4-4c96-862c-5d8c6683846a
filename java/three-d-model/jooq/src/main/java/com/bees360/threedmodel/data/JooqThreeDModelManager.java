package com.bees360.threedmodel.data;

import static com.bees360.jooq.persistent.threedmodel.Tables.IMAGE_FACET;
import static com.bees360.jooq.persistent.threedmodel.Tables.MODEL;
import static com.bees360.jooq.persistent.threedmodel.Tables.MODEL_FACET;
import static com.bees360.jooq.persistent.threedmodel.Tables.MODEL_IMAGE;

import static org.jooq.impl.DSL.field;

import com.bees360.api.common.Point;
import com.bees360.jooq.persistent.threedmodel.tables.records.ModelFacetRecord;
import com.bees360.jooq.persistent.threedmodel.tables.records.ModelImageRecord;
import com.bees360.jooq.util.DSLUtils;
import com.bees360.threedmodel.BiImageId;
import com.bees360.threedmodel.Component;
import com.bees360.threedmodel.Facet;
import com.bees360.threedmodel.ImagePoint;
import com.bees360.threedmodel.MappingInfo;
import com.bees360.threedmodel.MappingUtil;
import com.bees360.threedmodel.Message.ThreeDModelMessage.Component.Facet.Line.Type;
import com.bees360.threedmodel.Message.ThreeDModelMessage.Component.Facet.Orientation;
import com.bees360.threedmodel.PointOrientation;
import com.bees360.threedmodel.RecordComponent;
import com.bees360.threedmodel.RecordFacet;
import com.bees360.threedmodel.RecordThreeDModel;
import com.bees360.threedmodel.ThreeDModel;
import com.bees360.threedmodel.ThreeDModelManager;
import com.bees360.util.Iterables;
import com.google.gson.Gson;

import jakarta.annotation.Nonnull;

import lombok.extern.log4j.Log4j2;

import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.tools.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Log4j2
public class JooqThreeDModelManager implements ThreeDModelManager {

    private final DSLContext dsl;

    private static final Gson gson = new Gson();

    private static final List<Field<?>> IMAGE_FACET_SELECT_FIELD =
            List.of(
                    IMAGE_FACET.MODEL_ID,
                    IMAGE_FACET.IMAGE_ID,
                    IMAGE_FACET.FACET_ID,
                    field("st_asText(path_2d_full)"),
                    field("st_asText(path_2d_seen)"),
                    field("st_asText(path_2d_crsp_overview)"),
                    field("st_asText(center_point)"));

    private static final List<Field<?>> MODEL_FACET_SELECT_FIELD =
            List.of(
                    MODEL_FACET.MODEL_ID,
                    MODEL_FACET.COMPONENT_ID,
                    MODEL_FACET.FACET_ID,
                    MODEL_FACET.AREA,
                    MODEL_FACET.NAME,
                    MODEL_FACET.PITCH,
                    field("st_asText(path)"),
                    MODEL_FACET.PATH_TYPE,
                    MODEL_FACET.SHARED_FACET,
                    MODEL_FACET.PLANE_PROP,
                    MODEL_FACET.PLANE_COEF,
                    MODEL_FACET.IS_HIGH_ROOF,
                    MODEL_FACET.ORIENTATION);

    public JooqThreeDModelManager(DSLContext dsl) {
        this.dsl = dsl;
    }

    @Override
    public void addImageInModel(Iterable<String> imageIds, String modelId, String addedBy) {
        modelValidate(modelId);
        var modelImageRecords =
                Iterables.toStream(imageIds)
                        .map(
                                imageId -> {
                                    var record = new ModelImageRecord();
                                    record.set(MODEL_IMAGE.IMAGE_ID, imageId);
                                    record.set(MODEL_IMAGE.MODEL_ID, modelId);
                                    record.set(MODEL_IMAGE.IS_DELETE, false);
                                    record.set(MODEL_IMAGE.UPDATED_BY, addedBy);
                                    return record;
                                })
                        .collect(Collectors.toList());
        DSLUtils.upsert(
                dsl, MODEL_IMAGE, modelImageRecords, MODEL_IMAGE.MODEL_ID, MODEL_IMAGE.IMAGE_ID);
    }

    @Override
    public void deleteImageFromModel(Iterable<String> imageIds, String modelId, String updatedBy) {
        modelValidate(modelId);
        var modelImageRecords =
                Iterables.toStream(imageIds)
                        .map(
                                imageId -> {
                                    var record = new ModelImageRecord();
                                    record.set(MODEL_IMAGE.IMAGE_ID, imageId);
                                    record.set(MODEL_IMAGE.MODEL_ID, modelId);
                                    record.set(MODEL_IMAGE.IS_DELETE, true);
                                    record.set(MODEL_IMAGE.UPDATED_BY, updatedBy);
                                    return record;
                                })
                        .collect(Collectors.toList());
        dsl.batchUpdate(modelImageRecords).execute();
    }

    @Override
    public void setLineType(
            String modelId,
            int componentId,
            int facetId,
            int lineId,
            @Nonnull Type lineType,
            String updatedBy) {
        modelValidate(modelId);
        var modelFacetRecord =
                dsl.select()
                        .from(MODEL_FACET)
                        .where(MODEL_FACET.MODEL_ID.eq(modelId))
                        .and(MODEL_FACET.COMPONENT_ID.eq(String.valueOf(componentId)))
                        .and(MODEL_FACET.FACET_ID.eq(String.valueOf(facetId)))
                        .fetchAny();
        if (modelFacetRecord == null) {
            throw new IllegalArgumentException(
                    "facet(" + modelId + " " + componentId + " " + facetId + ") does not exist");
        }
        var record = (ModelFacetRecord) modelFacetRecord;
        var pathType = gson.fromJson(record.getValue(MODEL_FACET.PATH_TYPE), int[].class);
        if (lineId < 0 || lineId >= pathType.length) {
            throw new IllegalArgumentException("lineId is out of bound of pathType");
        }
        pathType[lineId] = lineType.getNumber();
        record.set(MODEL_FACET.PATH_TYPE, gson.toJson(pathType));
        record.set(MODEL_FACET.UPDATED_BY, updatedBy);
        record.update();
    }

    @Override
    public void setRotationDegree(String modelId, double rotationDegree, String updatedBy) {
        modelValidate(modelId);
        dsl.update(MODEL)
                .set(MODEL.ROTATION_DEGREE, (float) rotationDegree)
                .where(MODEL.ID.eq(modelId))
                .execute();
    }

    @Nonnull
    @Override
    public Map<String, Iterable<String>> getModelId(Iterable<String> imageIds) {
        if (imageIds == null || Iterables.toCollection(imageIds).isEmpty()) {
            throw new IllegalArgumentException("imageId should not be null or empty");
        }
        Map<String, List<String>> modelImageMap =
                dsl.select(MODEL_IMAGE.IMAGE_ID, MODEL_IMAGE.MODEL_ID)
                        .from(MODEL_IMAGE)
                        .join(MODEL)
                        .on(MODEL.ID.eq(MODEL_IMAGE.MODEL_ID))
                        .where(MODEL_IMAGE.IMAGE_ID.in(Iterables.toList(imageIds)))
                        .and(MODEL.IS_DELETED.eq(false))
                        .fetchGroups(MODEL_IMAGE.IMAGE_ID, r -> r.get(MODEL_IMAGE.MODEL_ID));
        return new HashMap<>(modelImageMap);
    }

    @Override
    public Iterable<? extends ThreeDModel> getModel(String groupKey) {
        var modelRecord =
                dsl.select()
                        .from(MODEL)
                        .where(MODEL.GROUP_KEY.eq(groupKey))
                        .and(MODEL.IS_DELETED.eq(false))
                        .fetch();
        var modelIds =
                modelRecord.stream()
                        .map(record -> record.get(MODEL.ID))
                        .collect(Collectors.toList());
        var modelFacetRecord =
                dsl.select(MODEL_FACET_SELECT_FIELD)
                        .from(MODEL_FACET)
                        .where(MODEL_FACET.MODEL_ID.in(modelIds))
                        .fetch();
        return getThreeDModelFromRecord(modelRecord, modelFacetRecord);
    }

    @Nonnull
    @Override
    public Map<BiImageId, Iterable<MappingInfo>> getImageMapping(Iterable<BiImageId> biImageIds) {
        var res = new HashMap<BiImageId, Iterable<MappingInfo>>();
        biImageIds.forEach(
                biImageId -> {
                    String modelId = getModelIdByBiImageId(biImageId);
                    var originImageFacet = getImageFacet(modelId, biImageId.getOriginImageId());
                    if (biImageId.getTargetImageId().equals(biImageId.getOriginImageId())) {
                        var mappingInfo =
                                facetsMappingTo(biImageId.getOriginImageId(), originImageFacet);
                        res.put(biImageId, mappingInfo);
                        return;
                    }
                    var coeficientMap = getCoeficientMap(modelId);
                    var biModelImage =
                            getModelImages(
                                    List.of(
                                            biImageId.getOriginImageId(),
                                            biImageId.getTargetImageId()),
                                    modelId);
                    var camOrigin =
                            gson.fromJson(
                                    biModelImage
                                            .get(biImageId.getOriginImageId())
                                            .getCamPropertyMatrix(),
                                    double[][].class);
                    var camTarget =
                            gson.fromJson(
                                    biModelImage
                                            .get(biImageId.getTargetImageId())
                                            .getCamPropertyMatrix(),
                                    double[][].class);
                    var mappingInfo =
                            facetsMappingTo(
                                    biImageId.getTargetImageId(),
                                    originImageFacet,
                                    camOrigin,
                                    camTarget,
                                    coeficientMap);
                    res.put(biImageId, mappingInfo);
                });
        return res;
    }

    @Nonnull
    @Override
    public Map<String, Iterable<MappingInfo>> getPointMapping(
            Iterable<MappingInfo> originMappings) {
        var result = new HashMap<String, Iterable<MappingInfo>>();
        // 批量映射
        originMappings.forEach(
                origin -> {
                    var originImageId = origin.getImageId();
                    var originPoints = origin.getMapping();
                    // 获取image所在model的modelId
                    var modelIds = Iterables.toList(getModelId(originImageId));
                    // 多个model处理,找出point所在facet量最多的modelId
                    String finalModelId = null;
                    int minCannotMappingCount = Iterables.toCollection(originPoints).size() + 1;
                    Map<Point, Iterable<ImageFacet>> finalPointFacetMap = Map.of();
                    for (String modelId : modelIds) {
                        var pointFacetMap =
                                pointInImageFacetInMemory(modelId, originImageId, originPoints);
                        // 该model无法映射的点的个数
                        var cannotMappingCount = new AtomicInteger(0);
                        pointFacetMap
                                .values()
                                .forEach(
                                        imageFacets -> {
                                            if (Iterables.toList(imageFacets).isEmpty()) {
                                                cannotMappingCount.incrementAndGet();
                                            }
                                        });
                        if (cannotMappingCount.get() < minCannotMappingCount) {
                            finalModelId = modelId;
                            minCannotMappingCount = cannotMappingCount.get();
                            finalPointFacetMap = pointFacetMap;
                        }
                        log.info(
                                "cannot mapping count: {} of model: {}",
                                cannotMappingCount.get(),
                                modelId);
                    }
                    // 映射
                    var infos =
                            pointMappingTo(
                                    finalModelId, originImageId, originPoints, finalPointFacetMap);
                    result.put(originImageId, infos);
                });
        return result;
    }

    @Override
    public Map<ImagePoint, PointOrientation> getPointOrientation(Iterable<ImagePoint> imagePoints) {
        var pointOrientationMap = new HashMap<ImagePoint, PointOrientation>();
        imagePoints.forEach(
                imagePoint -> {
                    var imageId = imagePoint.getImageId();
                    var point = imagePoint.getPoint();
                    var modelIds = getModelId(imageId);
                    modelIds.forEach(
                            modelId -> {
                                // 理论上一个point只会在一个model的facet里
                                if (pointOrientationMap.containsKey(imagePoint)) {
                                    return;
                                }
                                var imageFacet =
                                        pointInImageFacetFix(
                                                pointInImageFacetInMemory(modelId, imageId, point),
                                                imageId,
                                                modelId);
                                var modelFacetMap = getModelFacetMap(modelId);
                                pointOrientationMap.put(
                                        imagePoint,
                                        PointOrientation.of(
                                                modelId,
                                                Orientation.forNumber(
                                                        modelFacetMap
                                                                .get(imageFacet.getFacetId())
                                                                .getOrientation())));
                            });
                    if (!pointOrientationMap.containsKey(imagePoint)) {
                        throw new IllegalArgumentException(
                                "point: " + point.getX() + " " + point.getY() + "not in any facet");
                    }
                });
        return pointOrientationMap;
    }

    /**
     * check if model exist in database if not, throw IllegalArgumentException
     *
     * @param modelId modelId of model who will be checked
     */
    private void modelValidate(String modelId) {
        if (StringUtils.isBlank(modelId)) {
            throw new IllegalArgumentException("modelId should not be null or empty");
        }
        var modelCount =
                dsl.selectCount()
                        .from(MODEL)
                        .where(MODEL.ID.eq(modelId))
                        .and(MODEL.IS_DELETED.eq(false))
                        .fetchOne();
        if (modelCount == null || !Objects.equals(modelCount.value1(), 1)) {
            throw new IllegalArgumentException(
                    "The model(id: " + modelId + ") has not been created or deleted yet");
        }
    }

    private List<MappingInfo> pointMappingTo(
            String modelId,
            String originImageId,
            Iterable<Point> originPoints,
            Map<Point, Iterable<ImageFacet>> pointInFacet) {
        // key = targetImageId, value=mapping
        var pointMapping = new HashMap<String, List<Point>>();
        // 判断这些点在哪一个imageFacet中
        var pointFacetMap = pointInImageFacetFix(pointInFacet, originImageId, modelId);
        // 获取model中所有的image，对他们进行映射(量比较大)
        var modelImages = getModelImage(modelId);
        // 获取源modelImage的camPropertyMatrix
        var originCam =
                gson.fromJson(
                        modelImages.stream()
                                .filter(modelImage -> modelImage.getImageId().equals(originImageId))
                                .map(ModelImage::getCamPropertyMatrix)
                                .collect(Collectors.toList())
                                .get(0),
                        double[][].class);
        // key=facetId, value=modelFacet
        var modelFacetMap = getModelFacetMap(modelId);
        // 逐个点进行映射 注意顺序
        originPoints.forEach(
                point -> {
                    var inFacet = pointFacetMap.get(point);
                    var planeCoef =
                            gson.fromJson(
                                    modelFacetMap.get(inFacet.getFacetId()).getPlaneCoef(),
                                    double[].class);
                    // 对所有model中的图片进行映射
                    modelImages.forEach(
                            modelImage -> {
                                // 源图片无需映射
                                if (modelImage.getImageId().equals(originImageId)) {
                                    return;
                                }
                                var mappingTo =
                                        MappingUtil.twoDPointToAnother(
                                                point,
                                                originCam,
                                                gson.fromJson(
                                                        modelImage.getCamPropertyMatrix(),
                                                        double[][].class),
                                                planeCoef);
                                if (!pointMapping.containsKey(modelImage.getImageId())) {
                                    pointMapping.put(modelImage.getImageId(), new ArrayList<>());
                                }
                                pointMapping.get(modelImage.getImageId()).add(mappingTo);
                            });
                });
        return pointMapping.keySet().stream()
                .map(
                        targetImageId ->
                                MappingInfo.of(
                                        targetImageId, null, pointMapping.get(targetImageId)))
                .collect(Collectors.toList());
    }

    /**
     * 调整point所在的imageFacet 对于单个点： 若point在超过一个Facet中，取Area最大的Facet 对于所有点：
     * 情况1：所有的点都没有在任何ImageFacet中：将该Image中的Area最大的Facet作为所有点所在的Facet
     * 情况2：有一部分点没有在任何Facet中，将存在于Facet中的其他的点的Facet用于这些点 情况3：所有点都存在于至少一个Facet中，直接返回单点处理后的Map
     */
    private Map<Point, ImageFacet> pointInImageFacetFix(
            Map<Point, Iterable<ImageFacet>> pointFacetsMap, String imageId, String modelId) {
        var notInFacetCount = new AtomicInteger(0);
        var modelFacetMap = getModelFacetMap(modelId);
        var pointFacetMap = new HashMap<Point, ImageFacet>();
        pointFacetsMap.forEach(
                (point, imageFacets) -> {
                    var facets = Iterables.toList(imageFacets);
                    if (facets.isEmpty()) {
                        notInFacetCount.incrementAndGet();
                    } else if (facets.size() == 1) {
                        pointFacetMap.put(point, facets.get(0));
                    } else {
                        ImageFacet maxFacet = facets.get(0);
                        for (int i = 1; i < facets.size(); i++) {
                            if (modelFacetMap.get(facets.get(i).getFacetId()).getArea()
                                    > modelFacetMap.get(maxFacet.getFacetId()).getArea()) {
                                maxFacet = facets.get(i);
                            }
                        }
                        pointFacetMap.put(point, maxFacet);
                    }
                });
        // 所有点都没有在任何一个Facet中
        if (notInFacetCount.get() == pointFacetsMap.keySet().size()) {
            var facets = getImageFacet(modelId, imageId);
            var maxFacet = facets.get(0);
            for (int i = 1; i < facets.size(); i++) {
                if (modelFacetMap.get(facets.get(i).getFacetId()).getArea()
                        > modelFacetMap.get(maxFacet.getFacetId()).getArea()) {
                    maxFacet = facets.get(i);
                }
            }
            var finalFacet = maxFacet;
            pointFacetsMap.keySet().forEach(point -> pointFacetMap.put(point, finalFacet));
        } else if (notInFacetCount.get() > 0) { // 有一部分点没有在任何Facet中
            ImageFacet maxFacet = null;
            var points = pointFacetMap.keySet();
            for (Point point : points) {
                if (maxFacet == null) {
                    maxFacet = pointFacetMap.get(point);
                }
                if (modelFacetMap.get(pointFacetMap.get(point).getFacetId()).getArea()
                        > modelFacetMap.get(maxFacet.getFacetId()).getArea()) {
                    maxFacet = pointFacetMap.get(point);
                }
            }
            var finalFacet = maxFacet;
            pointFacetsMap
                    .keySet()
                    .forEach(
                            point -> {
                                if (!pointFacetMap.containsKey(point)) {
                                    pointFacetMap.put(point, finalFacet);
                                }
                            });
        }
        return pointFacetMap;
    }

    private ImageFacet pointInImageFacetFix(
            Iterable<ImageFacet> pointFacetsMap, String imageId, String modelId) {
        var pointKey = Point.of(0, 0);
        return pointInImageFacetFix(Map.of(pointKey, pointFacetsMap), imageId, modelId)
                .get(pointKey);
    }

    /**
     * 获取点所在的imageFacet(内存计算版） 优点：无需调用数据库 缺点：边缘点易出现误差
     *
     * @return key=point, value=point所在的imageFacets
     */
    private Map<Point, Iterable<ImageFacet>> pointInImageFacetInMemory(
            String modelId, String imageId, Iterable<Point> points) {
        var imageFacets = getImageFacet(modelId, imageId);
        var pointFacetMap = new HashMap<Point, Iterable<ImageFacet>>();
        points.forEach(
                point -> {
                    for (ImageFacet imageFacet : imageFacets) {
                        if (GeometryUtil.pointInWktPolygon(
                                point,
                                GeometryUtil.wktPolygonToPoint(imageFacet.getPath2DSeen()))) {
                            pointFacetMap.put(point, List.of(imageFacet));
                            break;
                        }
                    }
                    if (!pointFacetMap.containsKey(point)) {
                        pointFacetMap.put(point, new ArrayList<>());
                    }
                });
        return pointFacetMap;
    }

    private Iterable<ImageFacet> pointInImageFacetInMemory(
            String modelId, String imageId, Point point) {
        return pointInImageFacetInMemory(modelId, imageId, List.of(point)).get(point);
    }

    private Map<Integer, ModelFacet> getModelFacetMap(String modelId) {
        return dsl.select(MODEL_FACET_SELECT_FIELD)
                .from(MODEL_FACET)
                .where(MODEL_FACET.MODEL_ID.eq(modelId))
                .fetchMap(
                        r -> Integer.parseInt(r.get(MODEL_FACET.FACET_ID)), RecordModelFacet::new);
    }

    private List<ModelImage> getModelImage(String modelId) {
        return dsl.select()
                .from(MODEL_IMAGE)
                .where(MODEL_IMAGE.MODEL_ID.eq(modelId))
                .and(MODEL_IMAGE.IS_DELETE.eq(false))
                .fetch(RecordModelImage::new);
    }

    private String getModelIdByBiImageId(BiImageId biImageId) {
        var modelIdMap =
                getModelId(List.of(biImageId.getOriginImageId(), biImageId.getTargetImageId()));
        var originModelId = modelIdMap.get(biImageId.getOriginImageId());
        var targetModelId = Iterables.toSet(modelIdMap.get(biImageId.getTargetImageId()));
        for (String modelId : originModelId) {
            if (targetModelId.contains(modelId)) {
                return modelId;
            }
        }
        throw new IllegalArgumentException(
                "image: "
                        + biImageId.getOriginImageId()
                        + " and image: "
                        + biImageId.getTargetImageId()
                        + " not in a common model");
    }

    private List<ImageFacet> getImageFacet(String modelId, String imageId) {
        return dsl.select(IMAGE_FACET_SELECT_FIELD)
                .from(IMAGE_FACET)
                .where(IMAGE_FACET.MODEL_ID.eq(modelId))
                .and(IMAGE_FACET.IMAGE_ID.eq(imageId))
                .fetch(RecordImageFacet::new);
    }

    private Map<Integer, double[]> getCoeficientMap(String modelId) {
        var modelFacets = getModelFacet(modelId);
        return modelFacets.stream()
                .collect(
                        Collectors.toMap(
                                ModelFacet::getFacetId,
                                modelFacet ->
                                        gson.fromJson(modelFacet.getPlaneCoef(), double[].class)));
    }

    private List<ModelFacet> getModelFacet(String modelId) {
        return dsl.select(MODEL_FACET_SELECT_FIELD)
                .from(MODEL_FACET)
                .where(MODEL_FACET.MODEL_ID.eq(modelId))
                .fetch(RecordModelFacet::new);
    }

    private Map<String, ModelImage> getModelImages(List<String> imageIds, String modelId) {
        return dsl.select()
                .from(MODEL_IMAGE)
                .where(MODEL_IMAGE.MODEL_ID.eq(modelId))
                .and(MODEL_IMAGE.IMAGE_ID.in(imageIds))
                .fetchMap(MODEL_IMAGE.IMAGE_ID, RecordModelImage::new);
    }

    private Iterable<MappingInfo> facetsMappingTo(
            String imageId,
            List<ImageFacet> imageFacets,
            double[][] camPropMatrixOrigin,
            double[][] camPropMatrixTarget,
            Map<Integer, double[]> planeCoefMap) {
        var mappingInfos = new ArrayList<MappingInfo>();
        for (ImageFacet imageFacet : imageFacets) {
            var mappingPoints = new ArrayList<Point>();
            var points = GeometryUtil.wktPolygonToPoint(imageFacet.getPath2DSeen());
            var planeCoef = planeCoefMap.get(imageFacet.getFacetId());
            for (Point point : points) {
                mappingPoints.add(
                        MappingUtil.twoDPointToAnother(
                                point, camPropMatrixOrigin, camPropMatrixTarget, planeCoef));
            }
            mappingInfos.add(MappingInfo.of(imageId, imageFacet.getFacetId(), mappingPoints));
        }
        return mappingInfos;
    }

    private Iterable<MappingInfo> facetsMappingTo(String imageId, List<ImageFacet> imageFacets) {
        var mappingInfos = new ArrayList<MappingInfo>();
        for (ImageFacet imageFacet : imageFacets) {
            var points = GeometryUtil.wktPolygonToPoint(imageFacet.getPath2DSeen());
            var mappingPoint = new ArrayList<>(Iterables.toCollection(points));
            mappingInfos.add(MappingInfo.of(imageId, imageFacet.getFacetId(), mappingPoint));
        }
        return mappingInfos;
    }

    private Iterable<Component> getComponentFromRecord(Iterable<Record> records) {
        var modelFacetMap = new HashMap<String, List<Facet>>();
        var highRoofMap = new HashMap<String, Boolean>();
        records.forEach(
                record -> {
                    Facet facet = new RecordFacet(record);
                    String componentId = record.get(MODEL_FACET.COMPONENT_ID);
                    if (!modelFacetMap.containsKey(componentId)) {
                        modelFacetMap.put(componentId, new ArrayList<>());
                    }
                    modelFacetMap.get(componentId).add(facet);
                    highRoofMap.put(componentId, record.get(MODEL_FACET.IS_HIGH_ROOF));
                });
        return modelFacetMap.keySet().stream()
                .map(
                        componentId ->
                                new RecordComponent(
                                        componentId,
                                        modelFacetMap.get(componentId),
                                        highRoofMap.get(componentId)))
                .collect(Collectors.toList());
    }

    private Iterable<ThreeDModel> getThreeDModelFromRecord(
            Iterable<Record> modelRecords, Iterable<Record> modelFacetRecords) {
        var modelRecordMap = new HashMap<String, List<Record>>();
        var northMap = new HashMap<String, String>();
        var modelNameMap = new HashMap<String, String>();
        var rotationDegreeMap = new HashMap<String, Float>();

        modelRecords.forEach(
                record -> {
                    var modelId = record.get(MODEL.ID);
                    if (!modelRecordMap.containsKey(modelId)) {
                        modelRecordMap.put(modelId, new ArrayList<>());
                    }
                    modelFacetRecords.forEach(
                            r -> {
                                if (r.get(MODEL_FACET.MODEL_ID).equals(modelId)) {
                                    modelRecordMap.get(modelId).add(r);
                                }
                            });
                    northMap.put(modelId, record.get(MODEL.NORTH));
                    modelNameMap.put(modelId, record.get(MODEL.MODEL_NAME));
                    Float rotationDegree = record.get(MODEL.ROTATION_DEGREE);
                    rotationDegreeMap.put(modelId, rotationDegree == null ? 0.0f : rotationDegree);
                });
        return modelRecordMap.keySet().stream()
                .map(
                        modelId ->
                                new RecordThreeDModel(
                                        modelId,
                                        northMap.get(modelId),
                                        rotationDegreeMap.get(modelId),
                                        getComponentFromRecord(modelRecordMap.get(modelId)),
                                        modelNameMap.get(modelId)))
                .collect(Collectors.toList());
    }
}
