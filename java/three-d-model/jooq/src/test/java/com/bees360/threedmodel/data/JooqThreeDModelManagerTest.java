package com.bees360.threedmodel.data;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.threedmodel.ThreeDModelManager;
import com.bees360.threedmodel.config.JooqThreeDModelManagerConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;

@SpringBootTest
@ApplicationAutoConfig
public class JooqThreeDModelManagerTest extends BaseThreeDModelTest {

    @Import({
        JooqThreeDModelDataManagerConfig.class,
        JooqThreeDModelManagerConfig.class,
    })
    @Configuration
    static class Config {}

    public JooqThreeDModelManagerTest(
            @Autowired ThreeDModelDataManager repository,
            @Autowired ThreeDModelManager threeDModelManager)
            throws IOException {
        super(repository, threeDModelManager);
        threeDDataTest();
    }

    @Test
    @Override
    public void testBatchSetImageInModel() {
        super.testBatchSetImageInModel();
    }

    @Test
    @Override
    public void testSetImageInModel() {
        super.testSetImageInModel();
    }

    @Test
    @Override
    public void testSetLineType() {
        super.testSetLineType();
    }

    @Test
    @Override
    public void testSetRotationDegree() {
        super.testSetRotationDegree();
    }

    @Test
    @Override
    public void testGetModelIdByImageIds() {
        super.testGetModelIdByImageIds();
    }

    @Test
    @Override
    public void testGetModelByGroupKey() {
        super.testGetModelByGroupKey();
    }

    @Test
    @Override
    public void testGetImageMapping() {
        super.testGetImageMapping();
    }

    @Test
    @Override
    public void testGetPointsMapping() {
        super.testGetPointsMapping();
    }

    @Test
    @Override
    public void testGetPointOrientation() {
        super.testGetPointOrientation();
    }
}
