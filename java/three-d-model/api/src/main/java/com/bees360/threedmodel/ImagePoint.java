package com.bees360.threedmodel;

import com.bees360.api.common.Point;

/** 作为获取图片中点所在方向的参数 */
public interface ImagePoint {

    // 点的坐标
    Point getPoint();

    // 点所在的ImageId
    String getImageId();

    static ImagePoint of(String imageId, Point point) {
        return new ImagePoint() {
            @Override
            public Point getPoint() {
                return point;
            }

            @Override
            public String getImageId() {
                return imageId;
            }
        };
    }
}
