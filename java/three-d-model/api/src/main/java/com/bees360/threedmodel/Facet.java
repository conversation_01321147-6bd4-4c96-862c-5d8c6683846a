package com.bees360.threedmodel;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.Entity;
import com.bees360.api.Proto;
import com.bees360.threedmodel.Message.ThreeDModelMessage;
import com.bees360.util.Iterables;

import java.util.stream.Collectors;

import javax.annotation.Nonnull;

public interface Facet extends Entity, Proto<ThreeDModelMessage.Component.Facet> {
    String NAMESPACE = "threedmodel/component/facet";

    @Override
    default String getNamespace() {
        return NAMESPACE;
    }

    @Nonnull
    Iterable<? extends Line> getPath();

    double getArea();

    double getPitch();

    @Nonnull
    String getPlaneProp();

    ThreeDModelMessage.Component.Facet.Orientation getOrientation();

    @Nonnull
    String getName();

    @Override
    default ThreeDModelMessage.Component.Facet toMessage() {
        var builder = ThreeDModelMessage.Component.Facet.newBuilder();
        acceptIfNotNull(builder::setId, getId(), Integer::valueOf);
        builder.addAllPath(
                Iterables.toStream(getPath()).map(Line::toMessage).collect(Collectors.toList()));
        acceptIfNotNull(builder::setArea, getArea());
        acceptIfNotNull(builder::setPitch, getPitch());
        acceptIfNotNull(builder::setPlaneProp, getPlaneProp());
        acceptIfNotNull(builder::setOrientation, getOrientation());
        acceptIfNotNull(builder::setName, getName());
        return builder.build();
    }
}
