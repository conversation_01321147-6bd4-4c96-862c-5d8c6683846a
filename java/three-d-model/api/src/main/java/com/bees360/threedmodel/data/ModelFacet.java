package com.bees360.threedmodel.data;

/** facet in model */
interface ModelFacet {
    /**
     * get model id
     *
     * @return {@link String}
     */
    String getModelId();

    /**
     * get component id
     *
     * @return {@link int}
     */
    int getComponentId();

    /**
     * get facet id
     *
     * @return {@link int}
     */
    int getFacetId();

    /**
     * get area
     *
     * @return {@link double}
     */
    double getArea();

    /**
     * get name
     *
     * @return {@link String}
     */
    String getName();

    /**
     * get pitch
     *
     * @return {@link double}
     */
    double getPitch();

    /**
     * @return {@link String} WktPolyHedRalSurface: PolyHedRalSurface(((x1 y1 z1,x2 y2 z2,...,xn-1
     *     yn-1 zn-1,x1 y1 z1)))
     */
    String getPath();

    /**
     * get path type
     *
     * @return {@link String} 一维json数组
     */
    String getPathType();

    /**
     * get shared facet
     *
     * @return {@link String} 一维json数组
     */
    String getSharedFacet();

    /**
     * get plane prop
     *
     * @return {@link String} 二维json数组 3x3
     */
    String getPlaneProp();

    /**
     * get plane coef
     *
     * @return {@link String} 一维json数组
     */
    String getPlaneCoef();

    /**
     * is high roof
     *
     * @return {@link boolean}
     */
    boolean isHighRoof();

    /**
     * get orientation
     *
     * @return {@link int}
     */
    int getOrientation();

    static ModelFacet of(
            String modelId,
            int componentId,
            int facetId,
            double area,
            String name,
            double pitch,
            String path,
            String pathType,
            String sharedFacet,
            String planeProp,
            String planeCoef,
            boolean highRoof,
            int orientation) {
        return new ModelFacet() {
            @Override
            public String getModelId() {
                return modelId;
            }

            @Override
            public int getComponentId() {
                return componentId;
            }

            @Override
            public int getFacetId() {
                return facetId;
            }

            @Override
            public double getArea() {
                return area;
            }

            @Override
            public String getName() {
                return name;
            }

            @Override
            public double getPitch() {
                return pitch;
            }

            @Override
            public String getPath() {
                return path;
            }

            @Override
            public String getPathType() {
                return pathType;
            }

            @Override
            public String getSharedFacet() {
                return sharedFacet;
            }

            @Override
            public String getPlaneProp() {
                return planeProp;
            }

            @Override
            public String getPlaneCoef() {
                return planeCoef;
            }

            @Override
            public boolean isHighRoof() {
                return highRoof;
            }

            @Override
            public int getOrientation() {
                return orientation;
            }
        };
    }
}
