package com.bees360.threedmodel;

import com.bees360.api.common.ThreeDPoint;

import jakarta.annotation.Nonnull;

public class ProtobufLine implements Line {

    private final Message.ThreeDModelMessage.Component.Facet.Line message;

    public ProtobufLine(Message.ThreeDModelMessage.Component.Facet.Line message) {
        this.message = message;
    }

    @Nonnull
    @Override
    public String getId() {
        return String.valueOf(message.getId());
    }

    @Nonnull
    @Override
    public Message.ThreeDModelMessage.Component.Facet.Line.Type getType() {
        return message.getType();
    }

    @Nonnull
    @Override
    public ThreeDPoint getStart() {
        var start = message.getStart();
        return ThreeDPoint.of(start.getX(), start.getY(), start.getZ());
    }

    @Nonnull
    @Override
    public ThreeDPoint getEnd() {
        var end = message.getEnd();
        return ThreeDPoint.of(end.getX(), end.getY(), end.getZ());
    }
}
