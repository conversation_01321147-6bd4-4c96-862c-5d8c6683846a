package com.bees360.threedmodel;

import com.bees360.util.Iterables;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

public class ProtobufFacet implements Facet {

    private final Message.ThreeDModelMessage.Component.Facet message;

    public ProtobufFacet(Message.ThreeDModelMessage.Component.Facet message) {
        this.message = message;
    }

    @Nonnull
    @Override
    public String getId() {
        return String.valueOf(message.getId());
    }

    @Nonnull
    @Override
    public Iterable<? extends Line> getPath() {
        return Iterables.transform(
                Iterables.from(message.getPathList().iterator()), ProtobufLine::new);
    }

    @Override
    public double getArea() {
        return message.getArea();
    }

    @Override
    public double getPitch() {
        return message.getPitch();
    }

    @Nonnull
    @Override
    public String getPlaneProp() {
        return message.getPlaneProp();
    }

    @Nullable
    @Override
    public Message.ThreeDModelMessage.Component.Facet.Orientation getOrientation() {
        return message.getOrientation();
    }

    @Nonnull
    @Override
    public String getName() {
        return message.getName();
    }
}
