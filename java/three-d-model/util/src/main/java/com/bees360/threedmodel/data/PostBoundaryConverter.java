package com.bees360.threedmodel.data;

import com.bees360.threedmodel.Message.ThreeDModelMessage.Component.Facet.Orientation;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

class PostBoundaryConverter {

    public static final int FACET_ID_START = 0;

    private static final Gson gson = new Gson();

    /**
     * 提取postBoundary中的ImageFacet
     *
     * @param postBoundary json
     * @param modelId 所属modelId
     * @return imageFacetList
     */
    public static List<ImageFacet> extractImageFacet(PostBoundary postBoundary, String modelId) {
        List<ImageFacet> facets = new ArrayList<>();
        for (PostBoundary.Image image : postBoundary.getImages()) {
            String imageId = image.getImageId();
            for (PostBoundary.FacetInImage facet : image.getFacets()) {
                facets.add(
                        ImageFacet.of(
                                modelId,
                                imageId,
                                facet.getFacetId(),
                                GeometryUtil.arrayToWktPolygon(facet.getPath2dFull()),
                                GeometryUtil.arrayToWktPolygon(facet.getPath2dSeen()),
                                GeometryUtil.arrayToWktPolygon(facet.getPath2dCrspOverview()),
                                facet.getCenterPoint()[0],
                                facet.getCenterPoint()[1]));
            }
        }
        return facets;
    }

    /**
     * 提取postBoundary中的ModelFacet
     *
     * @param postBoundary json
     * @param modelId 所属modelId
     * @return modelFacetList
     */
    public static List<ModelFacet> extractModelFacet(PostBoundary postBoundary, String modelId) {

        var referenceFacet = postBoundary.getMarkedFacet();
        int referFacetId = referenceFacet.getFacetId();
        int orientation = referenceFacet.getOrientation();
        Orientation referenceOrientation = Orientation.forNumber(orientation);
        double[][] referPlaneProp = null;

        var facetsWithoutOri = new HashMap<Integer, ArrayList<PostBoundary.RoofFacet>>();
        var modelFacets = new ArrayList<ModelFacet>();
        for (PostBoundary.Component component : postBoundary.getComponents()) {
            int componentId = component.getId();
            for (PostBoundary.RoofFacet facet : component.getFacets()) {
                if (facet.getFacetId() == referFacetId) {
                    referPlaneProp = facet.getPlaneProp();
                    modelFacets.add(
                            convertFacetToModelFacet(
                                    modelId, componentId, facet, referenceOrientation.getNumber()));
                    continue;
                }
                if (referPlaneProp == null) {
                    facetsWithoutOri.computeIfAbsent(componentId, (key) -> new ArrayList<>());
                    facetsWithoutOri.get(componentId).add(facet);
                } else {
                    int ori =
                            calculateOrientationForFacets(
                                    referPlaneProp, referenceOrientation, facet.getPlaneProp());
                    modelFacets.add(convertFacetToModelFacet(modelId, componentId, facet, ori));
                }
            }
        }

        double[][] planProp = referPlaneProp;
        facetsWithoutOri.forEach(
                (componentId, facets) -> {
                    for (PostBoundary.RoofFacet facet : facets) {
                        int ori =
                                calculateOrientationForFacets(
                                        planProp, referenceOrientation, facet.getPlaneProp());
                        modelFacets.add(convertFacetToModelFacet(modelId, componentId, facet, ori));
                    }
                });

        return modelFacets;
    }

    private static ModelFacet convertFacetToModelFacet(
            String modelId, int componentId, PostBoundary.RoofFacet facet, int orientation) {
        return ModelFacet.of(
                modelId,
                componentId,
                facet.getFacetId(),
                facet.getArea(),
                createFacetName(facet.getFacetId()),
                facet.getPitch(),
                GeometryUtil.arrayToWktPolyHedRalSurface(facet.getBoundary()),
                objectToJson(facet.getPathType()),
                objectToJson(facet.getSharedFacet()),
                objectToJson(facet.getPlaneProp()),
                objectToJson(facet.getPlaneCoef()),
                facet.getIsHighRoof(),
                orientation);
    }

    /**
     * get ModelImage with camPropertyMatrix from postBoundaryJson
     *
     * @param modelId modelId
     * @param json postBoundaryJson
     * @return modelImageList
     */
    public static List<ModelImage> extractModelImage(String modelId, PostBoundary json) {
        List<PostBoundary.Image> list = Arrays.asList(json.getImages());
        return list.stream()
                .map(
                        image ->
                                ModelImage.of(
                                        modelId,
                                        image.getImageId(),
                                        objectToJson(image.getCamPropertyMatrix())))
                .collect(Collectors.toList());
    }

    private static int calculateOrientationForFacets(
            double[][] referPlaneProp, Orientation referOrientation, double[][] planeProp) {
        var frontFacetNormalVector = calculateFrontVector(referPlaneProp, referOrientation);
        return calculateOrientation(frontFacetNormalVector, planeProp[2]).getNumber();
    }

    private static Orientation calculateOrientation(
            double[] frontFacetNormalVector, double[] vector) {
        double angle = calculateClockwiseRotationAngle(frontFacetNormalVector, vector);
        return judgeAngleOrientation(angle);
    }

    private static Orientation judgeAngleOrientation(double angle) {
        // make value of angle between 0 and 360, to make sure the function return an orientation.
        angle = (angle % 360 + 360) % 360;
        if (angle < 0) {
            return null;
        } else if (angle < 45) {
            return Orientation.FRONT;
        } else if (angle < 135) {
            return Orientation.RIGHT;
        } else if (angle < 225) {
            return Orientation.BACK;
        } else if (angle < 315) {
            return Orientation.LEFT;
        } else if (angle <= 360) {
            return Orientation.FRONT;
        } else {
            return null;
        }
    }

    /** Calculate the clockwise rotation angle between two vectors. */
    private static double calculateClockwiseRotationAngle(double[] from, double[] to) {
        double epsilon = 1.0e-6;
        double nyPI = Math.acos(-1.0);
        double dot, degree, angle;
        // transform to unit vector
        from = transformToUnitVector(from[0], from[1]);
        to = transformToUnitVector(to[0], to[1]);
        // dot product
        dot = from[0] * to[0] + from[1] * to[1];
        if (Math.abs(dot - 1.0) <= epsilon) {
            angle = 0.0;
        } else if (Math.abs(dot + 1.0) <= epsilon) {
            angle = nyPI;
        } else {
            // cross product
            double cross = from[0] * to[1] - to[0] * from[1];
            angle = Math.acos(dot);

            // vector 'to' is clockwise from vector 'from' with respect to the origin (0.0)
            if (cross < 0) {
                angle = 2 * nyPI - angle;
            }
        }
        degree = angle * 180.0 / nyPI;
        return degree;
    }

    private static double[] transformToUnitVector(double x, double y) {
        double length = Math.sqrt(x * x + y * y);
        x /= length;
        y /= length;
        return new double[] {x, y};
    }

    /**
     * F: {1, 1}, B: {-1, -1}, L: {1, -1}, R: {-1, 1} B L + R F
     *
     * @param orientation the orientation of the specified facet
     * @param facetId the id of the specified facet
     * @param facets all facets of the project including the specified facet
     * @return
     */
    public static double[] calculateFrontVector(double[][] planeProp, Orientation orientation) {
        var normalVectorX = planeProp[2][0];
        var normalVectorY = planeProp[2][1];
        var frontVector = new double[2];
        switch (orientation) {
            case FRONT:
                {
                    frontVector[0] = normalVectorX;
                    frontVector[1] = normalVectorY;
                    break;
                }
            case BACK:
                {
                    frontVector[0] = -normalVectorX;
                    frontVector[1] = -normalVectorY;
                    break;
                }
            case LEFT:
                {
                    frontVector[0] = -normalVectorY;
                    frontVector[1] = normalVectorX;
                    break;
                }
            case RIGHT:
                {
                    frontVector[0] = normalVectorY;
                    frontVector[1] = -normalVectorX;
                    break;
                }
            default:
                {
                    throw new IllegalArgumentException("unsupported orientation type");
                }
        }
        return frontVector;
    }

    private static String createFacetName(int facetId) {
        // make 0 to be A.
        facetId = facetId - FACET_ID_START;
        int offset = facetId % 26;
        String name = (char) ('A' + offset) + "";
        facetId = facetId / 26;

        while (facetId > 0) {
            offset = facetId % 26;
            name = (char) ('A' + offset - 1) + name;
            facetId = facetId / 26;
        }
        return name;
    }

    public static <T> String objectToJson(T obj) {
        return gson.toJson(obj);
    }

    public static <T> T jsonToObject(String str, Class<T> classOfT) {
        return gson.fromJson(str, classOfT);
    }
}
