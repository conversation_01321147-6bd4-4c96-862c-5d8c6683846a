package com.bees360.irs.util;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;

@Log4j2
public class CalendarAdapter extends XmlAdapter<String, LocalDateTime> {

    private static final DateTimeFormatter FORMATTER_FOR_PARSING =
            new DateTimeFormatterBuilder()
                    .appendOptional(DateTimeFormatter.ISO_DATE_TIME)
                    .appendOptional(DateTimeFormatter.ISO_LOCAL_DATE)
                    .toFormatter();

    private static final DateTimeFormatter FORMATTER_FOR_FORMATTING =
            DateTimeFormatter.ISO_DATE_TIME;

    @Override
    public LocalDateTime unmarshal(String value) throws Exception {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        try {
            return LocalDateTime.parse(value, FORMATTER_FOR_PARSING);
        } catch (Exception e) {
            log.warn("Fail to parse LocalDateTime from {}", value, e);
        }
        try {
            return LocalDate.parse(value, FORMATTER_FOR_PARSING).atStartOfDay();
        } catch (Exception e) {
            log.warn("Fail to parse LocalDate from {}", value, e);
            throw e;
        }
    }

    @Override
    public String marshal(LocalDateTime dateTime) throws Exception {
        if (dateTime == null) {
            return "";
        }
        try {
            return dateTime.format(FORMATTER_FOR_FORMATTING);
        } catch (Exception e) {
            log.warn("Fail to format LocalDateTime {}", dateTime, e);
            throw e;
        }
    }
}
