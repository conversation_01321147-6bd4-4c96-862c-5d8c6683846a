package com.bees360.delivery;

import com.bees360.jooq.config.JooqConfig;

import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    JooqConfig.class,
})
@Configuration
public class JooqProjectDeliveryConfig {

    @Bean
    JooqStatefulProjectDelivery jooqProjectDelivery(
            ProjectDelivery executionProjectDelivery,
            DSLContext dsl,
            @Value("${app.delivery.jooq.fail-on-task-error:true}") Boolean failOnTaskError) {
        return new JooqStatefulProjectDelivery(executionProjectDelivery, dsl, failOnTaskError);
    }
}
