package com.bees360.delivery;

import static com.bees360.jooq.persistent.delivery.Tables.PROJECT_DELIVERY_TASK;

import com.bees360.jooq.persistent.delivery.enums.ProjectDeliveryTaskStatus;

import lombok.extern.log4j.Log4j2;

import org.jooq.DSLContext;

import java.util.Optional;

@Log4j2
class JooqProjectDeliveryTaskRepository {

    private final DSLContext dsl;

    public JooqProjectDeliveryTaskRepository(DSLContext dsl) {
        this.dsl = dsl;
        log.info("Created {}(dsl={})", this, dsl);
    }

    public JooqProjectDeliveryTask findExistedOrCreate(
            String projectId, String deliveryType, long version) {
        return dsl.insertInto(PROJECT_DELIVERY_TASK)
                .columns(
                        PROJECT_DELIVERY_TASK.PROJECT_ID,
                        PROJECT_DELIVERY_TASK.DELIVERY_TYPE,
                        PROJECT_DELIVERY_TASK.VERSION,
                        PROJECT_DELIVERY_TASK.STATUS)
                .values(projectId, deliveryType, version, ProjectDeliveryTaskStatus.NEW)
                .onConflict(
                        PROJECT_DELIVERY_TASK.PROJECT_ID,
                        PROJECT_DELIVERY_TASK.DELIVERY_TYPE,
                        PROJECT_DELIVERY_TASK.VERSION)
                .doUpdate()
                .set(PROJECT_DELIVERY_TASK.DELIVERY_TYPE, deliveryType)
                .returning(PROJECT_DELIVERY_TASK.asterisk())
                .fetchOne(JooqProjectDeliveryTask::new);
    }

    public void updateStatus(String deliveryId, ProjectDeliveryTaskStatus status, String errorLog) {
        errorLog = Optional.ofNullable(errorLog).orElse("");
        dsl.update(PROJECT_DELIVERY_TASK)
                .set(PROJECT_DELIVERY_TASK.STATUS, status)
                .set(PROJECT_DELIVERY_TASK.ERROR_LOG, errorLog)
                .where(PROJECT_DELIVERY_TASK.ID.eq(deliveryId))
                .execute();
    }
}
