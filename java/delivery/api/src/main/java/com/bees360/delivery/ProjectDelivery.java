package com.bees360.delivery;

import com.google.common.util.concurrent.ListenableFuture;

public interface ProjectDelivery {

    String DEFAULT_DELIVERY_TYPE = "deliver-project";

    /** 使用 {@code DEFAULT_DELIVERY_KEY} 作为 delivery type 执行任务。 */
    default ListenableFuture<Void> deliver(String projectId, long version) {
        return deliver(projectId, DEFAULT_DELIVERY_TYPE, version);
    }

    /**
     * 相同的 (projectId, deliveryType, version) 会对应相同的 delivery 任务。如果对应的任务已经存在，会直接返回执行结果，否则会提交一个新的任务。
     *
     * @param projectId
     * @param deliveryType 具体支持的key需要实现支持，不同的 project 可能会支持不同的 delivery type，用以区别不同的 Delivery 任务。
     * @param version delivery 的版本
     * @return
     */
    ListenableFuture<Void> deliver(String projectId, String deliveryType, long version);
}
