secretsmanager:
  enabled: false
logging:
  level:
    com.bees360.delivery.DeliverableResourceArchiver: DEBUG
grpc:
  client:
    userProvider:
      address: static://bees360-bifrost-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
app:
  delivery:
    automation:
      pipeline:
        enabled: true
        triggered-by-project-status:
          returned_to_client:
            enabled: true
            task-keys:
              - upload_report_to_towerhill
            from-statuses: PENDING
            to-status: READY
    features:
      summary-resource-provider:
        type: openapi
    pipeline-task:
      task-key-to-deliver-type:
        deliver_project: PROJECT
        upload_images_to_lc360: IMAGE
        deliver_project_to_amwins: PROJECT
        upload_report_to_towerhill: REPORT_SUMMARY
    job:
      retry:
        retry-count: 0
    customer:
      towerhill:
        enable: true
        customer-key: "Tower Hill Insurance Group"
        resource-transfer:
          job-name: resource_transfer_towerhill
          target: sftp://towerhill/upload/
          retry:
            retry-count: 5
            retry-delay: PT2M
        resource-deletion-detect:
          max-concurrency: 5
          detect-delay: PT5M
          retry:
            retry-count: 12
            retry-delay: PT10M
            retry-delay-increase-factor: 2.0
        deliverable:
          report-types:
            - 17 # Roof-only Underwriting Report
            - 42 # Full-scope Underwriting Report with Recovery Cost Estimate
            - 18 # Full-scope Underwriting Report
            - 19 # Inspection Closeout Report

sftp:
  clients:
    - endpoint: sftp://towerhill
      host: sftp.thig.com
      username: bees360
      password: ENC(lHQO+roXRFfaWkt+dl7Ic0Xt/yz2IHwa)
      session-timeout: PT30m
      connect-timeout: PT15m

resource:
  sftp:
    - client: towerhill
      base-folder: upload

rabbit:
  client:
    host: sandbox.mq.bees360.com
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: combee
    password: ENC(SBAlqbh3dW7cZyZvIqlrupmneJHDpNLxsHKTdiEpCd8=)

http:
  client:
    apache:
      maxConnPerRoute: 16
      maxConnTotal: 64
      evictIdleConnectionsAfter: PT10M
      connectionTimeToLive: PT10M
      evictExpiredConnections: true
      redirectStrategy: always
      request:
        connectTimeout: PT15S
        connectionRequestTimeout: PT30S
        socketTimeout: PT180S
        redirectEnabled: true
        expectContinueEnabled: true
        normalizeUri: true
  aws-lambda:
    enabled: true
    endpoint: https://7qkzkgfabm3wnwswmu37nxrsjy0juklc.lambda-url.us-east-2.on.aws/
    client-secret: ENC(p8RDI+9nK1Q+qny6PE34m4ax0bamcWC6JtWclY+NUYzHDjdljnKsFA==)
    customer-template-key:
      "[Tower Hill Insurance Group]": custom-thl
