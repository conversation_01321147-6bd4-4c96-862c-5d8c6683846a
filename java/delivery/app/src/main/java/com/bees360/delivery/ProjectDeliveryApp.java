package com.bees360.delivery;

import static com.bees360.resource.ResourcePools.buildPrefixCompositeResourcePool;

import com.bees360.apolloconfig.config.ApolloClientConfig;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.contract.config.GrpcContractManagerConfig;
import com.bees360.customer.Customer;
import com.bees360.customer.CustomerProvider;
import com.bees360.customer.config.GrpcCustomerClientConfig;
import com.bees360.delivery.config.AutomatationPipelineConfig;
import com.bees360.delivery.config.ProjectDeliveryPipelineTaskJobConfig;
import com.bees360.delivery.config.ProjectDeliveryProviderConfig;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.grpc.config.GrpcClientConfig;
import com.bees360.http.HttpClient;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.image.config.GrpcImageClientConfig;
import com.bees360.image.config.GrpcImageGroupClientConfig;
import com.bees360.image.config.GrpcImageTagClientConfig;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.pipeline.config.GrpcPipelineClientConfig;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.config.GrpcExternalIntegrationManagerConfig;
import com.bees360.project.config.GrpcProjectClientConfig;
import com.bees360.project.config.GrpcProjectContactClientConfig;
import com.bees360.project.config.GrpcProjectIIMangerConfig;
import com.bees360.project.image.DefaultProjectImageProvider;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.project.report.UniqueProjectReportProvider;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.report.ReportGroupManager;
import com.bees360.report.config.GrpcReportGroupManagerConfig;
import com.bees360.report.config.GrpcReportManagerClientConfig;
import com.bees360.resource.HttpSafeResourceClient;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.config.GrpcResourceClientConfig;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.user.config.GrpcUserProviderConfig;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.net.URI;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.function.UnaryOperator;

@Log4j2
@Import({
    JooqConfig.class,
    RabbitApiConfig.class,
    RabbitJobDispatcher.class,
    RabbitJobScheduler.class,
    RabbitEventDispatcher.class,
    RabbitEventPublisher.class,
    GrpcPipelineClientConfig.class,
    GrpcImageClientConfig.class,
    GrpcImageTagClientConfig.class,
    GrpcReportGroupManagerConfig.class,
    GrpcReportManagerClientConfig.class,
    GrpcExternalIntegrationManagerConfig.class,
    GrpcProjectIIMangerConfig.class,
    GrpcContractManagerConfig.class,
    GrpcImageGroupClientConfig.class,
    ProjectDeliveryApp.Config.class,
    GrpcPipelineClientConfig.class,
//    GrpcResourceClientConfig.class,
    GrpcCustomerClientConfig.class,
    GrpcProjectClientConfig.class,
    GrpcProjectContactClientConfig.class,
    GrpcUserProviderConfig.class,
    ApacheHttpClientConfig.class,
    DefaultProjectImageProvider.class,
    GrpcClientConfig.class,
    AutomatationPipelineConfig.class,

    // apollo
    ApolloClientConfig.class,
})
@EnableEncryptableProperties
@EnableConfigurationProperties
@ApplicationAutoConfig
public class ProjectDeliveryApp {

    public static void main(String[] args) {
        ExitableSpringApplication.run(ProjectDeliveryApp.class, args);
    }

    @Import({
        AutoRegisterJobExecutorConfig.class,
        JooqProjectDeliveryConfig.class,
        ProjectDeliveryProviderConfig.class,
        ProjectDeliveryPipelineTaskJobConfig.class,
    })
    @Configuration
    static class Config {

        /** 使用 Jooq 进行封装，增加执行状态的记录。 */
        @Bean({"deliverProjectJobExecutorProjectDelivery"})
        ProjectDelivery projectDelivery(JooqStatefulProjectDelivery jooqStatefulProjectDelivery) {
            return jooqStatefulProjectDelivery;
        }

        /** 负责完成实际的上传工作 */
        @Bean
        ProjectDelivery executionProjectDelivery(
                @Qualifier("projectDeliveryDispatcher")
                        Function<String, ProjectDelivery> projectDeliveryDispatcher) {
            var delivery =
                    new ProjectDelivery() {
                        @Override
                        public ListenableFuture<Void> deliver(
                                String projectId, String deliveryType, long version) {
                            var delivery = projectDeliveryDispatcher.apply(projectId);
                            if (delivery == null) {
                                return Futures.immediateFailedFuture(
                                        new IllegalArgumentException(
                                                String.format(
                                                        "No ProjectDelivery found for project %s.",
                                                        projectId)));
                            }
                            return delivery.deliver(projectId, deliveryType, version);
                        }
                    };
            log.info(
                    "Created {}(projectDeliveryDispatcher={})",
                    delivery,
                    projectDeliveryDispatcher);
            return delivery;
        }

        @Bean
        BinaryOperator<String> projectIntegrationIdProvider(
                ExternalIntegrationProvider externalIntegrationProvider) {

            BinaryOperator<String> provider =
                    (projectId, integrationType) -> {
                        // TODO 目前所有的integration都是创建项目和接收数据的集成，需要增加字段以区分是否为交付目标的集成
                        var integrations =
                                Iterables.filter(
                                        externalIntegrationProvider.findAllByProjectId(projectId),
                                        i ->
                                                StringUtils.equals(
                                                        integrationType, i.getIntegrationType()));
                        var list = Iterables.toList(integrations);
                        if (list.isEmpty()) {
                            return null;
                        }
                        if (list.size() == 1) {
                            return list.get(0).getReferenceNumber();
                        }
                        var integrationTypes =
                                Iterables.transform(list, ExternalIntegration::getIntegrationType);
                        var message =
                                String.format(
                                        "There are multiple integrations for the project %s: %s.",
                                        projectId, integrationTypes);
                        throw new IllegalStateException(message);
                    };

            log.info(
                    "Created {}(externalIntegrationProvider={})",
                    provider,
                    externalIntegrationProvider);
            return provider;
        }

        @Bean
        UnaryOperator<String> customerKeyToCustomerIdConverter(CustomerProvider customerProvider) {
            return key ->
                    Optional.ofNullable(customerProvider.findByKey(key))
                            .map(Customer::getId)
                            .orElse(null);
        }

        @Bean
        public ResourcePool resourcePool(
                HttpClient apacheHttpClient) {
            Map<String, ResourcePool> map = new LinkedHashMap<>();
            map.put("http:", new HttpSafeResourceClient(URI.create("http:/"), apacheHttpClient));
            map.put("https:", new HttpSafeResourceClient(URI.create("https:/"), apacheHttpClient));
            return buildPrefixCompositeResourcePool(map.entrySet());
        }

        @Bean
        public ProjectReportProvider projectReportProvider(ReportGroupManager reportGroupManager) {
            return new UniqueProjectReportProvider(
                    new DefaultProjectReportManager(reportGroupManager));
        }

        @Bean
        Function<String, Resource> resourceProvider(ResourcePool resourcePool) {
            // 此处仍需要为正式使用而斟酌
            return resourcePool::get;
        }

        @Bean
        Supplier<String> robotUserIdSupplier(
                UserProvider userProvider,
                @Value("${app.delivery.user.robot-email}") String robotUserEmail) {

            Supplier<String> robotUserIdSupplier =
                    () -> {
                        var robotUser =
                                Iterables.toList(userProvider.findUserByEmail(robotUserEmail))
                                        .stream()
                                        .min(Comparator.comparing(User::getId))
                                        .orElse(null);
                        if (robotUser == null) {
                            var message =
                                    String.format(
                                            "Robot User with email %s not found.", robotUserEmail);
                            throw new IllegalStateException(message);
                        }
                        return robotUser.getId();
                    };
            log.info(
                    "Created {}(userProvider={},robotUserEmail={})",
                    robotUserIdSupplier,
                    userProvider,
                    robotUserEmail);
            return robotUserIdSupplier;
        }
    }
}
