package com.bees360.delivery.external;

import com.bees360.map.BasicMap;
import com.bees360.map.util.InMemoryMap;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SpringBootTest
public class BasicMapExternalManagerTest extends AbstractExternalManagerTest {
    @Configuration
    static class Config {
        @Bean
        BasicMapExternalManager basicMapExternalManager(BasicMap externalManager) {
            return new BasicMapExternalManager(externalManager);
        }

        @Bean
        BasicMap externalManager() {
            return new InMemoryMap();
        }
    }

    public BasicMapExternalManagerTest(@Autowired BasicMapExternalManager basicMapExternalManager) {
        super(basicMapExternalManager);
    }

    @Test
    @Override
    protected void testFindExternalId() {
        super.testFindExternalId();
    }

    @Test
    @Override
    protected void testFindExternalGroupId() {
        super.testFindExternalGroupId();
    }

    @Test
    @Override
    protected void testSave() {
        super.testSave();
    }
}
