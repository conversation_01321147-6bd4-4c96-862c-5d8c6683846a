package com.bees360.delivery;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.delivery.config.ProjectDeliveryProviderConfig;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.resource.ResourcePool;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.RandomStringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.concurrent.SettableListenableFuture;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.BinaryOperator;
import java.util.function.Function;

@Log4j2
@SpringBootTest(
        classes = {
            RctDeliveryTest.Config.class,
        },
        properties = {
            "GRPC_SERVICE_NAME=127.0.0.1",
            "GRPC_SERVER_PORT=6983",
            "ENABLE_AMWINS_DELIVERY=false",
            "ENABLE_LC360_DELIVERY=false",
            "ENABLE_RCT_DELIVERY=true"
        })
@ActiveProfiles({"test", "override"})
@ApplicationAutoConfig
class RctDeliveryTest {

    @Import({
        JooqConfig.class,
        ProjectDeliveryProviderConfig.class,
        MockDeliverableResourceProviderConfig.class,
    })
    @Configuration
    static class Config {

        @Bean
        Function<String, Iterable<? extends DeliverableResource>> rctDeliverableReportProvider() {
            return mock(Function.class);
        }

        @Bean
        Function<String, Iterable<? extends DeliverableResource>> rctDeliverableImageProvider() {
            return mock(Function.class);
        }

        @Bean
        ResourcePool resourcePool() {
            return mock(ResourcePool.class);
        }

        @Bean("projectIntegrationIdProvider")
        BinaryOperator<String> projectIntegrationIdProvider() {
            return mock(BinaryOperator.class);
        }

        @Bean("rctImageResourceDelivery")
        ResourceDelivery rctImageResourceDelivery() {
            return mock(ResourceDelivery.class);
        }

        @Bean("rctReportResourceDelivery")
        ResourceDelivery rctReportResourceDelivery() {
            return mock(ResourceDelivery.class);
        }
    }

    @Autowired
    @Qualifier("projectIntegrationIdProvider")
    BinaryOperator<String> projectIntegrationIdProvider;

    @Autowired
    @Qualifier("rctProjectDelivery")
    ProjectDelivery executionProjectDelivery;

    @Autowired
    Function<String, Iterable<? extends DeliverableResource>> rctDeliverableImageProvider;

    @Autowired
    Function<String, Iterable<? extends DeliverableResource>> rctDeliverableReportProvider;

    @Autowired ResourceDelivery rctImageResourceDelivery;
    @Autowired ResourceDelivery rctReportResourceDelivery;

    @Test
    void testRctImageDelivery() throws ExecutionException, InterruptedException, TimeoutException {
        var projectId = RandomStringUtils.randomAlphanumeric(12);
        when(projectIntegrationIdProvider.apply(anyString(), anyString())).thenReturn(projectId);
        var resources =
                List.of(
                        DeliverableResource.of(
                                RandomStringUtils.randomAlphanumeric(3),
                                "fileName",
                                "description",
                                "resourceUrl",
                                Map.of()));
        doReturn(resources).when(rctDeliverableImageProvider).apply(anyString());
        doReturn(Futures.immediateFuture(null))
                .when(rctImageResourceDelivery)
                .deliver(anyString(), any(DeliverableResource.class));

        var future =
                executionProjectDelivery.deliver(projectId, "IMAGE", System.currentTimeMillis());

        SettableListenableFuture<Boolean> resultFuture = new SettableListenableFuture<>();

        Futures.addCallback(
                future,
                new FutureCallback<Void>() {

                    @Override
                    public void onSuccess(@Nullable Void result) {
                        resultFuture.set(true);
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        log.error(t.getMessage(), t);
                        resultFuture.set(false);
                    }
                },
                MoreExecutors.directExecutor());

        assertTrue(resultFuture.get(10, TimeUnit.SECONDS));
    }

    @Test
    void testRctReportDelivery() throws ExecutionException, InterruptedException, TimeoutException {
        var projectId = RandomStringUtils.randomAlphanumeric(12);

        when(projectIntegrationIdProvider.apply(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(projectId);
        var resources =
                List.of(
                        DeliverableResource.of(
                                RandomStringUtils.randomAlphanumeric(3),
                                "fileName",
                                "description",
                                "resourceUrl",
                                Map.of()));
        doReturn(resources).when(rctDeliverableReportProvider).apply(anyString());
        doReturn(Futures.immediateFuture(null))
                .when(rctReportResourceDelivery)
                .deliver(anyString(), any(DeliverableResource.class));
        var future =
                executionProjectDelivery.deliver(projectId, "REPORT", System.currentTimeMillis());

        SettableListenableFuture<Boolean> resultFuture = new SettableListenableFuture<>();

        Futures.addCallback(
                future,
                new FutureCallback<Void>() {

                    @Override
                    public void onSuccess(@Nullable Void result) {
                        resultFuture.set(true);
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        log.error(t.getMessage(), t);
                        resultFuture.set(false);
                    }
                },
                MoreExecutors.directExecutor());

        assertTrue(resultFuture.get(10, TimeUnit.SECONDS));
    }
}
