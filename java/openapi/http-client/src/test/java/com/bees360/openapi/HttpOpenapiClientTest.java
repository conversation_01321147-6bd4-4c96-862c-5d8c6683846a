package com.bees360.openapi;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.bees360.api.ApiException;
import com.bees360.http.HttpClient;
import com.bees360.http.MockHttpClient;
import com.bees360.openapi.Message.ApiResponse;
import com.bees360.openapi.Message.ProjectMessage;
import com.bees360.openapi.Message.ProjectQuery;
import com.bees360.openapi.Message.ReportMessage;
import com.bees360.openapi.Message.SetStatusRequest;
import com.bees360.resource.FileResource;
import com.bees360.resource.Resource;
import com.google.protobuf.util.JsonFormat;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.HttpVersion;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHttpResponse;
import org.apache.http.message.BasicStatusLine;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.skyscreamer.jsonassert.JSONAssert;

import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
class HttpOpenapiClientTest {

    public static final long PROJECT_ID = 1021760;
    public static final String REPORT_ID = "JxloUBFQp46H5bRktwzfJwbk-zElqOsB";
    public static final String IMAGE_ID = "-a9K9vk6UgS2h5aBG8mnZxAaQa0LrW4g";

    private static final JsonFormat.Printer printer = JsonFormat.printer();
    private static final JsonFormat.Parser parser = JsonFormat.parser();

    private AutoCloseable closeable;
    private Resource resource;
    private HttpOpenapiClient httpOpenapiClient;

    @Mock private HttpClient httpClient;
    @Mock private Function<HttpUriRequest, HttpResponse> requestForResponse;

    @BeforeEach
    void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
        httpOpenapiClient =
                new HttpOpenapiClient(URI.create(""), new MockHttpClient(requestForResponse));
        resource = mock(FileResource.class);
    }

    @AfterEach
    void tearDown() throws Exception {
        closeable.close();
    }

    @Test
    void createProject() throws IOException {

        var requestJson = getDataFromResource("create_project_request.json");
        var responseJson = getDataFromResource("create_project_response.json");

        when(requestForResponse.apply(any())).thenReturn(newOkHttpResponse(responseJson));

        var builder = ProjectMessage.newBuilder();
        parser.merge(requestJson, builder);
        var requestMessage = builder.build();
        verifyMessageResponse(requestJson, requestMessage);

        var projectMessage = httpOpenapiClient.createProject(requestMessage);

        verifyMessageResponse(responseJson, createApiResponse(List.of(projectMessage)));
    }

    @SneakyThrows
    @Test
    void getProjectDetails() {
        var responseJson = getDataFromResource("get_project_details_response.json");

        when(requestForResponse.apply(any())).thenReturn(newOkHttpResponse(responseJson));

        var projectMessage = httpOpenapiClient.getProjectDetails(PROJECT_ID);

        verifyMessageResponse(responseJson, createApiResponse(List.of(projectMessage)));
    }

    @Test
    void getProjectStatus() {
        var responseJson = getDataFromResource("get_project_status_response.json");

        when(requestForResponse.apply(any())).thenReturn(newOkHttpResponse(responseJson));

        var projectMessage = httpOpenapiClient.getProjectStatus(PROJECT_ID);

        verifyMessageResponse(responseJson, createApiResponse(List.of(projectMessage)));
    }

    private ApiResponse createApiResponse(Iterable<ProjectMessage> projectMessages) {
        var builder = Message.ApiResponse.newBuilder();
        for (var message : projectMessages) {
            builder.addProject(message);
        }
        return builder.build();
    }

    private ApiResponse createApiResponse(ReportMessage reportMessage) {
        return Message.ApiResponse.newBuilder().addReport(reportMessage).build();
    }

    @Test
    void getProjectsByStatus() {
        ProjectQuery projectQuery =
                ProjectQuery.newBuilder()
                        .setStatus("Project Created")
                        .setLimit(10)
                        .setStart(PROJECT_ID)
                        .build();

        var responseJson = getDataFromResource("get_projects_by_status_response.json");

        when(requestForResponse.apply(any())).thenReturn(newOkHttpResponse(responseJson));

        var projectMessage = httpOpenapiClient.getProjectsByStatus(projectQuery);

        verifyMessageResponse(responseJson, createApiResponse(projectMessage));
    }

    @Test
    void updateProjectStatus() throws IOException {
        var requestJson = getDataFromResource("update_project_status_request.json");
        var responseJson = getDataFromResource("update_project_status_response.json");

        var requestBuilder = SetStatusRequest.newBuilder();
        parser.merge(requestJson, requestBuilder);
        var requestMessage = requestBuilder.build();

        verifyMessageResponse(requestJson, requestMessage);

        when(requestForResponse.apply(any())).thenReturn(newOkHttpResponse(responseJson));

        var projectMessage = httpOpenapiClient.updateProjectStatus(PROJECT_ID, requestMessage);

        verifyMessageResponse(responseJson, createApiResponse(List.of(projectMessage)));
    }

    @Test
    void getProjectImages() {
        var responseJson = getDataFromResource("get_project_images_response.json");

        when(requestForResponse.apply(any())).thenReturn(newOkHttpResponse(responseJson));

        var projectMessage = httpOpenapiClient.getProjectImages(PROJECT_ID);

        verifyMessageResponse(responseJson, createApiResponse(List.of(projectMessage)));
    }

    @Test
    void getProjectImageArchive() {
        when(httpClient.execute(any(), any())).thenReturn(resource);
        var httpOpenapiClient = new HttpOpenapiClient(URI.create(""), httpClient);
        assertNotNull(httpOpenapiClient.getProjectImageArchive(PROJECT_ID));
    }

    @Test
    void getProjectAvailableReports() {
        var responseJson = getDataFromResource("get_project_available_reports_response.json");

        when(requestForResponse.apply(any())).thenReturn(newOkHttpResponse(responseJson));

        var projectMessage = httpOpenapiClient.getProjectAvailableReports(PROJECT_ID);

        verifyMessageResponse(responseJson, createApiResponse(List.of(projectMessage)));
    }

    @Test
    void getReportFile() {
        when(httpClient.execute(any(), any())).thenReturn(resource);
        var httpOpenapiClient = new HttpOpenapiClient(URI.create(""), httpClient);
        assertNotNull(httpOpenapiClient.getReportFile(REPORT_ID, true));
    }

    @SneakyThrows
    @Test
    void getReportSummary() {

        var responseJson = getDataFromResource("get_report_summary_response.json");

        when(requestForResponse.apply(any())).thenReturn(newOkHttpResponse(responseJson));

        var reportMessage = httpOpenapiClient.getReportSummary(REPORT_ID);

        verifyMessageResponse(
                responseJson, Message.ProjectMessage.newBuilder().addReport(reportMessage).build());
    }

    @SneakyThrows
    @Test
    void getCloseoutReportSummary() {

        var responseJson = getDataFromResource("get_closeout_report_summary_response.json");
        when(requestForResponse.apply(any())).thenReturn(newOkHttpResponse(responseJson));

        var reportMessage = httpOpenapiClient.getReportSummary(REPORT_ID);
        verifyMessageResponse(
                responseJson, Message.ProjectMessage.newBuilder().addReport(reportMessage).build());
    }

    @SneakyThrows
    private void verifyMessageResponse(
            String expectedJson, com.google.protobuf.Message actualMessage) {
        var actualJson = printer.print(actualMessage);
        JSONAssert.assertEquals(expectedJson, actualJson, false);
    }

    @SneakyThrows
    private String getDataFromResource(String resourceName) {
        return IOUtils.resourceToString(
                resourceName, StandardCharsets.UTF_8, this.getClass().getClassLoader());
    }

    @Test
    void getImageFile() {
        when(httpClient.execute(any(), any())).thenReturn(resource);
        var httpOpenapiClient = new HttpOpenapiClient(URI.create(""), httpClient);
        assertNotNull(httpOpenapiClient.getImageFile(IMAGE_ID));
    }

    @ParameterizedTest
    @ValueSource(ints = {400, 401, 403, 404, 409, 422, 429, 500, 503, 504})
    void testNon2xxResponse(int status) {
        final var errorMessage = RandomStringUtils.randomAlphanumeric(7);

        HttpOpenapiClient httpOpenapiClient =
                new HttpOpenapiClient(URI.create(""), new MockHttpClient(requestForResponse));

        when(requestForResponse.apply(any()))
                .thenReturn(newHttpResponseWithJsonError(status, errorMessage));

        try {
            httpOpenapiClient.getProjectStatus(7);
            fail();
        } catch (ApiException ex) {
            assertEquals(ex.getMessage(), errorMessage);
        }
    }

    @Test
    void testNon2xxEmptyContentResponse() {

        HttpOpenapiClient httpOpenapiClient =
                new HttpOpenapiClient(URI.create(""), new MockHttpClient(requestForResponse));

        when(requestForResponse.apply(any()))
                .thenReturn(newHttpResponseEmptyContent(HttpStatus.SC_BAD_REQUEST));

        try {
            httpOpenapiClient.getProjectStatus(7);
            fail();
        } catch (ApiException ex) {
            assertEquals(ex.getMessage(), "");
        }
    }

    private HttpResponse newHttpResponseEmptyContent(int statusCode) {
        return newHttpResponseWithContent(statusCode, "");
    }

    private HttpResponse newHttpResponseWithContent(int statusCode, String content) {
        var statusLine = new BasicStatusLine(HttpVersion.HTTP_1_1, statusCode, "");
        var httpEntity = new StringEntity(content, ContentType.APPLICATION_JSON);

        var httpResponse = new BasicHttpResponse(statusLine);
        httpResponse.setEntity(httpEntity);
        return httpResponse;
    }

    private HttpResponse newOkHttpResponse(String data) {
        return newHttpResponseWithContent(HttpStatus.SC_OK, data);
    }

    private HttpResponse newHttpResponseWithJsonError(int statusCode, String errorMessage) {
        var errorJson = String.format("{\"error\":{\"message\": \"%s\"}}", errorMessage);
        return newHttpResponseWithContent(statusCode, errorJson);
    }
}
