package com.bees360.openapi;

import com.bees360.http.HttpClient;
import com.bees360.oauth.OAuthHttpClient;
import com.bees360.oauth.OAuthToken;

import lombok.NonNull;

import java.net.URI;

public class HttpOpenapiClients {

    private final URI context;

    public HttpOpenapiClients(@NonNull String context) {
        this.context = URI.create(context);
    }

    public HttpOpenapiClient getApi(@NonNull HttpClient httpClient) {
        return new HttpOpenapiClient(context, httpClient);
    }

    public HttpOpenapiClient getApi(@NonNull HttpClient httpClient, @NonNull OAuthToken toke) {
        return new HttpOpenapiClient(context, new OAuthHttpClient(httpClient, toke));
    }
}
