package com.bees360.openapi.vendor;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;

import com.bees360.openapi.config.VendorApiResourceServerConfig;
import com.bees360.openapi.http.VendorWebhookTestClient;
import com.bees360.openapi.vendor.model.VendorProjectStatusRequest;
import com.bees360.openapi.vendor.project.VendorProjectStatusManager;
import com.bees360.project.Message;
import com.google.gson.Gson;

import lombok.SneakyThrows;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.client.methods.HttpUriRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpMethod;

import java.time.Instant;

@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {VendorProjectEndpointTest.Config.class},
        properties = {
            "spring.config.location = classpath:application-test.yml",
        })
@EnableAutoConfiguration
public class VendorProjectEndpointTest {

    @Import({
        VendorProjectEndpoint.class,
        VendorApiResourceServerConfig.class,
        VendorApiExceptionHandler.class,
    })
    static class Config {}

    private final VendorWebhookTestClient beesPilotClient =
            VendorWebhookTestClient.getClient("X-BeesPilot-Signature");

    @MockBean VendorProjectStatusManager projectStatusManager;

    @LocalServerPort private int port;

    @SneakyThrows
    @Test
    void testUpdateProjectStatus() {
        var projectId = 1 + RandomStringUtils.randomNumeric(4);
        var status = Message.ProjectStatus.SITE_INSPECTED;
        Mockito.when(
                        projectStatusManager.updateStatus(
                                eq(projectId), eq(status), any(), anyLong(), any(), eq(false)))
                .thenAnswer(e -> true);
        var request = genStatusRequest(projectId, status.getNumber(), false);
        var response = beesPilotClient.execute(request);

        Assertions.assertEquals(200, response.getStatusLine().getStatusCode());
        Mockito.verify(projectStatusManager, Mockito.timeout(2000).times(1))
                .updateStatus(eq(projectId), eq(status), any(), anyLong(), any(), eq(false));
    }

    @SneakyThrows
    @Test
    void testRollbackProjectStatus() {
        var projectId = 1 + RandomStringUtils.randomNumeric(4);
        var status = Message.ProjectStatus.SITE_INSPECTED;
        Mockito.when(
                        projectStatusManager.updateStatus(
                                eq(projectId), eq(status), any(), anyLong(), any(), eq(true)))
                .thenAnswer(e -> true);
        var request = genStatusRequest(projectId, status.getNumber(), true);
        var response = beesPilotClient.execute(request);

        Assertions.assertEquals(200, response.getStatusLine().getStatusCode());
        Mockito.verify(projectStatusManager, Mockito.timeout(2000).times(1))
                .updateStatus(eq(projectId), eq(status), any(), anyLong(), any(), eq(true));
    }

    @SneakyThrows
    @Test
    void testUpdateProjectStatusWhenProjectIdIllegal() {
        var projectId = 1 + RandomStringUtils.randomAlphabetic(4);
        var status = Message.ProjectStatus.SITE_INSPECTED;

        var request = genStatusRequest(projectId, status.getNumber(), false);
        var response = beesPilotClient.execute(request);
        Assertions.assertEquals(400, response.getStatusLine().getStatusCode());
    }

    @SneakyThrows
    @Test
    void testUpdateProjectStatusWhenStatusNotExists() {
        var projectId = 1 + RandomStringUtils.randomAlphabetic(4);

        var request = genStatusRequest(projectId, 20, false);
        var response = beesPilotClient.execute(request);
        Assertions.assertEquals(400, response.getStatusLine().getStatusCode());
    }

    @SneakyThrows
    @Test
    void testUpdateProjectStatusWhenOperationAborted() {
        var projectId = 1 + RandomStringUtils.randomNumeric(4);
        var status = Message.ProjectStatus.SITE_INSPECTED;
        Mockito.when(
                        projectStatusManager.updateStatus(
                                eq(projectId), eq(status), any(), anyLong(), any(), eq(false)))
                .thenAnswer(e -> false);
        var request = genStatusRequest(projectId, status.getNumber(), false);
        var response = beesPilotClient.execute(request);
        Assertions.assertEquals(400, response.getStatusLine().getStatusCode());
    }

    @SneakyThrows
    private HttpUriRequest genStatusRequest(String projectId, Integer status, boolean isRollback) {
        var url = "http://localhost:" + port + "/v1/vendor/project/" + projectId + "/status";
        final Gson gson = new Gson();
        var request = new VendorProjectStatusRequest();
        request.setStatus("" + status);
        request.setComment("test comment");
        request.setIsRollback(isRollback);
        request.setUpdatedAt(Instant.now().toEpochMilli());
        return beesPilotClient.genJsonRequest(HttpMethod.PUT.name(), url, gson.toJson(request));
    }
}
