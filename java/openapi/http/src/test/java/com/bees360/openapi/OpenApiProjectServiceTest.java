package com.bees360.openapi;

import static com.bees360.building.Message.BuildingType.RESIDENTIAL_SINGLE_FAMILY;
import static com.bees360.openapi.OpenApiProject.AGENT_ROLE;
import static com.bees360.openapi.OpenApiProject.INSURED_ROLE;
import static com.bees360.project.Message.ProjectStatus.RETURNED_TO_CLIENT;
import static com.bees360.project.Message.ServiceType.FOUR_POINT_UNDERWRITING;

import com.bees360.contract.Contract;
import com.bees360.contract.ContractManager;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.status.ProjectStatus;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.util.DateTimes;
import com.google.protobuf.util.JsonFormat;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@SpringJUnitConfig
class OpenApiProjectServiceTest {

    @Autowired private OpenApiProjectService openApiProjectService;
    @MockBean private ProjectIIManager projectIIManager;
    @MockBean private ContactManager contactManager;
    @MockBean private ProjectStatusManager projectStatusProvider;
    @MockBean private ContractManager contractManager;

    @MockBean private OpenApiProjectCreator openApiProjectCreator;

    private static final JsonFormat.Printer printer = JsonFormat.printer();
    private static final JsonFormat.Parser parser = JsonFormat.parser();

    @Configuration
    @Import({
        OpenApiProjectService.class,
    })
    static class Config {}

    @Test
    void testGetProjectDetails() {
        var expectProject = getProjectMessage("get_project_details_response.json");

        projectIIManagerFindByIdStub(expectProject);

        contactManagerGetStatusStub(expectProject);

        contractManagerFindByIdStub(expectProject);

        var actualProject = openApiProjectService.getProjectDetails(expectProject.getId());

        assertProjectMessageEquals(expectProject, actualProject);
    }

    @Test
    void testGetProjectStatus() {
        var expectProject = getProjectMessage("get_project_status_response.json");

        ProjectStatusProviderFindByProjectIdStub(expectProject);

        var actualProject = openApiProjectService.getProjectStatus(expectProject.getId());

        assertProjectMessageEquals(expectProject, actualProject);
    }

    @SneakyThrows
    private Message.ProjectMessage getProjectMessage(String fileName) {
        var projectMessageJson = getDataFromResource(fileName);
        var builder = Message.ProjectMessage.newBuilder();
        parser.merge(projectMessageJson, builder);
        return builder.build();
    }

    private void projectIIManagerFindByIdStub(Message.ProjectMessage message) {
        var builder = com.bees360.project.Message.ProjectMessage.newBuilder();
        builder.setId(String.valueOf(message.getId()));

        var buildingMessage =
                com.bees360.building.Message.BuildingMessage.newBuilder()
                        .setYearBuilt(message.getYearBuilt())
                        .setType(RESIDENTIAL_SINGLE_FAMILY)
                        .setTypeValue(0)
                        .build();

        var addressMessage =
                com.bees360.address.Message.AddressMessage.newBuilder()
                        .setStreetNumber(message.getStreetAddress().split(" ", 2)[0])
                        .setRoute(message.getStreetAddress().split(" ", 2)[1])
                        .setCity(message.getCity())
                        .setState(message.getState())
                        .setCountry(message.getCountry())
                        .setZip(message.getZipcode())
                        .build();
        var policyMessage =
                com.bees360.policy.Message.PolicyMessage.newBuilder()
                        .setBuilding(buildingMessage)
                        .setPolicyNo(message.getPolicyNumber())
                        .setAddress(addressMessage)
                        .setPolicyEffectiveDate(
                                DateTimes.toProtoDate(
                                        parseLocalDate(message.getPolicyEffectiveDate())))
                        .build();

        var contractMessage =
                com.bees360.contract.Message.ContractMessage.newBuilder().setId("1").build();

        var projectMessage =
                builder.setBuilding(buildingMessage)
                        .setPolicy(policyMessage)
                        .setContract(contractMessage)
                        .setServiceType(FOUR_POINT_UNDERWRITING)
                        .setInspectionNumber(message.getInspectionNumber())
                        .build();

        Mockito.doReturn(ProjectII.from(projectMessage))
                .when(projectIIManager)
                .findById(Mockito.anyString());
    }

    private void contactManagerGetStatusStub(Message.ProjectMessage message) {

        Mockito.doReturn(
                        List.of(
                                Contact.of(
                                        null,
                                        message.getInsuredName(),
                                        message.getInsuredEmail(),
                                        message.getInsuredPhone(),
                                        null,
                                        null,
                                        INSURED_ROLE,
                                        null,
                                        null,
                                        null,
                                        false),
                                Contact.of(
                                        null,
                                        message.getAgentName(),
                                        message.getAgentEmail(),
                                        message.getAgentPhone(),
                                        null,
                                        null,
                                        AGENT_ROLE,
                                        null,
                                        null,
                                        null,
                                        false)))
                .when(contactManager)
                .findByProjectId(Mockito.anyString());
    }

    private void contractManagerFindByIdStub(Message.ProjectMessage message) {
        var customerMessage =
                com.bees360.customer.Message.CustomerMessage.newBuilder()
                        .setName(message.getInsuredBy())
                        .build();
        var contractMessage =
                com.bees360.contract.Message.ContractMessage.newBuilder()
                        .setInsuredBy(customerMessage)
                        .build();
        Mockito.doReturn(Contract.from(contractMessage))
                .when(contractManager)
                .findById(Mockito.anyString());
    }

    private void ProjectStatusProviderFindByProjectIdStub(Message.ProjectMessage message) {

        var status =
                new ProjectStatus() {
                    @Nonnull
                    @Override
                    public String getProjectId() {
                        return String.valueOf(message.getId());
                    }

                    @Nonnull
                    @Override
                    public com.bees360.project.Message.ProjectStatus getStatus() {
                        return RETURNED_TO_CLIENT;
                    }

                    @Nonnull
                    @Override
                    public Instant getUpdatedAt() {
                        return Instant.now();
                    }

                    @Nonnull
                    @Override
                    public String getUpdatedBy() {
                        return " ";
                    }

                    @Nullable
                    @Override
                    public String getComment() {
                        return null;
                    }
                };
        Mockito.doReturn(status).when(projectStatusProvider).getStatus(Mockito.anyString());
    }

    @SneakyThrows
    private void assertProjectMessageEquals(
            Message.ProjectMessage expectMessage, Message.ProjectMessage actualMessage) {
        var expectedJson = printer.print(expectMessage);
        var actualJson = printer.print(actualMessage);
        JSONAssert.assertEquals(expectedJson, actualJson, false);
    }

    @SneakyThrows
    private String getDataFromResource(String resourceName) {
        return IOUtils.resourceToString(
                resourceName, StandardCharsets.UTF_8, this.getClass().getClassLoader());
    }

    private LocalDate parseLocalDate(String localDate) {
        return LocalDate.parse(localDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
}
