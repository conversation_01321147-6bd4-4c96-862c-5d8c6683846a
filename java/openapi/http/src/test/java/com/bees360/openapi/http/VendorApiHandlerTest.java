package com.bees360.openapi.http;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

import com.bees360.auth.config.JwtResourceServerConfig;
import com.bees360.openapi.config.OpenApiExceptionHandler;
import com.bees360.openapi.config.VendorApiResourceServerConfig;
import com.bees360.openapi.vendor.TestEchoEndpoint;
import com.bees360.openapi.vendor.VendorApiExceptionHandler;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpMethod;
import org.springframework.test.annotation.DirtiesContext;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {VendorApiHandlerTest.Config.class},
        properties = {
            "spring.config.location = classpath:application-test.yml",
        })
@DirtiesContext
@EnableAutoConfiguration
@EnableConfigurationProperties
public class VendorApiHandlerTest {

    @Import({
        VendorApiResourceServerConfig.class,
        TestEchoEndpoint.class,
        VendorApiExceptionHandler.class,
        OpenApiExceptionHandler.class,
        JwtResourceServerConfig.class,
    })
    @Configuration
    static class Config {}

    private final VendorWebhookTestClient beesPilotClient =
            VendorWebhookTestClient.getClient("X-BeesPilot-Signature");
    private final VendorWebhookTestClient unknownClient =
            VendorWebhookTestClient.getClient("UNKNOWN");
    private final Gson gson = new Gson();
    @LocalServerPort private int port;

    @Test
    void vendorEchoTest() throws Exception {
        var url = genUrl("/v1/vendor/echo");
        var body = loadJsonString("vendor_test_request.json");

        var request = beesPilotClient.genJsonRequest(HttpMethod.PUT.name(), url, body);
        var response = beesPilotClient.execute(request);
        var responseObject =
                gson.fromJson(
                        IOUtils.toString(response.getEntity().getContent()), JsonObject.class);

        var userId = responseObject.get("userId").getAsString();
        var userEmail = responseObject.get("userEmail").getAsString();
        var data = responseObject.get("data").getAsString();

        assertEquals(200, response.getStatusLine().getStatusCode());
        assertEquals("9999", userId);
        assertEquals("<EMAIL>", userEmail);
        assertEquals(body, data);
    }

    @Test
    void illegalArgumentTest() throws Exception {
        var url = genUrl("/v1/vendor/echo/illegalArgument");
        requestThenAssertCode(HttpMethod.POST.name(), url, beesPilotClient, 400);
    }

    @Test
    void permissionDeniedTest() throws Exception {
        var url = genUrl("/v1/vendor/echo/permissionDenied");
        requestThenAssertCode(HttpMethod.POST.name(), url, beesPilotClient, 403);
    }

    @Test
    void notFoundTest() throws Exception {
        var url = genUrl("/v1/vendor/echo/notFound");
        requestThenAssertCode(HttpMethod.POST.name(), url, beesPilotClient, 404);
    }

    @Test
    void illegalStateTest() throws Exception {
        var url = genUrl("/v1/vendor/echo/illegalState");
        requestThenAssertCode(HttpMethod.POST.name(), url, beesPilotClient, 500);
    }

    @Test
    void otherTest() throws Exception {
        var url = genUrl("/v1/vendor/echo/internalServerError");
        requestThenAssertCode(HttpMethod.POST.name(), url, beesPilotClient, 500);
        url = "http://localhost:" + port + "/v1/vendor/echo/unauthenticated";
        requestThenAssertCode(HttpMethod.POST.name(), url, beesPilotClient, 500);
    }

    @Test
    void unauthenticatedTest() throws Exception {
        // unauthenticated request should not be wrapped
        var url = genUrl("/v1/vendor/echo/unauthenticated");
        var request = unknownClient.genJsonRequest(HttpMethod.POST.name(), url, "{}");
        var response = unknownClient.execute(request);
        assertEquals(401, response.getStatusLine().getStatusCode());
    }

    private void requestThenAssertCode(
            String method, String url, VendorWebhookTestClient client, int expectCode)
            throws Exception {
        var request = client.genJsonRequest(method, url, "{}");
        var response = client.execute(request);
        var responseObject =
                gson.fromJson(
                        IOUtils.toString(response.getEntity().getContent()), JsonObject.class);

        var errors = responseObject.get("errors").getAsJsonArray();
        assertEquals(expectCode, response.getStatusLine().getStatusCode());
        assertFalse(errors.isEmpty());
    }

    private String loadJsonString(String path) throws IOException {
        return IOUtils.resourceToString(
                path, StandardCharsets.UTF_8, this.getClass().getClassLoader());
    }

    private String genUrl(String path) {
        return "http://localhost:" + port + path;
    }
}
