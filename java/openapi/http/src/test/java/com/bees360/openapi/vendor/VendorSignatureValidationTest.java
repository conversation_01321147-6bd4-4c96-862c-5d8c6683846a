package com.bees360.openapi.vendor;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.bees360.auth.config.JwtResourceServerConfig;
import com.bees360.openapi.config.VendorApiResourceServerConfig;
import com.bees360.openapi.http.VendorWebhookTestClient;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.RequestBuilder;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpMethod;
import org.springframework.test.annotation.DirtiesContext;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {VendorSignatureValidationTest.Config.class},
        properties = {
            "spring.config.location = classpath:application-test.yml",
        })
@DirtiesContext
@EnableAutoConfiguration
@EnableConfigurationProperties
public class VendorSignatureValidationTest {

    @Import({
        VendorApiResourceServerConfig.class,
        TestEchoEndpoint.class,
        JwtResourceServerConfig.class,
    })
    @Configuration
    static class Config {}

    private final VendorWebhookTestClient beesPilotClient =
            VendorWebhookTestClient.getClient("X-BeesPilot-Signature");

    private final VendorWebhookTestClient unknownClient =
            VendorWebhookTestClient.getClient("UNKNOWN");

    private final Gson gson = new Gson();

    @LocalServerPort private int port;

    @Test
    void invalidSignatureTest() throws Exception {
        var url = genUrl("/v1/vendor/echo");
        var body = loadJsonString("vendor_test_request.json");

        // incorrect head, should not be authenticated
        var incorrectHeaderRequest = unknownClient.genJsonRequest(HttpMethod.PUT.name(), url, body);
        var incorrectHeaderResponse = unknownClient.execute(incorrectHeaderRequest);
        assertEquals(401, incorrectHeaderResponse.getStatusLine().getStatusCode());

        // invalid signature, should not be authenticated
        var request = beesPilotClient.genJsonRequest(HttpMethod.PUT.name(), url, body);
        var inValidSignatureRequest =
                RequestBuilder.copy(request).setHeader("X-BeesPilot-Signature", "unknown").build();
        var inValidSignatureResponse = beesPilotClient.execute(inValidSignatureRequest);
        assertEquals(401, inValidSignatureResponse.getStatusLine().getStatusCode());
    }

    @Test
    void vendorEchoEndpointTest() throws Exception {
        var url = genUrl("/v1/vendor/echo");
        var body = loadJsonString("vendor_test_request.json");

        var request = beesPilotClient.genJsonRequest(HttpMethod.PUT.name(), url, body);
        var response = beesPilotClient.execute(request);
        var responseObject =
                gson.fromJson(
                        IOUtils.toString(response.getEntity().getContent()), JsonObject.class);

        var userId = responseObject.get("userId").getAsString();
        var userEmail = responseObject.get("userEmail").getAsString();
        var data = responseObject.get("data").getAsString();

        assertEquals("9999", userId);
        assertEquals("<EMAIL>", userEmail);
        assertEquals(body, data);
    }

    private String loadJsonString(String path) throws IOException {
        return IOUtils.resourceToString(
                path, StandardCharsets.UTF_8, this.getClass().getClassLoader());
    }

    private String genUrl(String path) {
        return "http://localhost:" + port + path;
    }
}
