package com.bees360.openapi;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.bees360.openapi.Message.ProjectMessage;
import com.bees360.openapi.Message.ReportMessage;
import com.bees360.openapi.http.SummaryReportJsonEncoder;
import com.google.protobuf.Struct;
import com.google.protobuf.util.JsonFormat;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;

import java.nio.charset.StandardCharsets;

class SummaryReportJsonEncoderTest {

    @Test
    void testNull() {
        var encoder = new SummaryReportJsonEncoder();
        var result = encoder.encode(null);
        assertEquals("", result.toStringUtf8());
    }

    @SneakyThrows
    @Test
    void testFull() {
        var encoder = new SummaryReportJsonEncoder();
        var summary = createSummary("report_summary_full_summary_only.json");
        var project = createProject("report_summary_full_project_only.json");
        var report =
                SummaryReport.of(
                        "aJAgK76i2zXT9sZ_BxVEw-cDX2Ku98Kv",
                        "Full-scope Underwriting Report",
                        project,
                        summary);
        var result = encoder.encode(report);
        JSONAssert.assertEquals(
                loadResource("report_summary_full_expected.json"), result.toStringUtf8(), true);
    }

    @SneakyThrows
    @Test
    void testFullWithAdditionalProperties() {
        var encoder = new SummaryReportJsonEncoder();
        var summary = createSummary("report_summary_full_with_additional_summary_only.json");
        var project = createProject("report_summary_full_project_only.json");
        var report =
                SummaryReport.of(
                        "aJAgK76i2zXT9sZ_BxVEw-cDX2Ku98Kv",
                        "Full-scope Underwriting Report",
                        project,
                        summary);
        var result = encoder.encode(report);
        JSONAssert.assertEquals(
                loadResource("report_summary_full_with_additional_expected.json"),
                result.toStringUtf8(),
                true);
    }

    @SneakyThrows
    @Test
    void testNullValueFieldShouldIgnored() {
        var encoder = new SummaryReportJsonEncoder();
        var summary = createSummary("report_summary_333316_original.json");
        var report =
                SummaryReport.of(
                        "jy7nSE46o8nk5tAtvuTfz-0DBfjIIpvK",
                        "Full-scope Underwriting Report",
                        null,
                        summary);
        var result = encoder.encode(report);
        JSONAssert.assertEquals(
                loadResource("report_summary_333316_from_openapi.json"),
                result.toStringUtf8(),
                true);
    }

    @SneakyThrows
    @Test
    void testFactorListSummary() {
        var encoder = new SummaryReportJsonEncoder();
        var summary = createSummary("report_summary_thig_factor_value.json");
        var report = SummaryReport.of(null, null, null, summary);
        var result = encoder.encode(report);
        var expected =
                "{\"summary\":" + loadResource("report_summary_thig_factor_value.json") + "}";
        JSONAssert.assertEquals(expected, result.toStringUtf8(), true);
    }

    @SneakyThrows
    @Test
    void testSummaryAdditionalPropertiesNullable() {
        var encoder = new SummaryReportJsonEncoder();
        var summary = createSummary("report_summary_only_additional.json");
        var report = SummaryReport.of(null, null, null, summary);
        var result = encoder.encode(report);
        var expected =
                "{\"summary\":"
                        + loadResource("report_summary_only_additional_expected.json")
                        + "}";
        JSONAssert.assertEquals(expected, result.toStringUtf8(), true);
    }

    @SneakyThrows
    private SummaryProject createProject(String resourceName) {
        var json = loadResource(resourceName);
        var builder = ProjectMessage.newBuilder();
        JsonFormat.parser().ignoringUnknownFields().merge(json, builder);
        return ProtoSummaryProject.from(builder.build());
    }

    @SneakyThrows
    private Summary createSummary(String resourceName) {
        var summaryJson = loadResource(resourceName);
        var builder = ReportMessage.Summary.newBuilder();
        JsonFormat.parser().ignoringUnknownFields().merge(summaryJson, builder);
        var addtionalPropertiesBuilder = Struct.newBuilder();
        JsonFormat.parser().ignoringUnknownFields().merge(summaryJson, addtionalPropertiesBuilder);
        builder.setAdditionalProperties(addtionalPropertiesBuilder.build());
        return ProtoSummary.from(builder.build());
    }

    @SneakyThrows
    private String loadResource(String name) {
        return IOUtils.resourceToString(
                name, StandardCharsets.UTF_8, this.getClass().getClassLoader());
    }
}
