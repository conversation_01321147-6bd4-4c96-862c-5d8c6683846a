package com.bees360.openapi.http;

import com.bees360.util.RequestValidator;
import com.bees360.webhook.validator.SignatureValidator;
import com.google.gson.Gson;

import lombok.extern.log4j.Log4j2;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Log4j2
public class VendorWebhookTestClient {

    private HttpClient httpClient = HttpClients.createDefault();
    private final Gson gson = SignatureValidator.GSON;
    private final String fakeAuthToken = "FAKE_AUTH_TOKEN";
    private final String signatureHeader;

    public VendorWebhookTestClient(String signatureHeader) {
        this.signatureHeader = signatureHeader;
    }

    public VendorWebhookTestClient(String signatureHeader, HttpClient httpClient) {
        this(signatureHeader);
        this.httpClient = httpClient;
    }

    public static VendorWebhookTestClient getClient(String signatureHeader) {
        return new VendorWebhookTestClient(signatureHeader);
    }

    public <T> HttpUriRequest genJsonRequest(String method, String url, T params) throws Exception {
        var entity = "";
        if (params instanceof String) {
            entity = (String) params;
        } else {
            entity = gson.toJson(params);
        }

        Map<String, Object> parameterMap = gson.fromJson(entity, Map.class);
        Map<String, String> jsonMap = new HashMap<>();
        parameterMap.forEach((key, value) -> jsonMap.put(key, gson.toJson(value)));
        RequestValidator requestValidator = new RequestValidator(fakeAuthToken);
        String signature = requestValidator.getValidationSignature(url, jsonMap);
        log.debug("Generate signature {} for request url: {}, params: {}", signature, url, params);
        return RequestBuilder.create(method)
                .setUri(url)
                .setEntity(new StringEntity(entity))
                .setHeader("Content-type", "application/json")
                .setHeader(signatureHeader, signature)
                .build();
    }

    public HttpResponse execute(HttpUriRequest request) throws IOException {
        return httpClient.execute(request);
    }
}
