package com.bees360.openapi.vendor;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.bees360.auth.config.JwtResourceServerConfig;
import com.bees360.openapi.config.VendorApiResourceServerConfig;
import com.bees360.openapi.http.VendorWebhookTestClient;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpMethod;
import org.springframework.test.annotation.DirtiesContext;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {VendorSignatureValidationIncorrectConfigurationTest.Config.class},
        properties = {
            "spring.config.location = classpath:application-test.yml",
            "vendor.signature-validation.bees-pilot.user-email = ",
            "vendor.signature-validation.bees-pilot.user-id = "
        })
@DirtiesContext
@EnableAutoConfiguration
@EnableConfigurationProperties
public class VendorSignatureValidationIncorrectConfigurationTest {

    @Import({
        VendorApiResourceServerConfig.class,
        TestEchoEndpoint.class,
        JwtResourceServerConfig.class,
    })
    @Configuration
    static class Config {}

    private final VendorWebhookTestClient beesPilotClient =
            VendorWebhookTestClient.getClient("X-BeesPilot-Signature");

    @LocalServerPort private int port;

    @Test
    void vendorEchoEndpointTest() throws Exception {
        var url = "http://localhost:" + port + "/v1/vendor/echo";
        var body = loadJsonString("vendor_test_request.json");

        var request = beesPilotClient.genJsonRequest(HttpMethod.PUT.name(), url, body);
        var response = beesPilotClient.execute(request);
        assertEquals(401, response.getStatusLine().getStatusCode());
    }

    private String loadJsonString(String path) throws IOException {
        return IOUtils.resourceToString(
                path, StandardCharsets.UTF_8, this.getClass().getClassLoader());
    }
}
