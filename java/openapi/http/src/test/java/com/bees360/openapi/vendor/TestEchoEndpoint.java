package com.bees360.openapi.vendor;

import com.bees360.api.InternalException;
import com.bees360.api.NotFoundException;
import com.bees360.api.PermissionDeniedException;
import com.bees360.api.UnauthenticatedException;
import com.bees360.user.User;

import jakarta.servlet.http.HttpServletRequest;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.io.IOUtils;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Map;

@Log4j2
@RestController
@RequestMapping
public class TestEchoEndpoint {

    public TestEchoEndpoint() {
        log.info("Created.");
    }

    @PutMapping("/vendor/echo")
    Map<String, Object> vendorTesting(
            @AuthenticationPrincipal User user, HttpServletRequest request) throws IOException {
        return Map.of(
                "userId",
                user.getId(),
                "userEmail",
                user.getEmail(),
                "data",
                IOUtils.toString(request.getInputStream()));
    }

    @PostMapping("/vendor/echo/illegalArgument")
    void illegalArgumentTesting() {
        throw new IllegalArgumentException("Illegal argument exception for test.");
    }

    @PostMapping("/vendor/echo/permissionDenied")
    void permissionDeniedTesting() {
        throw new PermissionDeniedException("Permission Denied exception for test.");
    }

    @PostMapping("/vendor/echo/notFound")
    void notFoundTesting() {
        throw new NotFoundException("Not found exception for test.");
    }

    @PostMapping("/vendor/echo/illegalState")
    void illegalStateTesting() {
        throw new IllegalStateException("Illegal State exception for test.");
    }

    @PostMapping("/vendor/echo/internalServerError")
    void internalServerErrorTesting() {
        throw new InternalException("Internal exception for test.");
    }

    @PostMapping("/vendor/echo/unauthenticated")
    void unauthenticatedTesting() {
        throw new UnauthenticatedException("unauthenticated exception for test.");
    }
}
