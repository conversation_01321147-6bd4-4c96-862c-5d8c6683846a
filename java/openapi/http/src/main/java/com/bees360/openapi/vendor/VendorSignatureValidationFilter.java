package com.bees360.openapi.vendor;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.auth.DefaultUserDetails;
import com.bees360.user.Account;
import com.bees360.user.Message;
import com.bees360.user.User;
import com.bees360.webhook.ContentCachingRequestWrapper;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import lombok.extern.log4j.Log4j2;

import org.springframework.core.annotation.Order;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Log4j2
@Order
public class VendorSignatureValidationFilter extends OncePerRequestFilter {

    private final Map<String, VendorSignatureValidator> validatorRegistry;

    public VendorSignatureValidationFilter(
            Map<String, VendorSignatureValidator> validatorRegistry) {
        this.validatorRegistry = validatorRegistry;
        log.info("Created {}(validatorRegistry={}).", this, this.validatorRegistry);
    }

    @Override
    protected void doFilterInternal(
            HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        var wrapper = new ContentCachingRequestWrapper(request);
        for (Map.Entry<String, VendorSignatureValidator> registerEntry :
                validatorRegistry.entrySet()) {
            var signatureHeader = registerEntry.getKey();
            var validator = registerEntry.getValue();
            var signature = wrapper.getHeader(signatureHeader);
            // validate if signature is not null
            if (signature == null) {
                continue;
            }

            if (validator.verify(wrapper)) {
                var issuer = validator.getIssuer();
                if (issuer == null) {
                    log.warn(
                            "Cannot validate signature with header '{}':No issuer's user"
                                    + " information.",
                            registerEntry.getKey());
                    filterChain.doFilter(wrapper, response);
                    return;
                }

                // set issuer's information to holder for authentication
                SecurityContextHolder.getContext()
                        .setAuthentication(
                                new PreAuthenticatedAuthenticationToken(
                                        new DefaultUserDetails(toAccount(issuer)),
                                        "",
                                        List.of(new SimpleGrantedAuthority("vendor"))));
            }
        }

        filterChain.doFilter(wrapper, response);
    }

    private Account toAccount(User user) {
        var builder = Message.AccountMessage.newBuilder();
        builder.setUser(user.toMessage());
        builder.setId(user.getEmail());
        acceptIfNotNull(builder::setId, user.getEmail());
        return Account.from(builder.build());
    }
}
