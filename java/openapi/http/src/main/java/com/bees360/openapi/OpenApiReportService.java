package com.bees360.openapi;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.NotFoundException;
import com.bees360.image.ImageGroupProvider;
import com.bees360.openapi.Message.ProjectMessage;
import com.bees360.openapi.util.OpenApiReport;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportGroupProvider;
import com.bees360.report.ReportProvider;
import com.bees360.util.Iterables;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.Map;
import java.util.Optional;

@Log4j2
class OpenApiReportService {

    private final ReportGroupProvider reportGroupProvider;

    private final ImageGroupProvider imageGroupProvider;

    private final ReportProvider reportProvider;

    private final ProjectIIManager projectIIManager;

    private final ContactManager contactManager;

    private final ProjectReportProvider projectReportProvider;

    public OpenApiReportService(
            @NonNull ReportGroupProvider reportGroupProvider,
            @NonNull ImageGroupProvider imageGroupProvider,
            @NonNull ReportProvider reportProvider,
            @NonNull ProjectIIManager projectIIManager,
            @NonNull ContactManager contactManager,
            @NonNull ProjectReportProvider projectReportProvider) {
        this.reportGroupProvider = reportGroupProvider;
        this.imageGroupProvider = imageGroupProvider;
        this.reportProvider = reportProvider;
        this.projectIIManager = projectIIManager;
        this.contactManager = contactManager;
        this.projectReportProvider = projectReportProvider;

        log.info(
                "Created {}(reportGroupProvider={}, imageGroupProvider={}, reportProvider={},"
                        + " projectIIManager={}, contactManager={}, projectReportProvider={})",
                this,
                reportGroupProvider,
                imageGroupProvider,
                reportProvider,
                projectIIManager,
                contactManager,
                projectReportProvider);
    }

    public com.bees360.openapi.Message.ReportMessage getReportWithSummary(String reportId) {
        var projectMessage = createProjectMessageForReportSummary(reportId);
        return Optional.ofNullable(reportProvider.findById(reportId))
                .map(r -> new OpenApiReport(r, projectMessage))
                .map(OpenApiReport::toMessage)
                .orElse(null);
    }

    private ProjectMessage createProjectMessageForReportSummary(String reportId) {
        var projectIds = Iterables.toList(projectReportProvider.findProjectId(reportId));
        if (projectIds.size() != 1) {
            return ProjectMessage.getDefaultInstance();
        }
        var projectId = projectIds.get(0);
        var projectII = projectIIManager.findById(projectId);
        var contacts = contactManager.findByProjectId(projectId);
        var openapiProject = OpenApiProject.from(projectII, contacts);

        var builder = ProjectMessage.newBuilder().setId(Long.parseLong(projectId));
        acceptIfNotNull(builder::setInspectionNumber, openapiProject.getInspectionNumber());
        acceptIfNotNull(builder::setServiceName, openapiProject.getServiceName());
        acceptIfNotNull(builder::setInsuredName, openapiProject.getInsuredName());
        acceptIfNotNull(
                builder::setInspectionTime,
                projectII.getInspectionAppointmentTime(),
                Instant::toEpochMilli);
        acceptIfNotNull(
                builder::setCompletionTime,
                projectII.getInspectionCompletedTime(),
                Instant::toEpochMilli);
        return builder.build();
    }

    public String provideReportResourceUrl(String reportId, Boolean isCompressed) {
        return Optional.ofNullable(reportProvider.findById(reportId))
                .filter(report -> report.getResourceUrl() != null)
                .map(
                        report -> {
                            Map<Message.ReportMessage.Resource.Type, String> typeMap =
                                    report.getResourceUrl();
                            if (isCompressed == null) {
                                String url =
                                        typeMap.get(Message.ReportMessage.Resource.Type.COMPRESSED);
                                if (StringUtils.isEmpty(url)) {
                                    url = typeMap.get(Message.ReportMessage.Resource.Type.ORIGIN);
                                }
                                return url;
                            } else if (isCompressed) {
                                return typeMap.get(Message.ReportMessage.Resource.Type.COMPRESSED);
                            } else {
                                return typeMap.get(Message.ReportMessage.Resource.Type.ORIGIN);
                            }
                        })
                .orElseThrow(
                        () -> {
                            throw new NotFoundException(
                                    String.format("report file of:%s can not be found", reportId));
                        });
    }

    // TODO filter report whose report type are contained in the service type of the project
    public Iterable<? extends Report> listAvailableReportsInProject(long projectId) {
        return projectReportProvider.find(
                projectId + "", null, Message.ReportMessage.Status.APPROVED);
    }
}
