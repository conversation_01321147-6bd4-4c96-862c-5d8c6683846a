package com.bees360.openapi.vendor;

import com.bees360.user.Message;
import com.bees360.user.User;
import com.bees360.webhook.validator.SignatureValidator;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class DefaultVendorSignatureValidator extends SignatureValidator
        implements VendorSignatureValidator {

    private final User issuer;

    public DefaultVendorSignatureValidator(
            String authToken,
            String signatureHeader,
            String webhookUrlOverride,
            String userId,
            String userEmail) {
        super(authToken, signatureHeader, webhookUrlOverride);
        var userBuilder = Message.UserMessage.newBuilder();
        userBuilder.setId(userId);
        userBuilder.setEmail(userEmail);
        this.issuer = User.from(userBuilder.build());
    }

    @Override
    public User getIssuer() {
        return issuer;
    }
}
