package com.bees360.openapi;

import com.bees360.customer.Customer;
import com.bees360.openapi.Message.ApiResponse;
import com.bees360.openapi.Message.ProjectMessage;
import com.bees360.openapi.Message.SetStatusRequest;
import com.bees360.openapi.util.OpenApiReport;
import com.bees360.report.Report;
import com.bees360.user.User;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Log4j2
@Validated
@RestController
@RequestMapping(
        value = "${http.openapi.endpoint:}/project",
        produces = MediaType.APPLICATION_JSON_VALUE,
        consumes = MediaType.APPLICATION_JSON_VALUE)
public class ProjectEndpoint {

    private final OpenApiProjectService openApiProjectService;
    private final OpenApiImageService projectOpenApiImageService;
    private final OpenApiReportService projectOpenApiReportService;

    public ProjectEndpoint(
            @NonNull OpenApiProjectService openApiProjectService,
            @NonNull OpenApiImageService projectOpenApiImageService,
            @NonNull OpenApiReportService projectOpenApiReportService) {
        this.openApiProjectService = openApiProjectService;
        this.projectOpenApiImageService = projectOpenApiImageService;
        this.projectOpenApiReportService = projectOpenApiReportService;
        log.info(
                "Created {}(openApiProjectService={}, projectOpenApiImageService={},"
                        + " projectOpenApiReportService={})",
                this,
                openApiProjectService,
                projectOpenApiImageService,
                projectOpenApiReportService);
    }

    @PostMapping("")
    public ApiResponse createProject(
            @RequestBody ProjectMessage projectRequest,
            @AuthenticationPrincipal User user,
            @RequestHeader("Authorization") String token) {
        String insuredByName = projectRequest.getInsuredBy();
        Customer insuredBy =
                Customer.of(
                        com.bees360.customer.Message.CustomerMessage.newBuilder()
                                .setName(insuredByName)
                                .build());
        OpenApiProjectCreationRequest openapiProjectCreationRequest =
                new OpenApiProjectCreationRequest();
        openapiProjectCreationRequest.setToken(token.replaceAll("Bearer\\s+", ""));
        openapiProjectCreationRequest.setCreatedBy(user.getId());
        openapiProjectCreationRequest.setOpenApiProjectMessage(projectRequest);
        openapiProjectCreationRequest.setInsuredBy(insuredBy);
        ProjectMessage creationResult =
                openApiProjectService.createProject(openapiProjectCreationRequest);
        return ApiResponse.newBuilder().addProject(creationResult).build();
    }

    @GetMapping("/{projectId}")
    public ApiResponse getProjectDetails(@PathVariable long projectId) {
        return ApiResponse.newBuilder()
                .addProject(openApiProjectService.getProjectDetails(projectId))
                .build();
    }

    @GetMapping("/{projectId}/status")
    public ApiResponse getProjectStatus(@PathVariable long projectId) {
        return ApiResponse.newBuilder()
                .addProject(openApiProjectService.getProjectStatus(projectId))
                .build();
    }

    /**
     * Search the list of projects by project status. The list will be sorted base on the projectId
     * in ascending order.
     */
    @GetMapping("")
    public ApiResponse getProjectsByStatus(
            @RequestParam(value = "status", required = false) String projectStatus,
            @RequestParam(value = "limit", required = false, defaultValue = "100") int countLimit,
            @RequestParam(value = "start", required = false, defaultValue = "0") String startId) {
        if (countLimit < 1 || countLimit > 500) {
            throw new IllegalArgumentException("The parameter limit must be between 1 and 500");
        }
        return ApiResponse.newBuilder()
                .addAllProject(
                        openApiProjectService.searchProjectsByStatus(
                                projectStatus, startId, countLimit))
                .build();
    }

    @PutMapping("/{projectId}/status")
    public ApiResponse updateProjectStatus(
            @PathVariable long projectId,
            @RequestBody SetStatusRequest statusRequest,
            @AuthenticationPrincipal User user) {
        var userId = user.getId();
        return ApiResponse.newBuilder()
                .addProject(
                        openApiProjectService.updateProjectStatus(projectId, statusRequest, userId))
                .build();
    }

    @GetMapping("/{projectId}/image")
    public ApiResponse getProjectImages(@PathVariable long projectId) {

        Iterable<Message.ImageMessage> projectImages =
                projectOpenApiImageService.getProjectImages(projectId);

        return ApiResponse.newBuilder()
                .addProject(
                        ProjectMessage.newBuilder()
                                .setId(projectId)
                                .addAllImage(projectImages)
                                .build())
                .build();
    }

    /**
     * When image archive is available to download, will return a 307 Temporary Redirect response,
     * which redirects to a location to download the image archive.
     *
     * <p>The default Content-Type is application/zip. Currently, we only support .zip format.
     */
    @GetMapping("/{projectId}/image/archive")
    public void getProjectImageArchive(@PathVariable long projectId) {
        throw new UnsupportedOperationException();
    }

    @GetMapping("/{projectId}/report")
    public ApiResponse getProjectAvailableReports(@PathVariable long projectId) {
        Iterable<? extends Report> reports =
                projectOpenApiReportService.listAvailableReportsInProject(projectId);
        List<Message.ReportMessage> reportMessages =
                OpenApiReport.toMessages(OpenApiReport.fromReports(reports));
        return ApiResponse.newBuilder()
                .addProject(
                        ProjectMessage.newBuilder()
                                .setId(projectId)
                                .addAllReport(reportMessages)
                                .build())
                .build();
    }
}
