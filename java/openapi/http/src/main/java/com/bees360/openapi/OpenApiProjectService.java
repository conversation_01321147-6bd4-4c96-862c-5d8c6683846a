package com.bees360.openapi;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.contract.ContractManager;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.status.OpenApiProjectStatusEnum;
import com.bees360.project.status.ProjectStatus;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.stream.Collectors;

@Log4j2
class OpenApiProjectService {
    private final ProjectIIManager projectIIManager;
    private final ProjectStatusManager projectStatusManager;
    private final ContactManager contactManager;
    private final ContractManager contractManager;

    private final OpenApiProjectCreator openApiProjectCreator;

    public OpenApiProjectService(
            ProjectIIManager projectIIManager,
            ProjectStatusManager projectStatusManager,
            ContactManager contactManager,
            ContractManager contractManager,
            OpenApiProjectCreator openApiProjectCreator) {
        this.projectIIManager = projectIIManager;
        this.projectStatusManager = projectStatusManager;
        this.contactManager = contactManager;
        this.contractManager = contractManager;
        this.openApiProjectCreator = openApiProjectCreator;
        log.info(
                "Created '{} (projectIIManager={},projectStatusProvider={},"
                        + " contactManager={},contractManager={})'",
                this,
                projectIIManager,
                projectStatusManager,
                contactManager,
                contractManager);
    }

    public Message.ProjectMessage createProject(
            OpenApiProjectCreationRequest openApiProjectCreationRequest) {
        return openApiProjectCreator.createProject(openApiProjectCreationRequest);
    }

    public Message.ProjectMessage getProjectDetails(long projectId) {
        var project = projectIIManager.findById(String.valueOf(projectId));
        if (project == null) {
            throw new NoSuchElementException(
                    String.format("Project with id: %s not found", projectId));
        }

        var contract =
                Optional.ofNullable(project.getContract())
                        .map(con -> contractManager.findById(con.getId()))
                        .orElse(null);

        ProjectII projectII = project;
        if (contract != null) {
            projectII =
                    ProjectII.from(
                            project.toMessage().toBuilder()
                                    .setContract(contract.toMessage())
                                    .build());
        }

        var contacts = contactManager.findByProjectId(String.valueOf(projectId));
        return OpenApiProject.from(projectII, contacts).toMessage();
    }

    public Message.ProjectMessage getProjectStatus(long projectId) {
        var status = projectStatusManager.getStatus(String.valueOf(projectId));
        if (status == null) {
            throw new NoSuchElementException(
                    String.format("Project status with id: %s not found", projectId));
        }
        var builder = Message.ProjectMessage.newBuilder();

        acceptIfNotNull(builder::setId, projectId);

        acceptIfNotNull(
                builder::setStatus,
                OpenApiProjectStatusEnum.valueOfProjectStatus(status.getStatus()).getName());

        return builder.build();
    }

    public List<Message.ProjectMessage> searchProjectsByStatus(
            String status, String startId, int countLimit) {
        Integer statusCode =
                Optional.ofNullable(status)
                        .filter(StringUtils::isNotEmpty)
                        .map(OpenApiProjectStatusEnum::valueOfName)
                        .map(OpenApiProjectStatusEnum::getProjectStatus)
                        .map(com.bees360.project.Message.ProjectStatus::getNumber)
                        .orElse(null);
        if (statusCode == null && StringUtils.isNotEmpty(status)) {
            throw new IllegalArgumentException("Invalid project status value: " + status);
        }
        var projects = projectIIManager.findByStatus(startId, statusCode, countLimit);
        return Iterables.toStream(projects)
                .map(OpenApiProject::from)
                .map(OpenApiProject::toMessage)
                .map(this::extractUsefulInfoForFindByStatus)
                .collect(Collectors.toList());
    }

    public Message.ProjectMessage updateProjectStatus(
            long projectId, Message.SetStatusRequest statusRequest, String userId) {
        OpenApiProjectStatusEnum statusToUpdate =
                OpenApiProjectStatusEnum.valueOfName(statusRequest.getValue());
        if (statusToUpdate == null || !statusToUpdate.isSupportUpdate()) {
            throw new UnsupportedOperationException(
                    String.format(
                            "update is not supported for status:%s", statusRequest.getValue()));
        }
        projectStatusManager.updateStatusWithComment(
                String.valueOf(projectId),
                statusToUpdate.getProjectStatus(),
                userId,
                statusRequest.getComment());
        return convertProjectStatusToProjectMessage(
                projectStatusManager.getStatus(String.valueOf(projectId)));
    }

    private Message.ProjectMessage convertProjectStatusToProjectMessage(ProjectStatus status) {
        return Message.ProjectMessage.newBuilder()
                .setId(Long.parseLong(status.getProjectId()))
                .setStatus(
                        OpenApiProjectStatusEnum.valueOfProjectStatus(status.getStatus()).getName())
                .build();
    }

    private Message.ProjectMessage extractUsefulInfoForFindByStatus(
            Message.ProjectMessage projectMessage) {
        Message.ProjectMessage.Builder builder = Message.ProjectMessage.newBuilder();
        builder.setId(projectMessage.getId());
        builder.setStreetAddress(projectMessage.getStreetAddress());
        builder.setCity(projectMessage.getCity());
        builder.setState(projectMessage.getState());
        builder.setZipcode(projectMessage.getZipcode());
        builder.setCountry(projectMessage.getCountry());
        builder.setServiceName(projectMessage.getServiceName());
        builder.setStatus(projectMessage.getStatus());
        return builder.build();
    }
}
