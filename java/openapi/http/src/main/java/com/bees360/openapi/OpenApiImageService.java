package com.bees360.openapi;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.image.Image;
import com.bees360.image.ImageTagCategoryEnum;
import com.bees360.image.ImageTagProvider;
import com.bees360.image.tag.ImageTag;
import com.bees360.project.image.ProjectImageProvider;
import com.bees360.report.image.ReportImageProvider;
import com.bees360.util.Iterables;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;

import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Log4j2
class OpenApiImageService {

    private final ProjectImageProvider projectImageProvider;
    private final ReportImageProvider reportImageProvider;
    private final ImageTagProvider imageTagManager;
    private final OpenApiReportService openApiReportService;

    private final String CATEGORY_TYPE_ROOF = "Roof";
    private final String SOURCE_TYPE_DRONE = "Drone";
    private final String SOURCE_TYPE_MOBILE = "Mobile";
    private final String SOURCE_TYPE_ANNOTATED = "Annotated";

    public OpenApiImageService(
            @NonNull ProjectImageProvider projectImageProvider,
            @NonNull ReportImageProvider reportImageProvider,
            @NonNull ImageTagProvider imageTagManager,
            @NonNull OpenApiReportService openApiReportService) {
        this.projectImageProvider = projectImageProvider;
        this.reportImageProvider = reportImageProvider;
        this.imageTagManager = imageTagManager;
        this.openApiReportService = openApiReportService;
        log.info(
                "Created {}(projectImageProvider={}, reportImageProvider={}, imageTagManager={},"
                        + " openApiReportService={}, projectGroupType={})",
                this,
                projectImageProvider,
                reportImageProvider,
                imageTagManager,
                openApiReportService);
    }

    public Iterable<Message.ImageMessage> getProjectImages(long projectId) {

        Iterable<? extends Image> images = projectImageProvider.findByProjectId(projectId + "");
        Set<String> imageIds =
                Iterables.toStream(images).map(Image::getId).collect(Collectors.toSet());
        Set<String> imageIdsInReport = findImagesInReport(projectId);

        var resultImages =
                CollectionUtils.subtract(imageIds, imageIdsInReport).stream()
                        .map(
                                imgId -> {
                                    var categoryTagMap = getImgTags(imgId);
                                    return buildImageMessage(imgId, categoryTagMap).build();
                                })
                        .collect(Collectors.toList());

        var imagesInReport =
                imageIdsInReport.stream()
                        .map(
                                imgId -> {
                                    var categoryTagMap = getImgTags(imgId);
                                    return buildImageMessage(imgId, categoryTagMap)
                                            .setSource(SOURCE_TYPE_ANNOTATED)
                                            .build();
                                })
                        .collect(Collectors.toList());

        return Stream.concat(resultImages.stream(), imagesInReport.stream())
                .sorted(
                        Comparator.comparing(Message.ImageMessage::getSource)
                                .thenComparing(Message.ImageMessage::getType)
                                .thenComparing(Message.ImageMessage::getDirection))
                .collect(Collectors.toList());
    }

    private Set<String> findImagesInReport(long projectId) {
        Set<String> imageIds = new HashSet<>();
        for (var report : openApiReportService.listAvailableReportsInProject(projectId)) {
            reportImageProvider
                    .findImagesByReportId(report.getId())
                    .forEach(img -> imageIds.add(img.getId()));
        }
        return imageIds;
    }

    private Message.ImageMessage.Builder buildImageMessage(
            String imageId, Map<String, ? extends ImageTag> imgTagMap) {
        var builder = Message.ImageMessage.newBuilder();
        acceptIfNotNull(builder::setId, imageId);
        // direction of tag is mapped to direction field in the returned message
        acceptIfNotNull(
                builder::setDirection,
                Optional.ofNullable(imgTagMap.get(ImageTagCategoryEnum.DIRECTION.getDisplay()))
                        .map(ImageTag::getTitle)
                        .orElse(null));
        // scope of tag is mapped to title field in the returned message
        acceptIfNotNull(
                builder::setType,
                Optional.ofNullable(imgTagMap.get(ImageTagCategoryEnum.SCOPE.getDisplay()))
                        .map(ImageTag::getTitle)
                        .orElse(null));
        // category of tag is mapped to source field in the returned message
        acceptIfNotNull(
                builder::setSource,
                Optional.ofNullable(imgTagMap.get(ImageTagCategoryEnum.CATEGORY.getDisplay()))
                        .map(ImageTag::getTitle)
                        .map(
                                title ->
                                        CATEGORY_TYPE_ROOF.equals(title)
                                                ? SOURCE_TYPE_DRONE
                                                : SOURCE_TYPE_MOBILE)
                        .orElse(null));
        return builder;
    }

    private Map<String, ? extends ImageTag> getImgTags(String imageId) {
        return Iterables.toStream(
                        imageTagManager.findByCategories(
                                imageId,
                                List.of(
                                        ImageTagCategoryEnum.DIRECTION,
                                        ImageTagCategoryEnum.SCOPE,
                                        ImageTagCategoryEnum.CATEGORY)))
                .collect(Collectors.toMap(ImageTag::getCategory, v -> v, (v1, v2) -> v1));
    }
}
