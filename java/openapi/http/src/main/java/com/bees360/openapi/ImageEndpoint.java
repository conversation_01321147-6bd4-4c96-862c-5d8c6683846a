package com.bees360.openapi;

import com.bees360.api.NotFoundException;
import com.bees360.http.util.UrlRedirection;
import com.bees360.image.Image;
import com.bees360.image.ImageManager;
import com.bees360.image.Message;

import jakarta.servlet.http.HttpServletResponse;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Log4j2
@RestController
@RequestMapping(
        value = "${http.openapi.endpoint:}/image",
        produces = MediaType.APPLICATION_JSON_VALUE,
        consumes = MediaType.APPLICATION_JSON_VALUE)
public class ImageEndpoint {

    private final ImageManager imageManager;

    public ImageEndpoint(@NonNull ImageManager imageManager) {
        this.imageManager = imageManager;
        log.info("Created {}(imageManager={})", this, imageManager);
    }

    /**
     * Get image by image id
     *
     * <p>If image with the specified imageId is found, the server may directly return the image
     * binary data or redirect the request to another location to access the image.
     */
    @GetMapping("/{imageId}/file")
    public void getImageFile(
            @PathVariable String imageId, HttpServletResponse httpServletResponse) {
        var image = imageManager.findById(imageId);
        var resourceUrl =
                Optional.ofNullable(image)
                        .map(Image::getResource)
                        .map(
                                resources -> {
                                    for (var resource : resources) {
                                        if (Objects.equals(
                                                Message.ImageMessage.Resource.Type.LARGE,
                                                resource.getType())) {
                                            return resource.getUrl();
                                        }
                                    }
                                    return null;
                                })
                        .orElse(null);

        if (StringUtils.isEmpty(resourceUrl)) {
            throw new NotFoundException(String.format("File for image %s not found.", imageId));
        }
        UrlRedirection.redirectTemporary(
                httpServletResponse, MediaType.IMAGE_JPEG_VALUE, resourceUrl);
    }
}
