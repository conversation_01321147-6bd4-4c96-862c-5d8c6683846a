package com.bees360.openapi;

import com.bees360.http.HttpClient;
import com.bees360.oauth.OAuthHttpClient;
import com.bees360.oauth.OAuthToken;
import com.google.common.base.Preconditions;

import lombok.extern.log4j.Log4j2;

import java.net.URI;

@Log4j2
public class HttpOpenApiProjectCreator implements OpenApiProjectCreator {

    private final HttpClient httpClient;

    private final URI context;

    public HttpOpenApiProjectCreator(HttpClient httpClient, URI context) {
        this.httpClient = httpClient;
        this.context = context;
        log.info("start http project creator:{}", this.getClass().getName());
    }

    @Override
    public Message.ProjectMessage createProject(ProjectCreationRequest projectCreationRequest) {
        Preconditions.checkArgument(
                projectCreationRequest instanceof OpenApiProjectCreationRequest,
                "project creation request does not contain token");
        OpenApiProjectCreationRequest openapiProjectCreationRequest =
                (OpenApiProjectCreationRequest) projectCreationRequest;
        HttpOpenapiClient httpOpenapiClient =
                initializeOpenApiClient(openapiProjectCreationRequest.getToken());
        return httpOpenapiClient.createProject(
                ((OpenApiProjectCreationRequest) projectCreationRequest)
                        .getOpenApiProjectMessage());
    }

    private HttpOpenapiClient initializeOpenApiClient(String token) {
        OAuthToken oAuthToken = OAuthToken.of(token, null, null);
        OAuthHttpClient oAuthHttpClient = new OAuthHttpClient(httpClient, oAuthToken);
        return new HttpOpenapiClient(context, oAuthHttpClient);
    }
}
