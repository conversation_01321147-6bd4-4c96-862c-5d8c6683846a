package com.bees360.openapi;

import com.bees360.api.NotFoundException;
import com.bees360.http.util.UrlRedirection;

import jakarta.servlet.http.HttpServletResponse;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.ValueConstants;

import java.util.List;

/**
 * <AUTHOR>
 */
@Log4j2
@RestController
@RequestMapping(
        value = "${http.openapi.endpoint:}/report",
        produces = MediaType.APPLICATION_JSON_VALUE,
        consumes = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class ReportEndpoint {

    private final OpenApiReportService openApiReportService;

    /**
     * If the report is available, will return a 307 Temporary Redirect response, which redirects to
     * a location to download the report.
     *
     * <p>The default Content-Type will be application/pdf. Currently we only supported .pdf format.
     */
    @GetMapping("/{reportId}/file")
    public void getReportFile(
            @PathVariable String reportId,
            @RequestParam(required = false) String compressed,
            HttpServletResponse response) {
        Boolean isCompressed;

        if (compressed == null || ValueConstants.DEFAULT_NONE.equals(compressed)) {
            isCompressed = null;
        } else {
            isCompressed = !compressed.equals("false");
        }
        String url = openApiReportService.provideReportResourceUrl(reportId, isCompressed);
        UrlRedirection.redirectTemporary(response, MediaType.APPLICATION_PDF_VALUE, url);
    }

    @GetMapping("/{reportId}/summary")
    public SummaryEndpointResult getReportSummary(@PathVariable String reportId) {
        Message.ReportMessage report = openApiReportService.getReportWithSummary(reportId);
        if (report == null) {
            throw new NotFoundException(String.format("report '%s' cannot be found", reportId));
        }

        return new SummaryEndpointResult(
                SummaryReport.of(
                        report.getId(),
                        report.getType(),
                        ProtoSummaryProject.from(report.getProject()),
                        ProtoSummary.from(report.getSummary())));
    }

    @Data
    static class SummaryEndpointResult {
        private Iterable<? extends SummaryReport> report;

        public SummaryEndpointResult(Iterable<? extends SummaryReport> summaryReports) {
            this.report = summaryReports;
        }

        public SummaryEndpointResult(SummaryReport summaryReport) {
            this(List.of(summaryReport));
        }
    }
}
