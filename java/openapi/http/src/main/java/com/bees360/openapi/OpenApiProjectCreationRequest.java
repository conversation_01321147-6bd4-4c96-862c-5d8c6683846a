package com.bees360.openapi;

import static com.bees360.openapi.util.OpenApiProjectMessageConverter.extractAgentContact;
import static com.bees360.openapi.util.OpenApiProjectMessageConverter.extractInspection;
import static com.bees360.openapi.util.OpenApiProjectMessageConverter.extractInsuredContact;

import com.bees360.customer.Customer;
import com.bees360.openapi.util.OpenApiProjectMessageConverter;
import com.bees360.policy.Policy;
import com.bees360.project.Contact;
import com.bees360.project.inspection.Inspection;
import com.bees360.project.underwriting.Underwriting;
import com.bees360.util.Functions;
import com.bees360.util.JwtTokenParser;
import com.google.common.base.Preconditions;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Getter
@Setter
public class OpenApiProjectCreationRequest implements OpenApiProjectCreator.ProjectCreationRequest {

    private static final String BEES360_COMPANY_ID = "1062";

    private static final String BEES360_TEST_CARRIER_ID = "2744";

    private String token;

    private String createdBy;

    private Message.ProjectMessage openApiProjectMessage;

    private Customer insuredBy;

    @Override
    public Optional<Customer> getInsuredBy() {
        return Optional.ofNullable(insuredBy);
    }

    @Override
    public Optional<Customer> getProcessedBy() {
        return Optional.of(extractProcessedByFromToken());
    }

    @Override
    public Policy getPolicy() {
        return OpenApiProjectMessageConverter.extractPolicy(openApiProjectMessage);
    }

    @Override
    public Optional<Inspection> getInspection() {
        return Optional.ofNullable(extractInspection(openApiProjectMessage));
    }

    @Override
    public Underwriting getUnderwriting() {
        String serviceName = openApiProjectMessage.getServiceName();
        OpenApiServiceTypeEnum openApiServiceTypeEnum =
                OpenApiServiceTypeEnum.valueOfName(serviceName);
        Preconditions.checkArgument(openApiServiceTypeEnum != null, "Invalid service type");
        Underwriting underwriting =
                Underwriting.UnderwritingBuilder.newBuilder()
                        .setServiceType(openApiServiceTypeEnum.getServiceType())
                        .build();
        return underwriting;
    }

    @Override
    public Collection<Contact> getContacts() {
        ArrayList<Contact> list = new ArrayList<>(2);
        Functions.acceptIfNotNull(list::add, extractAgentContact(openApiProjectMessage));
        Functions.acceptIfNotNull(list::add, extractInsuredContact(openApiProjectMessage));
        return list;
    }

    private Customer extractProcessedByFromToken() {
        Map<String, Object> map = JwtTokenParser.parseToken(this.token);
        String userCompanyId = (String) map.get("company_id");
        Preconditions.checkArgument(userCompanyId != null, "No company id is found with jwt token");
        String processedBy = userCompanyId;
        if (Objects.equals(userCompanyId, BEES360_COMPANY_ID)) {
            processedBy = BEES360_TEST_CARRIER_ID;
        }
        return Customer.of(
                com.bees360.customer.Message.CustomerMessage.newBuilder()
                        .setId(processedBy)
                        .build());
    }
}
