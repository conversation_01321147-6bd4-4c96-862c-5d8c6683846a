package com.bees360.openapi.project;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.openapi.Message;
import com.bees360.openapi.ProjectEndpointITestBase;
import com.bees360.project.status.OpenApiProjectStatusEnum;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import java.net.URL;

@Log4j2
@ActiveProfiles("sandbox-test")
@ApplicationAutoConfig
public class ProjectEndpointITest extends ProjectEndpointITestBase {

    @Test
    public void testGetS3URI() {
        URL url = resourcePool.asResourceUrlProvider().getGetUrl("test");
        log.debug("url:{}", url);
        Assertions.assertNotNull(url);
    }

    @Test
    public void testGetProjectImages() {
        mockReportsInGroup();
        int imageSize = mockImagesInGroup();
        mockImageTags();
        long projectId = RandomUtils.nextLong();
        Message.ApiResponse projectImages = projectEndpoint.getProjectImages(projectId);
        log.debug("resp:{}", projectImages);
        Assertions.assertNotNull(projectImages);
        Assertions.assertEquals(imageSize, projectImages.getProject(0).getImageList().size());
        Assertions.assertEquals(projectId, projectImages.getProject(0).getId());
    }

    @Test
    public void testFilterProjectImages() {
        mockReportsInGroup();
        mockImageTags();
        long projectId = RandomUtils.nextLong();
        Message.ApiResponse projectImages = projectEndpoint.getProjectImages(projectId);
        log.debug("resp:{}", projectImages);
        Assertions.assertNotNull(projectImages);
        Assertions.assertEquals(projectId, projectImages.getProject(0).getId());
    }

    @Test
    public void testAvailableReportsEmpty() {
        long projectId = RandomUtils.nextLong();
        Message.ApiResponse reports = projectEndpoint.getProjectAvailableReports(projectId);
        log.debug("resp:{}", reports);
        Assertions.assertEquals(0, reports.getProject(0).getReportCount());
    }

    @Test
    public void testAvailableReports() {
        int reportSize = mockReportsInGroup();
        long projectId = RandomUtils.nextLong();
        Message.ApiResponse reports = projectEndpoint.getProjectAvailableReports(projectId);
        log.debug("resp:{}", reports);
        Assertions.assertEquals(reportSize, reports.getProject(0).getReportCount());
    }

    @Test
    public void testEmptyReports() {
        long projectId = RandomUtils.nextLong();
        Message.ApiResponse reports = projectEndpoint.getProjectAvailableReports(projectId);
        log.debug("resp:{}", reports);
        Assertions.assertNotNull(reports.getReportList());
        Assertions.assertEquals(projectId, reports.getProject(0).getId());
    }

    @Test
    public void testFindByStatusWithNullCondition() {
        int size = mockProjectIIManagerWithAll();
        Message.ApiResponse projectsByStatus =
                projectEndpoint.getProjectsByStatus(null, size, null);
        log.debug("resp:{}", projectsByStatus);
        Assertions.assertEquals(size, projectsByStatus.getProjectList().size());
    }

    @Test
    public void testFindByStatusWithCreatedStatus() {
        int size = mockProjectIIManagerWithCreatedStatus();
        Message.ApiResponse projectsByStatus =
                projectEndpoint.getProjectsByStatus(
                        OpenApiProjectStatusEnum.valueOfProjectStatus(
                                        com.bees360.project.Message.ProjectStatus.PROJECT_CREATED)
                                .getName(),
                        size,
                        null);
        log.debug("resp:{}", projectsByStatus);
        Assertions.assertEquals(size, projectsByStatus.getProjectList().size());
    }

    @Test
    public void testFindByStatusWithReturnToClientStatus() {
        int size = mockProjectIIManagerWithReturnToClientStatus();
        Message.ApiResponse projectsByStatus =
                projectEndpoint.getProjectsByStatus(
                        OpenApiProjectStatusEnum.valueOfProjectStatus(
                                        com.bees360.project.Message.ProjectStatus
                                                .RETURNED_TO_CLIENT)
                                .getName(),
                        size,
                        null);
        log.debug("resp:{}", projectsByStatus);
        Assertions.assertEquals(size, projectsByStatus.getProjectList().size());
    }
}
