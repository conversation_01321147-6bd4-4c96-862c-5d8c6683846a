package com.bees360.openapi;

import com.bees360.address.AddressRepository;
import com.bees360.address.JooqAddressRepository;
import com.bees360.building.JooqBuildingRepository;
import com.bees360.contract.ContractManager;
import com.bees360.contract.JooqContractRepository;
import com.bees360.customer.JooqCustomerRepository;
import com.bees360.image.config.JooqImageManagerConfig;
import com.bees360.image.config.JooqImageResourceManagerConfig;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.policy.JooqPolicyRepository;
import com.bees360.project.BuildingManager;
import com.bees360.project.JooqProjectContactRepository;
import com.bees360.project.JooqProjectIIRepository;
import com.bees360.project.JooqProjectStatusRepository;
import com.bees360.project.ProjectIIManager;
import com.bees360.report.JooqReportGroupManager;
import com.bees360.report.JooqReportManager;
import com.bees360.report.JooqReportResourceManager;
import com.bees360.report.ReportManager;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.config.S3ResourceRepositoryFactoryConfig;
import com.bees360.resource.factory.S3ResourceRepositoryFactory;
import com.bees360.resource.util.ContentTypes;
import com.bees360.user.config.GrpcUserProviderConfig;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.net.URI;
import java.util.function.BiFunction;

@Configuration
@Import(
        value = {
            JooqConfig.class,
            JooqImageResourceManagerConfig.class,
            S3ResourceRepositoryFactoryConfig.class,
            JooqProjectContactRepository.class,
            JooqProjectStatusRepository.class,
            JooqCustomerRepository.class,
            JooqImageManagerConfig.class,
            GrpcUserProviderConfig.class,
        })
public class OpenApiJooqConfig {

    @Bean
    ResourcePool resourcePool(
            S3ResourceRepositoryFactory s3ResourceRepositoryFactory,
            @Value("${openapi.resource.uri}") String openApiResourceURI) {
        return s3ResourceRepositoryFactory.get(URI.create(openApiResourceURI));
    }

    @Bean
    JooqReportResourceManager jooqReportResourceManager(
            DSLContext dslContext, ResourcePool resourcePool) {
        return new JooqReportResourceManager(dslContext, resourcePool);
    }

    @Bean
    public BiFunction<String, String, String> fileExtensionProvider() {
        return (contentType, uri) -> {
            String extension = ContentTypes.getFileExtension(contentType);
            if (StringUtils.isNotBlank(extension)) {
                return extension;
            }
            if (StringUtils.isNotBlank(uri)) {
                var path = URI.create(uri).getPath();
                extension = FilenameUtils.getExtension(path);
                extension =
                        StringUtils.isBlank(extension)
                                ? extension
                                : FilenameUtils.EXTENSION_SEPARATOR + extension;
            }
            return extension;
        };
    }

    // TODO use the real implementation of summary version provider, openApi service only uses
    //  reportProvider interface, so summary version provider can be null here
    @Bean
    JooqReportManager jooqReportManager(
            DSLContext dslContext,
            JooqReportResourceManager jooqReportResourceManager,
            ResourcePool resourcePool,
            BiFunction<String, String, String> fileExtensionProvider) {
        return new JooqReportManager(
                dslContext,
                jooqReportResourceManager,
                null,
                resourcePool,
                resourcePool.asResourceUrlProvider(),
                fileExtensionProvider);
    }

    @Bean
    JooqReportGroupManager jooqReportGroupManager(
            DSLContext dslContext, ReportManager jooqReportManager) {
        return new JooqReportGroupManager(dslContext, jooqReportManager);
    }

    @Bean
    AddressRepository addressProvider(DSLContext dslContext) {
        return new JooqAddressRepository(dslContext);
    }

    @Bean
    BuildingManager buildingManager(DSLContext dslContext) {
        return new JooqBuildingRepository(dslContext);
    }

    @Bean
    ProjectIIManager baseProjectIIManager(DSLContext dslContext) {
        return new JooqProjectIIRepository(dslContext);
    }

    @Bean
    ContractManager contractManager(DSLContext dslContext) {
        return new JooqContractRepository(dslContext);
    }

    @Bean
    JooqPolicyRepository basePolicyManager(DSLContext dslContext) {
        return new JooqPolicyRepository(dslContext);
    }
}
