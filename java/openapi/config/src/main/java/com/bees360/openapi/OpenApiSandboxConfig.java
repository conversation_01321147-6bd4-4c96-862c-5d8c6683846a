package com.bees360.openapi;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.activity.ActivityManager;
import com.bees360.address.Address;
import com.bees360.address.AddressRepository;
import com.bees360.address.ForwardingAddressProvider;
import com.bees360.auth.config.BasicWebSecurityConfig;
import com.bees360.auth.config.JwtAuthorizationServerConfig;
import com.bees360.auth.token.JwtTokenEnhancer;
import com.bees360.contract.ContractManager;
import com.bees360.customer.CustomerProvider;
import com.bees360.event.AutoRecoveryPostgresEventDispatcher;
import com.bees360.event.EventDispatcher;
import com.bees360.event.EventListener;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.PostgresEventListener;
import com.bees360.event.config.AutoRecoveryPostgresEventDispatcherConfig;
import com.bees360.event.util.EventListeners;
import com.bees360.event.util.ForwardingSplitEventPublisher;
import com.bees360.policy.PolicyManager;
import com.bees360.project.BuildingManager;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.listener.EmitActivityOnStatusChanged;
import com.bees360.user.Account;
import com.bees360.user.AccountProvider;
import com.bees360.user.Message.AccountMessage;
import com.bees360.user.UserProvider;
import com.google.common.collect.Iterables;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.jooq.tools.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2ClientCredentialsAuthenticationToken;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;

@Log4j2
@Configuration
@Import({
    OpenApiSandboxConfig.AuthConfig.class,
    JwtAuthorizationServerConfig.class,
    DataSourceAutoConfiguration.class,
    OpenApiJooqConfig.class,
    AutoRecoveryPostgresEventDispatcherConfig.class,
})
@ConditionalOnProperty(prefix = "openapi.instance", name = "type", havingValue = "single")
public class OpenApiSandboxConfig {

    @Import({
        BasicWebSecurityConfig.class,
    })
    @Configuration
    public static class AuthConfig {

        @Bean
        public PasswordEncoder passwordEncoder() {
            return new Md5BCryptPasswordEncoder();
        }

        @Data
        @ConfigurationProperties(prefix = "openapi.security")
        public static class OpenApiSecurityProperties {

            private List<UsernamePassword> accounts = new ArrayList<>();

            @Data
            public static class UsernamePassword {
                private String username;
                private String password;
            }
        }

        @Bean
        public OpenApiSecurityProperties openApiSecurityProperties() {
            return new OpenApiSecurityProperties();
        }

        @Bean
        public AccountProvider userCredentialsProvider(
                UserProvider userProvider, OpenApiSecurityProperties openApiSecurityProperties) {

            var accountProvider =
                    new AccountProvider() {

                        @Override
                        public Account findAccountById(String username) {

                            var account =
                                    openApiSecurityProperties.getAccounts().stream()
                                            .filter(
                                                    a ->
                                                            StringUtils.equals(
                                                                    a.getUsername(), username))
                                            .findFirst();
                            if (account.isEmpty()) {
                                return null;
                            }
                            var user =
                                    Iterables.tryFind(
                                            userProvider.findUserByEmail(username),
                                            u -> StringUtils.equals(u.getEmail(), username));
                            if (!user.isPresent()) {
                                return null;
                            }
                            return Account.from(
                                    AccountMessage.newBuilder()
                                            .setUser(user.get().toMessage())
                                            .setSecret(account.get().getPassword())
                                            .setId(username)
                                            .build());
                        }

                        @Override
                        public Account findAccountByAuthCode(String authCode) {
                            return null;
                        }
                    };
            log.info("Created {}", accountProvider);
            return accountProvider;
        }

        @Bean
        public JwtTokenEnhancer clientCredentialsTokenEnhancer(AccountProvider accountProvider) {
            return context -> {
                if (!(context.getAuthorizationGrant()
                        instanceof OAuth2ClientCredentialsAuthenticationToken)) {
                    return;
                }
                var map =
                        ((OAuth2ClientCredentialsAuthenticationToken)
                                        context.getAuthorizationGrant())
                                .getAdditionalParameters();
                var username = (String) map.get("username");
                var account = accountProvider.findAccountById(username);
                var user = account.getUser();
                var claimBuilder = context.getClaims();

                acceptIfNotNull((param) -> claimBuilder.claim("id", param), user.getId());
                acceptIfNotNull((param) -> claimBuilder.claim("user_id", param), user.getId());
                acceptIfNotNull((param) -> claimBuilder.claim("email", param), user.getEmail());
                acceptIfNotNull((param) -> claimBuilder.claim("name", param), user.getName());
                acceptIfNotNull((param) -> claimBuilder.claim("phone", param), user.getPhone());
                acceptIfNotNull(
                        (param) -> claimBuilder.claim("authorities", param),
                        user.getAllAuthority());
                acceptIfNotNull(
                        (param) -> claimBuilder.claim("photo", param),
                        user.getPhoto(),
                        URL::toString);
            };
        }
    }

    @Configuration
    @EnableConfigurationProperties
    @ConfigurationProperties(prefix = "event.app.postgres")
    @Data
    static class PostgresProperties {
        List<String> eventNames;
        String channel;
    }

    @Configuration
    static class StartUpConfig {

        StartUpConfig(
                AutoRecoveryPostgresEventDispatcher postgresEventDispatcher,
                EventPublisher rabbitEventPublisher,
                PostgresProperties properties,
                List<EventListener> eventListenerList,
                EventDispatcher eventDispatcher) {
            for (String name : properties.getEventNames()) {
                postgresEventDispatcher.enlist(
                        EventListeners.forwardToPublisher(name, rabbitEventPublisher));
            }

            postgresEventDispatcher.enlist(
                    new PostgresEventListener(properties.getChannel(), rabbitEventPublisher));

            eventListenerList.forEach(eventDispatcher::enlist);
        }
    }

    @Bean(name = {"eventDispatcher", "eventPublisher"})
    public EventPublisher eventPublisher() {
        return new ForwardingSplitEventPublisher(
                new InMemoryEventPublisher(Executors.newCachedThreadPool()));
    }

    @Bean
    public EventListener emitActivityOnStatusChanged(ActivityManager activityManager) {
        return new EmitActivityOnStatusChanged(activityManager);
    }

    @Bean
    public OpenApiProjectCreator localProjectCreatorFacade(
            PolicyManager policyManager,
            ContractManager contractManager,
            BuildingManager buildingManager,
            AddressRepository addressRepository,
            ProjectIIManager projectIIManager,
            ContactManager contactManager,
            CustomerProvider customerProvider) {
        ForwardingAddressProvider forwardingAddressProvider =
                new ForwardingAddressProvider(addressRepository) {

                    @Override
                    public Address normalize(Address address) {
                        String addressId = addressRepository.save(address);
                        return addressRepository.findById(addressId);
                    }
                };
        return new DefaultOpenApiProjectCreator(
                policyManager,
                contractManager,
                buildingManager,
                forwardingAddressProvider,
                projectIIManager,
                contactManager,
                customerProvider);
    }
}
