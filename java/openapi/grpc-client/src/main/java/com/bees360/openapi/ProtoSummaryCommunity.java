package com.bees360.openapi;

import com.google.protobuf.Struct;
import com.google.protobuf.Value;

import lombok.NonNull;

import java.util.Map;

public class ProtoSummaryCommunity implements SummaryCommunity {
    private final Map<String, Value> additionalProperties;

    public ProtoSummaryCommunity(@NonNull Struct additionalProperties) {
        this.additionalProperties = additionalProperties.getFieldsMap();
    }

    public static ProtoSummaryCommunity from(Struct additionalProperties) {
        return additionalProperties == null
                ? null
                : new ProtoSummaryCommunity(additionalProperties);
    }

    @Override
    public Integer getNumResidentialUnits() {
        return Protobufs.intValue(additionalProperties.get("numResidentialUnits"));
    }

    @Override
    public Integer getNumBldg() {
        return Protobufs.intValue(additionalProperties.get("numBldg"));
    }

    @Override
    public Boolean getHasBalcony() {
        return Protobufs.booleanValue(additionalProperties.get("hasBalcony"));
    }

    @Override
    public Boolean getPetsAllowed() {
        return Protobufs.booleanValue(additionalProperties.get("petsAllowed"));
    }

    @Override
    public Boolean getHasTennisCourt() {
        return Protobufs.booleanValue(additionalProperties.get("hasTennisCourt"));
    }

    @Override
    public Boolean getHasPlayground() {
        return Protobufs.booleanValue(additionalProperties.get("hasPlayground"));
    }

    @Override
    public Boolean getHasBasketballCourt() {
        return Protobufs.booleanValue(additionalProperties.get("hasBasketballCourt"));
    }
}
