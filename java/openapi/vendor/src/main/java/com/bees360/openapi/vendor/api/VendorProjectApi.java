/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.5.0).
 * https://openapi-generator.tech Do not edit the class manually.
 */
package com.bees360.openapi.vendor.api;

import com.bees360.openapi.vendor.model.VendorProjectStatusRequest;
import com.bees360.user.User;

import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.annotation.Generated;
import javax.validation.Valid;

@Generated(
        value = "org.openapitools.codegen.languages.SpringCodegen",
        comments = "Generator version: 7.5.0")
@Validated
public interface VendorProjectApi {

    /**
     * PUT /vendor/project/{projectId}/status : update project status for vendor update or rollback
     * project status
     *
     * @param projectId The project ID previously returned when creating the project. (required)
     * @param vendorProjectStatusRequest VendorProjectStatusRequest (required)
     * @return CommonResponse (status code 200)
     */
    @RequestMapping(
            method = RequestMethod.PUT,
            value = "/vendor/project/{projectId}/status",
            produces = {"application/json"},
            consumes = {"application/json"})
    @ResponseStatus(HttpStatus.OK)
    void vendorUpdateProjectStatus(
            @AuthenticationPrincipal User user,
            @PathVariable("projectId") String projectId,
            @Valid @RequestBody VendorProjectStatusRequest vendorProjectStatusRequest);
}
