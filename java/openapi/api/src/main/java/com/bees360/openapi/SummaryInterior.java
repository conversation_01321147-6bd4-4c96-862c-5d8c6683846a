package com.bees360.openapi;

import com.bees360.api.Proto;
import com.bees360.openapi.Message.ReportMessage.Summary.Interior;

public interface SummaryInterior extends Proto<Interior> {

    /** Conditional of overall interior Excellent-5, Good-4, Average3, Fair-2, Poor-1 Required */
    String getOverallCondition();
    /** If active or inactive leaks are visible Optional */
    Boolean getHasVisibleLeaks();
    /** If there is existing interior damage is present Optional */
    Boolean getHasExistingDamage();

    SummaryInteriorPlumbing getPlumbing();

    SummaryInteriorElectric getElectric();

    SummaryInteriorFloorplan getFloorplan();

    SummaryInteriorWaterHeater getWaterHeater();

    SummaryInteriorBurglarAlarm getBurglarAlarm();

    SummaryInteriorFireAlarm getFireAlarm();

    SummaryInteriorHeatingCooling getHeatingCooling();

    /** List of text comments to interior. */
    Iterable<String> getComments();
}
