package com.bees360.symbility.config;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

public class SymbilityProperties {
    private SymbilityProperties() {}

    @Data
    @Configuration
    @ConfigurationProperties(prefix = "symbility.account")
    public static class SymbilityAccountProperties {
        private String userId;
        private String userIdType;
        private String timelineUserId;
        private String timelineUserIdType;
    }

    @Data
    @Configuration
    @ConfigurationProperties(prefix = "symbility.auth")
    public static class SymbilityAuthProperties {
        /** access token过期时间安全间隔 */
        private Duration accessTokenExpSafePadding = Duration.ofMinutes(10);
        /** oauth 配置数组中 Symbility 对象字段名 */
        private String oauthPropertyKey = "symbility";
    }
}
