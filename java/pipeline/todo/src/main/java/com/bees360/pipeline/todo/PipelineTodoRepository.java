package com.bees360.pipeline.todo;

import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.todo.Message;

import java.time.Instant;
import java.util.Map;

import javax.annotation.Nullable;

public interface PipelineTodoRepository {
    void addOrUpdateTodo(
            String user,
            String projectId,
            String taskKey,
            PipelineStatus status,
            Instant updatedAt,
            @Nullable String comment,
            String updatedBy,
            Message.PipelineTask.Relationship todoRelationship);

    default void addOrUpdateTodo(
            String user,
            String projectId,
            String taskKey,
            PipelineStatus status,
            Instant updatedAt,
            @Nullable String comment,
            String updatedBy) {
        addOrUpdateTodo(
                user,
                projectId,
                taskKey,
                status,
                updatedAt,
                comment,
                updatedBy,
                Message.PipelineTask.Relationship.OWNER);
    }

    default void addOrUpdateTodo(
            String user,
            String projectId,
            String taskKey,
            PipelineStatus status,
            Instant updatedAt,
            @Nullable String comment) {
        addOrUpdateTodo(user, projectId, taskKey, status, updatedAt, comment, null);
    }

    default void addOrUpdateTodo(
            String user,
            String projectId,
            String taskKey,
            PipelineStatus status,
            Instant updatedAt) {
        addOrUpdateTodo(user, projectId, taskKey, status, updatedAt, null, null);
    }

    void changeTodoOwner(
            String oldUser,
            String user,
            String projectId,
            String taskKey,
            @Nullable Instant updatedAt);

    default void changeTodoOwner(String oldUser, String user, String projectId, String taskKey) {
        changeTodoOwner(oldUser, user, projectId, taskKey, null);
    }

    void batchChangeTodoOwner(Map<String, String> todoOwnerMap);

    void deleteTodo(@Nullable String user, String projectId, String taskKey, Instant updatedAt);

    void completeTodo(
            @Nullable String user,
            String projectId,
            String taskKey,
            Instant updatedAt,
            String completedBy);
}
