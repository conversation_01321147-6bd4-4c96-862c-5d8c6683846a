package com.bees360.pipeline.todo;

import com.bees360.jooq.config.JooqConfig;
import com.bees360.pipeline.PipelineService;
import com.bees360.todo.TodoManager;
import com.bees360.todo.TodoRepository;

import org.jooq.DSLContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@Import({JooqConfig.class})
public class PipelineTodoRepositoryConfig {
    @Bean
    JooqPipelineTodoRepository pipelineTodoRepository(
            DSLContext dsl,
            TodoRepository commonPipelineTodoRepository,
            PipelineService pipelineService,
            TodoManager jooqTodoManager) {
        return new JooqPipelineTodoRepository(
                dsl, commonPipelineTodoRepository, pipelineService, jooqTodoManager);
    }
}
