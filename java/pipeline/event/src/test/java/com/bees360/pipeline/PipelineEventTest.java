package com.bees360.pipeline;

import static com.bees360.pipeline.Message.PipelineStatus.DONE;
import static com.bees360.pipeline.Message.PipelineStatus.ONGOING;
import static com.bees360.pipeline.Message.PipelineStatus.PENDING;
import static com.bees360.pipeline.Message.PipelineStatus.READY;
import static com.bees360.pipeline.TestUtils.createPipelineDef;
import static com.bees360.pipeline.TestUtils.randomKey;
import static com.bees360.pipeline.TestUtils.randomOwner;
import static com.bees360.pipeline.TestUtils.randomPipelineDef;
import static com.bees360.pipeline.TestUtils.randomTaskDef;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.EventListener;
import com.bees360.event.EventPublisher;
import com.bees360.event.PostgresEventDispatcher;
import com.bees360.event.PostgresEventListener;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.event.config.PostgresEventDispatcherConfig;
import com.bees360.event.registry.PipelineChanged;
import com.bees360.event.registry.PipelineStageChanged;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.event.util.AbstractUnnamedEventListener;
import com.bees360.event.util.EventListeners;
import com.bees360.rabbit.config.RabbitApiConfig;

import lombok.Data;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

@SpringBootTest
@ApplicationAutoConfig
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:PipelineITest.yml")
public class PipelineEventTest {

    static class PipelineTaskChangeListener
            extends AbstractUnnamedEventListener<PipelineTaskChanged> {
        private final PipelineTaskChanged excepted;
        private final AtomicBoolean visited;

        public PipelineTaskChangeListener(PipelineTaskChanged excepted, AtomicBoolean visited) {
            this.excepted = excepted;
            this.visited = visited;
        }

        @Override
        public void handle(PipelineTaskChanged event) {
            assertNotNull(event, "Pipeline task changed event should not be null");
            assertEquals(
                    excepted.getPipelineId(),
                    event.getPipelineId(),
                    "The pipeline id should be equals.");
            assertEquals(
                    excepted.getTaskDefKey(),
                    event.getTaskDefKey(),
                    "The task def key should be equals");
            var state = event.getState();
            var oldState = event.getOldState();
            assertEquals(
                    excepted.getOldState().getStatus(),
                    oldState.getStatus(),
                    "The old status should 'PENDING'");
            assertEquals(
                    excepted.getState().getOwnerId(),
                    state.getOwnerId(),
                    "The owner should be equals");
            assertEquals(
                    excepted.getState().getStatus(),
                    state.getStatus(),
                    "The task status should be equals.");
            assertEquals(
                    excepted.getComment(),
                    event.getComment(),
                    "The task comment should be equals.");
            assertEquals(
                    excepted.getUpdatedBy(),
                    event.getUpdatedBy(),
                    "The task updatedBy should be equals.");
            visited.set(true);
        }
    }

    @Configuration
    @Import(
            value = {
                JooqPipelineConfig.class,
                RabbitEventDispatcher.class,
                PostgresEventDispatcherConfig.class,
                RabbitEventPublisher.class,
                RabbitApiConfig.class,
                SetPipelineStateOnStageStateChanged.class,
            })
    static class Config {
        @ConfigurationProperties(prefix = "postgres")
        @EnableConfigurationProperties
        @Data
        static class EventProperties {
            private List<String> eventNames;
            private String channel;
        }
    }

    public PipelineEventTest(
            @Autowired Config.EventProperties eventProperties,
            @Autowired RabbitEventDispatcher rabbitEventDispatcher,
            @Autowired PostgresEventDispatcher postgresEventDispatcher,
            @Autowired List<EventListener> eventListeners,
            @Autowired EventPublisher eventPublisher) {
        for (String eventName : eventProperties.eventNames) {
            postgresEventDispatcher.enlist(
                    EventListeners.forwardToPublisher(eventName, eventPublisher));
        }
        postgresEventDispatcher.enlist(
                new PostgresEventListener(eventProperties.getChannel(), eventPublisher));
        eventListeners.forEach(e -> rabbitEventDispatcher.enlist(e, 1));
    }

    @Autowired private PipelineService pipelineService;
    @Autowired private PipelineDefService pipelineDefService;
    @Autowired private RabbitEventDispatcher eventDispatcher;

    @Value("${bees360.system-user:10000}")
    public String SYSTEM_USER;

    @Test
    public void testTaskChangeListener() throws InterruptedException {
        // create pipelineDefService
        PipelineTaskDef taskDef1 = randomTaskDef();
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDef1));
        var taskDefKey = taskDef1.getKey();
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        var owner = randomOwner().getId();
        var changedBy = randomOwner().getId();
        AtomicBoolean visited = new AtomicBoolean();
        pipelineService.createPipeline(pipelineId, defKey);
        pipelineService.setTaskOwner(pipelineId, taskDefKey, owner, changedBy);
        var event = new PipelineTaskChanged();
        event.setPipelineId(pipelineId);
        event.setTaskDefKey(taskDefKey);
        event.setUpdatedBy(SYSTEM_USER);
        event.setState(PipelineTaskChanged.State.newBuilder().ownerId(owner).status(DONE).build());
        event.setOldState(PipelineTaskChanged.State.newBuilder().status(READY).build());
        var listener = new PipelineTaskChangeListener(event, visited);
        eventDispatcher.enlist(listener);

        pipelineService.setTaskStatus(pipelineId, taskDefKey, DONE);
        Thread.sleep(3000);

        eventDispatcher.delist(listener);
        assertTrue(visited.get(), "EventListener should handle event.");
    }

    @Test
    public void testSetTaskStatusWithComment() throws InterruptedException {
        // create pipelineDefService
        PipelineTaskDef taskDef1 = randomTaskDef();
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDef1));
        var taskDefKey = taskDef1.getKey();
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        AtomicBoolean visited = new AtomicBoolean();
        pipelineService.createPipeline(pipelineId, defKey);
        var comment = RandomStringUtils.randomAlphabetic(8);
        var event = new PipelineTaskChanged();
        event.setPipelineId(pipelineId);
        event.setTaskDefKey(taskDefKey);
        event.setComment(comment);
        event.setUpdatedBy(SYSTEM_USER);
        event.setState(PipelineTaskChanged.State.newBuilder().status(ONGOING).build());
        event.setOldState(PipelineTaskChanged.State.newBuilder().status(READY).build());
        var listener = new PipelineTaskChangeListener(event, visited);
        eventDispatcher.enlist(listener);
        pipelineService.setTaskStatus(pipelineId, taskDefKey, ONGOING, comment);
        Thread.sleep(3000);

        eventDispatcher.delist(listener);
        assertTrue(visited.get(), "EventListener should handle event.");
    }

    @Test
    public void testSetTaskStatusWithCommentAndUpdatedBy() throws InterruptedException {
        // create pipelineDefService
        PipelineTaskDef taskDef1 = randomTaskDef();
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDef1));
        var taskDefKey = taskDef1.getKey();
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        AtomicBoolean visited = new AtomicBoolean();
        pipelineService.createPipeline(pipelineId, defKey);
        var comment = RandomStringUtils.randomAlphabetic(8);
        var updatedBy = RandomStringUtils.randomAlphabetic(8);
        var event = new PipelineTaskChanged();
        event.setPipelineId(pipelineId);
        event.setTaskDefKey(taskDefKey);
        event.setComment(comment);
        event.setUpdatedBy(updatedBy);
        event.setState(PipelineTaskChanged.State.newBuilder().status(ONGOING).build());
        event.setOldState(PipelineTaskChanged.State.newBuilder().status(READY).build());
        var listener = new PipelineTaskChangeListener(event, visited);
        eventDispatcher.enlist(listener);
        pipelineService.setTaskStatus(pipelineId, taskDefKey, ONGOING, comment, updatedBy);
        Thread.sleep(3000);

        eventDispatcher.delist(listener);
        assertTrue(visited.get(), "EventListener should handle event.");
    }

    @Test
    public void testPipelineStageChangedEvent() throws InterruptedException {
        // stage 10
        int stage = 10;
        PipelineTaskDef taskDef1 = randomTaskDef(stage);
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDef1));
        var taskDefKey = taskDef1.getKey();
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        String user = randomOwner().getId();
        pipelineService.createPipeline(pipelineId, defKey);
        pipelineService.setStageOwner(pipelineId, stage, user);
        var oldState = new PipelineStageChanged.State();
        oldState.setStatus(READY);
        oldState.setOwnerId(user);
        var state = new PipelineStageChanged.State();
        state.setStatus(ONGOING);
        state.setOwnerId(user);
        var changed = new PipelineStageChanged();
        changed.setStage(stage);
        changed.setPipelineId(pipelineId);
        changed.setOldState(oldState);
        changed.setState(state);
        AtomicBoolean visited = new AtomicBoolean();
        var listener =
                new AbstractUnnamedEventListener<PipelineStageChanged>() {
                    @Override
                    public void handle(PipelineStageChanged event) {
                        assertChangeEquals(changed, event);
                        visited.set(true);
                    }
                };
        eventDispatcher.enlist(listener);
        pipelineService.setTaskStatus(pipelineId, taskDefKey, ONGOING);

        Thread.sleep(3000);
        assertTrue(visited.get(), "EventListener should handle event.");
    }

    /**
     * 这个测试会偶发出现失败，修复完成之前Disabled Pipeline changed事件的发送应该要设置成同步，否则出现延迟会可能丢失PipelineChanged事件
     * 遍历100次测试可以复现该问题
     */
    @Disabled
    @Test
    public void testPipelineChangedEvent() throws InterruptedException {
        // stage 10
        int stage1 = 10;
        int stage2 = 20;
        PipelineTaskDef taskDef1 = randomTaskDef(stage1);
        PipelineTaskDef taskDef2 = randomTaskDef(stage2);
        PipelineDef pipelineDef =
                randomPipelineDef(
                        Set.of(taskDef1, taskDef2.addPrerequisite(Set.of(taskDef1.getKey()))));
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        pipelineService.createPipeline(pipelineId, defKey);
        AtomicBoolean visited = new AtomicBoolean();

        var oldState = new PipelineChanged.State();
        oldState.setStage(stage2);
        oldState.setStatus(PENDING);
        var state = new PipelineChanged.State();
        state.setStatus(READY);
        state.setStage(stage2);

        PipelineChanged changed = new PipelineChanged();
        changed.setPipelineId(pipelineId);
        changed.setState(state);
        changed.setOldState(oldState);
        var listener =
                new AbstractUnnamedEventListener<PipelineChanged>() {
                    @Override
                    public void handle(PipelineChanged event) {
                        if (Objects.deepEquals(changed, event)) {
                            visited.set(true);
                        }
                    }
                };
        eventDispatcher.enlist(listener, 1);
        pipelineService.setTaskStatus(pipelineId, taskDef1.getKey(), DONE);

        Thread.sleep(1500);
        assertTrue(visited.get(), "EventListener should handle event.");
        eventDispatcher.delist(listener);
    }

    @Test
    public void testSetSameOwnerInTaskAndPublishOneEvent() throws InterruptedException {
        // create pipelineDefService
        PipelineTaskDef taskDef1 = randomTaskDef();
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDef1));
        var taskDefKey = taskDef1.getKey();
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        var owner = randomOwner().getId();
        var changedBy = randomOwner().getId();

        AtomicBoolean visited = new AtomicBoolean();
        AtomicInteger visitedTime = new AtomicInteger(0);
        var taskOwnerChangedListener =
                new AbstractUnnamedEventListener<PipelineTaskChanged>() {
                    @Override
                    public void handle(PipelineTaskChanged event) {
                        visited.set(true);
                        visitedTime.getAndIncrement();
                    }
                };
        pipelineService.createPipeline(pipelineId, defKey);
        eventDispatcher.enlist(taskOwnerChangedListener);

        pipelineService.setTaskOwner(pipelineId, taskDefKey, owner, changedBy);

        Thread.sleep(1000);
        assertTrue(visited.get(), "EventListener should handle event.");
        var publishEventTime = visitedTime.get();
        pipelineService.setTaskOwner(pipelineId, taskDefKey, owner, changedBy);
        Thread.sleep(1000);
        assertEquals(publishEventTime, visitedTime.get());
    }

    void assertChangeEquals(PipelineChanged expected, PipelineChanged event) {
        assertNotNull(event, "Pipeline changed event should not be null");
        assertEquals(
                expected.getPipelineId(),
                event.getPipelineId(),
                "The pipeline id should be equals.");
        var state = event.getState();
        var oldState = event.getOldState();
        assertEquals(
                expected.getState().getStage(), state.getStage(), "The stage should be equals");
        assertEquals(
                expected.getState().getStatus(),
                state.getStatus(),
                "The stage status should be equals.");
        assertEquals(
                expected.getOldState().getStage(),
                oldState.getStage(),
                "The old stage should match");
        assertEquals(
                expected.getOldState().getStatus(),
                oldState.getStatus(),
                "The old stage status should be match");
    }

    void assertChangeEquals(PipelineStageChanged expected, PipelineStageChanged event) {
        assertNotNull(event, "Pipeline stage changed event should not be null");
        assertEquals(
                expected.getPipelineId(),
                event.getPipelineId(),
                "The pipeline id should be equals.");
        var state = event.getState();
        var oldState = event.getOldState();
        assertEquals(
                expected.getState().getStatus(),
                state.getStatus(),
                "The stage status should be equals");
        assertEquals(
                expected.getState().getOwnerId(),
                state.getOwnerId(),
                "The stage owner should be equals");
        assertEquals(
                expected.getOldState().getStatus(),
                oldState.getStatus(),
                "The old stage status should match");
        assertEquals(
                expected.getOldState().getOwnerId(),
                oldState.getOwnerId(),
                "The old stage owner should be match");
    }
}
