package com.bees360.project.listener;

import com.bees360.api.InvalidArgumentException;
import com.bees360.event.registry.ProjectOperationTagAdded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ProjectOperationTagEnum;

import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Log4j2
public class SetTaskStatusOnOperationTagAdded
        extends AbstractNamedEventListener<ProjectOperationTagAdded> {
    private final PipelineService pipelineService;
    private final Map<Integer, List<SetPipelineTaskStatusOnCallRecord.Record>> recordMap;

    public SetTaskStatusOnOperationTagAdded(
            PipelineService pipelineService,
            Map<Integer, List<SetPipelineTaskStatusOnCallRecord.Record>> recordMap) {
        this.pipelineService = pipelineService;
        this.recordMap = recordMap;
        log.info(
                "Created '{}(pipelineService={}, recordMap={})'",
                this,
                this.pipelineService,
                this.recordMap);
    }

    @Override
    public boolean handle(ProjectOperationTagAdded event) {
        var tagId = event.getOperationTagId();
        var records = recordMap.get(tagId);
        if (records == null) {
            return true;
        }
        var pipelineId = event.getProjectId();
        for (SetPipelineTaskStatusOnCallRecord.Record record : records) {
            var tagEnum = ProjectOperationTagEnum.valueOf(tagId);
            var comment =
                    Optional.ofNullable(tagEnum)
                            .map(ProjectOperationTagEnum::getDisplay)
                            .orElse("");
            setPipelineTaskStatus(pipelineId, record.getTaskDefKey(), record.getStatus(), comment);
        }
        return true;
    }

    private void setPipelineTaskStatus(
            String pipelineId, String taskDefKey, Message.PipelineStatus status, String comment) {
        try {
            pipelineService.setTaskStatus(pipelineId, taskDefKey, status, comment);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn(
                    "Failed to set pipeline '{}' task '{} to '{}'",
                    pipelineId,
                    taskDefKey,
                    status,
                    e);
        }
    }
}
