package com.bees360.project.listener;

import static com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE;

import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.PipelineService;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class ClosePipelineOnProjectStateClosed
        extends AbstractNamedEventListener<ProjectStateChangedEvent> {
    private final PipelineService pipelineService;

    public ClosePipelineOnProjectStateClosed(PipelineService pipelineService) {
        this.pipelineService = pipelineService;
        log.info("Created '{}'(pipelineService={})", this, this.pipelineService);
    }

    @Override
    public void handle(ProjectStateChangedEvent event) {
        var currentState = event.getCurrentState();
        var oldState = event.getOldState();
        if (currentState == null
                || oldState == null
                || !PROJECT_CLOSE.equals(currentState.getState())
                || PROJECT_CLOSE.equals(oldState)) {
            return;
        }
        var pipelineId = String.valueOf(event.getProjectId());
        pipelineService.closePipeline(pipelineId);
        log.info("Successfully closed pipeline '{}' when project closed.", pipelineId);
    }
}
