package com.bees360.project.listener;

import com.bees360.api.InvalidArgumentException;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;

import lombok.extern.log4j.Log4j2;

import java.util.Map;
import java.util.function.BiPredicate;

@Log4j2
public class SetTaskStatusOnTaskStatusChanged
        extends AbstractNamedEventListener<PipelineTaskChanged> {
    private final PipelineService pipelineService;
    private final Map<
                    BiPredicate<String, PipelineTaskChanged>,
                    SetPipelineTaskStatusOnCallRecord.Record>
            recordMap;

    public SetTaskStatusOnTaskStatusChanged(
            PipelineService pipelineService,
            Map<BiPredicate<String, PipelineTaskChanged>, SetPipelineTaskStatusOnCallRecord.Record>
                    recordMap) {
        this.pipelineService = pipelineService;
        this.recordMap = recordMap;
        log.info(
                "Created '{}(pipelineService={}, recordMap={})'",
                this,
                this.pipelineService,
                this.recordMap);
    }

    @Override
    public void handle(PipelineTaskChanged event) {
        var pipelineId = event.getPipelineId();

        recordMap.forEach(
                (predicate, record) -> {
                    if (predicate.test(pipelineId, event)) {
                        setPipelineTaskStatus(
                                pipelineId, record.getTaskDefKey(), record.getStatus());
                    }
                });
    }

    private void setPipelineTaskStatus(
            String pipelineId, String taskDefKey, Message.PipelineStatus status) {
        try {
            pipelineService.setTaskStatus(pipelineId, taskDefKey, status);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn(
                    "Failed to set pipeline '{}' task '{} to '{}'",
                    pipelineId,
                    taskDefKey,
                    status,
                    e);
        }
    }
}
