package com.bees360.project.listener;

import com.bees360.event.registry.ContactChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.pipeline.assign.TaskAssignRecord;
import com.bees360.project.Contact;
import com.bees360.project.ProjectII;
import com.bees360.repository.Provider;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;

/**
 * ContactChange分别包括了三种情况：1.同时更新了邮箱和role；2.仅更新了邮箱；3.仅更新了role。对于这三种情况，可以进行以下统一处理：将new role对应的task
 * owner设置为new contact对应的用户，移除old role特有的tasks的owner。
 */
@Log4j2
public class SetTaskOwnerOnProjectContactChanged
        extends AbstractNamedEventListener<ContactChangedEvent> {
    private final PipelineService pipelineService;
    private final Provider<ProjectII> projectProvider;
    private final UserProvider userProvider;
    private final BiFunction<String, ProjectII, List<String>> contactRoleProjectTaskFetcher;

    public SetTaskOwnerOnProjectContactChanged(
            PipelineService pipelineService,
            Provider<ProjectII> projectProvider,
            UserProvider userProvider,
            BiFunction<String, ProjectII, List<String>> contactRoleProjectTaskFetcher) {
        this.pipelineService = pipelineService;
        this.projectProvider = projectProvider;
        this.userProvider = userProvider;
        this.contactRoleProjectTaskFetcher = contactRoleProjectTaskFetcher;
        log.info(
                "Created '{}(pipelineService={}, projectProvider={}, userProvider={},"
                        + " contactRoleProjectTaskFetcher={})'",
                this,
                this.pipelineService,
                this.projectProvider,
                this.userProvider,
                this.contactRoleProjectTaskFetcher);
    }

    @Override
    public void handle(ContactChangedEvent event) throws IOException {

        var oldContact = event.getOldValue();
        var newContact = event.getNewValue();
        var newEmail = Optional.ofNullable(newContact).map(Contact::getPrimaryEmail).orElse(null);
        var newRole = Optional.ofNullable(newContact).map(Contact::getRole).orElse(null);
        var oldEmail = Optional.ofNullable(oldContact).map(Contact::getPrimaryEmail).orElse(null);
        var oldRole = Optional.ofNullable(oldContact).map(Contact::getRole).orElse(null);

        // neither user nor role is changed
        if (Objects.equals(newEmail, oldEmail) && Objects.equals(newRole, oldRole)) {
            return;
        }
        var projectId = event.getProjectId();
        var project = projectProvider.findById(projectId);

        var oldRoleTask = contactRoleProjectTaskFetcher.apply(oldRole, project);
        var newRoleTask = contactRoleProjectTaskFetcher.apply(newRole, project);
        // not defined in config
        if (CollectionUtils.isEmpty(oldRoleTask) && CollectionUtils.isEmpty(newRoleTask)) {
            return;
        }

        var newUser = findUserEqualsEmail(newEmail);
        var oldUser = findUserEqualsEmail(oldEmail);

        // set owner of tasks for new role as new contact, remove owner of tasks for old role only
        if (newUser != null && CollectionUtils.isNotEmpty(newRoleTask)) {
            batchSetTaskOwner(newRoleTask, newUser.getId(), projectId, event.getUpdatedBy());
        }
        oldRoleTask.removeAll(newRoleTask);
        if (oldUser != null && CollectionUtils.isNotEmpty(oldRoleTask)) {
            batchRemoveTaskOwner(oldRoleTask, projectId, event.getUpdatedBy());
        }
    }

    private User findUserEqualsEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return null;
        }
        var users = userProvider.findUserByEmail(email);
        if (users == null) {
            return null;
        }
        return Iterables.toStream(users)
                .filter(u -> StringUtils.equals(u.getEmail(), email))
                .findFirst()
                .orElse(null);
    }

    private void batchSetTaskOwner(
            List<String> taskKeys, String userId, String projectId, String updatedBy) {
        var setTaskOwnerRequest =
                Iterables.transform(
                        taskKeys,
                        key -> TaskAssignRecord.buildTaskToBeAssigned(userId, projectId, key));
        pipelineService.batchSetTaskOwner(
                setTaskOwnerRequest,
                Message.TaskAssignChannelEnum.UNDEFINED,
                ContactChangedEvent.class.getSimpleName(),
                updatedBy);
    }

    private void batchRemoveTaskOwner(List<String> taskKeys, String projectId, String updatedBy) {
        batchSetTaskOwner(taskKeys, StringUtils.EMPTY, projectId, updatedBy);
    }
}
