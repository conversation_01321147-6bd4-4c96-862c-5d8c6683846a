package com.bees360.project.listener;

import com.bees360.event.registry.CreateMagicPlanEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.pipeline.Pipeline;
import com.bees360.pipeline.PipelineService;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Objects;

@Log4j2
public class SetPipelineTaskOnCreateMagicPlan
        extends AbstractNamedEventListener<CreateMagicPlanEvent> {

    private static final String CREATE_MAGICPLAN = "create_magicplan";

    private final PipelineService pipelineService;

    public SetPipelineTaskOnCreateMagicPlan(PipelineService pipelineService) {
        this.pipelineService = pipelineService;

        log.info("Created '{}(pipelineService={}", this, this.pipelineService);
    }

    @Override
    public void handle(CreateMagicPlanEvent event) throws IOException {
        String projectId = event.getProjectId();
        Pipeline pipeline = pipelineService.getById(projectId);
        if (pipeline == null) {
            log.info("No pipeline is found with projectId: {}", projectId);
            return;
        }
        boolean pipelineContainsTask =
                Iterables.toStream(pipeline.getTask())
                        .anyMatch(
                                pipelineTask ->
                                        Objects.equals(pipelineTask.getKey(), CREATE_MAGICPLAN));
        if (!pipelineContainsTask) {
            log.info(
                    "No task with key: {} is found from pipeline, project id: {}.",
                    CREATE_MAGICPLAN,
                    projectId);
            return;
        }

        if (Objects.equals(CreateMagicPlanEvent.PILOT_NOT_IN_WORKGROUP_MSG, event.getMessage())) {
            pipelineService.setTaskStatus(
                    projectId,
                    CREATE_MAGICPLAN,
                    PipelineStatus.ERROR,
                    "Pilot is not in MagicPlan Workgroup.");
        } else if (event.isResult()) {
            pipelineService.setTaskStatus(
                    projectId, CREATE_MAGICPLAN, PipelineStatus.DONE, event.getMessage());
        }
    }
}
