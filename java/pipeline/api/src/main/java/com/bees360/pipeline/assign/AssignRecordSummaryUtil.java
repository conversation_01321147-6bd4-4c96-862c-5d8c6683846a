package com.bees360.pipeline.assign;

import com.bees360.util.Iterables;

import jakarta.annotation.Nullable;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class AssignRecordSummaryUtil {

    private static final String DEFAULT_RECORD = "";

    public static AssignSummary summaryRecordInOneRecordId(
            Iterable<? extends TaskAssignRecord> records, String recordId, AssignRule assignRule) {
        var recordList =
                Iterables.toStream(records).filter(Objects::nonNull).collect(Collectors.toList());
        return summaryFromChannelMapEntry(recordList, assignRule, recordId);
    }

    public static Iterable<? extends AssignSummary> summaryRecord(
            Iterable<? extends TaskAssignRecord> records, Map<String, AssignRule> assignRuleMap) {
        Map<String, List<TaskAssignRecord>> mapByRecordTime =
                Iterables.toStream(records)
                        .filter(r -> Objects.nonNull(r) && Objects.nonNull(r.getAssignRecordId()))
                        .collect(Collectors.groupingBy(AssignRecordSummaryUtil::fetchRecord));

        return mapByRecordTime.entrySet().stream()
                .map(
                        entry -> {
                            var recordId = entry.getKey();
                            var recordList = entry.getValue();
                            var channelExternalId = recordList.get(0).getChannelExternalId();
                            AssignRule rule = null;
                            if (Objects.nonNull(channelExternalId)) {
                                rule = assignRuleMap.get(channelExternalId);
                            }
                            return summaryFromChannelMapEntry(recordList, rule, recordId);
                        })
                .filter(Objects::nonNull)
                .sorted(
                        (s1, s2) ->
                                Integer.compare(
                                        Integer.parseInt(s2.getAssignRecordId()),
                                        Integer.parseInt(s1.getAssignRecordId())))
                .collect(Collectors.toList());
    }

    /** 根据record渠道，record渠道id和record时间，将record归类，三者都相同的record视作同一次分配。 */
    private static String fetchRecord(TaskAssignRecord record) {
        var recordInfoId = record.getAssignRecordId();
        return Objects.nonNull(recordInfoId) ? recordInfoId : DEFAULT_RECORD;
    }
    /**
     * 将分类之后的TaskAssignRecords统计为AssignRecordSummary
     *
     * @param records 已经完成分类的TaskAssignRecords，这些records的channel, channelExternalId, recordId必须一致。
     */
    private static AssignSummary summaryFromChannelMapEntry(
            List<? extends TaskAssignRecord> records,
            @Nullable AssignRule assignRule,
            String recordId) {
        if (records == null || records.isEmpty()) {
            return null;
        }
        var recordTime = records.get(0).getRecordTime();
        var targetId = records.get(0).getAssignTargetId();

        var assignProjectNumber =
                records.stream().map(TaskAssignRecord::getPipelineExternalId).distinct().count();
        var assignMemberCount =
                records.stream().map(TaskAssignRecord::getOwnerId).distinct().count();
        var assignTaskCount = records.size();
        return AssignSummary.of(
                assignRule,
                recordTime,
                targetId,
                (int) assignMemberCount,
                (int) assignProjectNumber,
                assignTaskCount,
                recordId);
    }
}
