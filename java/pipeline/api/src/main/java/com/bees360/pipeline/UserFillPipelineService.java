package com.bees360.pipeline;

import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;

import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Log4j2
public class UserFillPipelineService extends ForwardingPipelineService {
    private final PipelineService pipelineService;
    private final UserProvider userProvider;

    public UserFillPipelineService(PipelineService pipelineService, UserProvider userProvider) {
        this.pipelineService = pipelineService;
        this.userProvider = userProvider;
        log.info(
                "Created '{}(pipelineService={}, userProvider={}'",
                this,
                this.pipelineService,
                this.userProvider);
    }

    @Override
    protected PipelineService delegate() {
        return pipelineService;
    }

    @Override
    public Pipeline findById(String id) {
        return fillUser(super.findById(id));
    }

    @Override
    public Pipeline findByIdAndStage(String id, int stage) {
        return fillUser(super.findByIdAndStage(id, stage));
    }

    @Override
    public Pipeline findByIdAndStageWithPrereq(String id, int stage) {
        return fillUser(super.findByIdAndStageWithPrereq(id, stage));
    }

    private Pipeline fillUser(Pipeline pipeline) {
        if (pipeline == null) {
            return null;
        }

        var taskSet = Iterables.toSet(pipeline.getTask());
        var taskUsers = taskSet.stream().map(PipelineTask::getOwnerId).filter(Objects::nonNull);
        var supervisorUser =
                taskSet.stream()
                        .flatMap(task -> task.getSupervisorId().stream())
                        .filter(Objects::nonNull);
        var stageSet = Iterables.toSet(pipeline.getStage());
        var stageUsers = stageSet.stream().map(Stage::getOwnerId).filter(Objects::nonNull);
        var userIds =
                Stream.of(taskUsers, stageUsers, supervisorUser)
                        .flatMap(Function.identity())
                        .collect(Collectors.toSet());
        var userMap =
                Iterables.toStream(userProvider.findUserById(userIds))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(User::getId, Function.identity()));

        var taskSetMessage =
                taskSet.stream().map(task -> fillUser(task, userMap)).collect(Collectors.toSet());
        var stageSetMessage =
                stageSet.stream()
                        .map(stage -> fillUser(stage, userMap))
                        .collect(Collectors.toSet());
        var pipelineMsg = pipeline.toMessage();
        return Pipeline.from(
                Message.PipelineMessage.newBuilder()
                        .setId(pipelineMsg.getId())
                        .setDef(pipelineMsg.getDef())
                        .addAllStage(stageSetMessage)
                        .addAllTask(taskSetMessage)
                        .build());
    }

    private Message.PipelineMessage.Task fillUser(
            PipelineTask task, Map<String, ? extends User> userMap) {
        var taskBuilder = task.toMessage().toBuilder();
        var ownerId = task.getOwnerId();
        var supervisorId = task.getSupervisorId();
        if (ownerId != null && userMap.containsKey(ownerId)) {
            var user = userMap.get(ownerId);
            taskBuilder.setOwner(user.toMessage());
        }
        taskBuilder.clearSupervisor();
        if (CollectionUtils.isNotEmpty(supervisorId)) {
            supervisorId.forEach(
                    id -> {
                        if (userMap.containsKey(id)) {
                            taskBuilder.addSupervisor(userMap.get(id).toMessage());
                        }
                    });
        }
        return taskBuilder.build();
    }

    private Message.PipelineMessage.Stage fillUser(
            Stage stage, Map<String, ? extends User> userMap) {
        var ownerId = stage.getOwnerId();
        if (ownerId == null || !userMap.containsKey(ownerId)) {
            return stage.toMessage();
        }

        var user = userMap.get(ownerId);
        return stage.toMessage().toBuilder().setOwner(user.toMessage()).build();
    }
}
