package com.bees360.pipeline;

import com.bees360.pipeline.assign.SetTaskStatusRequest;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import org.apache.logging.log4j.util.TriConsumer;

@Log4j2
public class StatusChangeCheckPipelineService extends ForwardingPipelineService {
    private final PipelineService pipelineService;
    private final TriConsumer<String, String, Message.PipelineStatus> statusChangeCheckers;

    public StatusChangeCheckPipelineService(
            PipelineService pipelineService,
            TriConsumer<String, String, Message.PipelineStatus> statusChangeCheckers) {
        this.pipelineService = pipelineService;
        this.statusChangeCheckers = statusChangeCheckers;
        log.info(
                "created {}(pipelineService='{}'',statusChangeCheckers='{}')",
                this,
                pipelineService,
                statusChangeCheckers);
    }

    @Override
    protected PipelineService delegate() {
        return pipelineService;
    }

    @Override
    public void setTaskStatus(
            String id,
            String taskKey,
            Message.PipelineStatus status,
            @Nullable String comment,
            @Nullable String updatedBy) {
        statusChangeCheckers.accept(id, taskKey, status);
        delegate().setTaskStatus(id, taskKey, status, comment, updatedBy);
    }

    @Override
    public void batchSetTaskStatus(Iterable<SetTaskStatusRequest> requests, String updateBy) {
        requests.forEach(
                request ->
                        statusChangeCheckers.accept(
                                request.getPipelineExternalId(),
                                request.getTaskDefKey(),
                                request.getStatus()));
        delegate().batchSetTaskStatus(requests, updateBy);
    }
}
