package com.bees360.pipeline.assign;

import static com.bees360.pipeline.assign.AssignHandler.HandlerMappingEnum.ROUND_ROBIN;
import static com.bees360.pipeline.assign.TaskAssignRecord.buildTaskToBeAssigned;

import com.bees360.pipeline.Message;
import com.bees360.util.Iterables;

import jakarta.annotation.Nullable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/** Deprecated by <PERSON><PERSON><PERSON><PERSON><PERSON>，实现不能写在接口里，用接口 AssignTaskRecordCalculator 替代，接口稳定后会删除此类 */
@Log4j2
@Deprecated
public class AssignHandler {
    private final AssigneeManager assigneeManager;
    private final Consumer<List<PipelineAssignedTask>> handler;
    private final AssignManager assignManager;

    public AssignHandler(
            AssigneeManager assigneeManager,
            Consumer<List<PipelineAssignedTask>> handler,
            AssignManager assignManager) {

        this.assigneeManager = assigneeManager;
        this.handler = handler;
        this.assignManager = assignManager;
    }

    public AssignHandler(AssigneeManager assigneeManager, AssignManager assignManager) {
        this(assigneeManager, ((s) -> {}), assignManager);
    }

    @Data
    @AllArgsConstructor
    static class StatusInAssignment {
        private final String id;
        private int workload;
        private final int capacity;
        // save the original position in the list
        private final int position;
        // the times that the assign change pass the assignee, used in Round Robin
        private int assignTimes;

        public void addWorkload(int workload) {
            this.workload += workload;
        }

        public void addAssignTime() {
            this.assignTimes++;
        }
    }

    public void assign(
            Iterable<PipelineAssignedTask> pipelines,
            Iterable<? extends AssigneeSchedule> assignees,
            int comparatorCode,
            String assignerId) {
        assign(pipelines, assignees, comparatorCode, null, assignerId);
    }

    public void assign(
            Iterable<PipelineAssignedTask> pipelines,
            Iterable<? extends AssigneeSchedule> assignees,
            int comparatorCode,
            String assignRuleKey,
            String assignerId) {
        assignReturningRecords(
                pipelines,
                assignees,
                comparatorCode,
                assignRuleKey,
                null,
                false,
                false,
                assignerId);
    }

    public Set<TaskAssignRecord> assignReturningRecords(
            Iterable<PipelineAssignedTask> pipelines,
            Iterable<? extends AssigneeSchedule> assignees,
            int comparatorCode,
            String assignRuleKey,
            String groupId,
            boolean inPreview,
            boolean inManual,
            String assignerId) {
        AtomicInteger assigneePosition = new AtomicInteger(0);
        // create a map to save each assignee's status during the assignment
        var sortedStreamById =
                Iterables.toStream(assignees)
                        .sorted(
                                (a, b) ->
                                        CharSequence.compare(
                                                Objects.requireNonNull(a.getUser().getId()),
                                                Objects.requireNonNull(b.getUser().getId())));
        // Set sorted stream into a map, keep the sorted result
        Map<String, StatusInAssignment> assigneeMap =
                sortedStreamById.collect(
                        Collectors.toMap(
                                as -> as.getUser().getId(),
                                as ->
                                        new StatusInAssignment(
                                                as.getUser().getId(),
                                                as.getWorkload(),
                                                as.getCapacity(),
                                                assigneePosition.getAndIncrement(),
                                                0),
                                (u, v) -> u,
                                LinkedHashMap::new));
        var comparator = getComparator(comparatorCode);
        var statusIt = Iterables.cycle(assigneeMap.values()).iterator();
        Set<TaskAssignRecord> assignRecords = new HashSet<>();
        var channel =
                inManual
                        ? Message.TaskAssignChannelEnum.AUTO_ASSIGN_MANUAL
                        : Message.TaskAssignChannelEnum.AUTO_ASSIGN_SCHEDULED;
        var assignRecordId =
                assignManager.createRecordReturningId(assignRuleKey, channel, null, groupId);
        var failedPipeline = new ArrayList<>(Iterables.toList(pipelines));
        for (PipelineAssignedTask pipeline : pipelines) {
            assigneeMap.values().stream()
                    .filter(schedule -> getAvailableCapacity(schedule) >= pipeline.getDifficulty())
                    .min(comparator)
                    .ifPresent(
                            assignee -> {
                                try {
                                    assignRecords.addAll(
                                            doAssign(
                                                    assignee,
                                                    pipeline,
                                                    statusIt,
                                                    HandlerMappingEnum.get(comparatorCode),
                                                    channel,
                                                    assignRuleKey,
                                                    groupId,
                                                    assignRecordId,
                                                    inPreview,
                                                    assignerId));
                                    failedPipeline.remove(pipeline);
                                } catch (RuntimeException e) {
                                    // Avoid throwing too many exceptions. Error will be thrown with
                                    // all tasks failed after assignation completing according to
                                    // the handler.
                                    log.warn(
                                            "Auto-assignment failed when trying to assign {} to {}",
                                            pipeline,
                                            assignee,
                                            e);
                                }
                            });
        }
        if (CollectionUtils.isNotEmpty(failedPipeline)) {
            handler.accept(failedPipeline);
        }
        return assignRecords;
    }

    private int getAvailableCapacity(StatusInAssignment assigneeSchedule) {
        return assigneeSchedule.getCapacity() - assigneeSchedule.getWorkload();
    }

    private Set<TaskAssignRecord> doAssign(
            StatusInAssignment assigneeStatus,
            PipelineAssignedTask pipeline,
            Iterator<StatusInAssignment> cycleIt,
            HandlerMappingEnum comparator,
            Message.TaskAssignChannelEnum channel,
            String assignRuleKey,
            String groupId,
            String assignBatchId,
            boolean inPreview,
            String assignerId) {
        var userId = assigneeStatus.getId();
        var pipelineId = pipeline.getPipelineId();
        var taskKeys = pipeline.getTaskKey();
        if (!inPreview) {
            assigneeManager.assign(
                    userId,
                    pipelineId,
                    taskKeys,
                    channel,
                    assignRuleKey,
                    groupId,
                    assignBatchId,
                    assignerId);
        }
        var assignedTask =
                taskKeys.stream()
                        .map(task -> buildTaskToBeAssigned(userId, pipelineId, task, assignerId))
                        .collect(Collectors.toSet());
        assigneeStatus.addWorkload(pipeline.getDifficulty());
        if (comparator != ROUND_ROBIN) {
            return assignedTask;
        }
        assigneeStatus.addAssignTime();
        // every assignee between this and previous turn should increase one assign time.
        assigneeStatus = cycleIt.next();
        while (!assigneeStatus.getId().equals(userId)) {
            assigneeStatus.addAssignTime();
            assigneeStatus = cycleIt.next();
        }
        return assignedTask;
    }

    enum HandlerMappingEnum {
        ROUND_ROBIN(1),
        LEAST_WORKLOAD(2),
        MAX_CAPACITY(3);

        private final int code;

        HandlerMappingEnum(int code) {
            this.code = code;
        }

        @Nullable
        static HandlerMappingEnum get(int code) {
            for (HandlerMappingEnum value : values()) {
                if (value.code == code) {
                    return value;
                }
            }
            return null;
        }
    }

    public Comparator<StatusInAssignment> getComparator(int handlerCode) {
        var handleEnum = HandlerMappingEnum.get(handlerCode);
        if (handleEnum == null) {
            throw new IllegalArgumentException();
        }
        switch (handleEnum) {
            case ROUND_ROBIN:
                return (compare1, compare2) -> {
                    int compareAssignTimes =
                            Integer.compare(compare1.getAssignTimes(), compare2.getAssignTimes());
                    int compareAssignPosition =
                            Integer.compare(compare1.getPosition(), compare2.getPosition());
                    // If assign times is equal, use position to sort
                    return compareAssignTimes == 0 ? compareAssignPosition : compareAssignTimes;
                };
            case LEAST_WORKLOAD:
                return Comparator.comparingInt(StatusInAssignment::getWorkload);
            case MAX_CAPACITY:
                return Comparator.comparingDouble(s -> s.getWorkload() * 1.0 / s.getCapacity());
        }
        // All available situation should be in switch case, normally not reach here
        log.warn(
                "Not found correspond comparator with handler code: "
                        + handlerCode
                        + " and handle enum: "
                        + handleEnum
                        + ", using id comparator as default.");
        return Comparator.comparing(StatusInAssignment::getId);
    }
}
