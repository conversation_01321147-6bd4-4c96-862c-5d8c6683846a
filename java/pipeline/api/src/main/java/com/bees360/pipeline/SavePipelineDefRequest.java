package com.bees360.pipeline;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.Proto;
import com.bees360.util.Iterables;

import lombok.NonNull;

public interface SavePipelineDefRequest extends Proto<Message.SavePipelineDefRequest> {

    String getKey();

    String getName();

    Iterable<? extends PipelineDefTaskRelation> getTask();

    String getUserId();

    static SavePipelineDefRequest from(Message.SavePipelineDefRequest message) {
        var tasks = Iterables.transform(message.getTaskList(), PipelineDefTaskRelation::from);
        return new SavePipelineDefRequest() {
            @Override
            public String getKey() {
                return message.getKey();
            }

            @Override
            public String getName() {
                return message.getName();
            }

            @Override
            public Iterable<? extends PipelineDefTaskRelation> getTask() {
                return tasks;
            }

            @Override
            public String getUserId() {
                return message.getUserId();
            }

            @Override
            public Message.SavePipelineDefRequest toMessage() {
                return message;
            }
        };
    }

    @Override
    default Message.SavePipelineDefRequest toMessage() {
        var builder = Message.SavePipelineDefRequest.newBuilder();
        acceptIfNotNull(builder::setKey, getKey());
        acceptIfNotNull(builder::setName, getName());
        var tasks = getTask();
        if (tasks != null) {
            builder.addAllTask(Iterables.transform(tasks, PipelineDefTaskRelation::toMessage));
        }
        acceptIfNotNull(builder::setUserId, getUserId());
        return builder.build();
    }
}

interface PipelineDefTaskRelation extends Proto<Message.SavePipelineDefRequest.TaskRelation> {

    String getKey();

    @NonNull
    Iterable<String> getPrereqTaskDefKey();

    static PipelineDefTaskRelation from(Message.SavePipelineDefRequest.TaskRelation message) {
        return new PipelineDefTaskRelation() {
            @Override
            public String getKey() {
                return message.getKey();
            }

            @Override
            public @NonNull Iterable<String> getPrereqTaskDefKey() {
                return message.getPrereqTaskDefList();
            }

            @Override
            public Message.SavePipelineDefRequest.TaskRelation toMessage() {
                return message;
            }
        };
    }

    static PipelineDefTaskRelation from(String key, Iterable<String> prereqTaskDefKey) {
        return new PipelineDefTaskRelation() {
            @Override
            public String getKey() {
                return key;
            }

            @Override
            public @NonNull Iterable<String> getPrereqTaskDefKey() {
                return prereqTaskDefKey;
            }
        };
    }

    @Override
    default Message.SavePipelineDefRequest.TaskRelation toMessage() {
        var builder = Message.SavePipelineDefRequest.TaskRelation.newBuilder();
        acceptIfNotNull(builder::setKey, getKey());
        builder.addAllPrereqTaskDef(getPrereqTaskDefKey());
        return builder.build();
    }
}
