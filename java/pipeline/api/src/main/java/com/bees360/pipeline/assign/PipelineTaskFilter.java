package com.bees360.pipeline.assign;

import com.bees360.api.Proto;
import com.bees360.assignment.Message;

import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nullable;

public interface PipelineTaskFilter extends Proto<Message.PipelineTaskFilter> {
    String getTaskFilter();

    String getPipelineFilter();

    @Nullable
    String getFullFilter();

    @Nullable
    static PipelineTaskFilter from(Message.PipelineTaskFilter message) {
        if (Message.PipelineTaskFilter.getDefaultInstance().equals(message)) {
            return null;
        }
        return new PipelineTaskFilter() {
            @Override
            public String getTaskFilter() {
                return message.getTaskFilter();
            }

            @Override
            public String getPipelineFilter() {
                return message.getPipelineFilter();
            }

            @Nullable
            @Override
            public String getFullFilter() {
                if (StringUtils.isBlank(message.getFullFilter())) {
                    return null;
                }
                return message.getFullFilter();
            }

            @Override
            public Message.PipelineTaskFilter toMessage() {
                return message;
            }
        };
    }
}
