package com.bees360.pipeline;

import static com.bees360.pipeline.Message.PipelineStatus.DONE;
import static com.bees360.pipeline.TestUtils.createPipelineDef;
import static com.bees360.pipeline.TestUtils.randomAssignRule;
import static com.bees360.pipeline.TestUtils.randomKey;
import static com.bees360.pipeline.TestUtils.randomPipelineDef;
import static com.bees360.pipeline.TestUtils.randomTaskDefWithDifficulty;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.bees360.assignment.Message;
import com.bees360.pipeline.assign.AssignHandler;
import com.bees360.pipeline.assign.AssignManager;
import com.bees360.pipeline.assign.AssignRuleManager;
import com.bees360.pipeline.assign.AssigneeManager;
import com.bees360.pipeline.assign.PipelineTaskFilter;
import com.bees360.pipeline.assign.PipelineTaskProvider;
import com.bees360.user.Group;
import com.bees360.user.GroupManager;
import com.bees360.user.User;
import com.bees360.user.UserRepository;
import com.bees360.user.util.MockGroup;
import com.bees360.util.Iterables;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public abstract class AbstractAssigneeManagerTest {
    abstract AssigneeManager getAssigneeManager();

    abstract GroupManager getGroupManager();

    abstract UserRepository getUserRepository();

    abstract PipelineService getPipelineService();

    abstract PipelineDefService getPipelineDefService();

    abstract PipelineTaskProvider getFilter();

    abstract AssignRuleManager getAssignRuleManager();

    abstract AssignManager getAssignManager();

    AssignHandler getAssignStrategy() {
        return new AssignHandler(getAssigneeManager(), getAssignManager());
    }

    void testSetAndGetAssigneeCapacityAndAssign() {
        Map<String, Integer> capacityMap = new ConcurrentHashMap<>();
        var group1 = createRandomGroup("group_1");
        var group2 = createRandomGroup(RandomStringUtils.randomAlphabetic(6));
        var allUser = group1.getAllUser();
        allUser.forEach(
                u -> {
                    var capacity = RandomUtils.nextInt(50, 100);
                    capacityMap.put(u.getId(), capacity);
                });
        getAssigneeManager().setAssigneeCapacity(capacityMap);
        // group_1 works each MON to FRI which means each assignee has 5 workdays in a week,
        var schedulePeriod = 7;
        var assigneeSchedules =
                getAssigneeManager()
                        .getAssigneeStatus(
                                group1.getId(),
                                Instant.now().plus(schedulePeriod, ChronoUnit.DAYS));
        assigneeSchedules.forEach(
                assigneeSchedule -> {
                    Assertions.assertEquals(
                            capacityMap.get(assigneeSchedule.getUser().getId()) * 5,
                            assigneeSchedule.getCapacity());
                    Assertions.assertEquals(0, assigneeSchedule.getWorkload());
                });
        // create pipeline with tasks having low difficulty that can surely be assigned to assignees
        Map<String, Integer> pipelineDifficultyMap = new ConcurrentHashMap<>();
        Map<String, Iterable<String>> pipelineId2TaskDefKey = new ConcurrentHashMap<>();
        var pipelineId1 = createRandomPipeline(pipelineDifficultyMap, pipelineId2TaskDefKey);
        var pipelineId2 = createRandomPipeline(pipelineDifficultyMap, pipelineId2TaskDefKey);
        var taskDefKey1 = Iterables.toList(pipelineId2TaskDefKey.get(pipelineId1)).get(0);
        var taskDefKey2 = Iterables.toList(pipelineId2TaskDefKey.get(pipelineId2)).get(0);
        // assign task to a user but not complete the task, the task will be reassigned
        var userId = Iterables.toList(group2.getAllUser()).get(0).getId();
        var changedBy = RandomStringUtils.randomAlphabetic(8);
        getPipelineService().setTaskOwner(pipelineId1, taskDefKey1, userId, changedBy);
        var users = Iterables.toStream(allUser).map(User::getId).collect(Collectors.toList());
        String pipelineFilter =
                "\"pipeline\".\"external_id\" in ('" + pipelineId1 + "', '" + pipelineId2 + "')";
        var taskFilter =
                "\"pipeline_task_def\".\"key\" in ('"
                        + taskDefKey1
                        + "', '"
                        + taskDefKey2
                        + "') and (\"pipeline_task\".\"owner_id\" = '"
                        + userId
                        + "' or \"pipeline_task\".\"owner_id\" is null)";
        var pipelineTaskFilter =
                PipelineTaskFilter.from(
                        Message.PipelineTaskFilter.newBuilder()
                                .setTaskFilter(taskFilter)
                                .setPipelineFilter(pipelineFilter)
                                .build());
        var filteredTask = Iterables.toList(getFilter().getTaskKeyByQuery(pipelineTaskFilter));
        Assertions.assertEquals(
                pipelineDifficultyMap.get(filteredTask.get(0).getTaskKey().get(0)),
                filteredTask.get(0).getDifficulty());
        Assertions.assertEquals(
                pipelineDifficultyMap.get(filteredTask.get(1).getTaskKey().get(0)),
                filteredTask.get(1).getDifficulty());
        var assignRule = randomAssignRule();
        getAssignRuleManager().create(assignRule);
        // task 1 and 2 should be assigned to user 1 and 2
        getAssignStrategy()
                .assign(
                        filteredTask,
                        assigneeSchedules,
                        RandomUtils.nextInt(1, 3),
                        assignRule.getKey(),
                        changedBy);
        assigneeSchedules =
                getAssigneeManager()
                        .getAssigneeStatus(
                                group1.getId(),
                                Instant.now().plus(schedulePeriod, ChronoUnit.DAYS));
        var taskKeySet =
                filteredTask.stream()
                        .flatMap(task -> task.getTaskKey().stream())
                        .collect(Collectors.toSet());
        assigneeSchedules.forEach(
                a -> {
                    Assertions.assertEquals(a.toMessage().getTaskList().size(), 1);
                    Assertions.assertTrue(a.toMessage().getTask(0).getDifficulty() > 0);
                    Assertions.assertTrue(taskKeySet.contains(a.toMessage().getTask(0).getKey()));
                    Assertions.assertTrue(a.getWorkload() > 0);
                });
        var pipeline1 = getPipelineService().getById(pipelineId1);
        var tasks1 = Iterables.toList(pipeline1.getTask());
        var ownerId1 = tasks1.get(0).getOwnerId();
        if (!tasks1.get(0).getKey().equals(taskDefKey1)) {
            ownerId1 = tasks1.get(1).getOwnerId();
        }
        Assertions.assertTrue(users.contains(ownerId1));
        var pipeline2 = getPipelineService().getById(pipelineId2);
        var tasks2 = Iterables.toList(pipeline2.getTask());
        var ownerId2 = tasks2.get(0).getOwnerId();
        if (!tasks2.get(0).getKey().equals(taskDefKey2)) {
            ownerId2 = tasks2.get(1).getOwnerId();
        }
        Assertions.assertTrue(users.contains(ownerId2));
        pipelineId2TaskDefKey.forEach(
                (k, v) ->
                        v.forEach(
                                taskDefKey ->
                                        getPipelineService().setTaskStatus(k, taskDefKey, DONE)));
        assigneeSchedules =
                getAssigneeManager()
                        .getAssigneeStatus(
                                group1.getId(),
                                Instant.now().plus(schedulePeriod, ChronoUnit.DAYS));
        // workload should be 0 after task finished
        assigneeSchedules.forEach(
                assigneeSchedule -> Assertions.assertEquals(0, assigneeSchedule.getWorkload()));
        getAssigneeManager().deleteAssignee(users);
        getAssigneeManager()
                .deleteAssignee(
                        Iterables.toStream(group2.getAllUser())
                                .map(User::getId)
                                .collect(Collectors.toSet()));
    }

    void testSetAssigneeGroupCapacityScriptShouldBeFindInScheduleStatus() {
        Map<String, Integer> capacityMap = new ConcurrentHashMap<>();
        var groupId = RandomStringUtils.randomAlphabetic(6);
        var group = createRandomGroup(groupId);
        var capacityScript = "capacity";
        var userId = RandomStringUtils.randomAlphabetic(8);
        var allUser = group.getAllUser();
        allUser.forEach(
                u -> {
                    var capacity = RandomUtils.nextInt(50, 100);
                    capacityMap.put(u.getId(), capacity);
                });
        getAssigneeManager().setAssigneeCapacity(capacityMap);
        getAssigneeManager().setAssigneeCapacityScript(groupId, capacityScript, userId);
        var schedulePeriod = 7;
        var assigneeSchedules =
                getAssigneeManager()
                        .getAssigneeStatus(
                                groupId, Instant.now().plus(schedulePeriod, ChronoUnit.DAYS));
        for (var assigneeSchedule : assigneeSchedules) {
            Assertions.assertEquals(assigneeSchedule.toMessage().getGroupId(), groupId);
            Assertions.assertEquals(
                    assigneeSchedule.toMessage().getCapacityScript(), capacityScript);
        }

        getAssigneeManager().setAssigneeCapacityScript(groupId, null, userId);
        assigneeSchedules =
                getAssigneeManager()
                        .getAssigneeStatus(
                                groupId, Instant.now().plus(schedulePeriod, ChronoUnit.DAYS));
        for (var assigneeSchedule : assigneeSchedules) {
            Assertions.assertTrue(
                    StringUtils.isBlank(assigneeSchedule.toMessage().getCapacityScript()));
        }
    }

    void testSetAssigneeGroupCapacityScriptNormalOrNullShouldBeFind() {
        var groupId = RandomStringUtils.randomAlphabetic(6);
        var capacityScript = "capacity";
        var userId = RandomStringUtils.randomAlphabetic(8);
        getAssigneeManager().setAssigneeCapacityScript(groupId, capacityScript, userId);

        var assigneeGroup = getAssigneeManager().findAssigneeGroupByGroupId(groupId);
        Assertions.assertEquals(assigneeGroup.getGroupId(), groupId);
        Assertions.assertEquals(assigneeGroup.getCapacityScript(), capacityScript);

        getAssigneeManager().setAssigneeCapacityScript(groupId, null, userId);
        var assigneeGroupNull = getAssigneeManager().findAssigneeGroupByGroupId(groupId);
        Assertions.assertNull(assigneeGroupNull.getCapacityScript());
    }

    void testSetAssigneeGroupCapacityScriptBlankGroupIdShouldThrowException() {
        var groupId = RandomStringUtils.randomAlphabetic(6);
        var capacityScript = "capacity";
        var userId = RandomStringUtils.randomAlphabetic(8);
        assertThrows(
                IllegalArgumentException.class,
                () -> getAssigneeManager().setAssigneeCapacityScript(null, capacityScript, userId));
        assertThrows(
                IllegalArgumentException.class,
                () ->
                        getAssigneeManager()
                                .setAssigneeCapacityScript(groupId, capacityScript, null));
    }

    void testFindAssigneeGroupBlankGroupIdShouldThrowException() {
        assertThrows(
                IllegalArgumentException.class,
                () -> getAssigneeManager().findAssigneeGroupByGroupId(null));
    }

    public void testSetAndGetAndDeleteAssignee() {
        var userId1 = RandomStringUtils.randomAlphabetic(8);
        getUserRepository()
                .save(
                        User.from(
                                com.bees360.user.Message.UserMessage.newBuilder()
                                        .setId(userId1)
                                        .build()));
        var capacity1 = RandomUtils.nextInt();
        var userId2 = RandomStringUtils.randomAlphabetic(8);
        getUserRepository()
                .save(
                        User.from(
                                com.bees360.user.Message.UserMessage.newBuilder()
                                        .setId(userId2)
                                        .build()));
        var capacity2 = RandomUtils.nextInt();
        Map<String, Integer> assigneeMap = new HashMap<>();
        assigneeMap.put(userId1, capacity1);
        assigneeMap.put(userId2, capacity2);
        getAssigneeManager().setAssigneeCapacity(assigneeMap);
        var result = Iterables.toList(getAssigneeManager().listAllAssignee());
        var resultByUserId =
                result.stream().collect(Collectors.toMap(x -> x.getUser().getId(), x -> x));
        Assertions.assertTrue(resultByUserId.keySet().containsAll(assigneeMap.keySet()));
        assigneeMap.forEach(
                (userId, capacity) -> {
                    Assertions.assertTrue(resultByUserId.containsKey(userId));
                    Assertions.assertEquals(capacity, resultByUserId.get(userId).getCapacity());
                });
        getAssigneeManager().deleteAssignee(List.of(userId1, userId2));
        result = Iterables.toList(getAssigneeManager().listAllAssignee());
        var resultByUserId2 =
                result.stream().collect(Collectors.toMap(x -> x.getUser().getId(), x -> x));
        Assertions.assertFalse(resultByUserId2.keySet().containsAll(assigneeMap.keySet()));
    }

    Group createRandomGroup(String groupId) {
        MockGroup g = MockGroup.random();
        g.setId(groupId);
        g.setName(groupId);
        g.addRandomUser();
        g.addRandomUser();
        g.addRandomAuthority();
        getGroupManager().deleteGroup(groupId);
        getGroupManager().createGroup(g.getId(), g.getName(), g.getAllAuthority());
        getGroupManager()
                .addUsersToGroup(
                        g.getId(),
                        Iterables.toStream(g.getAllUser())
                                .map(User::getId)
                                .collect(Collectors.toList()));
        getUserRepository().saveAll(g.getAllUser());
        return g;
    }

    private String createRandomPipeline(
            Map<String, Integer> pipelineDifficultyMap,
            Map<String, Iterable<String>> pipelineId2TaskDefKey) {
        int difficulty1 = RandomUtils.nextInt(1, 10);
        int difficulty2 = RandomUtils.nextInt(1, 10);
        PipelineTaskDef taskDef1 = randomTaskDefWithDifficulty(difficulty1);
        PipelineTaskDef taskDef2 = randomTaskDefWithDifficulty(difficulty2);
        PipelineDef pipelineDef1 = randomPipelineDef(List.of(taskDef1, taskDef2));
        String defKey1 = pipelineDef1.getKey();
        createPipelineDef(getPipelineDefService(), pipelineDef1);
        String pipelineId = randomKey();
        getPipelineService().createPipeline(pipelineId, defKey1);
        pipelineDifficultyMap.put(taskDef1.getKey(), difficulty1);
        pipelineDifficultyMap.put(taskDef2.getKey(), difficulty1);
        pipelineId2TaskDefKey.put(pipelineId, List.of(taskDef1.getKey(), taskDef2.getKey()));
        return pipelineId;
    }
}
