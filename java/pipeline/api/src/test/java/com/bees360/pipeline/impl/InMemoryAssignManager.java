package com.bees360.pipeline.impl;

import com.bees360.pipeline.Message;
import com.bees360.pipeline.assign.AssignManager;

import java.util.concurrent.atomic.AtomicInteger;

public class InMemoryAssignManager implements AssignManager {
    private final AtomicInteger atomicInteger = new AtomicInteger(0);

    @Override
    public String createRecordReturningId(
            String channelExternalId,
            Message.TaskAssignChannelEnum channel,
            String changedBy,
            String assignTargetId) {
        return String.valueOf(atomicInteger.getAndIncrement());
    }
}
