package com.bees360.pipeline;

import com.bees360.pipeline.assign.AssignRule;
import com.bees360.pipeline.assign.TaskAssignRecordProvider;
import com.bees360.pipeline.assign.TaskAssignRecordQuery;
import com.bees360.util.Iterables;
import com.google.protobuf.Int32Value;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

@GrpcService
@Log4j2
public class GrpcTaskAssignRecordService
        extends TaskAssignRecordServiceGrpc.TaskAssignRecordServiceImplBase {
    private final TaskAssignRecordProvider taskAssignRecordProvider;

    public GrpcTaskAssignRecordService(TaskAssignRecordProvider taskAssignRecordProvider) {
        this.taskAssignRecordProvider = taskAssignRecordProvider;
        log.info("Created '{}'(taskAssignRecordProvider={})", this, this.taskAssignRecordProvider);
    }

    @Override
    public void findTaskAssignRecordByQuery(
            Message.GetTaskAssignRecordRequest request,
            StreamObserver<Message.TaskAssignRecord> responseObserver) {
        var result =
                taskAssignRecordProvider.getAssignRecordByQuery(
                        TaskAssignRecordQuery.from(request));
        Iterables.toStream(result).forEach(record -> responseObserver.onNext(record.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void preAssignForAssignRule(
            com.bees360.assignment.Message.AssignRule request,
            StreamObserver<Message.TaskAssignRecord> responseObserver) {
        var assignRule = AssignRule.from(request);
        var result = taskAssignRecordProvider.preAssignForAssignRule(assignRule);
        Iterables.toStream(result).forEach(record -> responseObserver.onNext(record.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void getTaskAssignRecordCountByQuery(
            Message.GetTaskAssignRecordRequest request,
            StreamObserver<Int32Value> responseObserver) {
        var result =
                taskAssignRecordProvider.getRecordCountByQuery(TaskAssignRecordQuery.from(request));
        responseObserver.onNext(Int32Value.of(result));
        responseObserver.onCompleted();
    }
}
