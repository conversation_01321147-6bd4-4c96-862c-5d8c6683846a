package com.bees360.pipeline;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.pipeline.assign.AssignManager;
import com.bees360.pipeline.assign.AssignRuleManager;
import com.bees360.pipeline.assign.AssigneeManager;
import com.bees360.pipeline.assign.JooqAssignRuleManager;
import com.bees360.pipeline.assign.JooqAssigneeManager;
import com.bees360.pipeline.assign.JooqPipelineTaskProvider;
import com.bees360.pipeline.assign.PipelineTaskFilter;
import com.bees360.pipeline.assign.PipelineTaskProvider;
import com.bees360.pipeline.assign.UserAvailableScheduleProvider;
import com.bees360.pipeline.config.GrpcAssigneeClientConfig;
import com.bees360.pipeline.impl.InMemoryAssignManager;
import com.bees360.scheduled.job.JooqCronScheduledJobManager;
import com.bees360.user.GroupManager;
import com.bees360.user.GroupProvider;
import com.bees360.user.InMemoryUserKeyProvider;
import com.bees360.user.InMemoryUserRepository;
import com.bees360.user.UserKeyProvider;
import com.bees360.user.UserRepository;
import com.bees360.user.util.InMemoryGroupManager;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.jooq.DSLContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.function.Predicate;

import javax.annotation.Resource;

@SpringBootTest
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:GrpcPipelineServiceTest.yml")
@DirtiesContext
@ApplicationAutoConfig
public class GrpcAssigneeServiceTest extends AbstractAssigneeManagerTest {
    @Override
    AssigneeManager getAssigneeManager() {
        return assigneeManager;
    }

    @Override
    GroupManager getGroupManager() {
        return groupManager;
    }

    @Override
    UserRepository getUserRepository() {
        return userRepository;
    }

    @Override
    PipelineService getPipelineService() {
        return pipelineService;
    }

    @Override
    PipelineDefService getPipelineDefService() {
        return pipelineDefService;
    }

    @Override
    PipelineTaskProvider getFilter() {
        return filter;
    }

    @Override
    AssignRuleManager getAssignRuleManager() {
        return assignRuleManager;
    }

    @Override
    AssignManager getAssignManager() {
        return assignManager;
    }

    @Configuration
    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class, // Create required server beans
        GrpcServerFactoryAutoConfiguration.class, // Select server implementation
        GrpcClientAutoConfiguration.class,
        ExceptionTranslateInterceptor.class,
    }) // Support @GrpcClient annotation
    @Import({
        GrpcAssigneeClientConfig.class,
        JooqPipelineDefService.class,
        JooqPipelineDefService.JooqPipelineDefRepository.class,
        JooqTaskDefService.class,
        JooqPipelineService.class,
        JooqPipelineManager.class,
        JooqConfig.class,
        JooqPipelineTaskProvider.class,
        JooqAssignRuleManager.class,
        JooqCronScheduledJobManager.class,
        InMemoryAssignManager.class,
        InMemoryUserKeyProvider.class,
        InMemoryUserRepository.class,
        InMemoryGroupManager.class
    })
    static class Config {

        @Bean
        JooqAssigneeManager jooqAssigneeManager(
                UserAvailableScheduleProvider assigneeWorkTimeProvider,
                PipelineService pipelineService,
                GroupProvider groupProvider,
                DSLContext dsl,
                UserKeyProvider userKeyProvider) {
            return new JooqAssigneeManager(
                    assigneeWorkTimeProvider, pipelineService, groupProvider, dsl, userKeyProvider);
        }

        @Bean
        public GrpcAssigneeService grpcAssigneeManager(JooqAssigneeManager assigneeManager) {
            return new GrpcAssigneeService(assigneeManager);
        }

        @Bean
        @Primary
        UserAvailableScheduleProvider assigneeWorkTimeProvider() {
            return (userId, endTime) -> 5;
        }

        @Bean("filterScriptLegalityPredicate")
        Predicate<PipelineTaskFilter> filterScriptLegalityPredicate() {
            return task -> true;
        }

        @Bean("lossFunctionScriptLegalityPredicate")
        Predicate<String> lossFunctionScriptLegalityPredicate() {
            return task -> true;
        }
    }

    @Autowired private PipelineDefService pipelineDefService;
    @Autowired private PipelineService pipelineService;
    @Autowired private TaskService taskService;
    @Autowired private GroupManager groupManager;
    @Autowired private UserRepository userRepository;
    @Autowired private PipelineTaskProvider filter;
    @Autowired private AssignRuleManager assignRuleManager;
    @Autowired private AssignManager assignManager;

    @Resource(name = "grpcAssigneeClient")
    private AssigneeManager assigneeManager;

    @Test
    public void testSetAndGetAndDeleteAssignee() {
        super.testSetAndGetAndDeleteAssignee();
    }

    @Test
    public void testSetAssigneeGroupCapacityScriptShouldBeFindInScheduleStatus() {
        super.testSetAssigneeGroupCapacityScriptShouldBeFindInScheduleStatus();
    }

    @Test
    public void testSetAssigneeGroupCapacityScriptNormalOrNullShouldBeFind() {
        super.testSetAssigneeGroupCapacityScriptNormalOrNullShouldBeFind();
    }

    @Test
    public void testSetAssigneeGroupCapacityScriptBlankGroupIdShouldThrowException() {
        super.testSetAssigneeGroupCapacityScriptBlankGroupIdShouldThrowException();
    }

    @Test
    public void testFindAssigneeGroupBlankGroupIdShouldThrowException() {
        super.testFindAssigneeGroupBlankGroupIdShouldThrowException();
    }

    @Test
    public void testSetAndGetAssigneeCapacityAndAssign() {
        super.testSetAndGetAssigneeCapacityAndAssign();
    }
}
