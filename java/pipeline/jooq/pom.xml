<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bees360-pipeline</artifactId>
        <groupId>com.bees360</groupId>
        <version>${revision}${changelist}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bees360-pipeline-jooq</artifactId>

    <properties>
        <jooq-tables>
            address | assignee | assignee_group | assignment_rule | assignment_rule_cron | contract | cron_job | customer | pipeline | pipeline_def | pipeline_def | pipeline_def_task_def | pipeline_stage | pipeline_task | pipeline_task_def | pipeline_task_def_prereq | pipeline_task_status_history | pipeline_task_supervisor | policy | project | project_claim | project_days_old | project_inspection | project_participant | project_state | project_state_enum | project_underwriting | tables | task_assign_channel | task_assign_record | task_assign_record_info | todo | todo_pipeline
        </jooq-tables>
        <jooq-package>com.bees360.jooq.persistent.pipeline</jooq-package>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-jooq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-pipeline-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-pipeline-api</artifactId>
            <version>${project.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>co.realms9</groupId>
            <artifactId>realms9-user-api</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.javafaker</groupId>
            <artifactId>javafaker</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jmock</groupId>
            <artifactId>jmock</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-scheduled-job-api</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-scheduled-job-jooq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-boot</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>dev.cel</groupId>
            <artifactId>cel</artifactId>
            <version>${cel.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.jooq</groupId>
                <artifactId>jooq-codegen-maven</artifactId>
                <executions>
                    <execution>
                        <id>convergence</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
