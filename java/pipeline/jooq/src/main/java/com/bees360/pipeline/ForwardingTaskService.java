package com.bees360.pipeline;

import com.bees360.pipeline.assign.SetTaskStatusRequest;
import com.bees360.pipeline.assign.TaskAssignRecord;

import jakarta.annotation.Nullable;

public abstract class ForwardingTaskService implements TaskService {
    abstract TaskService delegate();

    @Override
    public boolean isAllDone(String pipelineId, Iterable<String> taskDefKey) {
        return delegate().isAllDone(pipelineId, taskDefKey);
    }

    @Override
    public void setTaskStatus(
            String pipelineId,
            String key,
            Message.PipelineStatus status,
            @Nullable String comment,
            @Nullable String updateBy) {
        delegate().setTaskStatus(pipelineId, key, status, comment, updateBy);
    }

    @Override
    public void batchSetTaskStatus(
            Iterable<SetTaskStatusRequest> requests, @Nullable String updateBy) {
        delegate().batchSetTaskStatus(requests, updateBy);
    }

    @Override
    public void initialTask(String pipelineId, String taskDefKey) {
        delegate().initialTask(pipelineId, taskDefKey);
    }

    @Override
    public void removeTask(
            String pipelineId, String pipelineDefKey, String oldPipelineDefKey, String taskDefKey) {
        delegate().removeTask(pipelineId, pipelineDefKey, oldPipelineDefKey, taskDefKey);
    }

    @Override
    public PipelineTask findByPipelineIdAndTaskKey(String pipelineId, String taskDefKey) {
        return delegate().findByPipelineIdAndTaskKey(pipelineId, taskDefKey);
    }

    @Override
    public Iterable<? extends PipelineTask> getDirectNextTasks(String pipelineId, String taskKey) {
        return delegate().getDirectNextTasks(pipelineId, taskKey);
    }

    @Override
    public Iterable<? extends PipelineTask> getNextTasks(
            String pipelineId, Iterable<String> taskDefKey) {
        return delegate().getNextTasks(pipelineId, taskDefKey);
    }

    @Override
    public void setTaskOwner(
            String id, String taskKey, String ownerId, String assignRecordId, String changedBy) {
        delegate().setTaskOwner(id, taskKey, ownerId, assignRecordId, changedBy);
    }

    @Override
    public void batchSetTaskOwner(
            Iterable<? extends TaskAssignRecord> assignedTaskList,
            @Nullable String assignRecordId) {
        delegate().batchSetTaskOwner(assignedTaskList, assignRecordId);
    }

    @Override
    public Iterable<? extends PipelineTask> getByPipelineId(String pipelineId) {
        return delegate().getByPipelineId(pipelineId);
    }

    @Override
    public Iterable<? extends PipelineTask> getByPipelineIdAndStage(String pipelineId, int stage) {
        return delegate().getByPipelineIdAndStage(pipelineId, stage);
    }

    @Override
    public Iterable<? extends PipelineTask> getByPipelineIdAndStageWithPrereq(
            String pipelineId, int stage) {
        return delegate().getByPipelineIdAndStageWithPrereq(pipelineId, stage);
    }

    @Override
    public void batchSetTaskSupervisor(
            Iterable<SetTaskSupervisorRequest> requests, String changedBy) {
        delegate().batchSetTaskSupervisor(requests, changedBy);
    }

    @Override
    public void addTaskToPipeline(String pipelineId, PipelineTaskDef taskDef) {
        delegate().addTaskToPipeline(pipelineId, taskDef);
    }
}
