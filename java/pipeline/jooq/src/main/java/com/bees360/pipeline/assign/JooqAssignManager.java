package com.bees360.pipeline.assign;

import static com.bees360.jooq.persistent.pipeline.Tables.TASK_ASSIGN_RECORD;

import com.bees360.jooq.persistent.pipeline.enums.TaskAssignChannel;
import com.bees360.pipeline.Message;

import org.jooq.DSLContext;

import java.util.Optional;

public class Jooq<PERSON>signManager implements AssignManager {
    private final DSLContext dsl;

    public JooqAssignManager(DSLContext dsl) {
        this.dsl = dsl;
    }

    @Override
    public String createRecordReturningId(
            String channelExternalId,
            Message.TaskAssignChannelEnum channel,
            String changedBy,
            String assignTargetId) {
        var taskAssignChannel =
                Optional.ofNullable(channel)
                        .map(c -> TaskAssignChannel.valueOf(channel.name()))
                        .orElse(TaskAssignChannel.UNDEFINED);
        return dsl.insertInto(
                        TASK_ASSIGN_RECORD,
                        TASK_ASSIGN_RECORD.CHANNEL_EXTERNAL_ID,
                        TASK_ASSIGN_RECORD.CHANNEL,
                        TASK_ASSIGN_RECORD.CREATED_BY,
                        TASK_ASSIGN_RECORD.ASSIGN_TARGET_ID)
                .values(channelExternalId, taskAssignChannel, changedBy, assignTargetId)
                .returning(TASK_ASSIGN_RECORD.ID)
                .fetchOne(TASK_ASSIGN_RECORD.ID);
    }
}
