package com.bees360.pipeline;

import static com.bees360.jooq.persistent.pipeline.Tables.PIPELINE;
import static com.bees360.jooq.persistent.pipeline.Tables.PIPELINE_DEF;
import static com.bees360.jooq.persistent.pipeline.Tables.PIPELINE_STAGE;

import com.bees360.api.Proto;
import com.bees360.pipeline.assign.AssignManager;
import com.bees360.pipeline.assign.AssignRecordSummaryUtil;
import com.bees360.pipeline.assign.AssignSummary;
import com.bees360.pipeline.assign.SetTaskStatusRequest;
import com.bees360.pipeline.assign.TaskAssignRecord;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;

import lombok.extern.log4j.Log4j2;

import org.jooq.Condition;
import org.jooq.DSLContext;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

/**
 * Jooq based implementation of the {@code PipelineService} interface. Note that the pipeline id of
 * the {@link PipelineService} is map to the {@code externalId} in this class.
 */
@Log4j2
public class JooqPipelineService extends AbstractPipelineService {
    private final DSLContext dsl;
    private final PipelineDefService pipelineDefService;
    private final TaskService taskService;
    private final JooqPipelineManager pipelineManager;
    private static final int NONE_STAGE = 0;
    private final AssignManager assignManager;

    public JooqPipelineService(
            DSLContext dsl,
            PipelineDefService pipelineDefService,
            TaskService taskService,
            JooqPipelineManager pipelineManager,
            AssignManager assignManager) {
        this.dsl = dsl;
        this.pipelineDefService = pipelineDefService;
        this.taskService = taskService;
        this.pipelineManager = pipelineManager;
        this.assignManager = assignManager;

        log.info(
                "Created '{}(pipelineDefService={}, taskService={}, pipelineManager={})'",
                this,
                this.pipelineDefService,
                this.taskService,
                this.pipelineManager);
    }

    @Override
    PipelineDefService getPipelineDefService() {
        return pipelineDefService;
    }

    @Override
    TaskService getTaskService() {
        return taskService;
    }

    @Override
    void doAddPipeline(String defKey, String externalId) {
        var defId = getPipelineDefId(defKey);
        Preconditions.checkArgument(
                Objects.nonNull(defId),
                String.format("The pipeline definition '%s' is not found.", defId));

        dsl.insertInto(
                        PIPELINE,
                        PIPELINE.PIPELINE_DEF_ID,
                        PIPELINE.EXTERNAL_ID,
                        PIPELINE.STAGE,
                        PIPELINE.STATUS)
                .values(defId, externalId, NONE_STAGE, Message.PipelineStatus.DONE.getNumber())
                .execute();
    }

    @Override
    void doChangePipelineDef(String defKey, String externalId) {
        var defId = getPipelineDefId(defKey);
        Preconditions.checkArgument(
                Objects.nonNull(defId),
                String.format("The pipeline definition '%s' is not found.", defId));
        dsl.update(PIPELINE)
                .set(PIPELINE.PIPELINE_DEF_ID, defId)
                .where(PIPELINE.EXTERNAL_ID.eq(externalId))
                .execute();
    }

    @Override
    public String findPipelineDefKey(String externalId) {
        return pipelineManager.findPipelineDefKey(externalId);
    }

    private String getPipelineDefId(String pipelineDefKey) {
        return dsl.select(PIPELINE_DEF.ID)
                .from(PIPELINE_DEF)
                .where(PIPELINE_DEF.KEY.eq(pipelineDefKey))
                .fetchOne(PIPELINE_DEF.ID);
    }

    private String findPipelineId(String externalId) {
        return dsl.select(PIPELINE.ID)
                .from(PIPELINE)
                .where(PIPELINE.EXTERNAL_ID.eq(externalId))
                .fetchOne(PIPELINE.ID);
    }

    private String getAndCheckPipelineId(String externalId) {
        var id = findPipelineId(externalId);
        Preconditions.checkArgument(
                Objects.nonNull(id), "Cannot find pipeline with pipeline id '%s'", id);
        return id;
    }

    @Override
    public void setStageOwner(String externalId, int stage, String ownerId) {
        var pipelineId = getAndCheckPipelineId(externalId);
        Preconditions.checkArgument(
                Objects.nonNull(pipelineId), "The pipeline %s is not found", pipelineId);
        Instant now = Instant.now();
        Condition condition;
        if (ownerId != null) {
            condition = PIPELINE_STAGE.OWNER_ID.isNull().or(PIPELINE_STAGE.OWNER_ID.ne(ownerId));
        } else {
            condition = PIPELINE_STAGE.OWNER_ID.isNotNull();
        }
        dsl.insertInto(
                        PIPELINE_STAGE,
                        PIPELINE_STAGE.PIPELINE_ID,
                        PIPELINE_STAGE.STAGE,
                        PIPELINE_STAGE.STATUS,
                        PIPELINE_STAGE.OWNER_ID)
                .values(pipelineId, stage, Message.PipelineStatus.PENDING_VALUE, ownerId)
                .onConflict(PIPELINE_STAGE.PIPELINE_ID, PIPELINE_STAGE.STAGE)
                .doUpdate()
                .set(PIPELINE_STAGE.OWNER_ID, ownerId)
                .set(PIPELINE_STAGE.UPDATED_AT, Timestamp.from(now))
                .where(condition)
                .execute();
        log.info(
                "Successfully set pipeline '{}' stage '{}' owner to '{}'",
                externalId,
                stage,
                ownerId);
    }

    @Override
    public void setTaskOwner(String externalId, String taskKey, String ownerId, String changedBy) {
        var recordId = assignManager.createRecordReturningId(null, null, null, ownerId);
        taskService.setTaskOwner(externalId, taskKey, ownerId, recordId, changedBy);
    }

    @Override
    public AssignSummary batchSetTaskOwner(
            Iterable<? extends TaskAssignRecord> assignedTaskList,
            @Nullable Message.TaskAssignChannelEnum channel,
            @Nullable String channelExternalId,
            @Nullable String changedBy,
            @Nullable String assignGroupId,
            @Nullable String assignRecordId) {
        if (Objects.isNull(assignRecordId)) {
            assignRecordId =
                    assignManager.createRecordReturningId(
                            channelExternalId, channel, changedBy, assignGroupId);
        }

        taskService.batchSetTaskOwner(assignedTaskList, assignRecordId);
        return AssignRecordSummaryUtil.summaryRecordInOneRecordId(
                assignedTaskList, assignRecordId, null);
    }

    @Override
    public void setTaskStatus(
            String externalId,
            String taskKey,
            Message.PipelineStatus status,
            @Nullable String comment,
            String updatedBy) {
        taskService.setTaskStatus(externalId, taskKey, status, comment, updatedBy);
        log.info(
                "Successfully set pipeline '{}' task '{}' to '{}' with comment '{}' by user '{}'",
                externalId,
                taskKey,
                status,
                comment,
                updatedBy);
    }

    @Override
    public void batchSetTaskStatus(
            Iterable<SetTaskStatusRequest> requests, @Nullable String updateBy) {
        taskService.batchSetTaskStatus(requests, updateBy);
        log.info("Successfully batch set pipeline task '{}' by user '{}'", requests, updateBy);
    }

    @Override
    public void closePipeline(String id, @Nullable String changedBy, @Nullable Long version) {
        pipelineManager.closePipeline(id, changedBy, version);
        pipelineManager.calAndSetPipelineStatus(id);
    }

    @Override
    public void recoverPipeline(String id, @Nullable String changedBy, @Nullable Long version) {
        pipelineManager.recoverPipeline(id, changedBy, version);
        pipelineManager.calAndSetPipelineStatus(id);
    }

    @Override
    public Stage findPipelineState(String pipelineId) {
        return pipelineManager.findPipelineState(pipelineId);
    }

    @Override
    public Pipeline findById(String externalId) {
        var defKey = findPipelineDefKey(externalId);
        if (defKey == null) {
            return null;
        }
        var tasks = Iterables.toSet(taskService.getByPipelineId(externalId));
        var stages = pipelineManager.getStage(externalId);
        Message.PipelineMessage.Builder builder = Message.PipelineMessage.newBuilder();
        builder.setId(externalId);
        builder.setDef(getDef(defKey));
        builder.addAllTask(Iterables.transform(tasks, Proto::toMessage));
        builder.addAllStage(Iterables.transform(stages, Proto::toMessage));
        return Pipeline.from(builder.build());
    }

    @Override
    public Pipeline findByIdAndStage(String externalId, int stage) {
        var defKey = findPipelineDefKey(externalId);
        if (defKey == null) {
            return null;
        }
        var tasks = Iterables.toStream(taskService.getByPipelineIdAndStage(externalId, stage));
        var pipelineStage = pipelineManager.getStageByStage(externalId, stage);
        Message.PipelineMessage.Builder builder = Message.PipelineMessage.newBuilder();
        builder.setId(externalId);
        builder.setDef(getDef(defKey));
        Optional.ofNullable(pipelineStage)
                .ifPresent(s -> builder.addAllStage(Set.of(s.toMessage())));
        builder.addAllTask(tasks.map(Proto::toMessage).collect(Collectors.toSet()));
        return Pipeline.from(builder.build());
    }

    @Override
    public Pipeline findByIdAndStageWithPrereq(String externalId, int stage) {
        var defKey = findPipelineDefKey(externalId);
        if (defKey == null) {
            return null;
        }
        var tasks =
                Iterables.toStream(
                        taskService.getByPipelineIdAndStageWithPrereq(externalId, stage));
        Message.PipelineMessage.Builder builder = Message.PipelineMessage.newBuilder();
        builder.setId(externalId);
        builder.setDef(getDef(defKey));
        builder.addAllTask(tasks.map(Proto::toMessage).collect(Collectors.toSet()));
        return Pipeline.from(builder.build());
    }

    @Override
    public Iterable<String> findPipelineIdByTask(Iterable<PipelineTaskQuery> queries) {
        return pipelineManager.findPipelineIdByTask(queries);
    }

    @Override
    public void batchSetTaskSupervisor(
            Iterable<SetTaskSupervisorRequest> requests, String changedBy) {
        taskService.batchSetTaskSupervisor(requests, changedBy);
    }

    private Message.PipelineDefMessage getDef(String defKey) {
        return Message.PipelineDefMessage.newBuilder().setKey(defKey).build();
    }
}
