package com.bees360.pipeline;

import static com.bees360.pipeline.TestUtils.randomKey;
import static com.bees360.pipeline.TestUtils.randomOwner;
import static com.bees360.pipeline.TestUtils.randomPipelineDef;
import static com.bees360.pipeline.TestUtils.randomStatus;
import static com.bees360.pipeline.TestUtils.randomTaskDef;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.pipeline.assign.JooqAssignManager;
import com.bees360.util.Iterables;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@SpringBootTest
@ApplicationAutoConfig
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:PipelineITest.yml")
public class JooqPipelineServiceTest extends AbstractPipelineServiceTest {

    @Import({
        JooqConfig.class,
        JooqPipelineDefService.class,
        JooqPipelineDefService.JooqPipelineDefRepository.class,
        JooqTaskDefService.class,
        JooqPipelineService.class,
        JooqPipelineManager.class,
        JooqAssignManager.class,
    })
    @Configuration
    static class Config {}

    @Autowired private PipelineDefService pipelineDefService;
    @Autowired private PipelineService pipelineService;
    @Autowired private TaskService taskService;
    @Autowired private JooqPipelineManager pipelineManager;

    @Override
    PipelineService getPipelineService() {
        return pipelineService;
    }

    @Override
    PipelineDefService getPipelineDefService() {
        return pipelineDefService;
    }

    @Test
    void testSetPipelineTaskStatus() {
        super.testSetPipelineTaskStatus();
    }

    // 已废弃该逻辑
    @Disabled
    @Test
    void testRemovedTaskShouldNotBeFoundByGetDirectNextTasks() {
        PipelineTaskDef taskDef1 = randomTaskDef();
        PipelineTaskDef taskDef2 = randomTaskDef();
        PipelineTaskDef taskDef3 = randomTaskDef();
        PipelineDef pipelineDef =
                randomPipelineDef(
                        Set.of(
                                taskDef1,
                                taskDef2.addPrerequisite(Set.of(taskDef1.getKey())),
                                taskDef3.addPrerequisite(Set.of(taskDef2.getKey()))));
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDef);

        String pipelineId = randomKey();
        getPipelineService().createPipeline(pipelineId, defKey);
        var tasks = taskService.getDirectNextTasks(pipelineId, taskDef1.getKey());
        Assertions.assertEquals(
                1, Iterables.toList(tasks).size(), "The pipeline task size should be 1");

        pipelineDef =
                PipelineDef.from(
                        Message.PipelineDefMessage.newBuilder()
                                .setKey(pipelineDef.getKey())
                                .addTask(taskDef1.toMessage())
                                .build());
        updatePipelineDef(pipelineDef);

        tasks = taskService.getDirectNextTasks(pipelineId, taskDef1.getKey());
        Assertions.assertEquals(
                0, Iterables.toList(tasks).size(), "The pipeline task size should be 0");
    }

    @Test
    void testGetDirectNextTasks() {
        PipelineTaskDef taskDef1 = randomTaskDef();
        PipelineTaskDef taskDef2 = randomTaskDef();
        PipelineTaskDef taskDef3 = randomTaskDef();
        PipelineDef pipelineDef =
                randomPipelineDef(
                        Set.of(
                                taskDef1,
                                taskDef2.addPrerequisite(Set.of(taskDef1.getKey())),
                                taskDef3.addPrerequisite(Set.of(taskDef2.getKey()))));
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDef);

        String pipelineId = randomKey();
        getPipelineService().createPipeline(pipelineId, defKey);
        var tasks = taskService.getDirectNextTasks(pipelineId, taskDef1.getKey());
        Assertions.assertEquals(
                1, Iterables.toList(tasks).size(), "The pipeline task size should be 1");
    }

    // 已废弃该逻辑
    @Disabled
    @Test
    void testRemovedTaskShouldNotBeFoundByGetNextTasks() {
        PipelineTaskDef taskDef1 = randomTaskDef();
        PipelineTaskDef taskDef2 = randomTaskDef();
        PipelineTaskDef taskDef3 = randomTaskDef();
        PipelineDef pipelineDef =
                randomPipelineDef(
                        Set.of(
                                taskDef1,
                                taskDef2.addPrerequisite(Set.of(taskDef1.getKey())),
                                taskDef3.addPrerequisite(Set.of(taskDef2.getKey()))));
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDef);

        String pipelineId = randomKey();
        getPipelineService().createPipeline(pipelineId, defKey);
        var tasks = taskService.getNextTasks(pipelineId, Set.of(taskDef1.getKey()));
        Assertions.assertEquals(
                2, Iterables.toList(tasks).size(), "The pipeline task size should be 2");

        pipelineDef =
                PipelineDef.from(
                        Message.PipelineDefMessage.newBuilder()
                                .setKey(pipelineDef.getKey())
                                .addTask(taskDef1.toMessage())
                                .build());
        updatePipelineDef(pipelineDef);

        tasks = taskService.getNextTasks(pipelineId, Set.of(taskDef1.getKey()));
        Assertions.assertEquals(
                0, Iterables.toList(tasks).size(), "The pipeline task size should be 0");
    }

    @Test
    void testGetNextTasks() {
        PipelineTaskDef taskDef1 = randomTaskDef();
        PipelineTaskDef taskDef2 = randomTaskDef();
        PipelineTaskDef taskDef3 = randomTaskDef();
        PipelineDef pipelineDef =
                randomPipelineDef(
                        Set.of(
                                taskDef1,
                                taskDef2.addPrerequisite(Set.of(taskDef1.getKey())),
                                taskDef3.addPrerequisite(Set.of(taskDef2.getKey()))));
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDef);

        String pipelineId = randomKey();
        getPipelineService().createPipeline(pipelineId, defKey);
        var tasks = taskService.getNextTasks(pipelineId, Set.of(taskDef1.getKey()));
        Assertions.assertEquals(
                2, Iterables.toList(tasks).size(), "The pipeline task size should be 2");
    }

    // 已废弃该逻辑
    @Disabled
    @Test
    void testRemovedTaskShouldNotBeFoundByStageWithPrereq() {
        PipelineTaskDef taskDef1 = randomTaskDef(1);
        PipelineTaskDef taskDef2 = randomTaskDef(2);
        PipelineTaskDef taskDef3 = randomTaskDef(3);
        PipelineDef pipelineDef =
                randomPipelineDef(
                        Set.of(
                                taskDef1,
                                taskDef2.addPrerequisite(Set.of(taskDef1.getKey())),
                                taskDef3.addPrerequisite(Set.of(taskDef2.getKey()))));
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDef);

        String pipelineId = randomKey();
        getPipelineService().createPipeline(pipelineId, defKey);
        var tasks = taskService.getByPipelineIdAndStageWithPrereq(pipelineId, taskDef2.getStage());
        Assertions.assertEquals(
                2, Iterables.toList(tasks).size(), "The pipeline task size should be 2");

        pipelineDef =
                PipelineDef.from(
                        Message.PipelineDefMessage.newBuilder()
                                .setKey(pipelineDef.getKey())
                                .addTask(taskDef2.toMessage())
                                .build());
        updatePipelineDef(pipelineDef);

        tasks = taskService.getByPipelineIdAndStageWithPrereq(pipelineId, taskDef2.getStage());
        Assertions.assertEquals(
                1, Iterables.toList(tasks).size(), "The pipeline task size should be 1");
    }

    @Test
    void testGetPipelineDefByStageWithPrereq() {
        PipelineTaskDef taskDef1 = randomTaskDef(1);
        PipelineTaskDef taskDef2 = randomTaskDef(2);
        PipelineTaskDef taskDef3 = randomTaskDef(3);
        PipelineDef pipelineDef =
                randomPipelineDef(
                        Set.of(
                                taskDef1,
                                taskDef2.addPrerequisite(Set.of(taskDef1.getKey())),
                                taskDef3.addPrerequisite(Set.of(taskDef2.getKey()))));
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDef);

        String pipelineId = randomKey();
        getPipelineService().createPipeline(pipelineId, defKey);
        var tasks = taskService.getByPipelineIdAndStageWithPrereq(pipelineId, taskDef2.getStage());
        Assertions.assertEquals(
                2, Iterables.toList(tasks).size(), "The pipeline task size should be 2");
    }

    @Test
    void testSetPipelineTaskOwner() {
        super.testSetPipelineTaskOwner();
    }

    @Test
    void testSetPipelineTaskOwnerWithNullChangedByShouldSuccess() {
        super.testSetPipelineTaskOwnerWithNullChangedByShouldSuccess();
    }

    @Test
    void testBatchSetTaskOwnerShouldSucceed() {
        super.testBatchSetTaskOwnerShouldSucceed();
    }

    @Test
    void testBatchSetTaskStatusShouldSucceed() {
        super.testBatchSetTaskStatusShouldSucceed();
    }

    @Test
    void testBatchSetWithIllegalPipelineShouldThrowAndOtherShouldSucceed() {
        super.testBatchSetWithIllegalPipelineShouldThrowAndOtherShouldSucceed();
    }

    @Test
    void testSetPipelineStageOwner() {
        super.testSetPipelineStageOwner();
    }

    @Test
    void testFindByStage() {
        super.testFindByStage();
    }

    @Test
    void testFindByStageWithPrereq() {
        super.testFindByStageWithPrereq();
    }

    @Test
    void testGetPipelineIdByTask() {
        super.testGetPipelineIdByTask();
    }

    @Test
    void testGetPipelineIdByTaskWithOwner() {
        super.testGetPipelineIdByTaskWithOwner();
    }

    @Test
    void testGetPipelineIdByTaskAndWithTimeRange1() {
        super.testGetPipelineIdByTaskAndWithTimeRange1();
    }

    @Test
    void testGetPipelineIdByTaskAndWithTimeRange2() {
        super.testGetPipelineIdByTaskAndWithTimeRange2();
    }

    @Test
    void testGetPipelineIdByTaskAndWithTimeRange3() {
        super.testGetPipelineIdByTaskAndWithTimeRange3();
    }

    @Test
    void testGetPipelineIdByTaskAndWithTimeRange4() {
        super.testGetPipelineIdByTaskAndWithTimeRange4();
    }

    @Test
    void testFindByStageWithPrereqWithSameTaskDefInDiffPipeline() {
        // a -> b -> c
        var stageA = 1;
        var stageB = 2;
        var stageC = 3;
        var taskDefA = randomTaskDef(stageA);
        var taskDefB = randomTaskDef(stageB);
        var taskDefC = randomTaskDef(stageC);
        var taskDefBWithPrereq = taskDefB.addPrerequisite(Set.of(taskDefA.getKey()));
        var taskDefCWithPrereq = taskDefC.addPrerequisite(Set.of(taskDefB.getKey()));
        var def = randomPipelineDef(Set.of(taskDefA, taskDefB, taskDefC));
        var defWithPrereq =
                randomPipelineDef(Set.of(taskDefA, taskDefBWithPrereq, taskDefCWithPrereq));
        createPipelineDef(def);
        createPipelineDef(defWithPrereq);

        var pipelineId = randomKey();
        var pipelineIdWithPrereq = randomKey();
        pipelineService.createPipeline(pipelineId, def.getKey());
        pipelineService.createPipeline(pipelineIdWithPrereq, defWithPrereq.getKey());
        var pipeline = pipelineService.getByIdAndStageWithPrereq(pipelineId, stageB);
        var taskMap =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        Assertions.assertTrue(
                taskMap.containsKey(taskDefB.getKey()),
                String.format("The task '%s' should be contained.", taskDefB.getKey()));
        Assertions.assertFalse(
                taskMap.containsKey(taskDefA.getKey()),
                String.format("The task '%s' should not be contained.", taskDefA.getKey()));
        Assertions.assertFalse(
                taskMap.containsKey(taskDefC.getKey()),
                String.format("The task '%s' should not be contained.", taskDefA.getKey()));

        pipeline = pipelineService.getByIdAndStageWithPrereq(pipelineIdWithPrereq, stageB);
        taskMap =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        Assertions.assertTrue(
                taskMap.containsKey(taskDefB.getKey()),
                String.format("The task '%s' should be contained.", taskDefB.getKey()));
        Assertions.assertTrue(
                taskMap.containsKey(taskDefA.getKey()),
                String.format("The task '%s' should be contained.", taskDefA.getKey()));
        Assertions.assertFalse(
                taskMap.containsKey(taskDefC.getKey()),
                String.format("The task '%s' should not be contained.", taskDefA.getKey()));
    }

    @Test
    void testSetTaskStatusThrowIllegalArgumentExceptionWhenPipelineDoesNotExist() {
        super.testSetTaskStatusThrowIllegalArgumentExceptionWhenPipelineDoesNotExist();
    }

    @Test
    void testSetTaskOwnerThrowIllegalArgumentExceptionWhenPipelineDestNotExist() {
        super.testSetTaskOwnerThrowIllegalArgumentExceptionWhenPipelineDestNotExist();
    }

    @Test
    void testSetStageOwnerThrowIllegalArgumentExceptionWhenPipelineDoesNotExist() {
        super.testSetStageOwnerThrowIllegalArgumentExceptionWhenPipelineDoesNotExist();
    }

    @Test
    void testTaskStageOwnerThrowIllegalArgumentExceptionWhenTaskIsNotInPipeline() {
        PipelineTaskDef taskDef1 = randomTaskDef();
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDef1));
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDef);
        getPipelineService().createPipeline(randomKey(), defKey);

        PipelineDef pipelineDef2 = randomPipelineDef(Set.of(randomTaskDef()));
        createPipelineDef(pipelineDef2);
        String pipelineId = randomKey();
        getPipelineService().createPipeline(pipelineId, pipelineDef2.getKey());
        Assertions.assertThrows(
                IllegalArgumentException.class,
                () ->
                        getPipelineService()
                                .setTaskOwner(
                                        pipelineId,
                                        taskDef1.getKey(),
                                        randomOwner().getId(),
                                        randomOwner().getId()));
    }

    @Test
    void testTaskStatusThrowIllegalArgumentExceptionWhenTaskIsNotInPipeline() {
        PipelineTaskDef taskDef1 = randomTaskDef();
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDef1));
        String defKey = pipelineDef.getKey();
        createPipelineDef(pipelineDef);
        getPipelineService().createPipeline(randomKey(), defKey);

        PipelineDef pipelineDef2 = randomPipelineDef(Set.of(randomTaskDef()));
        createPipelineDef(pipelineDef2);
        String pipelineId = randomKey();
        getPipelineService().createPipeline(pipelineId, pipelineDef2.getKey());
        Assertions.assertThrows(
                IllegalArgumentException.class,
                () ->
                        getPipelineService()
                                .setTaskStatus(pipelineId, taskDef1.getKey(), randomStatus()));
    }

    @Test
    void testSaveInvalidPipelineDefKeyThrowIllegalArgumentException() {
        PipelineTaskDef taskDef1 = randomTaskDef();
        PipelineDef pipelineDef =
                PipelineDef.from(
                        Message.PipelineDefMessage.newBuilder()
                                .setKey(RandomStringUtils.randomAlphabetic(1025))
                                .addTask(taskDef1.toMessage())
                                .build());
        Assertions.assertThrows(
                IllegalArgumentException.class, () -> createPipelineDef(pipelineDef));
    }

    @Test
    void testSaveInvalidPipelineTaskDefKeyThrowIllegalArgumentException() {
        PipelineTaskDef taskDef =
                PipelineTaskDef.from(
                        Message.PipelineDefMessage.Task.newBuilder()
                                .setKey(RandomStringUtils.randomAlphabetic(33))
                                .build());
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDef));
        Assertions.assertThrows(
                IllegalArgumentException.class, () -> createPipelineDef(pipelineDef));
    }

    @Test
    void testUpdateNotExistsPipeline() {
        try {
            pipelineManager.calAndSetPipelineStatus("aaa");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof IndexOutOfBoundsException);
        }
    }
}
