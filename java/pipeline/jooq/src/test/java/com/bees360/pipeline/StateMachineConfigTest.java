package com.bees360.pipeline;

import static com.bees360.pipeline.Constants.TASK_KEY_HEADER;
import static com.bees360.pipeline.Event.CONDITION_READY;
import static com.bees360.pipeline.Event.EXCEPTION;
import static com.bees360.pipeline.Event.FINISH;
import static com.bees360.pipeline.Event.RESET;
import static com.bees360.pipeline.Event.START;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.pipeline.Message.PipelineStatus;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.StateMachineBuilder;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringBootTest
@ApplicationAutoConfig
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:PipelineITest.yml")
class StateMachineConfigTest {

    @Import(value = {JooqConfig.class, StateMachineBuilderConfig.class})
    @Configuration
    static class Config {
        @Bean
        public StateMachineService<String, PipelineStatus, Event> stateMachineService(
                StateMachineBuilder.Builder<PipelineStatus, Event> builder) {
            return new InMemoryStateMachineService<>(builder);
        }
    }

    @Autowired private StateMachineService<String, PipelineStatus, Event> stateMachineService;

    // ### start with pending
    @Test
    void testPending2Ready() {
        String key = randomKey();

        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(key);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());

        // from pending to ready
        stateMachineService.sendEvent(key, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
    }

    @Test
    void testPending2OngoingNotwork() {
        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // try to be ongoing from pending, but not work.
        stateMachineService.sendEvent(taskName, START);
        assertNotEquals(PipelineStatus.ONGOING, sm.getState().getId());
        // still pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
    }

    @Test
    void testPending2DoneNotwork() {

        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // try to be done from pending, but not work.
        stateMachineService.sendEvent(taskName, FINISH);
        assertNotEquals(PipelineStatus.DONE, sm.getState().getId());
        // still pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
    }

    @Test
    void testPending2ErrorNotwork() {

        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // try to be error from pending, but not work.
        stateMachineService.sendEvent(taskName, EXCEPTION);
        assertNotEquals(PipelineStatus.ERROR, sm.getState().getId());
        // still pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
    }

    // ### start with ready
    @Test
    void testReady2Ongoing() {

        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to ongoing
        stateMachineService.sendEvent(taskName, START);
        assertEquals(PipelineStatus.ONGOING, sm.getState().getId());
    }

    @Test
    void testReady2Done() {

        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to done
        stateMachineService.sendEvent(taskName, FINISH);
        assertEquals(PipelineStatus.DONE, sm.getState().getId());
    }

    @Test
    void testReady2Error() {

        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());

        // from pending to error
        stateMachineService.sendEvent(taskName, EXCEPTION);
        assertEquals(PipelineStatus.ERROR, sm.getState().getId());
    }

    @Test
    void testReady2Pending() {

        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());

        // from ready to pending
        stateMachineService.sendEvent(taskName, RESET);
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
    }

    // ### start with ongoing
    @Test
    void testOnGoing2Done() {

        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to ongoing
        stateMachineService.sendEvent(taskName, START);
        assertEquals(PipelineStatus.ONGOING, sm.getState().getId());
        // from ongoing to done
        stateMachineService.sendEvent(taskName, FINISH);
        assertEquals(PipelineStatus.DONE, sm.getState().getId());
    }

    @Test
    void testOnGoing2Error() {
        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to ongoing
        stateMachineService.sendEvent(taskName, START);
        assertEquals(PipelineStatus.ONGOING, sm.getState().getId());
        // from ongoing to error
        stateMachineService.sendEvent(taskName, EXCEPTION);
        assertEquals(PipelineStatus.ERROR, sm.getState().getId());
    }

    @Test
    void testOnGoing2Pending() {
        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to ongoing
        stateMachineService.sendEvent(taskName, START);
        assertEquals(PipelineStatus.ONGOING, sm.getState().getId());
        // from ongoing to pending
        stateMachineService.sendEvent(taskName, RESET);
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
    }

    @Test
    void testOnGoing2ReadyNotwork() {
        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to ongoing
        stateMachineService.sendEvent(taskName, START);
        assertEquals(PipelineStatus.ONGOING, sm.getState().getId());

        // try to be ready from ongoing, but not work.
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertNotEquals(PipelineStatus.READY, sm.getState().getId());

        // still in ongoing
        assertEquals(PipelineStatus.ONGOING, sm.getState().getId());
    }

    // ### start with done
    @Test
    void testDone2Ready() {
        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to ongoing
        stateMachineService.sendEvent(taskName, START);
        assertEquals(PipelineStatus.ONGOING, sm.getState().getId());
        // from ongoing to done
        stateMachineService.sendEvent(taskName, FINISH);
        assertEquals(PipelineStatus.DONE, sm.getState().getId());
        // from done to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
    }

    @Test
    void testDone2Pending() {
        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to ongoing
        stateMachineService.sendEvent(taskName, START);
        assertEquals(PipelineStatus.ONGOING, sm.getState().getId());
        // from ongoing to done
        stateMachineService.sendEvent(taskName, FINISH);
        assertEquals(PipelineStatus.DONE, sm.getState().getId());
        // from done to pending
        stateMachineService.sendEvent(taskName, RESET);
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
    }

    @Test
    void testDone2PendingShouldNotWorkBecauseGuard() {
        String taskKey = "test_task";
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskKey);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskKey, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ongoing to done
        stateMachineService.sendEvent(taskKey, FINISH);
        assertEquals(PipelineStatus.DONE, sm.getState().getId());
        // try to be pending from done, but not work.
        var ms = MessageBuilder.withPayload(RESET).setHeader(TASK_KEY_HEADER, taskKey).build();
        sm.sendEvent(ms);
        assertEquals(PipelineStatus.DONE, sm.getState().getId());
    }

    @Test
    void testDone2OngoingNotwork() {
        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to ongoing
        stateMachineService.sendEvent(taskName, START);
        assertEquals(PipelineStatus.ONGOING, sm.getState().getId());
        // from ongoing to done
        stateMachineService.sendEvent(taskName, FINISH);
        assertEquals(PipelineStatus.DONE, sm.getState().getId());
        // try to be ongoing from done, but not work.
        stateMachineService.sendEvent(taskName, START);
        assertNotEquals(PipelineStatus.ONGOING, sm.getState().getId());

        // still in done
        assertEquals(PipelineStatus.DONE, sm.getState().getId());
    }

    @Test
    void testDone2Error() {

        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to ongoing
        stateMachineService.sendEvent(taskName, START);
        assertEquals(PipelineStatus.ONGOING, sm.getState().getId());
        // from ongoing to done
        stateMachineService.sendEvent(taskName, FINISH);
        assertEquals(PipelineStatus.DONE, sm.getState().getId());
        // from done to error
        stateMachineService.sendEvent(taskName, EXCEPTION);
        assertEquals(PipelineStatus.ERROR, sm.getState().getId());
    }

    // ### start with error
    @Test
    void testError2OnGoing() {
        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to error
        stateMachineService.sendEvent(taskName, EXCEPTION);
        assertEquals(PipelineStatus.ERROR, sm.getState().getId());
        // from error to ongoing
        stateMachineService.sendEvent(taskName, START);
        assertEquals(PipelineStatus.ONGOING, sm.getState().getId());
    }

    @Test
    void testError2Done() {
        String taskName = randomKey();
        StateMachine<PipelineStatus, Event> sm = stateMachineService.getStateMachine(taskName);
        sm.start();
        // initial with pending
        assertEquals(PipelineStatus.PENDING, sm.getState().getId());
        // from pending to ready
        stateMachineService.sendEvent(taskName, CONDITION_READY);
        assertEquals(PipelineStatus.READY, sm.getState().getId());
        // from ready to error
        stateMachineService.sendEvent(taskName, EXCEPTION);
        assertEquals(PipelineStatus.ERROR, sm.getState().getId());
        // from error to done
        stateMachineService.sendEvent(taskName, FINISH);
        assertEquals(PipelineStatus.DONE, sm.getState().getId());
    }

    String randomKey() {
        return RandomStringUtils.randomAlphanumeric(16);
    }
}
