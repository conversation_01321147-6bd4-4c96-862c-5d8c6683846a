package com.bees360.pipeline;

import static com.bees360.pipeline.Message.PipelineStatus.DONE;
import static com.bees360.pipeline.Message.PipelineStatus.PENDING;
import static com.bees360.pipeline.Message.PipelineStatus.READY;
import static com.bees360.pipeline.TestUtils.createPipelineDef;
import static com.bees360.pipeline.TestUtils.randomKey;
import static com.bees360.pipeline.TestUtils.randomPipelineDef;
import static com.bees360.pipeline.TestUtils.randomTaskDef;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.util.Iterables;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@SpringBootTest
@ApplicationAutoConfig
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:PipelineITest.yml")
class TaskServiceITest {
    @Import({JooqPipelineConfig.class})
    @Configuration
    static class Config {}

    @Autowired private PipelineService pipelineService;
    @Autowired private PipelineDefService pipelineDefService;

    @Test
    void testAddTaskToTaskPipelineTail() {
        // Pipeline: A
        PipelineTaskDef taskDefA = randomTaskDef();
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDefA));
        String defKey1 = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        pipelineService.createPipeline(pipelineId, defKey1);

        // Pipeline: A -> B
        PipelineTaskDef taskDefB = randomTaskDef().addPrerequisite(Set.of(taskDefA.getKey()));
        var pipelineDef2 = randomPipelineDef(Set.of(taskDefA, taskDefB));
        createPipelineDef(pipelineDefService, pipelineDef2);
        String defKey2 = pipelineDef2.getKey();
        pipelineService.changePipelineDef(pipelineId, defKey2);

        var pipeline = pipelineService.getById(pipelineId);
        Map<String, PipelineTask> taskMap =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        Assertions.assertEquals(READY, taskMap.get(taskDefA.getKey()).getStatus());
        Assertions.assertEquals(PENDING, taskMap.get(taskDefB.getKey()).getStatus());
    }

    @Test
    void testAddTaskToPipelineTail2() {
        // Pipeline: A
        PipelineTaskDef taskDefA = randomTaskDef();
        var pipelineDef = randomPipelineDef(Set.of(taskDefA));
        String defKey1 = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        pipelineService.createPipeline(pipelineId, defKey1);
        pipelineService.setTaskStatus(pipelineId, taskDefA.getKey(), DONE);

        // Pipeline: A -> B
        PipelineTaskDef taskDefB = randomTaskDef().addPrerequisite(Set.of(taskDefA.getKey()));
        var pipelineDef2 = randomPipelineDef(Set.of(taskDefA, taskDefB));
        createPipelineDef(pipelineDefService, pipelineDef2);
        String defKey2 = pipelineDef2.getKey();
        pipelineService.changePipelineDef(pipelineId, defKey2);

        var pipeline = pipelineService.getById(pipelineId);
        Map<String, PipelineTask> taskMap =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        Assertions.assertEquals(DONE, taskMap.get(taskDefA.getKey()).getStatus());
        Assertions.assertEquals(READY, taskMap.get(taskDefB.getKey()).getStatus());
    }

    @Test
    void testAddTaskToPipelineHead() {
        // Pipeline: A
        PipelineTaskDef taskDefA = randomTaskDef();
        var pipelineDef = randomPipelineDef(Set.of(taskDefA));
        String defKey1 = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        pipelineService.createPipeline(pipelineId, defKey1);

        // Pipeline: B -> A
        PipelineTaskDef taskDefB = randomTaskDef();
        var pipelineDef2 =
                randomPipelineDef(
                        Set.of(taskDefA.addPrerequisite(Set.of(taskDefB.getKey())), taskDefB));
        taskDefA = taskDefA.addPrerequisite(Set.of(taskDefB.getKey()));
        createPipelineDef(pipelineDefService, pipelineDef2);
        String defKey2 = pipelineDef2.getKey();
        pipelineService.changePipelineDef(pipelineId, defKey2);

        var pipeline = pipelineService.getById(pipelineId);
        Map<String, PipelineTask> taskMap =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        Assertions.assertEquals(READY, taskMap.get(taskDefB.getKey()).getStatus());
        Assertions.assertEquals(PENDING, taskMap.get(taskDefA.getKey()).getStatus());
    }

    @Test
    void testAddTaskToPipelineHead2() {
        // Pipeline: A
        PipelineTaskDef taskDefA = randomTaskDef();
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDefA));
        String defKey1 = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        pipelineService.createPipeline(pipelineId, defKey1);
        pipelineService.setTaskStatus(pipelineId, taskDefA.getKey(), DONE);

        // Pipeline: B -> A
        PipelineTaskDef taskDefB = randomTaskDef();
        taskDefA = taskDefA.addPrerequisite(Set.of(taskDefB.getKey()));
        PipelineDef pipelineDef2 = randomPipelineDef(Set.of(taskDefA, taskDefB));
        createPipelineDef(pipelineDefService, pipelineDef2);
        String defKey2 = pipelineDef2.getKey();
        pipelineService.changePipelineDef(pipelineId, defKey2);

        var pipeline = pipelineService.getById(pipelineId);
        Map<String, PipelineTask> taskMap =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        Assertions.assertEquals(PENDING, taskMap.get(taskDefA.getKey()).getStatus());
        Assertions.assertEquals(READY, taskMap.get(taskDefB.getKey()).getStatus());
    }

    @Test
    void testAddTaskToPipelineMiddle() {
        // Pipeline: A -> B
        PipelineTaskDef taskDefA = randomTaskDef();
        PipelineTaskDef taskDefB = randomTaskDef();
        PipelineDef pipelineDef =
                randomPipelineDef(
                        Set.of(taskDefA, taskDefB.addPrerequisite(Set.of(taskDefA.getKey()))));
        String defKey1 = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        pipelineService.createPipeline(pipelineId, defKey1);
        pipelineService.setTaskStatus(pipelineId, taskDefA.getKey(), DONE);

        // Pipeline: A -> C -> B
        PipelineTaskDef taskDefC = randomTaskDef().addPrerequisite(Set.of(taskDefA.getKey()));
        taskDefB = taskDefB.addPrerequisite(Set.of(taskDefC.getKey()));
        PipelineDef pipelineDef2 = randomPipelineDef(Set.of(taskDefA, taskDefB, taskDefC));
        createPipelineDef(pipelineDefService, pipelineDef2);

        String defKey2 = pipelineDef2.getKey();
        pipelineService.changePipelineDef(pipelineId, defKey2);

        var pipeline = pipelineService.getById(pipelineId);
        Map<String, PipelineTask> taskMap =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        Assertions.assertEquals(DONE, taskMap.get(taskDefA.getKey()).getStatus());
        Assertions.assertEquals(READY, taskMap.get(taskDefC.getKey()).getStatus());
        Assertions.assertEquals(PENDING, taskMap.get(taskDefB.getKey()).getStatus());
    }

    @Test
    void testRemoveTaskFromPipeline() {
        // Pipeline: A -> B -> C
        PipelineTaskDef taskDefA = randomTaskDef();
        PipelineTaskDef taskDefB = randomTaskDef();
        PipelineTaskDef taskDefC = randomTaskDef();
        PipelineDef pipelineDef =
                randomPipelineDef(
                        Set.of(
                                taskDefA,
                                taskDefB.addPrerequisite(Set.of(taskDefA.getKey())),
                                taskDefC.addPrerequisite(Set.of(taskDefB.getKey()))));

        String defKey1 = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        pipelineService.createPipeline(pipelineId, defKey1);
        pipelineService.setTaskStatus(pipelineId, taskDefA.getKey(), DONE);
        pipelineService.setTaskStatus(pipelineId, taskDefB.getKey(), DONE);
        pipelineService.setTaskStatus(pipelineId, taskDefC.getKey(), DONE);

        // Pipeline: A -> C
        PipelineDef pipelineDef2 =
                randomPipelineDef(
                        Set.of(taskDefA, taskDefC.addPrerequisite(Set.of(taskDefA.getKey()))));
        createPipelineDef(pipelineDefService, pipelineDef2);
        String defKey2 = pipelineDef2.getKey();
        pipelineService.changePipelineDef(pipelineId, defKey2);

        var pipeline = pipelineService.getById(pipelineId);
        Map<String, PipelineTask> taskMap =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        Assertions.assertFalse(
                taskMap.containsKey(taskDefB.getKey()),
                String.format(
                        "Task %s should not in the pipeline %s", taskDefB.getKey(), pipelineId));
        Assertions.assertEquals(DONE, taskMap.get(taskDefA.getKey()).getStatus());
        Assertions.assertEquals(DONE, taskMap.get(taskDefC.getKey()).getStatus());
    }

    @Test
    void testRemoveTaskFromPipeline2() {
        // Pipeline: A -> B -> C -> D -> E
        PipelineTaskDef taskDefA = randomTaskDef();
        PipelineTaskDef taskDefB = randomTaskDef();
        PipelineTaskDef taskDefC = randomTaskDef();
        PipelineTaskDef taskDefD = randomTaskDef();
        PipelineTaskDef taskDefE = randomTaskDef();
        PipelineDef pipelineDef =
                randomPipelineDef(
                        Set.of(
                                taskDefA,
                                taskDefB.addPrerequisite(Set.of(taskDefA.getKey())),
                                taskDefC.addPrerequisite(Set.of(taskDefB.getKey())),
                                taskDefD.addPrerequisite(Set.of(taskDefC.getKey())),
                                taskDefE.addPrerequisite(Set.of(taskDefD.getKey()))));

        String defKey1 = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        pipelineService.createPipeline(pipelineId, defKey1);
        pipelineService.setTaskStatus(pipelineId, taskDefA.getKey(), DONE);
        pipelineService.setTaskStatus(pipelineId, taskDefB.getKey(), DONE);

        // Pipeline: A -> C
        PipelineDef pipelineDef2 =
                randomPipelineDef(
                        Set.of(taskDefA, taskDefC.addPrerequisite(Set.of(taskDefA.getKey()))));
        createPipelineDef(pipelineDefService, pipelineDef2);
        String defKey2 = pipelineDef2.getKey();
        pipelineService.changePipelineDef(pipelineId, defKey2);

        var pipeline = pipelineService.getById(pipelineId);
        Map<String, PipelineTask> taskMap =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        Assertions.assertFalse(
                taskMap.containsKey(taskDefB.getKey()),
                String.format(
                        "Task %s should not in the pipeline %s", taskDefB.getKey(), pipelineId));
        Assertions.assertEquals(DONE, taskMap.get(taskDefA.getKey()).getStatus());
        Assertions.assertEquals(READY, taskMap.get(taskDefC.getKey()).getStatus());
    }

    @Test
    void testRemoveTaskFromPipeline3() {
        // Pipeline: A -> B -> C -> D -> E -> F
        PipelineTaskDef taskDefA = randomTaskDef();
        PipelineTaskDef taskDefB = randomTaskDef();
        PipelineTaskDef taskDefC = randomTaskDef();
        PipelineTaskDef taskDefD = randomTaskDef();
        PipelineTaskDef taskDefE = randomTaskDef();
        PipelineTaskDef taskDefF = randomTaskDef();
        PipelineDef pipelineDef =
                randomPipelineDef(
                        Set.of(
                                taskDefA,
                                taskDefB.addPrerequisite(Set.of(taskDefA.getKey())),
                                taskDefC.addPrerequisite(Set.of(taskDefB.getKey())),
                                taskDefD.addPrerequisite(Set.of(taskDefC.getKey())),
                                taskDefE.addPrerequisite(Set.of(taskDefD.getKey())),
                                taskDefF.addPrerequisite(Set.of(taskDefE.getKey()))));

        String defKey1 = pipelineDef.getKey();
        createPipelineDef(pipelineDefService, pipelineDef);
        String pipelineId = randomKey();
        pipelineService.createPipeline(pipelineId, defKey1);
        pipelineService.setTaskStatus(pipelineId, taskDefA.getKey(), DONE);
        pipelineService.setTaskStatus(pipelineId, taskDefB.getKey(), DONE);
        pipelineService.setTaskStatus(pipelineId, taskDefC.getKey(), DONE);

        // Pipeline: A -> D -> C
        PipelineDef pipelineDef2 =
                randomPipelineDef(
                        Set.of(
                                taskDefA,
                                taskDefD.addPrerequisite(Set.of(taskDefA.getKey())),
                                taskDefC.addPrerequisite(Set.of(taskDefD.getKey()))));
        createPipelineDef(pipelineDefService, pipelineDef2);
        String defKey2 = pipelineDef2.getKey();
        pipelineService.changePipelineDef(pipelineId, defKey2);

        var pipeline = pipelineService.getById(pipelineId);
        Map<String, PipelineTask> taskMap =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        Assertions.assertFalse(
                taskMap.containsKey(taskDefB.getKey()),
                String.format(
                        "Task %s should not in the pipeline %s", taskDefB.getKey(), pipelineId));

        Assertions.assertEquals(DONE, taskMap.get(taskDefA.getKey()).getStatus());
        Assertions.assertEquals(READY, taskMap.get(taskDefD.getKey()).getStatus());
        Assertions.assertEquals(DONE, taskMap.get(taskDefC.getKey()).getStatus());

        pipelineService.changePipelineDef(pipelineId, defKey1);
        pipeline = pipelineService.getById(pipelineId);
        taskMap =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        Assertions.assertTrue(
                taskMap.containsKey(taskDefA.getKey()),
                String.format("Task %s should in the pipeline %s", taskDefA.getKey(), pipelineId));
        Assertions.assertTrue(
                taskMap.containsKey(taskDefB.getKey()),
                String.format("Task %s should in the pipeline %s", taskDefB.getKey(), pipelineId));
        Assertions.assertTrue(
                taskMap.containsKey(taskDefC.getKey()),
                String.format("Task %s should in the pipeline %s", taskDefC.getKey(), pipelineId));
        Assertions.assertTrue(
                taskMap.containsKey(taskDefD.getKey()),
                String.format("Task %s should in the pipeline %s", taskDefD.getKey(), pipelineId));
        Assertions.assertTrue(
                taskMap.containsKey(taskDefE.getKey()),
                String.format("Task %s should in the pipeline %s", taskDefE.getKey(), pipelineId));
        Assertions.assertTrue(
                taskMap.containsKey(taskDefF.getKey()),
                String.format("Task %s should in the pipeline %s", taskDefF.getKey(), pipelineId));
    }
}
