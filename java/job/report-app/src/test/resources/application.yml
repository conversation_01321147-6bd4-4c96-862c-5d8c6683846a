http:
  client:
    apache:
      maxConnPerRoute: 16
      maxConnTotal: 64
      evictIdleConnectionsAfter: PT10M
      connectionTimeToLive: PT10M
      evictExpiredConnections: true
      redirectStrategy: always
      request:
        connectTimeout: PT15S
        connectionRequestTimeout: PT30S
        socketTimeout: PT15S
        redirectEnabled: true
        expectContinueEnabled: true
        normalizeUri: true

rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password

redis:
  client:
    host: redis
    port: 6379
    database: 0

grpc-client:
  resource:
    disabled: true

# command job list（类别）配置

job:
  command:
    - name: "html_pdf"
      threadCount: 1
    - name: "pdf_compress"
      threadCount: 1

report:
  enable:
    true
  merge:
    enable: true
  generate-dps:
    enable: true
  generate-mps:
    enable: true
  fetch-report-from-zip:
    enable: true
    retry-count: 3
    retry-delay: PT2S
    retry-delay-increase-factor: 1.5

  split:
    enable: true
    split-step: 6
    # 5M
    compress-threshold-size: 5242880
    average-on-slice-two: true
  generate-report-and-save-chain-job:
    enabled: true
  generate-report-annotated-image:
    enabled: true
