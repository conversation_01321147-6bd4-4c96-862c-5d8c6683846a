package com.bees360.job;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.grpc.config.GrpcClientConfig;
import com.bees360.image.config.GrpcImageClientConfig;
import com.bees360.job.command.CommandJobConfig;
import com.bees360.report.config.GrpcReportManagerClientConfig;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import org.springframework.context.annotation.Import;

@EnableEncryptableProperties
@ApplicationAutoConfig
@Import({
    CommandJobConfig.class,
    ReportJobConfig.class,
    FetchReportFromZipJobExecutorConfig.class,
    GenerateReportAndSaveChainJobConfig.class,
    GrpcClientConfig.class,
    GenerateReportAnnotationImageJobExecutorConfig.class,
    GrpcReportManagerClientConfig.class,
    GrpcImageClientConfig.class,
})
public class App {

    public static void main(final String[] args) {
        ExitableSpringApplication.run(App.class, args);
    }
}
