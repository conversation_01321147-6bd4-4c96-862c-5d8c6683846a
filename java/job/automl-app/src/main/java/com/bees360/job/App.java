package com.bees360.job;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.grpc.config.GrpcClientConfig;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import org.springframework.context.annotation.Import;

@EnableEncryptableProperties
@ApplicationAutoConfig
@Import({
    AutomlJobConfig.class,
    GrpcClientConfig.class,
})
public class App {

    public static void main(final String[] args) {
        ExitableSpringApplication.run(App.class, args);
    }
}
