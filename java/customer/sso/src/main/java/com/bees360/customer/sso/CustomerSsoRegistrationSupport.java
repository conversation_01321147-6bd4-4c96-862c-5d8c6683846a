package com.bees360.customer.sso;

import com.bees360.auth.Message;
import com.google.common.base.Preconditions;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Base64;

public class CustomerSsoRegistrationSupport {

    private static final Base64.Encoder b64Encoder = Base64.getEncoder();

    private static final String PLACEHOLDER_REGISTRATION_ID = "{registrationId}";

    private static final String OAUTH_EMAIL_SCOPE = "email";

    private final String oauthRedirectEndpointPattern;

    private final String samlAcsUrlPattern;

    private final String serviceProviderIdPattern;

    public CustomerSsoRegistrationSupport(
            String oauthRedirectEndpoint, String samlAcsEndpoint, String serviceProviderIdBase) {
        this.oauthRedirectEndpointPattern = uriIdPattern(oauthRedirectEndpoint);
        this.samlAcsUrlPattern = uriIdPattern(samlAcsEndpoint);
        this.serviceProviderIdPattern = uriIdPattern(serviceProviderIdBase);
    }

    private static String uriIdPattern(String baseUrl) {
        return UriComponentsBuilder.fromHttpUrl(baseUrl)
                .pathSegment(PLACEHOLDER_REGISTRATION_ID)
                .build()
                .toUriString();
    }

    public Message.OAuthClientMessage createOAuthSsoRegistration(String companyId) {
        String registrationId = generateRegistrationIdFromCompanyId(companyId, SsoLoginType.OAUTH);
        String redirectEndpoint =
                oauthRedirectEndpointPattern.replace(PLACEHOLDER_REGISTRATION_ID, registrationId);
        return Message.OAuthClientMessage.newBuilder()
                .setId(registrationId)
                .setRedirectEndpoint(redirectEndpoint)
                .addScopes(OAUTH_EMAIL_SCOPE)
                .setCompanyId(companyId)
                .build();
    }

    public Message.SamlServiceProviderMessage createSamlSsoRegistration(String companyId) {
        String registrationId = generateRegistrationIdFromCompanyId(companyId, SsoLoginType.SAML);
        String acsUrl = samlAcsUrlPattern.replace(PLACEHOLDER_REGISTRATION_ID, registrationId);
        String serviceProviderId =
                serviceProviderIdPattern.replace(PLACEHOLDER_REGISTRATION_ID, registrationId);
        return Message.SamlServiceProviderMessage.newBuilder()
                .setId(registrationId)
                .setCompanyId(companyId)
                .setRequestSigned(false)
                .setAcsUrl(acsUrl)
                .setServiceProviderId(serviceProviderId)
                .build();
    }

    public static String generateRegistrationIdFromCompanyId(
            String companyId, SsoLoginType ssoLoginType) {
        Preconditions.checkArgument(StringUtils.isNotBlank(companyId));
        String raw = String.join(":", companyId, ssoLoginType.name());
        return b64Encoder.encodeToString(raw.getBytes());
    }

    public enum SsoLoginType {
        SAML,
        OAUTH
    }
}
