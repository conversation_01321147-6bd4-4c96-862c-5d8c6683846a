package com.bees360.report.util;

import com.bees360.report.Message.ReportMessage;
import com.bees360.report.Message.ReportMessage.Resource.Type;
import com.bees360.report.Report;
import com.bees360.report.ReportResource;
import com.bees360.report.ReportSummary;
import com.bees360.resource.Resource;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.Instant;
import java.util.Map;

@Getter
@Setter
@Accessors(chain = true)
public class InMemoryReport implements Report {

    private String id;

    private String type;

    private ReportMessage.Status status;

    private Map<Type, String> resourceUrl;

    private Map<Type, Resource> resource;

    private Map<Type, String> resourceKey;

    private Iterable<? extends ReportResource> resources;

    private String createdBy;

    private Instant createdAt;

    private ReportSummary summary;
}
