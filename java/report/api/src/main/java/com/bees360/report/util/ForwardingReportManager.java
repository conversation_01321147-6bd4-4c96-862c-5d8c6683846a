package com.bees360.report.util;

import com.bees360.report.Message.ReportMessage.Status;
import com.bees360.report.Report;
import com.bees360.report.ReportManager;
import com.bees360.report.ReportResourceSource;
import com.google.common.collect.ForwardingObject;

public abstract class ForwardingReportManager extends ForwardingObject implements ReportManager {

    @Override
    public Iterable<? extends Report> findAllById(Iterable<String> ids) {
        return delegate().findAllById(ids);
    }

    @Override
    public Report create(
            String reportType,
            String summary,
            String createdBy,
            Iterable<? extends ReportResourceSource> reportResources) {
        return delegate().create(reportType, summary, createdBy, reportResources);
    }

    @Override
    public void updateStatus(String id, Status status) {
        delegate().updateStatus(id, status);
    }

    @Override
    public void deleteById(String id) {
        delegate().deleteById(id);
    }

    @Override
    public void deleteAllById(Iterable<String> ids) {
        delegate().deleteAllById(ids);
    }

    @Override
    public Iterable<? extends Report> loadAll() {
        return delegate().loadAll();
    }

    @Override
    protected abstract ReportManager delegate();
}
