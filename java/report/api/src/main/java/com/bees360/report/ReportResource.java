package com.bees360.report;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.Proto;
import com.bees360.report.Message.ReportMessage;
import com.bees360.report.Message.ReportMessage.Resource.Type;
import com.bees360.resource.Resource;
import com.google.protobuf.util.Timestamps;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

public interface ReportResource extends Proto<ReportMessage.Resource> {

    @Nonnull
    String getId();

    @Nonnull
    Type getType();

    @Nonnull
    String getUrl();

    /** Added for refactoring, please use url. */
    @Nonnull
    @Deprecated
    String getKey();

    @Nonnull
    Resource getResource();

    @Nullable
    String getName();

    @Override
    default ReportMessage.Resource toMessage() {
        var resourceBuilder = ReportMessage.Resource.newBuilder();
        resourceBuilder.setId(getId());
        resourceBuilder.setType(getType());
        var metadata = getResource().getMetadata();
        acceptIfNotNull(resourceBuilder::setContentType, metadata.getContentType());
        acceptIfNotNull(resourceBuilder::setContentLength, metadata.getContentLength());
        acceptIfNotNull(resourceBuilder::setETag, metadata.getETag());
        acceptIfNotNull(
                resourceBuilder::setLastModified,
                metadata.getLastModified(),
                t -> Timestamps.fromMillis(t.toEpochMilli()));
        acceptIfNotNull(resourceBuilder::setUrl, getUrl());
        acceptIfNotNull(resourceBuilder::setResourceKey, getKey());
        acceptIfNotNull(resourceBuilder::setName, getName());

        return resourceBuilder.build();
    }
}
