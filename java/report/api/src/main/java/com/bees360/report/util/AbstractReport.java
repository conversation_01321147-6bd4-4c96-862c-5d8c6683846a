package com.bees360.report.util;

import com.bees360.report.Report;

import java.util.Objects;

abstract class AbstractReport implements Report {

    public static boolean equals(final Report report, final Object obj) {
        return obj instanceof Report && Objects.equals(report.getId(), ((Report) obj).getId());
    }

    public static int hashCode(final Report report) {
        return Objects.hash(report.getId());
    }

    @Override
    public boolean equals(final Object obj) {
        return equals(this, obj);
    }

    @Override
    public int hashCode() {
        return hashCode(this);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + "(" + getId() + ")";
    }
}
