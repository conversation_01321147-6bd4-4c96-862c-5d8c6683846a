package com.bees360.report.util;

import com.bees360.report.Message.ReportMessage.Status;
import com.bees360.report.Report;
import com.bees360.report.ReportGroupManager;
import com.bees360.report.ReportResourceSource;
import com.google.common.collect.ForwardingObject;

import javax.annotation.Nullable;

public abstract class ForwardingReportGroupManager extends ForwardingObject
        implements ReportGroupManager {

    @Override
    public Iterable<? extends Report> findReportInGroup(
            String groupId,
            String groupType,
            @Nullable String reportType,
            @Nullable Status status) {
        return delegate().findReportInGroup(groupId, groupType, reportType, status);
    }

    @Override
    public Iterable<? extends Report> findReportInHistory(
            String groupId,
            String groupType,
            @Nullable String reportType,
            @Nullable Status status) {
        return delegate().findReportInHistory(groupId, groupType, reportType, status);
    }

    @Override
    public Iterable<? extends Report> findAllReportInGroup(String groupId, String groupType) {
        return delegate().findAllReportInGroup(groupId, groupType);
    }

    @Override
    public Iterable<String> findGroupKey(String groupType, String reportId) {
        return delegate().findGroupKey(groupType, reportId);
    }

    @Override
    protected abstract ReportGroupManager delegate();

    @Override
    public Report createGroupReport(
            String groupId,
            String groupType,
            String reportType,
            String resourceKey,
            String summary,
            String htmlKey,
            String createdBy) {
        return delegate()
                .createGroupReport(
                        groupId, groupType, reportType, resourceKey, summary, htmlKey, createdBy);
    }

    @Override
    public Report createGroupReport(
            String groupId,
            String groupType,
            String reportType,
            String summary,
            String createdBy,
            Iterable<? extends ReportResourceSource> reportResources) {
        return delegate()
                .createGroupReport(
                        groupId, groupType, reportType, summary, createdBy, reportResources);
    }

    @Override
    public void addAllReportToGroup(
            String groupId, String groupType, Iterable<String> reportIds, String createdBy) {
        delegate().addAllReportToGroup(groupId, groupType, reportIds, createdBy);
    }

    @Override
    public void delete(
            String groupId, String groupType, Iterable<String> reportIds, String deletedBy) {
        delegate().delete(groupId, groupType, reportIds, deletedBy);
    }
}
