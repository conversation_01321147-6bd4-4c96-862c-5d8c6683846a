package com.bees360.report.config;

import com.bees360.grpc.GrpcApi;
import com.bees360.report.ReportGroupManagerGrpc.ReportGroupManagerBlockingStub;
import com.bees360.report.grpc.GrpcReportGroupManagerClient;
import com.bees360.report.grpc.UrlProtobufReportFactory;
import com.bees360.resource.Resource;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.function.Function;

@Configuration
public class GrpcReportGroupManagerConfig {

    @GrpcClient("reportManager")
    private ReportGroupManagerBlockingStub reportGroupManagerBlockingStub;

    @Bean
    public GrpcReportGroupManagerClient grpcReportGroupManagerClient(
            Function<String, Resource> uriResourceProvider) {
        return new GrpcReportGroupManagerClient(
                GrpcApi.of(reportGroupManagerBlockingStub),
                new UrlProtobufReportFactory(uriResourceProvider));
    }
}
