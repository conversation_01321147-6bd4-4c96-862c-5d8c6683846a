grpc:
  server:
    port: 9897
  client:
    reportManager:
      address: 'static://127.0.0.1:9897'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
s3:
  clients:
    - endpoint: http://s3-primary
      key: local-identity
      secret: local-credential
      pathStyleAccess: true
      checksumValidationEnabled: false
resource:
  s3:
    - client: s3-primary
      bucket: test-bucket
      url-expiration: PT1h
      url-stable-duration: PT15m
report:
  resource:
    uri: s3://s3-primary/test-bucket/
http:
  client:
    apache:
      maxConnPerRoute: 16
      maxConnTotal: 64
      evictIdleConnectionsAfter: PT10M
      connectionTimeToLive: PT10M
      evictExpiredConnections: true
      redirectStrategy: always
      request:
        connectTimeout: PT15S
        connectionRequestTimeout: PT120S
        socketTimeout: PT180S
        redirectEnabled: true
        expectContinueEnabled: true
        normalizeUri: true
