package com.bees360.job.registry;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

@JobPayload
@Data
@Builder(builderMethodName = "newBuilder", setterPrefix = "set")
@ToString
public class FetchReportFromZipJob {
    @Deprecated private String projectId;
    private String groupKey;
    private String groupType;
    private String zipUrl;
    private String filenameRegex;
    private Map<String, String> contentType2ReportType;
}
