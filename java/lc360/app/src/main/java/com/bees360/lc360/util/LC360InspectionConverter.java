package com.bees360.lc360.util;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.contract.Contract;
import com.bees360.lc360.Inspection;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.Message;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.claim.Claim;
import com.bees360.project.underwriting.Underwriting;

import lombok.NonNull;

import java.util.function.BiFunction;
import java.util.function.UnaryOperator;

public class LC360InspectionConverter {

    // get contract by dataset and division lookup id
    private final BiFunction<String, String, Contract> contractProvider;

    private final BiFunction<String, Inspection, ServiceTypeEnum> serviceTypeMappings;

    private final BiFunction<String, LC360InspectionAccessor, Boolean> lc360PolicyRenewalMappings;

    private final UnaryOperator<String> creatorProvider;

    public LC360InspectionConverter(
            @NonNull BiFunction<String, String, Contract> contractProvider,
            @NonNull BiFunction<String, Inspection, ServiceTypeEnum> serviceTypeMappings,
            BiFunction<String, LC360InspectionAccessor, Boolean> lc360PolicyRenewalMappings,
            UnaryOperator<String> creatorProvider) {
        this.contractProvider = contractProvider;
        this.serviceTypeMappings = serviceTypeMappings;
        this.lc360PolicyRenewalMappings = lc360PolicyRenewalMappings;
        this.creatorProvider = creatorProvider;
    }

    public ProjectCreationRequest buildCreationRequest(
            ExternalIntegration integration, Inspection inspection) {
        var dataset = integration.getDataset();
        var contract = contractProvider.apply(dataset, inspection.getDivisionLookupID());
        var creator = creatorProvider.apply(dataset);
        var inspectionAccessor =
                new LC360InspectionAccessor(
                        dataset, inspection, serviceTypeMappings, lc360PolicyRenewalMappings);
        return ProjectCreationRequest.from(
                buildProject(inspectionAccessor, integration, contract, creator));
    }

    private Message.ProjectMessage buildProject(
            LC360InspectionAccessor accessor,
            ExternalIntegration integration,
            Contract contract,
            String createdBy) {
        var builder = Message.ProjectMessage.newBuilder();

        assembleUnderwriting(builder, accessor.getUnderwriting());
        assembleClaim(builder, accessor.getClaim());
        assembleCreatedBy(builder, createdBy);
        acceptIfNotNull(builder::setContract, contract.toMessage());
        acceptIfNotNull(builder::setInspection, accessor.getInspection().toMessage());
        acceptIfNotNull(builder::setPolicy, accessor.getPolicy().toMessage());
        acceptIfNotNull(builder::addContact, accessor.getInsured().toMessage());
        acceptIfNotNull(builder::addContact, accessor.getAgent().toMessage());
        acceptIfNotNull(builder::setNote, accessor.getNotes());
        acceptIfNotNull(builder::setOperatingCompany, accessor.getOperatingCompany());
        acceptIfNotNull(builder::addIntegration, integration.toMessage());
        return builder.build();
    }

    private void assembleUnderwriting(
            Message.ProjectMessage.Builder builder, Underwriting underwriting) {
        if (underwriting == null) {
            return;
        }

        builder.setProjectType(com.bees360.project.base.Message.ProjectType.UNDERWRITING_TYPE);
        builder.setServiceType(underwriting.toMessage().getServiceType());
    }

    private void assembleClaim(Message.ProjectMessage.Builder builder, Claim claim) {
        if (claim == null) {
            return;
        }

        builder.setProjectType(com.bees360.project.base.Message.ProjectType.CLAIM_TYPE);
        builder.setServiceType(claim.toMessage().getServiceType());
        acceptIfNotNull(builder::setClaimNo, claim.getClaimNo());
        acceptIfNotNull(builder::setClaimType, claim.toMessage().getClaimType());
    }

    private void assembleCreatedBy(Message.ProjectMessage.Builder builder, String createdBy) {
        var creator = com.bees360.user.Message.UserMessage.newBuilder().setId(createdBy).build();
        builder.setCreateBy(creator);
    }
}
