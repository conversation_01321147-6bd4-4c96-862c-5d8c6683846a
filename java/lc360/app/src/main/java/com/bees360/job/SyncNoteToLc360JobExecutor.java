package com.bees360.job;

import com.bees360.job.registry.SyncNoteToLc360Job;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.lc360.Lc360Api;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.function.Function;

/** This executor is for add inspection note on LC360. */
@Log4j2
public class SyncNoteToLc360JobExecutor extends AbstractJobExecutor<SyncNoteToLc360Job> {

    private final Function<String, Lc360Api> lc360ApiProvider;

    public SyncNoteToLc360JobExecutor(Function<String, Lc360Api> lc360ApiProvider) {
        this.lc360ApiProvider = lc360ApiProvider;
        log.info("Created {}", this);
    }

    @Override
    protected void handle(SyncNoteToLc360Job job) throws IOException {
        log.info("Received job {}.", job);

        var inspectionId = job.getInspectionId();
        var lc360VendorApi = lc360ApiProvider.apply(job.getDataset()).getVendorApi();
        lc360VendorApi.addInspectionNote(inspectionId, job.getNote());
        log.info("Successfully add note for inspection {}.", inspectionId);
    }
}
