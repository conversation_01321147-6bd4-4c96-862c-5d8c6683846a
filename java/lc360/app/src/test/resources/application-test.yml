spring:
  jooq:
    sql-dialect: POSTGRES
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver
  mysql:
    jdbc:
      url: ************************************************************************************************************************************************************************
      username: root
      password: 123456

oauth:
  clients:
    lc360:
      token-endpoint: https://localhost/API/Auth/Token
      method: "POST"
      client-id: id
      client-secret: secret

lc360:
  app:
    set-inspection-scheduled-time:
      enabled: true
    enable-creation: true
    enable-update-generic-fields: true
    enable-handle-project-close-out: true
  process:
    enabled: true
  http:
    client:
      enabled: true
      context: https://localhost/API/V3
  datasets:
    - id: TEST
      divisionLookupIds:
        - DNU Commercial Division
      creator: 1022
      contract:
        insured-by: 1234
        processed-by: 4321
      service-type-mapper:
        mapping:
          "[Exterior]": EXTERIOR
          "[Interior]": WHITE_GLOVE
        default-value: WHITE_GLOVE
      policy-renewal-mapper:
        field: Transaction type
        mapping:
          "[New Business]": false
        default-value: true
    - id: Davies
      divisionLookupIds:
        - FFI_RES
        - TWFG_RES
      creator: 1022
      service-type-mapper:
        mapping:
          "[Exterior]": EXTERIOR
          "[Interior]": WHITE_GLOVE
        default-value: WHITE_GLOVE
      clients:
        "[FFI_RES]":
          client-name: "Florida Family"
          contract:
            insured-by: Florida Family
            processed-by: IRS
          closeout-lookup-id: FFI_RES_DRONE_CLOSEOUT
      http-client:
        oauth-client: davies
        context: https://localhost-davies/API
      forms:
        - name: "Exterior"
          template-id: "d8ce7e39-6925-45f4-9d19-d9e5656cec80"
          internal-template-key: "exterior"
          lookup-id: "Exterior"
        - name: "Loss Prevention / Liability Concerns"
          template-id: "5db15bf6-ef3d-4a52-ae75-30c2faba9618"
          internal-template-key: "lplc"
          lookup-id: "Loss Prevention / Liability Concerns"
        - name: "Closeout Survey"
          template-id: "closeout-form-template-key"
          internal-template-key: "closeout"
          for-closed: true
      enable-update-project-status: true
      enable-update-form: true
      enable-move-forward: true
      enable-handle-project-closeout: true
    - id: NO FORM TEMPLATE COMPANY
      enable-update-form: true
    - id: NO MOVE FORWARD COMPANY
      enable-update-form: true
      enable-move-forward: false
      forms:
        - name: "Exterior"
          template-id: "d8ce7e39-6925-45f4-9d19-d9e5656cec80"
          internal-template-key: "exterior"
          lookup-id: "Exterior"
  form:
    enabled: true
    enable-complete: true
    enable-move-forward-twice: true
    dataset:
      - name: "Exterior"
        template-id: "d8ce7e39-6925-45f4-9d19-d9e5656cec80"
        internal-template-key: "exterior"
        lookup-id: "Exterior"
      - name: "Interior"
        template-id: "f5413960-9193-4137-9a76-4d6046dd1e20"
        internal-template-key: "interior"
        lookup-id: "Interior"
      - name: "	Loss Prevention / Liability Concerns"
        template-id: "5db15bf6-ef3d-4a52-ae75-30c2faba9618"
        internal-template-key: "lplc"
        lookup-id: "Loss Prevention / Liability Concerns"
  image:
    group-type: LC360
  note:
    sync-activity:
      retry-count: 1
      retry-delay: PT1S
      retry-delay-increase-factor: 1.0

http:
  aws-lambda:
    enabled: true
    endpoint: http://localhost/aws/
    client-secret: 123456

rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password
grpc:
  server:
    port: ${GRPC_SERVER_PORT:9851}
  client:
    projectIntegration:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    integrationFormManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pipelineService:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    integrationSummaryClient:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    imageManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    imageTagManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectIIManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectContactManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    genericProjectCreator:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    contractManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    customerManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
app:
  project:
    closed:
      enable-handle: true
      customer:
        "[Insurance Company]":
          recipient: <EMAIL>
      close-reason:
        "[DENIED]":
          description: "Inspection was denied by the insured."
          email-template: "template_1.html"
      portal-link: "localhost/project/"
      closeout-reason:
        - INSURED_DENIED
mail:
  senders:
    "no-reply-sender": <EMAIL>

logging:
  level:
    com.bees360.event.TriggerSyncActivityJobOnChanged: debug
