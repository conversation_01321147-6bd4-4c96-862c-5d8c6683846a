package com.bees360.lc360.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.InspectionScheduledTimeChanged;
import com.bees360.http.HttpClient;
import com.bees360.lc360.Lc360Api;
import com.bees360.lc360.Lc360VendorApi;
import com.bees360.lc360.config.SetInspectionScheduledTimeConfig;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.time.Instant;
import java.util.List;
import java.util.function.Function;

@SpringBootTest(classes = SetInspectionScheduledTimeListenerTest.Config.class)
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
public class SetInspectionScheduledTimeListenerTest {

    @Configuration
    @Import({
        SetInspectionScheduledTimeConfig.class,
    })
    static class Config {
        @Bean
        public InMemoryEventPublisher inMemoryEventPublisher() {
            return new InMemoryEventPublisher(MoreExecutors.directExecutor());
        }

        @Bean
        public Lc360VendorApi lc360VendorApi(HttpClient httpClient) {
            return new Lc360Api("localhost", httpClient).getVendorApi();
        }

        @Bean
        Function<String, Lc360Api> lc360ApiProvider(Lc360VendorApi lc360VendorApi) {
            var lc360Api = Mockito.mock(Lc360Api.class);
            Mockito.when(lc360Api.getVendorApi()).thenAnswer(e -> lc360VendorApi);
            return s -> lc360Api;
        }

        @MockBean public HttpClient httpClient;
    }

    @MockBean private ExternalIntegrationProvider externalIntegrationProvider;

    @Autowired private EventPublisher eventPublisher;

    @Autowired private HttpClient httpClient;

    @Test
    void testSetInspectionScheduledTime() {
        var projectId = RandomStringUtils.randomNumeric(8);
        Mockito.when(externalIntegrationProvider.findAllByProjectId(eq(projectId)))
                .thenAnswer(e -> List.of(randomInspection(projectId)));

        var event = new InspectionScheduledTimeChanged();
        event.setProjectId(projectId);
        event.setScheduledTime(Instant.now().toEpochMilli());
        event.setReason(new InspectionScheduledTimeChanged.Reason());

        eventPublisher.publish(event);

        Mockito.verify(httpClient, Mockito.times(1)).execute(any(), any());
    }

    @Test
    void testIgnoreWhenNoInspectionRelated() {
        var projectId = RandomStringUtils.randomNumeric(8);
        Mockito.when(externalIntegrationProvider.findAllByProjectId(eq(projectId)))
                .thenAnswer(e -> List.of());

        var event = new InspectionScheduledTimeChanged();
        event.setProjectId(projectId);
        event.setScheduledTime(Instant.now().toEpochMilli());
        event.setReason(new InspectionScheduledTimeChanged.Reason());

        eventPublisher.publish(event);

        Mockito.verify(httpClient, Mockito.never()).execute(any(), any());
    }

    private ExternalIntegration randomInspection(String projectId) {
        return ExternalIntegration.from(
                Message.IntegrationMessage.newBuilder()
                        .setProjectId(projectId)
                        .setIntegrationType("LossControl360")
                        .setReferenceNumber(RandomStringUtils.randomAlphabetic(12))
                        .build());
    }
}
