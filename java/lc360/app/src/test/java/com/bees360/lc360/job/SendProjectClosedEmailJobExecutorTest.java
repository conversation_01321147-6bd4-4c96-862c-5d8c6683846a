package com.bees360.lc360.job;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.bees360.address.Address;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.contract.Contract;
import com.bees360.customer.Customer;
import com.bees360.job.Job;
import com.bees360.job.JobFuture;
import com.bees360.job.JobScheduler;
import com.bees360.job.SendProjectClosedEmailJobExecutor;
import com.bees360.job.registry.SendProjectClosedEmailJob;
import com.bees360.lc360.config.HandleProjectClosedConfig;
import com.bees360.policy.Policy;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;

import lombok.SneakyThrows;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.List;

@SpringJUnitConfig
@SpringBootTest(
        classes = SendProjectClosedEmailJobExecutorTest.Config.class,
        properties = "GRPC_SERVER_PORT=9125")
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
public class SendProjectClosedEmailJobExecutorTest {

    @Configuration
    @Import({
        HandleProjectClosedConfig.class,
    })
    @ApplicationAutoConfig
    static class Config {}

    @MockBean private JobScheduler jobScheduler;

    @MockBean private ProjectIIManager projectIIManager;

    @MockBean private ContactManager contactManager;

    @Autowired private SendProjectClosedEmailJobExecutor executor;

    @SneakyThrows
    @Test
    void testSendProjectClosedEmail() {
        var projectId = randomId();
        var reason = "DENIED";
        var insuredBy = "Insurance Company";

        mockData(projectId, insuredBy, true);
        var job = new SendProjectClosedEmailJob();
        job.setProjectId(projectId);
        job.setCloseReason(reason);
        executor.execute(Job.ofPayload(job));

        // assert if email job is sent.
        Mockito.verify(jobScheduler, Mockito.times(1)).schedule(Mockito.any());
    }

    @SneakyThrows
    @Test
    void testThrowExceptionWhenNoRecipient() {
        var projectId = randomId();
        var reason = "DENIED";
        var insuredBy = "NO RECIPIENT Company";

        mockData(projectId, insuredBy, true);
        var job = new SendProjectClosedEmailJob();
        job.setProjectId(projectId);
        job.setCloseReason(reason);
        Assertions.assertThrows(
                IllegalArgumentException.class, () -> executor.execute(Job.ofPayload(job)));
    }

    @SneakyThrows
    @Test
    void testThrowExceptionWhenNoEmailTemplate() {
        var projectId = randomId();
        var reason = "NO TEMPLATE REASON";
        var insuredBy = "Insurance Company";

        mockData(projectId, insuredBy, true);
        var job = new SendProjectClosedEmailJob();
        job.setProjectId(projectId);
        job.setCloseReason(reason);
        Assertions.assertThrows(
                IllegalArgumentException.class, () -> executor.execute(Job.ofPayload(job)));
    }

    @SneakyThrows
    @Test
    void testThrowExceptionWhenNoInsured() {
        var projectId = randomId();
        var reason = "DENIED";
        var insuredBy = "Insurance Company";

        mockData(projectId, insuredBy, false);
        var job = new SendProjectClosedEmailJob();
        job.setProjectId(projectId);
        job.setCloseReason(reason);
        Assertions.assertThrows(
                IllegalArgumentException.class, () -> executor.execute(Job.ofPayload(job)));
    }

    private void mockData(String projectId, String insuredBy, Boolean hasInsured) {
        var project = Mockito.mock(ProjectII.class);
        var policy = Mockito.mock(Policy.class);
        var address = Mockito.mock(Address.class);
        var contract = Mockito.mock(Contract.class);
        var customer = Mockito.mock(Customer.class);
        var insured =
                Contact.ContactBuilder.newBuilder()
                        .setRole(ContactRoleEnum.INSURED.getName())
                        .setFullName("test insured")
                        .build();
        Mockito.when(projectIIManager.findById(eq(projectId))).thenAnswer(e -> project);
        Mockito.when(project.getId()).thenAnswer(e -> projectId);
        Mockito.when(project.getPolicy()).thenAnswer(e -> policy);
        Mockito.when(policy.getPolicyNo()).thenAnswer(e -> randomId());
        Mockito.when(policy.getAddress()).thenAnswer(e -> address);
        Mockito.when(address.getAddress()).thenAnswer(e -> RandomStringUtils.randomAlphabetic(8));
        Mockito.when(project.getContract()).thenAnswer(e -> contract);
        Mockito.when(contract.getInsuredBy()).thenAnswer(e -> customer);
        Mockito.when(customer.getCompanyKey()).thenAnswer(e -> insuredBy);
        if (hasInsured) {
            Mockito.when(contactManager.findByProjectId(eq(projectId)))
                    .thenAnswer(e -> List.of(insured));
        }
        Mockito.when(jobScheduler.schedule(any())).thenAnswer(e -> Mockito.mock(JobFuture.class));
    }

    private static String randomId() {
        return "1" + RandomStringUtils.randomNumeric(6);
    }
}
