package com.bees360.lc360.util;

import static com.bees360.util.Defaults.nullIfEmpty;

import com.bees360.contract.Contract;
import com.bees360.lc360.Inspection;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.Message;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.util.Iterables;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.function.BiFunction;
import java.util.function.UnaryOperator;

public class LC360InspectionConverterTest {

    private final LC360InspectionConverter inspectionConverter;

    public LC360InspectionConverterTest() {
        var contractProvider = contractProvider();
        var serviceTypeMapper = serviceTypeMapper();
        var policyRenewalMapper = policyRenewalMapper();
        var creatorProvider = creatorProvider();
        this.inspectionConverter =
                new LC360InspectionConverter(
                        contractProvider, serviceTypeMapper, policyRenewalMapper, creatorProvider);
    }

    private BiFunction<String, String, Contract> contractProvider() {
        return (a, b) ->
                Contract.from(
                        com.bees360.contract.Message.ContractMessage.newBuilder()
                                .setId("123")
                                .setInsuredBy(
                                        com.bees360.customer.Message.CustomerMessage.newBuilder()
                                                .setId("1234")
                                                .setName("Insured Company")
                                                .build())
                                .setProcessedBy(
                                        com.bees360.customer.Message.CustomerMessage.newBuilder()
                                                .setId("4321")
                                                .setName("Processed Company")
                                                .build())
                                .build());
    }

    private UnaryOperator<String> creatorProvider() {
        return s -> "1234";
    }

    private BiFunction<String, Inspection, ServiceTypeEnum> serviceTypeMapper() {
        return (a, b) -> ServiceTypeEnum.WHITE_GLOVE;
    }

    private BiFunction<String, LC360InspectionAccessor, Boolean> policyRenewalMapper() {
        return (a, b) -> false;
    }

    @Test
    public void testInspectionConverter() {
        var inspection = LC360InspectionTestUtil.getInspection();
        var integration =
                ExternalIntegration.from(
                        Message.IntegrationMessage.newBuilder()
                                .setDataset("Test Company")
                                .setIntegrationType("LossControl360")
                                .setReferenceNumber(inspection.getInspectionID())
                                .build());

        var project = inspectionConverter.buildCreationRequest(integration, inspection);

        Assertions.assertEquals(ProjectTypeEnum.UNDERWRITING, project.getProjectType());
        Assertions.assertEquals("1234", project.getCreatedBy());
        assertContract(project);
        assertInspection(project, inspection);
        assertContract(project);
        assertPolicy(project, inspection);
        assertInsuredAndAgent(project, inspection);
        assertUnderwriting(project);
    }

    private void assertContract(ProjectCreationRequest project) {
        var contract = project.getContract();
        Assertions.assertEquals(contract.getId(), "123");
        var insured = contract.getInsuredBy();
        var processed = contract.getProcessedBy();
        Assertions.assertEquals(insured.getName(), "Insured Company");
        Assertions.assertEquals(processed.getName(), "Processed Company");
    }

    private void assertInspection(ProjectCreationRequest project, Inspection inspection) {
        Assertions.assertEquals(
                String.valueOf(inspection.getInspectionNumber()),
                project.getInspection().getInspectionNo());
    }

    private void assertPolicy(ProjectCreationRequest project, Inspection inspection) {
        var policy = project.getPolicy();
        Assertions.assertEquals(inspection.getPolicyNumber(), policy.getPolicyNo());
        Assertions.assertEquals(false, policy.isRenewal());

        var expected = inspection.getLocationAddress();
        var address = policy.getAddress();
        Assertions.assertEquals(
                String.join(", ", expected.getStreet1(), nullIfEmpty(expected.getStreet2())),
                address.getStreetAddress());
        Assertions.assertEquals(expected.getCity(), address.getCity());
        Assertions.assertEquals(expected.getRegion1(), address.getState());
        Assertions.assertEquals(expected.getCountry(), address.getCountry());
        Assertions.assertEquals(expected.getZipCode(), address.getZip());

        var building = policy.getBuilding();
        Assertions.assertEquals(2000, building.getYearBuilt());
        Assertions.assertEquals(
                com.bees360.building.Message.BuildingType.COMMERCIAL, building.getType());
    }

    private void assertUnderwriting(ProjectCreationRequest project) {
        Assertions.assertEquals(
                ServiceTypeEnum.WHITE_GLOVE, project.getUnderwriting().getServiceType());
    }

    private void assertInsuredAndAgent(ProjectCreationRequest project, Inspection inspection) {
        var expectedInsured = inspection.getPolicyHolder();
        var insured =
                Iterables.toStream(project.getContact())
                        .filter(c -> "Insured".equals(c.getRole()))
                        .findFirst()
                        .orElse(null);

        Assertions.assertEquals(expectedInsured.getFirstName(), insured.getFirstName());
        Assertions.assertEquals(expectedInsured.getLastName(), insured.getLastName());
        Assertions.assertEquals(expectedInsured.getEmail(), insured.getPrimaryEmail());
        Assertions.assertEquals(expectedInsured.getCellPhone(), insured.getPrimaryPhone());
        Assertions.assertEquals(2, Iterables.toList(insured.getOtherPhone()).size());

        var expectedAgent = inspection.getAgent();
        var agent =
                Iterables.toStream(project.getContact())
                        .filter(c -> "Agent".equals(c.getRole()))
                        .findFirst()
                        .orElse(null);
        Assertions.assertEquals(expectedAgent.getAgentName(), agent.getFullName());
        Assertions.assertEquals(expectedAgent.getPhoneNumber(), agent.getPrimaryPhone());
        Assertions.assertEquals(expectedAgent.getEmail(), agent.getPrimaryEmail());
    }
}
