package com.bees360.lc360;

import static com.bees360.job.registry.Lc360ActivitySyncJob.INTEGRATION_LC360;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.bees360.activity.Activity;
import com.bees360.activity.Message;
import com.bees360.activity.Message.ActivityMessage;
import com.bees360.activity.Message.ActivityMessage.ActionType;
import com.bees360.activity.Message.ActivityMessage.EntityType;
import com.bees360.event.TriggerSyncActivityJobOnChanged;
import com.bees360.event.registry.ActivityChangedEvent;
import com.bees360.job.Job;
import com.bees360.job.Lc360ActivitySyncJobExecutor;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.lc360.config.SyncActivityConfig;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message.IntegrationMessage;

import jakarta.annotation.PostConstruct;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.function.Function;

@SpringBootTest(
        classes = TriggerSyncActivityJobOnChangedTest.Config.class,
        properties = {"GRPC_SERVER_PORT=9098", "lc360.note.sync-activity.enabled=true"})
@ActiveProfiles("test")
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
class TriggerSyncActivityJobOnChangedTest {
    private static final String WEB_SOURCE = "WEB";

    @Import({
        InMemoryJobScheduler.class,
        SyncActivityConfig.class,
    })
    @Configuration
    static class Config {
        @Bean("externalIntegrationProvider")
        ExternalIntegrationProvider externalIntegrationProvider() {
            return mock(ExternalIntegrationProvider.class);
        }

        @Bean
        Lc360VendorApi lc360VendorApi() {
            return mock(Lc360VendorApi.class);
        }

        @Bean
        Function<String, Lc360Api> lc360ApiProvider(Lc360VendorApi lc360VendorApi) {
            var lc360Api = Mockito.mock(Lc360Api.class);
            Mockito.when(lc360Api.getVendorApi()).thenAnswer(e -> lc360VendorApi);
            return s -> lc360Api;
        }
    }

    @Autowired private ExternalIntegrationProvider externalIntegrationProvider;

    @Autowired private Lc360VendorApi lc360VendorApi;

    @SpyBean private InMemoryJobScheduler jobScheduler;

    @Autowired private TriggerSyncActivityJobOnChanged triggerSyncActivityJobOnChanged;
    @Autowired private Lc360ActivitySyncJobExecutor lc360ActivitySyncJobExecutor;

    @PostConstruct
    void enlistExecutor() {
        jobScheduler.enlist(lc360ActivitySyncJobExecutor);
    }

    @Test
    void lc360ActivitySyncTest() {
        var commentContent = RandomStringUtils.randomAlphabetic(15);
        var projectId = RandomStringUtils.randomNumeric(5);
        var activityId = RandomStringUtils.randomAlphanumeric(5);
        var inspectionId = RandomStringUtils.randomAlphabetic(5);
        var integration =
                ExternalIntegration.from(
                        IntegrationMessage.newBuilder()
                                .setProjectId(projectId)
                                .setReferenceNumber(inspectionId)
                                .setIntegrationType(INTEGRATION_LC360)
                                .build());
        // action为Contact
        var event =
                fromActivityChangedEvent(
                        activityId,
                        projectId,
                        ActionType.CONTACT.name(),
                        "",
                        "",
                        WEB_SOURCE,
                        commentContent);
        doReturn(List.of(integration))
                .when(externalIntegrationProvider)
                .findAllByProjectId(anyString());
        triggerSyncActivityJobOnChanged.handle(event);
        // 超时时间为1s
        verify(lc360VendorApi, timeout(3000).times(1))
                .addInspectionNote(inspectionId, commentContent);

        // entity为Comment
        event =
                fromActivityChangedEvent(
                        activityId,
                        projectId,
                        ActionType.RATE.name(),
                        EntityType.COMMENT.name(),
                        "AI",
                        WEB_SOURCE,
                        commentContent);
        triggerSyncActivityJobOnChanged.handle(event);
        // 超时时间为1s
        verify(lc360VendorApi, timeout(3000).times(2))
                .addInspectionNote(inspectionId, commentContent);
    }

    @Test
    void lc360NotSupportActivitySyncTest() {
        var commentContent = RandomStringUtils.randomAlphabetic(15);
        var projectId = RandomStringUtils.randomNumeric(5);
        var activityId = RandomStringUtils.randomAlphanumeric(5);
        var referenceNumber = RandomStringUtils.randomAlphabetic(5);
        var integration =
                ExternalIntegration.from(
                        IntegrationMessage.newBuilder()
                                .setProjectId(projectId)
                                .setReferenceNumber(referenceNumber)
                                .setIntegrationType(INTEGRATION_LC360)
                                .build());
        doReturn(List.of(integration))
                .when(externalIntegrationProvider)
                .findAllByProjectId(anyString());

        // action不为Contact
        var event2 =
                fromActivityChangedEvent(
                        activityId, projectId, ActionType.RATE.name(), "", "", "", "");
        triggerSyncActivityJobOnChanged.handle(event2);
        verify(jobScheduler, times(0)).schedule(any(Job.class));

        // entityType不为Comment
        var event3 =
                fromActivityChangedEvent(
                        activityId,
                        projectId,
                        ActionType.RATE.name(),
                        EntityType.AI_PROCESS.name(),
                        "",
                        "",
                        "");
        triggerSyncActivityJobOnChanged.handle(event3);
        verify(jobScheduler, times(0)).schedule(any(Job.class));

        // source为Web
        var event4 =
                fromActivityChangedEvent(
                        activityId,
                        projectId,
                        ActionType.CONTACT.name(),
                        EntityType.COMMENT.name(),
                        WEB_SOURCE,
                        "",
                        "");
        triggerSyncActivityJobOnChanged.handle(event4);
        verify(jobScheduler, times(0)).schedule(any(Job.class));

        // visibility不包括web
        var event5 =
                fromActivityChangedEvent(
                        activityId,
                        projectId,
                        ActionType.CONTACT.name(),
                        EntityType.COMMENT.name(),
                        "",
                        "",
                        "");
        triggerSyncActivityJobOnChanged.handle(event5);
        verify(jobScheduler, times(0)).schedule(any(Job.class));

        // content为空
        var event6 =
                fromActivityChangedEvent(
                        activityId,
                        projectId,
                        ActionType.CONTACT.name(),
                        EntityType.COMMENT.name(),
                        "",
                        WEB_SOURCE,
                        "");
        triggerSyncActivityJobOnChanged.handle(event6);
        verify(jobScheduler, times(0)).schedule(any(Job.class));

        // referenceNumber设为空字符串时inspectionId为空
        integration =
                ExternalIntegration.from(
                        IntegrationMessage.newBuilder()
                                .setProjectId(projectId)
                                .setReferenceNumber("")
                                .setIntegrationType(INTEGRATION_LC360)
                                .build());
        doReturn(List.of(integration))
                .when(externalIntegrationProvider)
                .findAllByProjectId(anyString());

        // inspectionId为空
        var event7 =
                fromActivityChangedEvent(
                        activityId,
                        projectId,
                        ActionType.CONTACT.name(),
                        EntityType.COMMENT.name(),
                        "",
                        WEB_SOURCE,
                        commentContent);
        triggerSyncActivityJobOnChanged.handle(event7);
        verify(jobScheduler, times(0)).schedule(any(Job.class));
    }

    private static ActivityChangedEvent fromActivityChangedEvent(
            String id,
            String projectId,
            String action,
            String entityType,
            String source,
            String visibility,
            String commentContent) {
        // 设置id为随机值避免转换为null
        var comment =
                Message.CommentMessage.newBuilder()
                        .setId(RandomStringUtils.randomAlphanumeric(5))
                        .setContent(commentContent)
                        .build();
        var entity =
                Message.ActivityMessage.Entity.newBuilder()
                        .setId(RandomStringUtils.randomAlphanumeric(5))
                        .setType(entityType)
                        .build();
        var activity =
                Activity.from(
                        ActivityMessage.newBuilder()
                                .setAction(action)
                                .setEntity(entity)
                                .setSource(source)
                                .addVisibility(visibility)
                                .setComment(comment)
                                .setProjectId(Long.parseLong(projectId))
                                .setId(id)
                                .build());
        var event = mock(ActivityChangedEvent.class);
        doReturn(activity).when(event).getSource();
        return event;
    }
}
