package com.bees360.lc360.config;

import com.bees360.apolloconfig.annotation.RefreshableConfigurationProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.apache.commons.collections4.map.CaseInsensitiveMap;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Configuration
@EnableConfigurationProperties
public class LC360CustomerConfig {

    @Setter
    @Getter
    @Configuration
    @RefreshableConfigurationProperties(prefix = "lc360")
    public static class Lc360CustomerProperties {

        private List<Dataset> datasets;

        @Setter
        @Getter
        public static class Dataset {
            private String id;

            private HttpClientConfig httpClient;

            private List<String> divisionLookupIds;

            private Boolean enableUpdateForm = false;

            private Boolean enableMoveForward = false;

            private Boolean enableMoveForwardTwice = false;

            private Boolean enableSyncActivity = false;

            private Boolean enableCancelInspection = false;

            private Boolean enableNotifyProjectClosed = false;

            private Boolean enableSyncProjectPauseNote = false;

            private Boolean enableSyncScheduledTime = false;

            private Boolean enableUpdateProjectStatus = false;

            private Boolean enableHandleProjectCloseout = false;

            private ImageConfig image = new ImageConfig();
        }

        @Getter
        @Setter
        public static class HttpClientConfig {
            private String oauthClient;

            private String context;
        }

        @Getter
        @Setter
        public static class ImageConfig {
            private Boolean enableShowOnReport = false;
            private Boolean enableImageSequenceNumberSuffix = true;
            private Boolean enableDeliverAllImages = false;

            /** Image sort configuration */
            private String hazardCategoryTagKey = "annotation_damage";

            /** Use a List to ensure the Group priority is consistent with the configuration */
            private List<ComparableCategoryGroup> sortedByCategoryGroup = new ArrayList<>();

            /** Use a List to ensure the Category priority is consistent with the configuration */
            private List<CategoryTagTriple> sortedByCategoryTagWeight = new ArrayList<>();

            /**
             * Default weight value,<br>
             * this value is used when a category is configured but its weight is not configured
             */
            private Integer defaultTagWeight = 1000;

            private String weightDelimiter = "#";
            private String groupDelimiter = "^^";
            private Set<String> fileNameCategories = new LinkedHashSet<>();
            private String fileNameDelimiter = "_";

            @Getter
            @Setter
            @NoArgsConstructor
            public static class ComparableCategoryGroup {
                private List<String> categoryGroup = new ArrayList<>();
                private Integer defaultGroupWeight = 1000;
                private Map<String, CategoryTagTriple> categoryTagWeights =
                        new CaseInsensitiveMap<>();
            }

            @Setter
            @Getter
            public static class CategoryTagTriple {
                String category;
                Integer defaultTagWeight = 1000;
                Map<String, Integer> tagWeights = new CaseInsensitiveMap<>();
            }
        }
    }
}
