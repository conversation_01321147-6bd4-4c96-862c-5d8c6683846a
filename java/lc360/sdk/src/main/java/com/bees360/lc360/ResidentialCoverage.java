package com.bees360.lc360;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResidentialCoverage {
    private BigDecimal coverageAIn;
    private BigDecimal coverageBIn;
    private BigDecimal coverageCIn;
    private BigDecimal coverageDIn;
    private BigDecimal coverageEIn;
    private BigDecimal coverageFIn;
    private BigDecimal coverageGIn;
}
