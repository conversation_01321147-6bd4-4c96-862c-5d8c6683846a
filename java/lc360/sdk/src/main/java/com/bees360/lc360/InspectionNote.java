package com.bees360.lc360;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InspectionNote {
    private String inspectionNoteID;
    private String text;
    private LocalDateTime creationDate;
    private String authorFirstName;
    private String authorLastName;
    private String type;
    private String authorUserName;
    private Boolean isPublic;
    private String currentInspectionStatusName;
    private String authorUserID;
    private Integer inspectionNoteTypeID;
    private Integer currentInspectionStatusID;
    private Boolean reviewed;
    private String reviewedByUserRoleID;
    private LocalDateTime dateReviewed;
    private String quickAnswerID;
    private LocalDate dateCalled;
}
