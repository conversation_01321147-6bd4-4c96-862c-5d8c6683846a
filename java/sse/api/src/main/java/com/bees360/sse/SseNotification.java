package com.bees360.sse;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.Notification;
import com.bees360.api.Proto;
import com.bees360.util.DateTimes;
import com.google.protobuf.util.Timestamps;

import org.springframework.lang.Nullable;

import java.util.Objects;

public interface SseNotification extends Proto<Notification.SseMessage> {
    String getId();

    String getSenderId();

    // 操作者的动作，如：捐款、更新、评论、收藏；
    @Nullable
    Notification.SseMessage.SenderAction getSenderAction();

    // 目标对象ID；
    String getObjectId();

    // 被操作对象类型，如：PROJECT等；
    Notification.SseMessage.ObjectType getObjectType();

    Iterable<String> getRecipientIds();

    @Nullable
    String getMessage();

    Long getCreatedAt();

    @Override
    default Notification.SseMessage toMessage() {
        var builder = Notification.SseMessage.newBuilder();
        acceptIfNotNull(builder::setId, getId());
        acceptIfNotNull(builder::setSenderId, getSenderId());
        acceptIfNotNull(builder::setSenderAction, getSenderAction());
        acceptIfNotNull(builder::setObjectId, getObjectId());
        acceptIfNotNull(builder::setObjectType, getObjectType());
        acceptIfNotNull(builder::addAllRecipients, getRecipientIds());
        acceptIfNotNull(builder::setMessage, getMessage());
        acceptIfNotNull(builder::setCreatedAt, getCreatedAt(), Timestamps::fromMillis);
        return builder.build();
    }

    static SseNotification from(Notification.SseMessage message) {
        if (Objects.equals(message, Notification.SseMessage.getDefaultInstance())) {
            return null;
        }

        return new SseNotification() {
            @Override
            public String getId() {
                return message.getId();
            }

            @Override
            public String getSenderId() {
                return message.getSenderId();
            }

            @Override
            public Notification.SseMessage.SenderAction getSenderAction() {
                return message.getSenderAction();
            }

            @Override
            public String getObjectId() {
                return message.getObjectId();
            }

            @Override
            public Notification.SseMessage.ObjectType getObjectType() {
                return message.getObjectType();
            }

            @Override
            public Iterable<String> getRecipientIds() {
                return message.getRecipientsList();
            }

            @Override
            public String getMessage() {
                return message.getMessage();
            }

            @Override
            public Long getCreatedAt() {
                if (message.hasCreatedAt()) {
                    return Objects.requireNonNull(DateTimes.toInstant(message.getCreatedAt()))
                            .toEpochMilli();
                }
                return null;
            }
        };
    }
}
