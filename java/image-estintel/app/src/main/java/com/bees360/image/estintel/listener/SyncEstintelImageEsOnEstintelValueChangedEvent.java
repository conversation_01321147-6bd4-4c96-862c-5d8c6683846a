package com.bees360.image.estintel.listener;

import static com.bees360.util.ProtoStructAdapter.jsonToStruct;

import build.buf.validate.ValidateProto;

import com.bees360.estintel.DataTypeManager;
import com.bees360.estintel.EstintelFormat;
import com.bees360.estintel.Extension;
import com.bees360.estintel.Factor;
import com.bees360.estintel.FactorProvider;
import com.bees360.estintel.v2.Message;
import com.bees360.event.registry.ValueChangeEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.image.estintel.EstintelImageESRepository;
import com.bees360.image.estintel.convert.EstintelImageEsModelConverter;
import com.google.protobuf.ExtensionRegistry;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Value;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Log4j2
public class SyncEstintelImageEsOnEstintelValueChangedEvent
        extends AbstractNamedEventListener<ValueChangeEvent> {
    private final EstintelImageESRepository esRepository;
    private final List<String> esSyncIdentificationFields;
    private final FactorProvider factorProvider;
    private final ExtensionRegistry extensionRegistry;
    private final EstintelImageEsModelConverter converter;
    private final String factorPackageName;

    public SyncEstintelImageEsOnEstintelValueChangedEvent(
            EstintelImageESRepository esRepository,
            List<String> esSyncIdentificationFields,
            FactorProvider factorProvider,
            EstintelFormat estintelFormat,
            DataTypeManager dataTypeManager,
            String factorPackageName,
            int maxConcurrency) {
        setMaxConcurrency(maxConcurrency);
        setSingleActiveConsumer(true);
        this.esRepository = esRepository;
        this.esSyncIdentificationFields = esSyncIdentificationFields;
        this.factorProvider = factorProvider;
        this.extensionRegistry = ExtensionRegistry.newInstance();
        Extension.registerAllExtensions(extensionRegistry);
        ValidateProto.registerAllExtensions(extensionRegistry);
        this.converter = new EstintelImageEsModelConverter(estintelFormat, dataTypeManager);
        this.factorPackageName = factorPackageName;
        log.info(
                "Created {} (esRepository='{}', esSyncIdentificationFields='{}',"
                        + " factorProvider='{}', extensionRegistry='{}', converter='{}',"
                        + " factorPackageName='{}', maxConcurrency='{}')",
                this,
                this.esRepository,
                this.esSyncIdentificationFields,
                this.factorProvider,
                this.extensionRegistry,
                this.converter,
                this.factorPackageName,
                maxConcurrency);
    }

    public SyncEstintelImageEsOnEstintelValueChangedEvent(
            EstintelImageESRepository esRepository,
            FactorProvider factorProvider,
            EstintelFormat estintelFormat,
            DataTypeManager dataTypeManager,
            String factorPackageName) {
        this(
                esRepository,
                List.of("ES_IMAGE", "ES_IMAGE_TAG", "ES_IMAGE_ANNOTATION"),
                factorProvider,
                estintelFormat,
                dataTypeManager,
                factorPackageName,
                1);
    }

    @Override
    public String getRoutingKey() {
        return String.format("estintel.value_change.%s.*", factorPackageName);
    }

    @Override
    public void handle(ValueChangeEvent valueChangeEvent) throws IOException {
        log.info("Received Event: {}", valueChangeEvent);
        var eventMessage = valueChangeEvent.getValueChangeEventMessage();
        if (Objects.equals(
                eventMessage.getValue(), Message.FactorValueMessage.getDefaultInstance())) {
            throw new IllegalArgumentException(
                    String.format("ValueChangeEvent data is null. event=%s", eventMessage));
        }
        var value = eventMessage.getValue();
        var factorId = value.getFactorId();
        var factor = findFactorById(factorId);
        if (Objects.isNull(factor) || Objects.isNull(factor.getAttribute())) {
            return;
        }

        var attributeMap = jsonToStruct(factor.getAttribute()).getFieldsMap();
        if (needSyncToImageEs(attributeMap)) {
            updateEstintelImageEs(value, factor);
        }
    }

    private void updateEstintelImageEs(Message.FactorValueMessage value, Factor factor) {
        log.info(
                String.format(
                        "Start trying to update estintelImageEs. valueId: %s", value.getId()));
        var imageEsEntity = converter.getImageEsEntity(value, factor);
        log.info("The generated EstintelImageEsGeneralEntity object: " + imageEsEntity);
        if (Objects.isNull(imageEsEntity)) {
            return;
        }
        var save = esRepository.saveByScript(imageEsEntity);
        log.info("End of update estintelImageEs. result: " + save);
    }

    private boolean needSyncToImageEs(Map<String, Value> attributeMap) {
        for (Map.Entry<String, Value> entry : attributeMap.entrySet()) {
            var key = entry.getKey();
            var value = entry.getValue();
            if (esSyncIdentificationFields.contains(key)
                    && value.hasBoolValue()
                    && value.getBoolValue()) {
                return true;
            }
        }
        return false;
    }

    private Factor findFactorById(String factorId) {
        var factor = factorProvider.findById(factorId);
        if (Objects.isNull(factor)) {
            return null;
        }
        return Factor.from(registerExtensions(factor.toMessage()));
    }

    private com.bees360.estintel.Message.FactorMessage registerExtensions(
            com.bees360.estintel.Message.FactorMessage factorMessage) {
        try {
            return factorMessage
                    .getParserForType()
                    .parseFrom(factorMessage.toByteArray(), extensionRegistry);
        } catch (InvalidProtocolBufferException e) {
            throw new IllegalStateException(e);
        }
    }
}
