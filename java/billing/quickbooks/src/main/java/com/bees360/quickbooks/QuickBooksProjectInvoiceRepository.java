package com.bees360.quickbooks;

import com.bees360.project.invoice.ProjectInvoice;
import com.bees360.repository.Repository;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.util.ContentTypes;
import com.bees360.util.Iterables;
import com.intuit.ipp.data.Attachable;
import com.intuit.ipp.data.AttachableRef;
import com.intuit.ipp.data.Invoice;
import com.intuit.ipp.data.ReferenceType;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.Collections;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
public class QuickBooksProjectInvoiceRepository implements Repository<ProjectInvoice> {

    private final QuickBooksService quickBooksService;

    private final Function<ProjectInvoice, Invoice> toQBInvoice;

    private final Function<Invoice, ProjectInvoice> fromQBInvoice;

    private final ResourcePool resourcePool;

    public QuickBooksProjectInvoiceRepository(
            QuickBooksService quickBooksService,
            Function<ProjectInvoice, Invoice> toQBInvoice,
            Function<Invoice, ProjectInvoice> fromQBInvoice,
            ResourcePool resourcePool) {
        this.quickBooksService = quickBooksService;
        this.toQBInvoice = toQBInvoice;
        this.fromQBInvoice = fromQBInvoice;
        this.resourcePool = resourcePool;
    }

    @Override
    public String namespace() {
        return "quickbooks";
    }

    /**
     * @param id docNumber of QuickBooks invoice
     * @return ProjectInvoice
     */
    @Override
    public ProjectInvoice findById(String id) {
        String invoiceId = quickBooksService.getInvoiceIdByDocNumber(id);
        Invoice invoice = quickBooksService.getInvoiceById(invoiceId);
        return fromQBInvoice.apply(invoice);
    }

    @Override
    public Iterable<? extends ProjectInvoice> findAllById(Iterable<String> ids) {
        Set<String> idSet = Iterables.toSet(ids);
        if (idSet.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> invoiceIds = quickBooksService.getInvoiceIdsByDocNumbers(ids);
        return quickBooksService.getInvoicesByIds(invoiceIds).stream()
                .map(fromQBInvoice)
                .collect(Collectors.toList());
    }

    @Override
    public Iterable<? extends ProjectInvoice> loadAll() {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean existsById(String id) {
        return quickBooksService.getInvoiceIdByDocNumber(id) != null;
    }

    /**
     * @param entity ProjectInvoice
     * @return QuickBooks invoice docNumber
     */
    @Override
    public String save(ProjectInvoice entity) {
        log.info(
                "Start to create invoice on QuickBooks, invoiceNo: {}",
                entity.getInvoice().getInvoiceNo());
        Invoice invoice = toQBInvoice.apply(entity);
        Invoice createdInvoice = quickBooksService.createInvoice(invoice);
        String fileKey = entity.getFileKey();
        if (StringUtils.isNotBlank(fileKey)) {
            log.info("start to upload file attachment to QuickBooks with key: {}", fileKey);
            uploadAttachment(createdInvoice, fileKey, entity.getInvoice().getInvoiceNo());
            log.info("finish to upload file attachment to QuickBooks with key: {}", fileKey);
        }
        return createdInvoice.getDocNumber();
    }

    @Override
    public Iterable<String> saveAll(Iterable<? extends ProjectInvoice> entities) {
        return Iterables.toStream(entities).map(this::save).collect(Collectors.toList());
    }

    /**
     * @param id QuickBooks invoice docNumber
     */
    @Override
    public void deleteById(String id) {
        String invoiceId = quickBooksService.getInvoiceIdByDocNumber(id);
        quickBooksService.deleteInvoiceById(invoiceId);
    }

    @Override
    public void deleteAllById(Iterable<String> ids) {
        List<String> invoiceIds = quickBooksService.getInvoiceIdsByDocNumbers(ids);
        quickBooksService.deleteInvoicesByIds(invoiceIds);
    }

    private void uploadAttachment(Invoice invoice, String fileKey, String invoiceNo) {
        ResourceMetadata meta = resourcePool.head(fileKey);
        if (meta == null) {
            throw new NoSuchElementException("No resource found with key:" + fileKey);
        }
        Resource resource = resourcePool.get(fileKey);
        String contentType = resource.getMetadata().getContentType();
        Attachable attachable = new Attachable();
        ReferenceType invoiceRef = QuickBooksService.toReference(invoice);
        invoiceRef.setType("Invoice");
        AttachableRef ref = new AttachableRef();
        ref.setEntityRef(invoiceRef);

        contentType = StringUtils.trimToEmpty(contentType);
        String fileExtension = ContentTypes.getFileExtension(contentType);
        if (StringUtils.isBlank(fileExtension)) {
            throw new IllegalArgumentException(
                    "Invalid fileExtension from resource, resource key:" + fileKey);
        }
        String fileName = invoiceNo + fileExtension;
        attachable.setFileName(fileName);
        attachable.setContentType(contentType);
        attachable.setAttachableRef(List.of(ref));
        try (var input = resource.open()) {
            quickBooksService.uploadAttachment(attachable, input);
        } catch (IOException e) {
            log.error(
                    "Failed to upload invoice attachment to quickbooks[QBInvoiceNo:{},"
                            + " QBInvoiceDocNumber:{}, fileKey:{}]",
                    invoice.getId(),
                    invoice.getDocNumber(),
                    fileKey);
            throw new UncheckedIOException(e);
        }
    }
}
