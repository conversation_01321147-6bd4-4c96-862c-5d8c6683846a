package com.bees360.quickbooks;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.bees360.customer.Customer;
import com.bees360.customer.CustomerProvider;
import com.bees360.customer.Message;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.ProjectInvoiceAdded;
import com.bees360.project.ProjectII;
import com.bees360.project.invoice.ProjectInvoice;
import com.bees360.project.invoice.ProjectInvoiceManager;
import com.bees360.project.invoice.ProjectInvoiceReceiptManager;
import com.bees360.quickbooks.config.CreatedQBInvoiceListenerConfig;
import com.bees360.quickbooks.config.QuickBooksBillingConfig;
import com.bees360.repository.Provider;
import com.bees360.repository.Repository;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.time.Instant;
import java.util.concurrent.Executor;

@SpringBootTest
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class QuickBooksListenerTest {
    @Configuration
    @Import({
        InMemoryEventPublisher.class,
        AutoRegisterEventListenerConfig.class,
        CreatedQBInvoiceListenerConfig.class,
    })
    @EnableConfigurationProperties(QuickBooksBillingConfig.QuickBooksProperties.class)
    static class config {
        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @Autowired private EventPublisher eventPublisher;
    @MockBean private Provider<ProjectII> projectIIProvider;
    @MockBean private ProjectInvoiceManager projectInvoiceManager;
    @MockBean private Repository<ProjectInvoice> quickBooksProjectInvoiceRepository;
    @MockBean private ProjectInvoiceReceiptManager projectInvoiceReceiptManager;
    @MockBean private CustomerProvider customerProvider;

    @Test
    public void testUWNotCreateQBOnProjectInvoiceAdded() {
        var projectId = String.valueOf(RandomUtils.nextLong());
        var customerId = RandomStringUtils.randomAlphabetic(6);
        var invoiceNo = RandomStringUtils.randomAlphabetic(6);
        var invoice = Mockito.mock(ProjectInvoice.class);
        var key = RandomStringUtils.randomAlphabetic(6);
        var c = getCustomer(key);
        Mockito.when(projectInvoiceManager.findByInvoiceNo(invoiceNo)).thenReturn(invoice);
        Mockito.when(projectIIProvider.get(projectId))
                .thenReturn(
                        getProject(com.bees360.project.base.Message.ProjectType.UNDERWRITING_TYPE));
        Mockito.when(customerProvider.findById(customerId)).thenReturn(c);
        var event = new ProjectInvoiceAdded();
        event.setInvoiceNo(invoiceNo);
        event.setProjectId(projectId);
        event.setCustomerId(customerId);
        event.setUpdatedAt(Instant.now());
        eventPublisher.publish(event);

        Mockito.verify(quickBooksProjectInvoiceRepository, Mockito.never()).save(invoice);
    }

    @Test
    public void testClaimCreateQBOnProjectInvoiceAdded() {
        var projectId = String.valueOf(RandomUtils.nextLong());
        var customerId = RandomStringUtils.randomAlphabetic(6);
        var invoiceNo = RandomStringUtils.randomAlphabetic(6);
        var invoice = Mockito.mock(ProjectInvoice.class);
        var key = RandomStringUtils.randomAlphabetic(6);
        var c = getCustomer(key);
        Mockito.when(projectInvoiceManager.findByInvoiceNo(invoiceNo)).thenReturn(invoice);
        Mockito.when(projectIIProvider.get(projectId))
                .thenReturn(getProject(com.bees360.project.base.Message.ProjectType.CLAIM_TYPE));
        Mockito.when(customerProvider.findById(customerId)).thenReturn(c);
        Mockito.when(quickBooksProjectInvoiceRepository.save(any())).thenAnswer(e -> invoiceNo);
        var event = new ProjectInvoiceAdded();
        event.setInvoiceNo(invoiceNo);
        event.setProjectId(projectId);
        event.setCustomerId(customerId);
        event.setUpdatedAt(Instant.now());
        eventPublisher.publish(event);

        Mockito.verify(projectInvoiceReceiptManager, Mockito.timeout(2000).times(1))
                .initializeReceipt(eq(invoiceNo));
        Mockito.verify(quickBooksProjectInvoiceRepository).save(invoice);
        Mockito.verify(projectInvoiceReceiptManager, Mockito.times(1))
                .setQBInvoiceNo(eq(invoiceNo), eq(invoiceNo));
        ;
    }

    @Test
    public void testClaimWithNotExistedCustomerNotCreateQBOnProjectInvoiceAdded() {
        var projectId = String.valueOf(RandomUtils.nextLong());
        var customerId = RandomStringUtils.randomAlphabetic(6);
        var invoiceNo = RandomStringUtils.randomAlphabetic(6);
        var invoice = Mockito.mock(ProjectInvoice.class);
        var key = "NOT_EXISTED";
        var c = getCustomer(key);
        Mockito.when(projectInvoiceManager.findByInvoiceNo(invoiceNo)).thenReturn(invoice);
        Mockito.when(projectIIProvider.get(projectId))
                .thenReturn(getProject(com.bees360.project.base.Message.ProjectType.CLAIM_TYPE));
        Mockito.when(customerProvider.findById(customerId)).thenReturn(c);
        var event = new ProjectInvoiceAdded();
        event.setInvoiceNo(invoiceNo);
        event.setProjectId(projectId);
        event.setCustomerId(customerId);
        event.setUpdatedAt(Instant.now());
        eventPublisher.publish(event);

        Mockito.verify(quickBooksProjectInvoiceRepository, Mockito.never()).save(invoice);
    }

    private Customer getCustomer(String key) {
        return Customer.of(
                Message.CustomerMessage.newBuilder()
                        .setId(RandomStringUtils.randomAlphabetic(16))
                        .setKey(key)
                        .build());
    }

    private ProjectII getProject(com.bees360.project.base.Message.ProjectType type) {
        return ProjectII.from(
                com.bees360.project.Message.ProjectMessage.newBuilder()
                        .setId(RandomStringUtils.randomAlphabetic(16))
                        .setProjectType(type)
                        .build());
    }
}
