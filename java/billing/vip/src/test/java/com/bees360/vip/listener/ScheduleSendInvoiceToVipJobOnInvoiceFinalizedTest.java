package com.bees360.vip.listener;

import static org.mockito.ArgumentMatchers.any;

import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.ProjectInvoiceStatusChanged;
import com.bees360.job.JobScheduler;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.util.function.Predicate;

@SpringBootTest(classes = ScheduleSendInvoiceToVipJobOnInvoiceFinalizedTest.Config.class)
@TestPropertySource(
        properties = {
            "spring.config.location = classpath:application-test.yml",
        })
public class ScheduleSendInvoiceToVipJobOnInvoiceFinalizedTest {

    @Import({AutoRegisterEventListenerConfig.class})
    @Configuration
    static class Config {

        @Bean
        public ScheduleSendInvoiceToVipJobOnInvoiceFinalized
                scheduleSendInvoiceToVipJobOnInvoiceFinalized(
                        JobScheduler jobScheduler,
                        @Qualifier("vipCustomerPredicate") Predicate<String> customerPredicate) {
            return new ScheduleSendInvoiceToVipJobOnInvoiceFinalized(
                    jobScheduler, customerPredicate);
        }

        @Bean("vipCustomerPredicate")
        Predicate<String> customerPredicate() {
            return s -> true;
        }

        @Bean
        InMemoryEventPublisher eventPublisher() {
            return new InMemoryEventPublisher(MoreExecutors.directExecutor());
        }
    }

    @MockBean JobScheduler jobScheduler;

    @Autowired EventPublisher eventPublisher;

    @Test
    void testScheduleJobOnInvoiceFinalized() {
        var event = new ProjectInvoiceStatusChanged();
        event.setCustomerId(RandomStringUtils.randomNumeric(4));
        event.setStatus("Finalized");
        eventPublisher.publish(event);

        Mockito.verify(jobScheduler, Mockito.times(1)).schedule(any());
    }

    @Test
    void testIgnoreWhenInvoiceNotFinalized() {
        var event = new ProjectInvoiceStatusChanged();
        event.setCustomerId(RandomStringUtils.randomNumeric(4));
        event.setStatus("Paid");
        eventPublisher.publish(event);

        Mockito.verify(jobScheduler, Mockito.never()).schedule(any());
    }
}
