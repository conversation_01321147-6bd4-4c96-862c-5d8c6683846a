package com.bees360.event.registry;

import jakarta.annotation.Nullable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

@Event
@Data
@AllArgsConstructor
public class InvoicePaymentReceived {

    public InvoicePaymentReceived(
            @NonNull String invoiceNo,
            @NonNull BigDecimal amountPaid,
            @Nullable String checkNo,
            @NonNull LocalDate paymentDate) {
        this.invoiceNo = invoiceNo;
        this.amountPaid = amountPaid;
        this.checkNo = Optional.ofNullable(checkNo);
        this.paymentDate = paymentDate;
    }

    @NonNull private String invoiceNo;

    @NonNull private BigDecimal amountPaid;

    private Optional<String> checkNo;

    @NonNull private LocalDate paymentDate;
}
