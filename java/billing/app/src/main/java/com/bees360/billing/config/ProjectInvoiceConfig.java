package com.bees360.billing.config;

import com.bees360.billing.invoice.ProjectInvoicePdfGenerator;
import com.bees360.billing.listener.CreateInvoiceReportOnProjectInvoiceAdded;
import com.bees360.contract.ContractManager;
import com.bees360.contract.config.GrpcContractManagerConfig;
import com.bees360.contract.util.FillLogoResourceUrlContractManager;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.handlebar.HandlebarTemplateConfig;
import com.bees360.handlebar.HandlebarTemplateProperties;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.SaveProjectInvoiceJobExecutor;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.config.GrpcProjectContactClientConfig;
import com.bees360.project.config.GrpcProjectIIMangerConfig;
import com.bees360.project.config.JooqProjectInvoiceRepositoryConfig;
import com.bees360.project.invoice.ProjectInvoiceManager;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.ResourceUrlProvider;
import com.bees360.resource.config.GrpcResourceClientConfig;

import lombok.Getter;
import lombok.Setter;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.time.Duration;
import java.util.function.Function;

@Configuration
@Import({
    JooqProjectInvoiceRepositoryConfig.class,
    GrpcProjectIIMangerConfig.class,
    GrpcProjectContactClientConfig.class,
    GrpcContractManagerConfig.class,
    GrpcResourceClientConfig.class,
    AutoRegisterJobExecutorConfig.class,
    AutoRegisterEventListenerConfig.class,
    HandlebarTemplateConfig.class,
})
@ConditionalOnProperty(prefix = "billing.app.invoice", name = "enabled", havingValue = "true")
public class ProjectInvoiceConfig {

    @Configuration
    @ConfigurationProperties(prefix = "billing.invoice.auto-generate-pdf.job.retry-properties")
    @Getter
    @Setter
    static class JobRetryProperties {
        private Integer count = 5;
        private Duration delay = Duration.ofMinutes(1);
        private Float delayIncreaseFactor = 1.0F;
    }

    @Bean
    public SaveProjectInvoiceJobExecutor saveProjectInvoiceJobExecutor(
            ProjectInvoiceManager projectInvoiceManager) {
        return new SaveProjectInvoiceJobExecutor(projectInvoiceManager);
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "billing.app.invoice.auto-generate-pdf",
            name = "enabled",
            havingValue = "true",
            matchIfMissing = true)
    public CreateInvoiceReportOnProjectInvoiceAdded createInvoiceReportOnProjectInvoiceAdded(
            ProjectInvoiceManager projectInvoiceManager,
            ProjectInvoicePdfGenerator projectInvoicePdfGenerator) {
        return new CreateInvoiceReportOnProjectInvoiceAdded(
                projectInvoicePdfGenerator, projectInvoiceManager);
    }

    @Bean
    public ProjectInvoicePdfGenerator projectInvoicePdfGenerator(
            ProjectIIManager projectIIManager,
            ContactManager contactManager,
            @Qualifier("fillLogoResourceUrlContractManager") ContractManager contractManager,
            JobScheduler jobScheduler,
            JobRetryProperties retryProperties,
            HandlebarTemplateProperties handlebarResourcePrefix) {
        Function<Job, RetryableJob> retryableJobConvertor =
                job ->
                        RetryableJob.of(
                                job,
                                retryProperties.getCount(),
                                retryProperties.getDelay(),
                                retryProperties.getDelayIncreaseFactor());
        var invoiceTemplatePath = handlebarResourcePrefix.getInvoiceTemplatePath();
        return new ProjectInvoicePdfGenerator(
                projectIIManager,
                contactManager,
                contractManager,
                jobScheduler,
                retryableJobConvertor,
                invoiceTemplatePath);
    }

    @Bean
    public ResourceUrlProvider resourceUrlProvider(ResourcePool resourcePool) {
        return resourcePool.asResourceUrlProvider();
    }

    @Bean("fillLogoResourceUrlContractManager")
    public ContractManager fillLogoResourceUrlContractManager(
            ContractManager contractManager, ResourceUrlProvider resourceUrlProvider) {
        return new FillLogoResourceUrlContractManager(contractManager, resourceUrlProvider);
    }
}
