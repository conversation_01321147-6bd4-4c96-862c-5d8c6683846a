package com.bees360.billing.invoice;

import com.bees360.contract.ContractManager;
import com.bees360.invoice.Invoice;
import com.bees360.job.HtmlToPdfJob;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.Message;
import com.bees360.project.ProjectIIManager;
import com.bees360.util.Iterables;
import com.bees360.util.Messages;
import com.bees360.util.SecureTokens;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.function.BiFunction;
import java.util.function.Function;

/** Schedule a html_to_pdf job to generate invoice pdf, and it will return a pdf key. */
@Log4j2
public class ProjectInvoicePdfGenerator implements BiFunction<String, Invoice, String> {

    private final ProjectIIManager projectIIManager;

    private final ContactManager contactManager;

    private final ContractManager contractManager;

    private final JobScheduler jobScheduler;

    private final Function<Job, RetryableJob> retryableJobConvertor;

    private static final String INVOICE_HTML_TEMPLATE = "static/NewInvoiceReportTemplate.html";

    public ProjectInvoicePdfGenerator(
            ProjectIIManager projectIIManager,
            ContactManager contactManager,
            ContractManager contractManager,
            JobScheduler jobScheduler,
            Function<Job, RetryableJob> retryableJobConvertor,
            Function<String, String> handlebarTemplateProvider) {
        this.projectIIManager = projectIIManager;
        this.contactManager = contactManager;
        this.contractManager = contractManager;
        this.jobScheduler = jobScheduler;
        this.retryableJobConvertor = retryableJobConvertor;
    }

    @Override
    public String apply(String projectId, Invoice invoice) {
        var summary = assembleProjectSummary(projectId, invoice);
        String pdfKey = SecureTokens.generateRandomHexToken();

        var jobId = invoicePdfJobId(projectId, pdfKey);
        var job = HtmlToPdfJob.getInstance(jobId, INVOICE_HTML_TEMPLATE, pdfKey, null, summary);
        var job =
                HtmlToPdfJob.getInstance(
                        jobId,
                        handlebarTemplateProvider.apply(INVOICE_HTML_TEMPLATE),
                        pdfKey,
                        null,
                        summary);
        log.debug("Create project invoice report with job {}, summary '{}'.", jobId, summary);
        jobScheduler.schedule(retryableJobConvertor.apply(job));
        return pdfKey;
    }

    private String assembleProjectSummary(String projectId, Invoice invoice) {
        var project = projectIIManager.findById(projectId);
        var builder = project.toMessage().toBuilder();
        builder.addInvoice(
                Message.ProjectMessage.Invoice.newBuilder().setInvoice(invoice.toMessage()));

        // fill insured and agent info
        var contacts = contactManager.findByProjectId(builder.getId());
        builder.addAllContact(Iterables.transform(contacts, Contact::toMessage));

        // fill company logo
        var contract = contractManager.findById(builder.getContract().getId());
        builder.setContract(contract.toMessage());

        return convertMessage(builder.build());
    }

    // Keep same with old invoice generation pdf job.
    // It will be extracted in com.bees360.project.listener.GenerateInvoiceListener
    private String invoicePdfJobId(String projectId, String pdfKey) {
        var invoiceKey = "INV";
        return String.join("-", "report", projectId, invoiceKey, pdfKey);
    }

    // project json need to be wrapped with single colon when handling in html.
    private String convertMessage(Message.ProjectMessage projectMessage) {
        String json = Messages.toJsonStringIncludingDefaultValue(projectMessage);
        var summary = json.replaceAll("\\\\u003d", "=").replaceAll("\\\\u0026", "&");
        return StringUtils.join("'", summary, "'");
    }
}
