package com.bees360.billing.config;

import com.bees360.event.EventDispatcher;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.GenerateProjectInvoiceJob;
import com.bees360.lago.JSON;
import com.bees360.lago.converter.LagoInvoiceConverter;
import com.bees360.lago.listener.LagoEventTriggeredJob;
import com.bees360.lago.model.InvoiceMetadataObject;
import com.bees360.lago.model.InvoiceObjectCustomer;
import com.bees360.lago.model.InvoiceObjectExtended;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Log4j2
@Configuration
@ConditionalOnProperty(prefix = "billing.app.invoice", name = "enable-lago", havingValue = "true")
public class LagoEventTriggerJobConfig {
    public static final String WEBHOOK_INVOICE_CREATED_TYPE = "invoice.created";

    @Data
    @Configuration
    @ConfigurationProperties("lago.trigger-job")
    static class TriggerJobProperties {

        private JobRetryProperties retry = new JobRetryProperties();

        @Data
        static class JobRetryProperties {
            private Integer retryCount = 3;
            private Duration retryDelay = Duration.ofSeconds(10);
            private Float retryDelayIncreaseFactor = 1.0F;
        }
    }

    /** invoice.created类型webhook触发该Job, key为webhookType, value为EventJobConverter */
    @Bean
    Map.Entry<String, Function<String, Job>> lagoInvoiceCreatedTriggeredJobEntry(
            TriggerJobProperties triggerJobProperties) {
        var retryProperties = triggerJobProperties.getRetry();
        var retryCount = retryProperties.getRetryCount();
        var retryDelay = retryProperties.getRetryDelay();
        var retryDelayIncreaseFactor = retryProperties.getRetryDelayIncreaseFactor();
        // 触发Invoice入库job, 此时metadata中必须包含project_id
        Function<String, Job> triggerGenerateInvoiceJob =
                data -> {
                    var invoiceObject = JSON.getGson().fromJson(data, InvoiceObjectExtended.class);
                    var invoice = LagoInvoiceConverter.toInvoice(invoiceObject);
                    // 从metadata中获取invoice关联projectId
                    var projectId =
                            invoiceObject.getMetadata().stream()
                                    .filter(
                                            meta ->
                                                    StringUtils.equalsIgnoreCase(
                                                            meta.getKey(), "project_id"))
                                    .map(InvoiceMetadataObject::getValue)
                                    .findAny()
                                    .orElse(null);
                    var customerId =
                            Optional.ofNullable(invoiceObject.getCustomer())
                                    .map(InvoiceObjectCustomer::getExternalId)
                                    .orElse(null);
                    if (StringUtils.isBlank(projectId) || StringUtils.isBlank(customerId)) {
                        log.error(
                                "There is no related project or customer for lago invoice, will not"
                                        + " trigger generateInvoiceJob, invoiceNo={}",
                                invoice.getInvoiceNo());
                        return null;
                    }
                    var job =
                            Job.ofPayload(
                                    GenerateProjectInvoiceJob.newBuilder()
                                            .setInvoice(invoice)
                                            .setProjectId(projectId)
                                            .setCustomerId(customerId)
                                            .build());
                    return RetryableJob.of(job, retryCount, retryDelay, retryDelayIncreaseFactor);
                };

        return Map.entry(WEBHOOK_INVOICE_CREATED_TYPE, triggerGenerateInvoiceJob);
    }

    /** 汇总所有需要通过LagoEvent转换的Job, key为webhookType, value为EventJobConverter */
    @Bean("lagoJobRegistryMap")
    Map<String, Function<String, Job>> lagoJobRegistryMap(
            @Qualifier("lagoInvoiceCreatedTriggeredJobEntry")
                    ObjectProvider<Map.Entry<String, Function<String, Job>>>
                            invoiceCreatedTriggeredJobEntry) {
        return Stream.of(invoiceCreatedTriggeredJobEntry)
                .map(ObjectProvider::getIfAvailable)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Bean
    LagoEventTriggeredJob lagoEventTriggeredJob(
            EventDispatcher eventDispatcher,
            JobScheduler jobScheduler,
            @Qualifier("lagoJobRegistryMap") Map<String, Function<String, Job>> jobRegistryMap) {
        var lagoEventTriggeredJob = new LagoEventTriggeredJob(jobScheduler, jobRegistryMap);
        eventDispatcher.enlist(lagoEventTriggeredJob);
        return lagoEventTriggeredJob;
    }
}
