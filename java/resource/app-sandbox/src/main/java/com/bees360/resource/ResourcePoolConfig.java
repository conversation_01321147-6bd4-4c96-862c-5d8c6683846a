package com.bees360.resource;

import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.map.PatternMap;
import com.bees360.map.ResourceKeyAliasMap;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.resource.config.S3ResourceRepositoryFactoryConfig;
import com.bees360.resource.factory.ResourcePoolFactory;
import com.google.common.collect.Maps;

import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.net.URI;
import java.time.Duration;
import java.util.Map;
import java.util.function.Function;

@Configuration
@Import({
    RabbitApiConfig.class,
    RabbitJobDispatcher.class,
    RabbitJobScheduler.class,
    RabbitEventPublisher.class,
    RabbitEventDispatcher.class,
    S3ResourceRepositoryFactoryConfig.class,
    JooqConfig.class,
    ApacheHttpClientConfig.class,
})
public class ResourcePoolConfig {

    @Bean
    Function<ResourcePool, PatternMap<String>> aliasMapProvider(
            DSLContext dslContext,
            @Value("${app.resource.pool-def.alias-map.key-grace-period:PT15m}")
                    Duration keyGracePeriod) {
        return pool -> new ResourceKeyAliasMap(dslContext, pool, keyGracePeriod);
    }

    /** handle the resource for endpoint, which should not access the private system resource. */
    @Bean
    ResourcePool endpointResourcePool(
            @Value("${app.resource.pool.main}") URI primaryPoolContext,
            ResourcePoolFactory resourcePoolFactory,
            Function<ResourcePool, PatternMap<String>> aliasMapProvider) {
        var primaryResourcePool = resourcePoolFactory.get(primaryPoolContext);
        var primaryAlias =
                new AliasResourceRepository(
                        primaryResourcePool, aliasMapProvider.apply(primaryResourcePool), null);
        return linked(primaryAlias, primaryResourcePool, true);
    }

    @Bean
    ResourcePool cdnResourcePool(
            @Value("${app.resource.pool.cdn}") URI cdnResourcePoolContext,
            ResourcePoolFactory resourcePoolFactory) {
        return resourcePoolFactory.get(cdnResourcePoolContext);
    }

    /** handle all resource */
    @Bean({"resourcePool", "grpcResourceServiceResourcePool"})
    ResourcePool resourcePool(ResourcePool endpointResourcePool, ResourcePool cdnResourcePool) {
        Map<String, ResourcePool> poolMap = Maps.newLinkedHashMap();
        poolMap.put("cdn://", cdnResourcePool);
        poolMap.put("", endpointResourcePool);
        return ResourcePools.buildPrefixCompositeResourcePool(poolMap.entrySet());
    }

    private ResourcePool linked(
            ResourcePool primaryPool,
            ResourcePool secondaryPool,
            Boolean allowSecondaryPoolAsUrlProvider) {
        return new LinkedResourcePool(
                primaryPool, secondaryPool, null, null, allowSecondaryPoolAsUrlProvider);
    }
}
