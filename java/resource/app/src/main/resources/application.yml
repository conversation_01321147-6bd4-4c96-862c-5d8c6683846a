spring:
  profiles:
    active: ${ENV}
    include: actuator
  application:
    name: resource-app
  datasource:
    url: ********************************************************
    username: db_user
    password: db_password
    platform: postgres
    hikari:
      # This property controls the maximum lifetime of a connection in the pool. Default: 1800000
      max-lifetime: 1800000
      # This property controls how frequently HikariCP will attempt to keep a connection alive,
      # in order to prevent it from being timed out by the database or network infrastructure.
      keepalive-time: 60000
      # This property controls the amount of time (millisecond) that a connection can be out of the pool
      # before a message is logged indicating a possible connection leak.
      leak-detection-threshold: 5000
  jooq:
    sql-dialect: POSTGRES

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    password: ${BEES360_SECRET_KEY}

basic:
  map:
    cache:
      maximum-size: 10000
      refreshAfterWrite: PT15m

s3:
  clients:
    - endpoint: http://s3-primary
      key: local-identity
      secret: local-credential
      pathStyleAccess: true
      checksumValidationEnabled: false
    - endpoint: http://s3-secondary
      key: local-identity
      secret: local-credential
      pathStyleAccess: true
      checksumValidationEnabled: false

redis:
  client:
    host: redis
    port: 6379
    database: 0
  locks:
    keyPrefix: lock/
    expirationTime: PT1S
rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password
resource:
  s3:
    - client: s3-primary
      bucket: new-bucket
      url-expiration: PT10S
      url-stable-duration: PT5S
    - client: s3-secondary
      bucket: secondary-bucket
      url-expiration: PT10S
      url-stable-duration: PT5S
    - client: s3-primary
      bucket: primary-bucket
      url-expiration: PT10S
      url-stable-duration: PT5S
    - client: s3-primary
      bucket: gs-bucket
      url-expiration: PT10S
      url-stable-duration: PT5S
    - client: s3-primary
      bucket: bees360-bucket
      url-expiration: PT10S
      url-stable-duration: PT5S
    - client: s3-primary
      bucket: cdn-bucket
      url-expiration: PT10S
      url-stable-duration: PT5S
    - client: s3-primary
      bucket: secure-bucket
      url-expiration: PT10S
      url-stable-duration: PT5S

  linked:
    primaryPool: s3://s3-primary/primary-bucket/
    secondaryPool: s3://s3-secondary/secondary-bucket/
  app:
    gs-resource-pool: s3://s3-primary/gs-bucket/
    bees360-resource-pool: s3://s3-primary/bees360-bucket/
    cdn-resource-pool: s3://s3-primary//cdn-bucket/
    bucket: new-bucket
    event-listener-switch: true
    secure-resource-pool: s3://s3-primary/secure-bucket/
    transfer:
      push:
        name: "resource_transfer_push"
        retry-count: 3
        retry-delay: PT2S
        thread-count: 8
      pull:
        name: "resource_transfer_pull"
        thread-count: 8
    firebase-image-transfer:
      enable: true
    request-decouple:
      enable: true
    endpoint-using-linked2:
      enable: true

webhook:
  bees360:
    publisher:
      authToken: "FAKE_AUTH_TOKEN"
      uriMap:
        jobTrigger: "http://localhost:8080/webhook/bees360/job_trigger"

#日志配置
logging:
  level:
    org.springframework.security: TRACE
#        com.bees360.resource.AliasResourceRepository: DEBUG
#        com.bees360.resource.LinkedResourcePool: DEBUG
#         com.bees360.resource.ResourceAppITest: DEBUG
#         com.bees360.resource.HttpResourceClient: DEBUG

http:
  cors:
    '[/resource/**]':
      allowed_origins: "*"
      allowed_headers: "*"
      allowed_methods: GET,HEAD,PUT,OPTIONS
      allow_credentials: false
      exposed_headers: Authorization,Access-Control-Allow-Origin,Access-Control-Allow-Credentials,X-Protobuf-Message,Location,ETag
      max_age: 86400
  request:
    matchers:
      - method: "*"
        path: "/resource/**"
        accessRule: "permitAll"
      - method: "*"
        path: "/error"
        accessRule: "permitAll"
  resource:
    endpoint: "/resource"
  client:
    apache:
      maxConnPerRoute: 32
      maxConnTotal: 128
      evictIdleConnectionsAfter: PT10M
      connectionTimeToLive: PT10M
      evictExpiredConnections: true
      redirectStrategy: always
      request:
        connectTimeout: PT15S
        connectionRequestTimeout: PT30S
        socketTimeout: PT15S
        redirectEnabled: true
        expectContinueEnabled: true
        normalizeUri: false
    netty:
      maxConcurrency: 16
      readTimeout: PT30M
      writeTimeout: PT30M
      connectTimeout: PT15S
      connectionAcquisitionTimeout: PT30S
      connectionTimeToLive: PT10M
grpc:
  server:
    port: 9898
    permitKeepAliveTime: PT10S
  client:
    resourcePool:
      address: 'static://127.0.0.1:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
auth:
  jwt:
    disabled: true

# ----------------------- logbook ---------------------
# @see https://github.com/zalando/logbook#configuration
# 必须配置为TRACE，否则不会有输出，也就是说这个模块用的是trace输出
# logging.level.org.zalando.logbook: TRACE

logbook:
  # 需要输出日志的接口，实际需要输出的接口 = include - exclude
  include:
    - /**
  # 不需要输出日志的接口
  exclude:
  # 开启日志输出
  filter.enabled: true
  # log无需认证的接口(无body)，含404接口 -- 不需要授权的接口容易受到攻击，从而导致logfile非常大
  secure-filter.enabled: true
  format.style: json
  strategy: body-only-if-status-at-least
  # 只有状态大于等于minimum-status时才输出body
  minimum-status: 400
  # 混淆请求字段
  obfuscate:
    headers:
      - Authorization
      - X-Secret
      - scope
      - grant_type
      - username
    parameters:
      - access_token
      - refresh_token
      - client_secret
      - client_id
      - password
  write:
    # 日志达到指定长度进行块划分
    chunk-size: 1024
    max-body-size: 6144
