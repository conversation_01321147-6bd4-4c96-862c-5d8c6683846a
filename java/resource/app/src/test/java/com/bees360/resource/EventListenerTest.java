package com.bees360.resource;

import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.event.autoconfig.EnableEventAutoRegister;
import com.bees360.event.registry.ResourceKeyAliasBind;
import com.bees360.map.BasicMap;
import com.bees360.map.RedisCacheMap;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.redis.config.RedissonConfig;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.time.Duration;

@SpringBootTest
public class EventListenerTest {

    @Import({
        RedissonConfig.class,
        RabbitApiConfig.class,
        RabbitEventDispatcher.class,
        RabbitEventPublisher.class,
        RemoveAliasCacheOnKeyAliasBind.class,
    })
    @EnableEventAutoRegister
    @Configuration
    static class Config {
        @Bean
        public RedisCacheMap<String, String> resourceKeyAliasRedisMap(RedissonClient client) {
            return new RedisCacheMap<>(client, "resource/alias/", Duration.ofDays(14), null);
        }
    }

    @Autowired private BasicMap<String, String> aliasMap;
    @Autowired private RabbitEventPublisher rabbitEventPublisher;

    @Test
    void testRemoveAliasCacheOnKeyAliasBind() throws InterruptedException {
        var key = RandomStringUtils.randomAlphabetic(10);
        var value = RandomStringUtils.randomAlphanumeric(20);
        aliasMap.put(key, value);

        var event = new ResourceKeyAliasBind();
        event.setKey(key);
        event.setAlias(value);

        rabbitEventPublisher.publish(event);
        Thread.sleep(3000);
        Assertions.assertFalse(aliasMap.containsKey(key));
    }
}
