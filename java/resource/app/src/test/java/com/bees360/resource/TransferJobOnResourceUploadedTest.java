package com.bees360.resource;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.event.autoconfig.EnableEventAutoRegister;
import com.bees360.event.registry.S3ObjectUploaded;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.resource.util.TestResourcePool;

import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.net.URI;
import java.time.Duration;
import java.time.Instant;

@SpringBootTest
@ApplicationAutoConfig(exclude = GrpcServerFactoryAutoConfiguration.class)
@SpringJUnitConfig
@Import({TransferJobOnResourceUploadedTest.Config.class})
public class TransferJobOnResourceUploadedTest extends TestResourcePool {
    @Import({
        RabbitApiConfig.class,
        RabbitEventDispatcher.class,
        RabbitEventPublisher.class,
        RabbitJobScheduler.class,
        RabbitJobDispatcher.class,
        JooqConfig.class,
    })
    @EnableEventAutoRegister
    @Configuration
    static class Config {
        @Bean
        public ResourcePool primaryPool() {
            return new InMemoryResourceRepository(URI.create("inmem://sz"));
        }

        @Bean
        public ResourcePool secondaryPool() {
            return new InMemoryResourceRepository(URI.create("inmem://us"));
        }

        @Bean
        public ResourceTransferJobExecutor resourceTransferJobExecutor(
                RabbitJobDispatcher rabbitJobDispatcher,
                ResourcePool primaryPool,
                ResourcePool secondaryPool,
                ResourceApp.ResourceTransferProperties properties) {
            var executor =
                    new ResourceTransferJobExecutor(
                            properties.getName(), primaryPool, secondaryPool);
            rabbitJobDispatcher.enlist(executor, properties.getThreadCount());
            return executor;
        }

        @Bean
        public ResourceApp.ResourceTransferProperties properties() {
            ResourceApp.ResourceTransferProperties properties =
                    new ResourceApp.ResourceTransferProperties();
            properties.setName("test-transfer-resource-sz-to-us");
            properties.setRetryCount(5);
            properties.setRetryDelay(Duration.ofMinutes(1));
            properties.setRetryDelayIncreaseFactor(2f);
            properties.setThreadCount(1);
            properties.setFromBucket("test-sz");
            return properties;
        }

        @Bean
        public S3ObjectUploadedEventListener s3ObjectUploadedEventListener(
                RabbitJobScheduler rabbitJobScheduler,
                ResourcePool secondaryPool,
                ResourceApp.ResourceTransferProperties properties) {
            return new S3ObjectUploadedEventListener(rabbitJobScheduler, secondaryPool, properties);
        }
    }

    @Autowired private RabbitEventPublisher rabbitEventPublisher;
    private final ResourcePool targetPool;

    public TransferJobOnResourceUploadedTest(
            @Autowired ResourcePool primaryPool, @Autowired ResourcePool secondaryPool) {
        setResourcePool(primaryPool);
        this.targetPool = secondaryPool;
    }

    @Test
    public void testResourceUploadedAndTransferToUs() throws InterruptedException {

        String key = generateRandomKey();
        Resource resource = createRandomResource();
        put(key, resource);

        ResourceMetadata metadataInPrimary = head(key);

        S3ObjectUploaded event = new S3ObjectUploaded();
        event.setBucket("test-sz");
        event.setETag(metadataInPrimary.getETag());
        event.setKey(key);
        event.setContentLength(metadataInPrimary.getContentLength());
        event.setLastModified(metadataInPrimary.getLastModified());

        rabbitEventPublisher.publish(event);

        waitForTransfer();

        Resource resourceInSecondary = targetPool.get(key);
        assertResourceMatch(resourceInSecondary, resource);
    }

    @Test
    public void testResourceUploadedWithDiffBucketShouldNotTransfer() throws InterruptedException {

        String key = generateRandomKey();
        Resource resource = createRandomResource();
        put(key, resource);

        ResourceMetadata metadataInPrimary = head(key);

        S3ObjectUploaded event = new S3ObjectUploaded();
        event.setBucket("test-us");
        event.setETag(metadataInPrimary.getETag());
        event.setKey(key);
        event.setContentLength(metadataInPrimary.getContentLength());
        event.setLastModified(metadataInPrimary.getLastModified());

        rabbitEventPublisher.publish(event);

        waitForTransfer();

        Resource resourceInSecondary = targetPool.get(key);
        Assertions.assertNull(resourceInSecondary);
    }

    @Test
    public void testResourceUploadedAndUsHasSameResourceShouldNotTransfer()
            throws InterruptedException {
        String key = generateRandomKey();
        Resource resource = createRandomResource();
        targetPool.put(key, resource);
        ResourceMetadata metadataInSecondary = targetPool.head(key);

        Thread.sleep(Duration.ofSeconds(1).toMillis());

        ResourceMetadata metadataInPrimary =
                ResourceMetadata.newBuilder()
                        .mergeFrom(metadataInSecondary)
                        .setLastModified(Instant.now())
                        .build();
        put(key, Resource.of(resource.toByteString(), metadataInPrimary));

        S3ObjectUploaded event = new S3ObjectUploaded();
        event.setBucket("test-sz");
        event.setETag(metadataInPrimary.getETag());
        event.setKey(key);
        event.setContentLength(metadataInPrimary.getContentLength());
        event.setLastModified(metadataInPrimary.getLastModified());

        rabbitEventPublisher.publish(event);

        waitForTransfer();

        Assertions.assertTrue(
                metadataInSecondary
                        .getLastModified()
                        .isBefore(metadataInPrimary.getLastModified()));
    }

    @Test
    public void testResourceUploadedWithDiffETagShouldTransfer() throws InterruptedException {

        String key = generateRandomKey();
        Resource resource = createRandomResource();
        put(key, resource);
        ResourceMetadata metadataInPrimary = head(key);

        Resource resourceInSecondary = createRandomResource();
        ResourceMetadata metadataInSecondary = resourceInSecondary.getMetadata();
        targetPool.put(key, resourceInSecondary);

        Assertions.assertNotEquals(metadataInPrimary.getETag(), metadataInSecondary.getETag());

        S3ObjectUploaded event = new S3ObjectUploaded();
        event.setBucket("test-sz");
        event.setETag(metadataInPrimary.getETag());
        event.setKey(key);
        event.setContentLength(metadataInPrimary.getContentLength());
        event.setLastModified(metadataInPrimary.getLastModified());

        rabbitEventPublisher.publish(event);

        waitForTransfer();

        resourceInSecondary = targetPool.get(key);
        assertResourceMatch(resourceInSecondary, resource);
    }

    private void waitForTransfer() throws InterruptedException {
        Thread.sleep(Duration.ofSeconds(3).toMillis());
    }
}
