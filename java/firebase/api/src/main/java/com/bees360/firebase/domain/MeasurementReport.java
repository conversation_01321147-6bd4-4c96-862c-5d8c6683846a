package com.bees360.firebase.domain;

import com.google.cloud.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MeasurementReport {

    public static final String COLLECTION_NAME = "measurement_report";

    @Data
    public static class Image {
        String direction;

        String url;
    }

    private String projectId;

    private List<Image> images;

    private Timestamp createTime;
}
