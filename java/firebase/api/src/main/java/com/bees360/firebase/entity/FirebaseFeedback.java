package com.bees360.firebase.entity;

import com.google.cloud.Timestamp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;
import java.util.Optional;

@Setter
@Getter
public class FirebaseFeedback implements Serializable {

    public enum TemplateEnum {
        PolicyCanceled("PolicyCanceled", "Policy canceled", 1),
        Denied("Denied", "Denied", 1),
        NotClosed("NotClosed", "Not closed", 3),
        PendingToSchedule("PendingToSchedule", "Insured responded and pending to schedule", 13),
        RestrictedAirspace("RestrictedAirspace", "Restricted airspace", 14),
        InsuredNotAtHome("InsuredNotAtHome", "Insured not at home", 15),
        InclementWeather("InclementWeather", "Inclement weather", 16),
        PersonalAccident("PersonalAccident", "Personal accident", 17),
        TechProblems("TechProblems", "Tech problems", 18),
        InsuredCOVIDQuarantine("InsuredCOVIDQuarantine", "Insured COVID quarantine", 19),
        NoResponse("NoResponse", "No response from insured after multiple attempts.", 1),
        DeniedPhoneTextEmail("DeniedPhoneTextEmail", "The insured denied my inspection.", 1);

        private String name;
        private long tagId;
        private String description;

        TemplateEnum(String name, String description, long tagId) {
            this.name = name;
            this.description = description;
            this.tagId = tagId;
        }

        public static TemplateEnum getEnum(String name) {
            for (TemplateEnum value : values()) {
                if (Objects.equals(name, value.name)) {
                    return value;
                }
            }

            return null;
        }

        public String getName() {
            return name;
        }

        public String getDescription() {
            return description;
        }

        public long getTagId() {
            return tagId;
        }
    }

    @Setter
    @Getter
    public static class Template {
        private String templateName;
    }

    private static final long serialVersionUID = -7103595837982384360L;
    private Timestamp createdTime;

    private String message;

    private Template template;

    private String type = "COMMENT";

    public String getContent() {
        var description =
                Optional.ofNullable(TemplateEnum.getEnum(template.getTemplateName()))
                        .map(TemplateEnum::getDescription)
                        .orElse("Others");
        return description + ": " + message;
    }
}
