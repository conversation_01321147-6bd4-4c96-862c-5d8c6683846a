package com.bees360.firebase.config;

import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.user.UserKeyProvider;
import com.bees360.util.JsonConverts;
import com.google.protobuf.ByteString;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Configuration
@Import({RabbitApiConfig.class, RabbitJobScheduler.class})
@EnableConfigurationProperties(JobSchedulerConfig.JobProperty.class)
@Log4j2
public class JobSchedulerConfig {
    @Data
    @ConfigurationProperties(prefix = "firebase.app.job-converter")
    public static class JobProperty {
        @Data
        private static class Property {
            // job名称
            private String jobName;
            // 飞手 id 在 job payload json 中 对应的路径，用来进行 id 转换。路径以'.'分割，比如：'pilotId'
            private List<String> pilotIdPaths = new ArrayList<>();
        }

        // job 相关配置
        private List<Property> jobs = new ArrayList<>();
        // job payload 转换相关配置
        // 是否开启 飞手 id 转换
        private boolean enabled;
        // 飞手 id 转换黑名单
        private List<String> pilotIdBlackList = new ArrayList<>();
    }

    @Bean("jobScheduler")
    public JobScheduler jsonConvertJobScheduler(
            RabbitJobScheduler rabbitJobScheduler,
            @Autowired @Qualifier("jobName2JsonConverter")
                    Map<String, Function<String, String>> jobName2JsonConverter) {
        return job -> {
            var payload = job.getPayload();
            var jobName = job.getName();
            if (!jobName2JsonConverter.containsKey(jobName)) {
                return rabbitJobScheduler.schedule(job);
            }

            var jobJsonConverter = jobName2JsonConverter.get(jobName);
            var json = payload.toStringUtf8();
            var convertedJson = jobJsonConverter.apply(json);
            log.debug("Converted json from '{}' to '{}'", payload, convertedJson);
            var convertedJob =
                    Job.of(job.getName(), job.getId(), ByteString.copyFromUtf8(convertedJson));
            return rabbitJobScheduler.schedule(convertedJob);
        };
    }

    @Bean
    @ConditionalOnMissingBean(name = "jobScheduler")
    public JobScheduler jobScheduler(RabbitJobScheduler rabbitJobScheduler) {
        return rabbitJobScheduler;
    }

    @Configuration
    @ConditionalOnProperty(
            prefix = "firebase.app.job-converter",
            value = "enabled",
            havingValue = "true")
    static class JobConverterConfig {

        @Import(com.bees360.user.config.GrpcUserKeyProviderConfig.class)
        @ConditionalOnProperty(
                prefix = "grpc.client.userKeyProvider",
                value = "enabled",
                havingValue = "true",
                matchIfMissing = true)
        @Configuration
        static class GrpcUserKeyProviderConfig {}

        @Bean
        Function<String, String> pilotIdConverter(
                UserKeyProvider userKeyProvider, JobProperty jobProperty) {
            var blackListUsers = jobProperty.getPilotIdBlackList();
            return (firebaseUserId) -> {
                if (StringUtils.isBlank(firebaseUserId)) {
                    // return origin firebase user id if it is null or empty.
                    return firebaseUserId;
                }

                // 黑名单模式, 如果firebaseUserId 在黑名单中则直接返回原始 firebaseUserId
                if (blackListUsers.contains(firebaseUserId)) {
                    log.debug(
                            "pilot '{}' in black list '{}', return origin firebase user instead.",
                            firebaseUserId,
                            blackListUsers);
                    return firebaseUserId;
                }

                var user = userKeyProvider.findUserByKey(firebaseUserId);
                if (user == null) {
                    log.debug(
                            "Skip to convert pilot id to bifrost user id: "
                                    + "'{}' maybe not a pilot id, using origin id '{}' instead.",
                            firebaseUserId,
                            firebaseUserId);
                    return firebaseUserId;
                }
                var userId = user.getId();
                log.info(
                        "Successfully convert pilot '{}' to bifrost user id '{}'",
                        firebaseUserId,
                        userId);
                return userId;
            };
        }

        @Bean
        public Map<String, Function<String, String>> jobName2JsonConverter(
                Function<String, String> pilotIdConverter, JobProperty jobProperty) {
            var events = jobProperty.getJobs();
            var map = new HashMap<String, Function<String, String>>();
            for (JobProperty.Property property : events) {
                map.put(
                        property.getJobName(),
                        getJsonConverter(pilotIdConverter, property.getPilotIdPaths()));
            }
            return map;
        }

        private static Function<String, String> getJsonConverter(
                Function<String, String> pilotIdConverter, List<String> fieldPaths) {
            return (json) -> {
                var convertedJson = json;
                for (String fieldPath : fieldPaths) {
                    convertedJson =
                            JsonConverts.replaceStringField(
                                    fieldPath, pilotIdConverter, convertedJson);
                }
                return convertedJson;
            };
        }
    }
}
