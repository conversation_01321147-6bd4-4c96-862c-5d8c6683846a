package com.bees360.job.registry;

import com.bees360.codec.SerializableObject;

import jakarta.annotation.Nullable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.Instant;

@JobPayload
@ToString
@SerializableObject
@Setter
@Getter
public class FirebaseMissionCompletedStuckV2 {
    private String pilotId;
    private String missionPath;
    private String projectId;

    // mission stuck 状态变更时间
    private Instant statusUpdateTime;

    @Nullable
    public String getMissionId() {
        if (missionPath == null) {
            return null;
        }

        return missionPath.substring(missionPath.lastIndexOf('/') + 1);
    }
}
