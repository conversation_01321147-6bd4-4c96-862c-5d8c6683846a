# Git Hooks

这个目录包含项目的 Git hooks，用于在代码提交过程中自动执行各种检查和操作。

## 🚀 快速安装

运行安装脚本来安装所有 Git hooks：

```bash
./git-hooks/install-hooks.sh
```

## 📋 可用的 Hooks

### pre-commit
- **功能**: 在每次 commit 前运行 pre-commit 检查
- **命令**: `pre-commit run --all-files --hook-stage commit`
- **作用**: 执行代码格式化、linting、测试等检查
- **失败时**: 阻止提交，需要修复问题后重新提交

### commit-msg
- **功能**: 检查提交消息格式
- **作用**: 确保提交消息符合项目规范

### pre-push
- **功能**: 在推送前执行检查
- **作用**: 最后一层质量检查

### prepare-commit-msg
- **功能**: 准备提交消息模板
- **作用**: 为提交消息提供标准格式

## 🔧 手动安装单个 Hook

如果只想安装特定的 hook：

```bash
# 复制 hook 文件到 .git/hooks/ 目录
cp git-hooks/pre-commit .git/hooks/pre-commit

# 添加执行权限
chmod +x .git/hooks/pre-commit
```

## 🛠️ 自定义配置

### 修改 pre-commit 行为

如果需要修改 pre-commit 的行为，编辑 `git-hooks/pre-commit` 文件：

```bash
# 只运行特定的 hooks
pre-commit run --hook-stage commit specific-hook-name

# 跳过某些文件
pre-commit run --all-files --hook-stage commit --exclude "*.generated.*"
```

### 临时跳过 pre-commit

在紧急情况下，可以跳过 pre-commit 检查：

```bash
git commit --no-verify -m "Emergency fix"
```

**⚠️ 注意**: 只在紧急情况下使用 `--no-verify`，平时应该通过修复问题来通过检查。

## 🔍 故障排除

### Hook 没有执行
1. 确认 hook 文件有执行权限：`ls -la .git/hooks/`
2. 确认文件在正确位置：`.git/hooks/pre-commit`
3. 重新运行安装脚本：`./git-hooks/install-hooks.sh`

### Pre-commit 命令不存在
确保已安装 pre-commit：
```bash
pip install pre-commit
# 或
brew install pre-commit
```

### 检查失败
1. 查看具体的错误信息
2. 运行 `pre-commit run --all-files` 手动检查
3. 修复报告的问题
4. 重新提交

## 📚 更多信息

- [Pre-commit 官方文档](https://pre-commit.com/)
- [Git Hooks 文档](https://git-scm.com/book/en/v2/Customizing-Git-Git-Hooks) 