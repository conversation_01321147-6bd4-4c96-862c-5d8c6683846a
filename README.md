# Solid

The new Bees360 backend.

[![pipeline status](https://gitlab.bees360.com/engineers/solid/badges/master/pipeline.svg)](https://gitlab.bees360.com/engineers/solid/commits/master)
[![coverage report](https://gitlab.bees360.com/engineers/solid/badges/master/coverage.svg)](https://gitlab.bees360.com/engineers/solid/commits/master)

## API Docs

API Docs is available at: [Javadoc](http://engineers.pages.gitlab.bees360.com/solid/)

## CI/CD流程

目前Solid项目采用GitLab CI作为CI/CD平台. 所有配置在.gitlab-ci.yml里面可以看到. 所有流程的最终解释请以.gitlab-ci.yml中的代码为准, 以文档为辅.

本项目的所有编译jar文件,

* 当本地编译时, 使用1.0.0-LOCAL作为版本号;
* 当在master分支以及merge request里测试时, 使用1.0.0-SNAPSHOT作为版本号;
* 当在release-X.Y.Z分支编译时, 使用X.Y.Z-RELEASE作为版本号.

对应的发布流程为,

* 如果master分支的pipeline的deploy阶段通过, 则会将1.0.0-SNAPSHOT版本发布到maven仓库中;
* 如果master分支的pipeline的pages任务完成, 则会将该版本对应的Javadoc发布到Gitlab Pages里面;
* 如果release-X.Y.Z的tag的pipeline的deploy阶段完成, 则会将X.Y.Z-RELEASE版本发布到maven仓库中;

## Pipeline测试报告

### 生成测试报告

新增module需添加到java/test-report-aggregate/pom.xml后，pipeline运行时会自动生成

### 本地生成测试报告

```shell
# 生成全部report
mvn clean verify
# 生成指定模块的report
mvn clean verify -pl com.bees360:$module_id -am
# demo
mvn clean verify -pl com.bees360:bees360-address-app -am
```

## 代码格式规范

所有 Java 代码必须使用 [Google Java Format](https://github.com/google/google-java-format) 进行格式化才允许进入 master 分支.
目前使用的 Google Java Format 版本为 1.7 (更高版本需要 Java 11).
在提交 Merge Request 的时候, 会有 check-format 的 job 自动触发, 如果代码经过 `google-java-format` 之后会发生变化, 则说明代码格式不符合要求, check-format 会报错.
开发可以在本地执行 `make format` 来自动格式化所有代码.

## 异常处理规范

请参照 [异常处理规范](https://gitlab.bees360.com/engineers/solid/wikis/%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E8%A7%84%E8%8C%83).


## 本地开发

请参考 [本地开发流程](https://gitlab.bees360.com/engineers/solid/-/blob/master/k8s/local/README.md)

安装到java项目本地maven的repository。
```bash
mvn -P local clean install
```

## 本地liquibase初始化
```
请注意以下变量应该填每个人本地的实际环境的值
LIQUIBASE_COMMAND_USERNAME
LIQUIBASE_COMMAND_PASSWORD
LIQUIBASE_COMMAND_URL

docker run -it -e LIQUIBASE_COMMAND_USERNAME=db_user -e LIQUIBASE_COMMAND_PASSWORD=db_password -e LIQUIBASE_COMMAND_URL=**************************************** -v $PWD/db/solid:/db/solid --rm harbor.9realms.co/private/liquibase sh -c 'cd /db/solid && liquibase update'
```
