-- liquibase formatted sql

-- changeset mingcong:add_event_record logicalFilePath:changelog.sql
CREATE SEQUENCE event_record_id_seq
START WITH *************;

-- Event System
CREATE TABLE IF NOT EXISTS event_record (
  id bigint PRIMARY key DEFAULT nextval('event_record_id_seq'),
  event_name varchar(255) NOT NULL, -- rabbitmq routing key length limit 255
  payload JSONB NOT NULL,
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE INDEX ON event_record(updated_at);
CREATE INDEX ON event_record(event_name);

CREATE OR REPLACE FUNCTION event_notify(event_name VARCHAR, payload VARCHAR) RETURNS VOID
    LANGUAGE plpgsql
AS '
    BEGIN
        -- Insert data into the event_record table
        INSERT INTO event_record (event_name, payload, updated_at)
        VALUES (event_name, payload::jsonb, NOW());
    END;
';
CREATE OR REPLACE FUNCTION event_notify(event_name VARCHAR, payload jsonb) RETURNS VOID
    LANGUAGE plpgsql
AS '
    BEGIN
        -- Insert data into the event_record table
        INSERT INTO event_record (event_name, payload, updated_at)
        VALUES (event_name, payload, NOW());
    END;
';

-- rollback DROP FUNCTION IF EXISTS event_notify(event_name VARCHAR, payload VARCHAR);
-- rollback DROP FUNCTION IF EXISTS event_notify(event_name VARCHAR, payload jsonb);
-- rollback DROP TABLE IF EXISTS event_record;
-- rollback DROP SEQUENCE IF EXISTS event_record_id_seq;
