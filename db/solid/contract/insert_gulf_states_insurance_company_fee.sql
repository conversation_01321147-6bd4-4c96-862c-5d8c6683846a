-- liquibase formatted sql
-- changeset shoushan:insert_gulf_states_insurance_company_fee logicalFilePath:changelog.sql
DO
'
    DECLARE
        companyId         bigint;
        fullInspectionId  bigint;
        hiveLocation      bigint;
        deniedOnLocation  bigint;
        contractId        bigint;
        serviceId         bigint;
    BEGIN
        select id into companyId from customer where key = ''Gulf State Insurance'';
        select id into fullInspectionId from contract_service where key = ''Full Inspection Base'';
        select id into hiveLocation from contract_service where key = ''Away from Hive Location'';
        select id into deniedOnLocation from contract_service where key = ''Full Inspection (denied on location)'';

        for contractId in (select id from contract where insured_by = companyId) loop
            for serviceId in (SELECT id from contract_service where key in (''Full Inspection Base'',
                ''All Other Interior'',''Catastrophe Fee'',''Commercial Roof (1 - 40 Squares)'',
                ''Commercial Roof (41 - 80 Squares)'',''Commercial Roof (81 - 120 Squares)'',
                ''Commercial Roof (120 + Squares)'',''Erroneous Assignment'',''Away from Hive Location'',
                ''Additional Structures exceeding 3'',''Benchmark Weather Report'',''HOVER - complete'',
                ''Full Inspection (denied on location)'')) loop

                    IF fullInspectionId = serviceId THEN
                        insert into "public".contract_service_item (contract_id, service_id, unit_price) values (contractId, serviceId, 350);
                    ELSEIF hiveLocation = serviceId THEN
                        insert into "public".contract_service_item (contract_id, service_id, unit_price) values (contractId, serviceId, 50);
                    ELSEIF deniedOnLocation = serviceId THEN
                        insert into "public".contract_service_item (contract_id, service_id, unit_price) values (contractId, serviceId, 350);
                    ELSE
                        insert into "public".contract_service_item (contract_id, service_id) values (contractId, serviceId);
                    END IF;
            END LOOP;
        END LOOP;
    END;
';

-- rollback delete from contract_service_item where contract_id in (select id from contract where insured_by=(select id from customer where key='Gulf State Insurance'));
