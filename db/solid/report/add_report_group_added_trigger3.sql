CREATE OR REPLACE FUNCTION notify_report_group_added() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
    DECLARE
        channel text := TG_ARGV[0];
        row RECORD;
        event TEXT;
        routing_key TEXT;
    BEGIN
    FOR row IN (SELECT id,
                               report_id,
                               group_key,
                               group_type,
                               created_by
                               FROM report_group
                               WHERE id=NEW.id)
            LOOP
	            routing_key := concat_ws(''.'', channel, row.group_type);
                event := json_build_object(''routing_key'', routing_key, ''payload'', row_to_json(row));
                PERFORM pg_notify(''publish_event''::TEXT, event::text);
            END LOOP;
        RETURN NULL;
    END;
';
