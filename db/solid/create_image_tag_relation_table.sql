
CREATE TABLE IF NOT EXISTS image_tag_relation (

    id          BIGSERIAL   NOT NULL,
    image_id    char(32)                    NOT NULL,
    tag_id      INT                         NOT NULL,
    created_by  varchar(32)                 NOT NULL,
    created_at  TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT NOW(),
    is_deleted  BIGINT                      NOT NULL DEFAULT 0,
    deleted_by  varchar(32),
    deleted_at  TIMESTAMP WITH TIME ZONE,
    created_by_ai       bool			    NOT NULL,
    CONSTRAINT image_tag_relation_pk PRIMARY KEY (id)
);
CREATE UNIQUE INDEX image_tag_id_index ON image_tag_relation(image_id, tag_id, is_deleted);
