CREATE OR REPLACE FUNCTION insert_project_status_history() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
    BEGIN
        INSERT INTO project_status_history (project_id, status, updated_at, updated_by, comment) VALUES (NEW.project_id, NEW.status, NEW.updated_at, NEW.updated_by, NEW.comment);
        RETURN NEW;
    END;
';

CREATE TRIGGER insert_project_status_history
    AFTER INSERT OR UPDATE
    ON project_status
    FOR EACH ROW
EXECUTE PROCEDURE insert_project_status_history();
