DROP TRIGGER IF EXISTS insert_project_timeline ON project_status_history;

CREATE OR R<PERSON>LACE FUNCTION insert_project_timeline() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
    DECLARE
        CREATED             INTEGER := 10;
        CUSTOMER_CONTACTED  INTEGER := 30;
        ASSIGNED_TO_PILOT   INTEGER := 50;
        SITE_INSPECTED      INTEGER := 70;
        IMAGE_UPLOADED      INTEGER := 80;
        RETURNED_TO_CLIENT  INTEGER := 90;
        CLIENT_RECEIVED     INTEGER := 100;
    BEGIN
        IF NEW.status = CREATED THEN
            INSERT INTO project_timeline (project_id, created_time) VALUES (NEW.project_id, NEW.updated_at)
            ON CONFLICT DO NOTHING;
        ELSIF NEW.status = CUSTOMER_CONTACTED THEN
            UPDATE project_timeline SET customer_contacted_time = NEW.updated_at WHERE project_id = NEW.project_id
            AND customer_contacted_time IS NULL;
        ELSIF NEW.status = ASSIGNED_TO_PILOT THEN
            UPDATE project_timeline SET assigned_to_pilot_time = NEW.updated_at WHERE project_id = NEW.project_id
            AND assigned_to_pilot_time IS NULL;
        ELSIF NEW.status = SITE_INSPECTED THEN
            UPDATE project_timeline SET site_inspected_time = NEW.updated_at WHERE project_id = NEW.project_id
            AND site_inspected_time IS NULL;
        ELSIF NEW.status = IMAGE_UPLOADED THEN
            UPDATE project_timeline SET image_uploaded_time = NEW.updated_at WHERE project_id = NEW.project_id
            AND image_uploaded_time IS NULL;
        ELSIF NEW.status = RETURNED_TO_CLIENT THEN
            UPDATE project_timeline SET first_return_to_client_time = NEW.updated_at WHERE project_id = NEW.project_id
            AND first_return_to_client_time IS NULL;

            UPDATE project_timeline SET last_return_to_client_time = NEW.updated_at WHERE project_id = NEW.project_id;
        ELSIF NEW.status = CLIENT_RECEIVED THEN
            UPDATE project_timeline SET first_client_received_time = NEW.updated_at WHERE project_id = NEW.project_id
            AND first_client_received_time IS NULL;

            UPDATE project_timeline SET last_client_received_time = NEW.updated_at WHERE project_id = NEW.project_id;
        ELSE
            -- do nothing.
        END IF;
        RETURN NEW;
    END;
';

CREATE TRIGGER insert_project_timeline
    AFTER INSERT OR UPDATE
    ON project_status
    FOR EACH ROW
EXECUTE PROCEDURE insert_project_timeline();
