CREATE OR REPLACE FUNCTION notify_project_status_changed() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
    DECLARE
        row RECORD;
    BEGIN
        FOR row IN (SELECT project_id,
                           status,
                           OLD.status as old_status,
                           updated_by,
                           comment,
                           to_char(updated_at at time zone ''UTC'', ''YYYY-MM-DD"T"HH24:MI:SS.US"Z"'') AS updated_at,
                           to_char(created_at at time zone ''UTC'', ''YYYY-MM-DD"T"HH24:MI:SS.US"Z"'') AS created_at
                    FROM project_status
                    WHERE project_id = NEW.project_id)
            LOOP
                PERFORM pg_notify(''project_status_changed''::text, row_to_json(row)::text);
            END LOOP;
        RETURN NULL;
    END;
';
