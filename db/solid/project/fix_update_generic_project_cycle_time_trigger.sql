CREATE OR REPLACE FUNCTION calculate_generic_project_cycle_time() RETURNS TRIGGER
    LANGUAGE plpgsql
    AS
    '
    DECLARE
        RECEIVED_STAGE                  VARCHAR := ''Received'';
        RETURN_STAGE                    VARCHAR := ''Returned'';
        PROJECT_CREATED                 INTEGER := 10;
        SECONDS_PER_DAY                 INTEGER := 86400;
        stage                           project_stage%rowtype;
        start_at                        TIMESTAMP;
        result                          REAL := 0;
        pre_cycle_time                  REAL := 0;
        canceled_time                   REAL := 0;
    BEGIN
        -- get project_stage by status and project type
        FOR stage IN (SELECT *
                        FROM project_stage
                        WHERE ( scope = 0 OR scope = (SELECT project_type FROM project WHERE id = NEW.project_id))
                        AND NEW.status = ANY (end_nodes))
        LOOP
            pre_cycle_time := 0;

            IF stage.name = RECEIVED_STAGE THEN
                INSERT INTO project_cycle_time (project_id, type, cycle_time, start_at, end_at)
                VALUES (NEW.project_id, stage.id, 0, NEW.updated_at, NEW.updated_at)
                ON CONFLICT DO NOTHING;
                RETURN NULL;
            ELSIF stage.name = RETURN_STAGE THEN
                SELECT MIN(updated_at) INTO start_at
                FROM project_status_history ps
                WHERE project_id = NEW.project_id
                AND status = ANY(stage.start_nodes)
                AND updated_at != NEW.updated_at;
            ELSE
                SELECT MAX(updated_at) INTO start_at
                FROM project_status_history
                WHERE project_id = NEW.project_id
                AND (status = ANY(stage.start_nodes) OR status = NEW.status)
                AND updated_at != NEW.updated_at;

                -- get pre cycle time if exists.
                SELECT cycle_time INTO pre_cycle_time
                FROM project_cycle_time
                WHERE project_id = NEW.project_id AND type = stage.id;
            END IF;

            IF start_at is NULL THEN
                CONTINUE;
            END IF;

            -- calculate stage cycle time
            result := EXTRACT(EPOCH FROM (NEW.updated_at::TIMESTAMP - start_at))/SECONDS_PER_DAY;

            IF result <= 0 THEN
                CONTINUE;
            END IF;

            -- get canceled time if exists.
            canceled_time := calculate_project_canceled_time(NEW.project_id, start_at);

            -- insert into project_cycle_time by result.
            INSERT INTO project_cycle_time (project_id, type, cycle_time, start_at, end_at)
            VALUES (NEW.project_id, stage.id, result - canceled_time, start_at, NEW.updated_at)
            ON CONFLICT (project_id, type)
            DO UPDATE SET cycle_time = pre_cycle_time + result - canceled_time, end_at = NEW.updated_at;

        END LOOP;

        RETURN NULL;
    END;
    ';
