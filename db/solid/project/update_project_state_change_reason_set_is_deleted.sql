UPDATE project_state_change_reason SET is_deleted = false
WHERE change_reason_type = 'CLOSE' AND display_text IN (
        'Policy Canceled',
        'Insured''s Request Beyond Carrier''s Deadline',
        'Under Renovation/Construction',
        'Insured Declines Drone Inspection',
        'Insured Withdraws Claims',
        'Insured Refuses to Schedule',
        'Inappropriate Type of Loss',
        'Cancellation by Client - Inspector No Show',
        'Cancellation by Client - Multiple Rescheduling Issues',
        'Cancellation by Client - Complaint from Agent',
        'Cancellation by Client - Unknown',
        'Unable to Obtain Airspace Authorization'
);

UPDATE project_state_change_reason SET "key" = 'POLICY CANCELED', change_reason_type = 'CLOSE', is_deleted = false
WHERE change_reason_type = 'UNDEFINED' AND display_text = 'Policy Canceled';
