-- liquibase formatted sql
-- changeset xiaojun:create_table_project_operating_company logicalFilePath:changelog.sql

CREATE TABLE public.project_operating_company (
    id                  BIGSERIAL PRIMARY KEY,
    project_id          BIGINT NOT NULL,
    operating_company   VARCHAR(256) NOT NULL,
    created_at          TIMESTAMP DEFAULT NOW(),
    UNIQUE (project_id)
);
-- rollback DROP TABLE project_operating_company;


-- changeset xiaojun:alter_table_project_operating_company_add_slot logicalFilePath:changelog.sql
ALTER TABLE public.project_operating_company REPLICA IDENTITY FULL;
ALTER PUBLICATION dbz_publication ADD TABLE public.project_operating_company;
-- rollback ALTER PUBLICATION dbz_publication DROP TABLE public.project_operating_company;
-- rollback ALTER TABLE public.project_operating_company REPLICA IDENTITY DEFAULT;


