-- liquibase formatted sql
-- changeset weizong:add_project_days_old logicalFilePath:changelog.sql

CREATE TABLE IF NOT EXISTS project_days_old
(
    id                      BIGSERIAL PRIMARY KEY,
    project_id              BIGINT NOT NUlL UNIQUE,
    -- 截至到上一次pause或者close的cycle time. 当effective date或者project state更新需要重新计算这个值。
    pre_days_old            INT NOT NULL DEFAULT 0,
    last_opened_at         	TIMESTAMP
);


ALTER TABLE project_state_history ADD COLUMN is_changed_from_open boolean DEFAULT false;

-- rollback ALTER TABLE project_state_history DROP COLUMN is_changed_from_open;
-- rollback DROP TABLE IF EXISTS project_days_old;
