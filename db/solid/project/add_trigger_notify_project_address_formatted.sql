-- liquibase formatted sql
-- changeset xiaojun:add_trigger_notify_project_address_formatted logicalFilePath:changelog.sql

CREATE OR REPLACE FUNCTION notify_project_address_formatted() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
    DECLARE
        row         RECORD;
        event       JSON;
    BEGIN
            SELECT INTO row
                p.id as project_id, p.origin_address, ad.id as address_id
            FROM project p
            LEFT JOIN policy pl ON pl.id = p.policy_id
            LEFT JOIN address ad ON ad.id = pl.address_id
            WHERE p.id = NEW.id;

            event = json_build_object(
                    ''routing_key'', ''address_formatted_event'',
                    ''payload'', row_to_json(row)
                );
            PERFORM pg_notify(''publish_event''::text, event::text);
        RETURN NULL;
    END;
';

CREATE TRIGGER trigger_notify_project_address_formatted
    AFTER INSERT
    ON project
    FOR EACH ROW
    EXECUTE PROCEDURE notify_project_address_formatted();

-- rollback DROP TRIGGER IF EXISTS trigger_notify_project_address_formatted ON project;
-- rollback DROP FUNCTION IF EXISTS notify_project_address_formatted();
