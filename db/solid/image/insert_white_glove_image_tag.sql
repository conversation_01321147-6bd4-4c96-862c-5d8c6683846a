-- liquibase formatted sql

-- changeset shoushan:insert_white_glove_image_tag logicalFilePath:changelog.sql
INSERT INTO image_tag (id, title, category, description ,seq_no) values (1721, 'Alarm' ,'Annotation','Component', 1721);
INSERT INTO image_tag (id, title, category, description ,seq_no) values (1722, 'Water Shutoff Valve' ,'Annotation','Component', 1722);
INSERT INTO image_tag (id, title, category, description ,seq_no) values (1723, 'Dry Hydrants' ,'Annotation','Component', 1723);
INSERT INTO image_tag (id, title, category, description ,seq_no) values (1724, 'Home Sprinklers' ,'Annotation','Component', 1724);
INSERT INTO image_tag (id, title, category, description ,seq_no) values (1725, 'Fire Extinguishers' ,'Annotation','Component', 1725);
INSERT INTO image_tag (id, title, category, description ,seq_no) values (1726, 'Fire Proof Cabinets' ,'Annotation','Component', 1726);
INSERT INTO image_tag (id, title, category, description ,seq_no) values (1727, 'Flammable Rag' ,'Annotation','Component', 1727);
INSERT INTO image_tag (id, title, category, description ,seq_no) values (1728, 'No Smoking Signs' ,'Annotation','Component', 1728);

-- rollback DELETE FROM image_tag where id in (1720, 1721, 1722, 1723, 1724, 1725, 1726, 1727, 1728);
