-- liquibase formatted sql
-- changeset shoushan:create_image_tag_singleton_trigger logicalFilePath:changelog.sql

CREATE OR REPLACE FUNCTION delete_same_category_image_tag() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
    DECLARE
        tagId               integer[];
        tagRelationId       bigint[];
        categoryTags        RECORD;
        relationTags        RECORD;
    BEGIN
        FOR categoryTags IN
            SELECT id FROM image_tag
                WHERE category<>''Annotation''
                    AND category=(SELECT category FROM image_tag WHERE id=NEW.tag_id)
            LOOP
                tagId = array_append(tagId, categoryTags.id);
            END LOOP;

        IF array_length(tagId, 1) IS NULL OR array_length(tagId, 1) <= 0 THEN
            RETURN NEW;
        END IF;

        FOR relationTags IN
            SELECT id FROM image_tag_relation
                WHERE image_id=NEW.image_id AND is_deleted=0 AND tag_id=ANY(tagId)
            LOOP
                tagRelationId = array_append(tagRelationId, relationTags.id);
            END LOOP;

        IF array_length(tagRelationId, 1) IS NULL OR array_length(tagRelationId, 1) <= 0 THEN
            RETURN NEW;
        END IF;

        UPDATE image_tag_relation
            SET is_deleted=id, deleted_by=NEW.created_by, deleted_at=now()
            WHERE id=ANY(tagRelationId);

        RETURN NEW;
    END;
';

DROP TRIGGER IF EXISTS delete_image_tag_in_same_category ON image_tag_relation;
CREATE TRIGGER delete_image_tag_in_same_category
    BEFORE INSERT ON image_tag_relation
    FOR EACH ROW
    EXECUTE PROCEDURE delete_same_category_image_tag();
-- rollback DROP TRIGGER delete_image_tag_in_same_category ON image_tag_relation;
-- rollback DROP FUNCTION delete_same_category_image_tag();
