-- liquibase formatted sql
-- changeset shoushan:add_hosta_entrance_tag_and_group logicalFilePath:changelog.sql

INSERT INTO image_tag (id, title, category, description, seq_no) VALUES (712, 'Hazardous Adjacent Property', 'Annotation', 'Hazard', 712);
INSERT INTO image_tag (id, title, category, description, seq_no) VALUES (1729, 'Entrance', 'Annotation', 'Component', 1729);
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('hallway_or_entry', 10, 'Entrance');
DELETE FROM image_tag_group where group_key = 'room_wall' and image_tag = 'Wall';

-- rollback INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('room_wall', 10, 'Wall');
-- rollback DELETE FROM image_tag_group WHERE group_key = 'hallway_or_entry' and image_tag = 'Entrance';
-- rollback DELETE FROM image_tag WHERE id = 1729;
-- rollback DELETE FROM image_tag WHERE id = 712;
