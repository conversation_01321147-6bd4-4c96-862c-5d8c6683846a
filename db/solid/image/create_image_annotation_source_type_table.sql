-- liquibase formatted sql

-- changeset longting:create_image_annotation_source_type_table logicalFilePath:changelog.sql
CREATE TABLE image_annotation_source_type (
    id         int2 PRIMARY KEY,
    name       varchar(64) NOT NULL
);

INSERT INTO "public"."image_annotation_source_type" ("id", "name") VALUES (1, 'ORIGINAL');
INSERT INTO "public"."image_annotation_source_type" ("id", "name") VALUES (2, 'MAPPING');
INSERT INTO "public"."image_annotation_source_type" ("id", "name") VALUES (3, 'TAG');
INSERT INTO "public"."image_annotation_source_type" ("id", "name") VALUES (4, 'ADD');

-- rollback drop table image_annotation_source_type;
