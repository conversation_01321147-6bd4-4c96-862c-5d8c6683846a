-- liquibase formatted sql

-- changeset xiaojun:insert_image_tag_group_drone_roof logicalFilePath:changelog.sql
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_roof_overhead', 10, 'Detached Garage');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_roof_overhead', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_roof_overhead', 10, 'Overview');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_birdsview', 10, 'Detached Garage');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_birdsview', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_closeup', 10, 'Detached Garage');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_closeup', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_closeup', 10, 'Closeup');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_closeup_front', 10, 'Front');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_closeup_right', 10, 'Right');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_closeup_back', 10, 'Rear');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_closeup_left', 10, 'Left');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_roof_layer', 10, 'Detached Garage');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_roof_layer', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('detached_garage_roof_layer', 10, 'Roof Layer');


INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_roof_overhead', 10, 'Storage Shed');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_roof_overhead', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_roof_overhead', 10, 'Overview');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_birdsview', 10, 'Storage Shed');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_birdsview', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_closeup', 10, 'Storage Shed');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_closeup', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_closeup', 10, 'Closeup');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_closeup_front', 10, 'Front');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_closeup_right', 10, 'Right');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_closeup_back', 10, 'Rear');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_closeup_left', 10, 'Left');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_roof_layer', 10, 'Storage Shed');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_roof_layer', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('storage_shed_roof_layer', 10, 'Roof Layer');

INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_roof_overhead', 10, 'Guest House');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_roof_overhead', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_roof_overhead', 10, 'Overview');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_birdsview', 10, 'Guest House');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_birdsview', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_closeup', 10, 'Guest House');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_closeup', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_closeup', 10, 'Closeup');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_closeup_front', 10, 'Front');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_closeup_right', 10, 'Right');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_closeup_back', 10, 'Rear');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_closeup_left', 10, 'Left');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_roof_layer', 10, 'Guest House');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_roof_layer', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('guest_house_roof_layer', 10, 'Roof Layer');

INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_roof_overhead', 10, 'Barn');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_roof_overhead', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_roof_overhead', 10, 'Overview');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_birdsview', 10, 'Barn');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_birdsview', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_closeup', 10, 'Barn');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_closeup', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_closeup', 10, 'Closeup');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_closeup_front', 10, 'Front');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_closeup_right', 10, 'Right');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_closeup_back', 10, 'Rear');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_closeup_left', 10, 'Left');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_roof_layer', 10, 'Barn');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_roof_layer', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('barn_roof_layer', 10, 'Roof Layer');

INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_roof_overhead', 10, 'Carport');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_roof_overhead', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_roof_overhead', 10, 'Overview');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_birdsview', 10, 'Carport');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_birdsview', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_closeup', 10, 'Carport');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_closeup', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_closeup', 10, 'Closeup');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_closeup_front', 10, 'Front');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_closeup_right', 10, 'Right');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_closeup_back', 10, 'Rear');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_closeup_left', 10, 'Left');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_roof_layer', 10, 'Carport');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_roof_layer', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('carport_roof_layer', 10, 'Roof Layer');

INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_roof_overhead', 10, 'Gazebo');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_roof_overhead', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_roof_overhead', 10, 'Overview');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_birdsview', 10, 'Gazebo');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_birdsview', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_closeup', 10, 'Gazebo');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_closeup', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_closeup', 10, 'Closeup');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_closeup_front', 10, 'Front');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_closeup_right', 10, 'Right');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_closeup_back', 10, 'Rear');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_closeup_left', 10, 'Left');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_roof_layer', 10, 'Gazebo');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_roof_layer', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('gazebo_roof_layer', 10, 'Roof Layer');

INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_roof_overhead', 10, 'Other Structure');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_roof_overhead', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_roof_overhead', 10, 'Overview');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_birdsview', 10, 'Other Structure');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_birdsview', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_closeup', 10, 'Other Structure');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_closeup', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_closeup', 10, 'Closeup');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_closeup_front', 10, 'Front');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_closeup_right', 10, 'Right');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_closeup_back', 10, 'Rear');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_closeup_left', 10, 'Left');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_roof_layer', 10, 'Other Structure');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_roof_layer', 10, 'Roof');
INSERT INTO image_tag_group (group_key, group_type, image_tag) VALUES ('other_structure_roof_layer', 10, 'Roof Layer');

-- rollback DELETE FROM image_tag_group where group_key in ('detached_garage_roof_overhead','detached_garage_birdsview','detached_garage_closeup','detached_garage_roof_layer');
-- rollback DELETE FROM image_tag_group where group_key in ('detached_garage_closeup_front','detached_garage_closeup_right','detached_garage_closeup_back','detached_garage_closeup_left');
-- rollback DELETE FROM image_tag_group where group_key in ('storage_shed_roof_overhead','storage_shed_birdsview','storage_shed_closeup','storage_shed_roof_layer');
-- rollback DELETE FROM image_tag_group where group_key in ('storage_shed_closeup_front','storage_shed_closeup_right','storage_shed_closeup_back','storage_shed_closeup_left');
-- rollback DELETE FROM image_tag_group where group_key in ('guest_house_roof_overhead','guest_house_birdsview','guest_house_closeup','guest_house_roof_layer');
-- rollback DELETE FROM image_tag_group where group_key in ('guest_house_closeup_front','guest_house_closeup_right','guest_house_closeup_back','guest_house_closeup_left');
-- rollback DELETE FROM image_tag_group where group_key in ('barn_roof_overhead','barn_birdsview','barn_closeup','barn_roof_layer');
-- rollback DELETE FROM image_tag_group where group_key in ('barn_closeup_front','barn_closeup_right','barn_closeup_back','barn_closeup_left');
-- rollback DELETE FROM image_tag_group where group_key in ('carport_roof_overhead','carport_birdsview','carport_closeup','carport_roof_layer');
-- rollback DELETE FROM image_tag_group where group_key in ('carport_closeup_front','carport_closeup_right','carport_closeup_back','carport_closeup_left');
-- rollback DELETE FROM image_tag_group where group_key in ('gazebo_roof_overhead','gazebo_birdsview','gazebo_closeup','gazebo_roof_layer');
-- rollback DELETE FROM image_tag_group where group_key in ('gazebo_closeup_front','gazebo_closeup_right','gazebo_closeup_back','gazebo_closeup_left');
-- rollback DELETE FROM image_tag_group where group_key in ('other_structure_roof_overhead','other_structure_birdsview','other_structure_closeup','other_structure_roof_layer');
-- rollback DELETE FROM image_tag_group where group_key in ('other_structure_closeup_front','other_structure_closeup_right','other_structure_closeup_back','other_structure_closeup_left');

