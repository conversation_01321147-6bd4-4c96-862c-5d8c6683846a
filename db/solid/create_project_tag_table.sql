-- liquibase formatted sql
-- changeset mingcong:add_tag logicalFilePath:changelog.sql
CREATE TABLE project_tag
(
    id          BIGSERIAL PRIMARY KEY,
    title       varchar(64),
    description varchar(128),
    color       varchar(20),
    icon        varchar(128),
    updated_at  TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at  TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
CREATE INDEX ON project_tag (title);
COMMENT ON COLUMN project_tag.title IS '标题';
COMMENT ON COLUMN project_tag.description IS '说明';
COMMENT ON COLUMN project_tag.color IS '颜色';
COMMENT ON COLUMN project_tag.icon IS '图标';
-- rollback drop table project_tag

-- changeset mingcong:add_tag_group logicalFilePath:changelog.sql
CREATE TABLE project_tag_company
(
    id         BIGSERIAL PRIMARY KEY,
    company_id bigint,
    tag_id     bigint,
    tag_type   int4,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
CREATE INDEX ON project_tag_company (company_id);
CREATE INDEX ON project_tag_company (tag_type);
COMMENT ON COLUMN project_tag_company.company_id IS '公司Id';
COMMENT ON COLUMN project_tag_company.tag_id IS '标签Id';
COMMENT ON COLUMN project_tag_company.tag_type IS '标签类别';
-- rollback drop table project_tag_company

-- changeset mingcong:add_tag_project logicalFilePath:changelog.sql
CREATE TABLE project_tag_project
(
    id         BIGSERIAL PRIMARY KEY,
    project_id bigint,
    tag_id     bigint,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
CREATE INDEX ON project_tag_project (project_id);
COMMENT ON COLUMN project_tag_project.tag_id IS '标签Id';
-- rollback drop table project_tag_project
