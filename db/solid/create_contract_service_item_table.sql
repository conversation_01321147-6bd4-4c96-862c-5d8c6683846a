-- liquibase formatted sql
-- changeset shoushan:add_contract_service_item_table logicalFilePath:changelog.sql
CREATE TABLE contract_service (
  id BIGSERIAL PRIMARY KEY,
  key varchar(128) NOT NULL,
  display_name varchar(128) NOT NULL,
  default_price decimal(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE UNIQUE INDEX ON contract_service (key);

CREATE TRIGGER set_update_at
BEFORE UPDATE ON contract_service
FOR EACH ROW
EXECUTE PROCEDURE trigger_set_timestamp();

INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Full Inspection Base', 'Full Inspection Base', 275);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Claim Interior', 'Claim Interior', 25);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Claim based PCA', 'Claim based PCA', 50);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Non-Claim based PCA', 'Non-Claim based PCA', 75);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Non-Claim based PCA Interior', 'Non-Claim based PCA Interior', 10);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Erroneous Assignment', 'Erroneous Assignment', 50);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Additional Structures exceeding 3', 'Additional Structures exceeding 3', 50);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('HOVER - complete', 'HOVER - complete', 48);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Quick Inspectoin Base', 'Quick Inspectoin Base', 225);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Quick Inspectoin Interior', 'Quick Inspectoin Interior', 40);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('All Other Interior', 'All Other Interior', 25);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Catastrphe Fee', 'Catastrphe Fee', 125);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Away from Hive Location', 'Away from Hive Location', 25);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Benchmark Weather Report', 'Benchmark Weather Report', 10);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('HOVER - roof only', 'HOVER - roof only', 28);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Quick Inspectoin 10K discount', 'Quick Inspectoin 10K discount', -10);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Full Inspectoin 10K discount', 'Full Inspectoin 10K discount', -15);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Cat 1-3', 'Cat 1-3', 75);
INSERT INTO "public"."contract_service" ("key", "display_name", "default_price") VALUES ('Cat 4-5', 'Cat 4-5', 150);

-- rollback drop table contract_service;

-- changeset shoushan:add_contract_service_table logicalFilePath:changelog.sql
CREATE TABLE contract_service_item (
  contract_id int8 NOT NULL,
  service_id int8 NOT NULL,
  unit_price decimal(10,2),
  is_deleted bool NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  CONSTRAINT "contract_service_item_pkey" PRIMARY KEY ("contract_id", "service_id")
);

CREATE TRIGGER set_update_at
BEFORE UPDATE ON contract_service_item
FOR EACH ROW
EXECUTE PROCEDURE trigger_set_timestamp();
-- rollback drop table contract_service_item;
