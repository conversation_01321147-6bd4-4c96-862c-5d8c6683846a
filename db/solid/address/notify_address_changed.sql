-- liquibase formatted sql
-- changeset yangguanrong:notify_address_changed logicalFilePath:changelog.sql
CREATE OR REPLACE FUNCTION notify_address_changed() RETURNS TRIGGER
    LANGUAGE plpgsql
AS '
    DECLARE
        payload       TEXT;
        channel       TEXT := TG_ARGV[0];
    BEGIN
        payload = json_build_object(''old_address'', row_to_json(OLD), ''new_address'', row_to_json(NEW));
        PERFORM pg_notify(channel, payload);
        RETURN NULL;
    END;
';

DROP TRIGGER IF EXISTS notify_address_changed_trigger ON address;
CREATE CONSTRAINT TRIGGER notify_address_changed_trigger
    AFTER INSERT OR UPDATE
    ON address
    DEFERRABLE INITIALLY DEFERRED
    FOR EACH ROW
    EXECUTE PROCEDURE notify_address_changed('address_changed');
-- rollback DROP TRIGGER IF EXISTS notify_address_changed_trigger ON address;
-- rollback DROP FUNCTION IF EXISTS notify_address_changed();
