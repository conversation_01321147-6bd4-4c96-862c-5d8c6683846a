-- liquibase formatted sql
-- changeset chaoran:1 endDelimiter:\nGO logicalFilePath:changelog.sql
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
-- rollback drop function trigger_set_timestamp

-- changeset chaoran:2 logicalFilePath:changelog.sql
CREATE TABLE resource_key_alias (
	key VARCHAR(512) NOT NULL,
	alias VARCHAR(40) NOT NULL,
	uploaded BOOLEAN NOT NULL DEFAULT false,
	created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
	updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
       	PRIMARY KEY(key, alias)
);
-- rollback drop table resource_key_alias

-- changeset chaoran:3 logicalFilePath:changelog.sql
CREATE INDEX ix_resource_key_alias_key_created_at ON resource_key_alias (
	key,
       	created_at
);
-- rollback drop index ix_resource_key_alias_key_created_at

-- changeset chaoran:4 logicalFilePath:changelog.sql
CREATE TRIGGER set_timestamp
BEFORE UPDATE ON resource_key_alias
FOR EACH ROW
EXECUTE PROCEDURE trigger_set_timestamp();
-- rollback drop trigger set_timestamp ON resource_key_alias

