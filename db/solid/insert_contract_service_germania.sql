-- insert germania contract service item
DO
'
    DECLARE
        companyId      bigint;
        contractId     bigint;
        serviceId  bigint;
    BEGIN
        select id into companyId from customer where key = ''Germania Insurance'';
        for contractId in (select id from contract where insured_by = companyId) loop
            for serviceId in (SELECT id from contract_service where key in (''Full Inspection Base'',
                ''Claim Interior'',''Claim based PCA'',''Non-Claim based PCA'',''Non-Claim based PCA Interior'',
                ''Erroneous Assignment'',''Additional Structures exceeding 3'',''HOVER - complete'',
                ''Cat 1-3'',''Cat 4-5'')) loop

                    insert into "public".contract_service_item (contract_id, service_id) values (contractId, serviceId);
            END LOOP;
        END LOOP;
    END;
';
