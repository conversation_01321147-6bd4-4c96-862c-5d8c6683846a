-- liquibase formatted sql
-- changeset weizong:create_user_key_trigger logicalFilePath:changelog.sql

CREATE OR REPLACE FUNCTION update_user_key()
    RETURNS TRIGGER
    LANGUAGE plpgsql
AS '
DECLARE
    realm_id VARCHAR;
BEGIN
    -- insert realm id on conflict do update
    SELECT get_user_realm_id(NEW.realm, NEW.external_id) INTO realm_id;
    IF realm_id IS NOT NULL THEN
        INSERT INTO bees360_user_key(user_id, user_key, user_key_type) 
        VALUES (NEW.id, realm_id, ''REALM_ID'') ON CONFLICT (user_id, user_key_type)
        DO UPDATE SET user_key = realm_id;
    END IF;

    -- insert external id on conflict do update
    IF OLD.external_id IS NULL OR NEW.external_id != OLD.external_id THEN
        
        INSERT INTO bees360_user_key(user_id, user_key, user_key_type) 
        VALUES (NEW.id, NEW.external_id, ''EXTERNAL_ID'') ON CONFLICT (user_id, user_key_type)
        DO UPDATE SET user_key = NEW.external_id;
    END IF;
    
    -- insert id on conflict do update
    IF OLD.id IS NULL OR NEW.id != OLD.id THEN
        INSERT INTO bees360_user_key(user_id, user_key, user_key_type) 
        VALUES (NEW.id, NEW.id, ''ID'') ON CONFLICT (user_id, user_key_type)
        DO UPDATE SET user_key = NEW.id;
    END IF;
    RETURN NULL;
END;
';
