-- liquibase formatted sql
-- changeset weizong:rollback_add_user_key logicalFilePath:changelog.sql

DROP TRIGGER IF EXISTS add_user_to_user_key ON bees360_user;
DROP FUNCTION update_user_key;
DROP FUNCTION get_user_realm_id;
DROP TABLE IF EXISTS bees360_user_realm;
DROP TABLE IF EXISTS bees360_user_key;
DROP TYPE IF EXISTS user_key_type_enum;
UPDATE bees360_user set uid = id;
ALTER TABLE bees360_user ALTER COLUMN id TYPE VARCHAR(32) USING id::VARCHAR;
DROP SEQUENCE id_sequence CASCADE;
UPDATE bees360_user set id = external_id;
ALTER TABLE bees360_user DROP CONSTRAINT bees360_user_pkey;
ALTER TABLE bees360_user DROP CONSTRAINT external_id_unique;
ALTER TABLE bees360_user ADD CONSTRAINT bees360_user_pkey PRIMARY KEY (external_id);
UPDATE bees360_group set realm = NULL WHERE id = 'OVJlYWxtcw==' AND name = '9Realms';
UPDATE bees360_group set realm = NULL WHERE id = 'QmVlczM2MA==' AND name = 'Bees360';
