-- liquibase formatted sql
-- changeset yangguanrong:add_mail_template_table logicalFilePath:changelog.sql
CREATE TABLE mail_template (
  id BIGSERIAL PRIMARY KEY,
  template_key VARCHAR(64) NOT NULL,
  subject_template TEXT NOT NULL,
  content_template TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  unique(template_key)
);

CREATE TRIGGER set_mail_template_updated_at
  BEFORE UPDATE ON mail_template
  FOR EACH ROW
  EXECUTE PROCEDURE trigger_set_timestamp();
-- rollback drop table mail_template;
