CREATE
    OR REPLACE FUNCTION set_previous_to_failed_when_insert_running() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
    DECLARE
        failed  int4 := 3;
        running int4 := 1;
    BEGIN
        update job_execution
        set status = failed
        where job_id = NEW.job_id
          and status = running;
        return NEW;
    END;
';

DROP TRIGGER IF EXISTS set_previous_to_failed_when_insert_running ON job_execution;

CREATE TRIGGER set_previous_to_failed_when_insert_running
    BEFORE INSERT
    ON job_execution
    FOR EACH ROW
EXECUTE PROCEDURE set_previous_to_failed_when_insert_running();
