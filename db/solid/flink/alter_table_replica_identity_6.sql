-- liquibase formatted sql

-- changeset xiaojun:alter_table_replica_identity_project_group logicalFilePath:changelog.sql
ALTER TABLE public.project_group REPLICA IDENTITY FULL;
ALTER PUBLICATION dbz_publication ADD TABLE public.project_group;

ALTER TABLE public.report REPLICA IDENTITY FULL;
ALTER PUBLICATION dbz_publication ADD TABLE public.report;

ALTER TABLE public.report_group REPLICA IDENTITY FULL;
ALTER PUBLICATION dbz_publication ADD TABLE public.report_group;

ALTER TABLE public.report_type REPLICA IDENTITY FULL;
ALTER PUBLICATION dbz_publication ADD TABLE public.report_type;

-- rollback ALTER TABLE public.report_type REPLICA IDENTITY DEFAULT;
-- rollback ALTER PUBLICATION dbz_publication DROP TABLE public.report_type;
-- rollback ALTER TABLE public.report_group REPLICA IDENTITY DEFAULT;
-- rollback ALTER PUBLICATION dbz_publication DROP TABLE public.report_group;
-- rollback ALTER TABLE public.report REPLICA IDENTITY DEFAULT;
-- rollback ALTER PUBLICATION dbz_publication DROP TABLE public.report;
-- rollback ALTER TABLE public.project_group REPLICA IDENTITY DEFAULT;
-- rollback ALTER PUBLICATION dbz_publication DROP TABLE public.project_group;
