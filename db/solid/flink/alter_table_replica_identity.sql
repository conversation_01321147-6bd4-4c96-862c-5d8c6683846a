-- liquibase formatted sql

-- changeset mingcong:alter_table_replica_identity logicalFilePath:changelog.sql
ALTER TABLE public.project REPLICA IDENTITY FULL;
ALTER TABLE public.project_claim REPLICA IDENTITY FULL;
ALTER TABLE public.policy REPL<PERSON>A IDENTITY FULL;
ALTER TABLE public.customer REPLICA IDENTITY FULL;
ALTER TABLE public.contract REPLICA IDENTITY FULL;
ALTER TABLE public.project_invoice REPLICA IDENTITY FULL;
ALTER TABLE public.project_invoice_item REPLICA IDENTITY FULL;
ALTER TABLE public.project_invoice_receipt REPLICA IDENTITY FULL;
ALTER TABLE public.project_invoice_file REPLICA IDENTITY FULL;
ALTER TABLE public.address REPLICA IDENTITY FULL;
ALTER TABLE public.building REPLICA IDENTITY FULL;
ALTER TABLE public.project_contact REPLICA IDENTITY FULL;
ALTER TABLE public.project_cycle_time REPL<PERSON>A IDENTITY FULL;
ALTER TABLE public.project_cycle_time_type_enum REPLICA IDENTITY FULL;
ALTER TABLE public.policy_type REPLICA IDENTITY FULL;
ALTER TABLE databasechangelog REPLICA IDENTITY FULL;

-- rollback ALTER TABLE public.project REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.project_claim REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.policy REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.customer REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.contract REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.project_invoice REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.project_invoice_item REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.project_invoice_receipt REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.project_invoice_file REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.address REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.building REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.project_contact REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.project_cycle_time REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.project_cycle_time_type_enum REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE public.policy_type REPLICA IDENTITY DEFAULT;
-- rollback ALTER TABLE databasechangelog REPLICA IDENTITY DEFAULT;
