-- liquibase formatted sql
-- changeset zhoubaio:create_policy_type_table logicalFilePath:changelog.sql

-- 保险公司保单类型
CREATE TABLE IF NOT EXISTS policy_type
(
    id                    SERIAL                   PRIMARY KEY,
    name                  varchar(64)              not null,--保单类型名称
    created_at            TIMESTAMP                NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX ON policy_type (name);
-- rollback drop table policy_type
