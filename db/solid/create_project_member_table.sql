-- liquibase formatted sql

-- changeset zhoubiao:add_member logicalFilePath:changelog.sql
CREATE TABLE IF NOT EXISTS project_member
(
    id            BIGSERIAL   PRIMARY KEY,
    project_id    BIGINT      NOT NULL,
    user_id       VARCHAR(32) NOT NULL,
    role_id       SMALLINT    NOT NULL,
    updated_by    <PERSON><PERSON><PERSON><PERSON>(32) NOT NULL,
    created_at    TIMESTAMP   NOT NULL DEFAULT NOW(),
    updated_at    TIMESTAMP   NOT NULL DEFAULT NOW(),
    deleted       BOOL        NOT NULL DEFAULT FALSE,
    UNIQUE (project_id, role_id)
);
CREATE TRIGGER set_project_member_update_at
    BEFORE UPDATE ON project_member
    FOR EACH ROW
    EXECUTE PROCEDURE trigger_set_timestamp();
-- rollback drop table project_member
