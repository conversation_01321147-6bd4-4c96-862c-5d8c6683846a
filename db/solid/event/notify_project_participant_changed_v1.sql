CREATE OR REPLACE FUNCTION notify_project_participant_changed() RETURNS TRIGGER
LANGUAGE plpgsql
AS
'
DECLARE
    payload               JSON;
    event                 JSON;
    channel               VARCHAR;
BEGIN
    IF NEW.user_id IS NOT NULL THEN
        payload = json_build_object(
            ''project_id'', NEW.project_id::VARCHAR,
            ''user_id'', NEW.user_id,
            ''is_deleted'', false
        );
        channel = ''project_participant_changed.project_participant_added'';
    ELSE
        payload = json_build_object(
            ''project_id'', OLD.project_id::VARCHAR,
            ''user_id'', OLD.user_id,
            ''is_deleted'', true
        );
        channel = ''project_participant_changed.project_participant_deleted'';
    END IF;
    event = json_build_object(
        ''routing_key'', channel,
        ''payload'', payload
    );
    PERFORM pg_notify(''publish_event''::text, event::text);
    RETURN NULL;
END;
';
