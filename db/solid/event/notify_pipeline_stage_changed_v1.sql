CREATE OR REPLACE FUNCTION notify_pipeline_stage_changed()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
declare
  -- declare variable
    channel text := TG_ARGV[0];
    type text := TG_ARGV[0];
    old_state JSON;
    new_state JSON;
    payload TEXT;
    pipeline_id TEXT;
    updated_at TEXT;

BEGIN
     SELECT tmp.external_id, to_char(tmp.updated_at at time zone 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"')
     INTO pipeline_id, updated_at
     FROM (
        SELECT p.external_id, ps.updated_at FROM pipeline p
        JOIN pipeline_stage ps
        ON p.id = ps.pipeline_id
        where ps.id = NEW.id
     ) tmp;
      -- Build the payload
      old_state := json_build_object('status',OLD.status, 'owner_id', OLD.owner_id);
      new_state := json_build_object('status',NEW.status, 'owner_id', NEW.owner_id);
      payload  := json_build_object('pipeline_id', pipeline_id,'stage',NEW.stage, 'old_state',old_state, 'state', new_state, 'updated_at', updated_at);
       -- Notify the channel
      PERFORM pg_notify(channel,payload);
      RETURN NULL;
END;
$$;
