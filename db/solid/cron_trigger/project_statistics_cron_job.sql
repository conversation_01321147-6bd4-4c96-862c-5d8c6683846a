-- liquibase formatted sql
-- changeset jianjing:1 add daily_claim_invoice_statistics_trigger
-- schedule at GMT time 14:00:00 every day
select cron.schedule('scheduled_daily_job', '0 14 * * *',
$$
select pg_notify('daily_job_trigger',
json_build_object('trigger_time', to_char((current_timestamp at time zone 'UTC'),'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'))::text
)
$$);
-- rollback select cron.unschedule('scheduled_daily_job');
