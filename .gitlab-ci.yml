include:
  - project: 'engineers/ci-base'
    ref: main
    file: /public-ci/public-ci.yml

default:
  image: harbor.9realms.co/private/ci-base:latest
  tags:
    - sz-runner

variables:
  TARGET_BRANCH: master
  SONAR_TOKEN: sqp_a7c4f8472daee4874df11ee0721f478f62b5de8c
  SONAR_PROJECTKEY: engineers_solid_AYGyoZ9VNd7Sfji3mmOd
  HARBOR_PROJECT: solid
  PROFILENAME: master
  LIQUIBASE_COMMAND_SOLID_URL: ****************************************
  LIQUIBASE_COMMAND_ACTIVITY_URL: *******************************************
  EVENT_SERVER: "TRUE"

.make_migrate: &make_migrate
  script:
    - export LIQUIBASE_COMMAND_URL=${LIQUIBASE_COMMAND_SOLID_URL} && cd db/solid && liquibase --contexts="!test" update
    - cd $CI_PROJECT_DIR
    - export LIQUIBASE_COMMAND_URL=${LIQUIBASE_COMMAND_ACTIVITY_URL}
    - cd db/activity && liquibase --contexts="!test" update
    - cd $CI_PROJECT_DIR

.make_migrate_test: &make_migrate_test
  script:
    - export LIQUIBASE_COMMAND_URL=${LIQUIBASE_COMMAND_SOLID_URL} && cd db/solid && liquibase --contexts="test" update
    - cd $CI_PROJECT_DIR
    - export LIQUIBASE_COMMAND_URL=${LIQUIBASE_COMMAND_ACTIVITY_URL}
    - cd db/activity && liquibase --contexts="test" update
    - cd $CI_PROJECT_DIR

liquibase-release:
  script:
    - export NAME=liquibase-solid TAG=${CI_COMMIT_REF_NAME#release-}-RELEASE LIQUIBASE_PATH=solid
    - !reference [.liquibase_build_command, script]
    - export NAME=liquibase-activity TAG=${CI_COMMIT_REF_NAME#release-}-RELEASE LIQUIBASE_PATH=activity
    - !reference [.liquibase_build_command, script]

liquibase-deploy-snapshot:
  script:
    - export NAME=liquibase-solid TAG=${CI_COMMIT_REF_NAME#test-}-TEST LIQUIBASE_PATH=solid
    - !reference [.liquibase_build_command, script]
    - export NAME=liquibase-activity TAG=${CI_COMMIT_REF_NAME#test-}-TEST LIQUIBASE_PATH=activity
    - !reference [.liquibase_build_command, script]

liquibase-verify:
  script:
    - export LIQUIBASE_COMMAND_URL=${LIQUIBASE_COMMAND_SOLID_URL}
    - cd db/solid && liquibase tag init && liquibase.sh && liquibase rollback init
    - cd $CI_PROJECT_DIR
    - export LIQUIBASE_COMMAND_URL=${LIQUIBASE_COMMAND_ACTIVITY_URL}
    - cd db/activity && liquibase tag init && liquibase.sh && liquibase rollback init
