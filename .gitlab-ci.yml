stages:
  - pre-commit
  - commit
  - style
  - build
  - test
  - sonar-check
  - function
  - pre-release
  - release

default:
  image: harbor.9realms.co/private/ci-base:git-dde408e4
  tags:
    - sz-runner

variables:
  MAVEN_OPTS: >-
    -Dhttps.protocols=TLSv1.2 -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository
    -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN
    -Dorg.slf4j.simpleLogger.showDateTime=true
    -Djava.awt.headless=true
  MAVEN_CLI_OPTS: >-
    -P ci -Dchangelist=-SNAPSHOT -DENV=CI
    -s .m2/settings.xml --batch-mode --errors --fail-fast --show-version
    -DinstallAtEnd=true -DdeployAtEnd=true
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_CERTDIR: "/certs"
  S3PROXY_CORS_ALLOW_ORIGINS: ".*"
  S3PROXY_CORS_ALLOW_METHODS: "HEAD GET PUT OPTIONS DELETE"
  S3PROXY_CORS_ALLOW_HEADERS: "*"
  S3PROXY_MAXIMUM_TIMESKEW: "21600"
  RABBITMQ_DEFAULT_USER: mq_username
  RABBITMQ_DEFAULT_PASS: mq_password
  MYSQL_ROOT_PASSWORD: 123456
  ITEM_NAME: "solid"
  ES_JAVA_OPTS: "-Xms256m -Xmx256m -XX:MaxDirectMemorySize=256m"
  LIQUIBASE_COMMAND_USERNAME: liquibase
  LIQUIBASE_COMMAND_PASSWORD: liquibase
  LIQUIBASE_COMMAND_SOLID_URL: ****************************************
  USER_DB_PASSWORD: user_db_user_pass

workflow:
  rules:
    # No pipeline when there is a MR whose title starts with Draft
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TITLE =~ /^(\[Draft\]|\(Draft\)|Draft:)/
      when: never
      # Trigger a pipeline in case of MR event, which means the MR is ready, since we did not go through the first case
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_PIPELINE_SOURCE == "schedule"
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_TAG

.maven-cache: &maven-cache
  key: ${CI_COMMIT_REF_SLUG}-maven-cache
  paths:
    - .m2/repository

.sonar-cache: &sonar-cache
  key: ${CI_COMMIT_REF_SLUG}-sonar-cache
  paths:
    - .sonar/cache

.commit-base:
  image: harbor.9realms.co/private/git

check-user-email:
  stage: commit
  extends: .commit-base
  only:
    - merge_requests
  cache: { }
  script:
    - ./check/commit/check-user-email.sh

check-commit-message:
  stage: commit
  extends: .commit-base
  only:
    - merge_requests
  cache: { }
  script:
    - ./check/commit/check-commit-message.sh

check-commit-line:
  stage: commit
  extends: .commit-base
  only:
    - merge_requests
  cache: { }
  script:
    - ./check/commit/check-commit-line.sh

check-commit-times:
  stage: commit
  extends: .commit-base
  only:
    - merge_requests
  cache: { }
  script:
    - ./check/commit/check-commit-times.sh

check-newline-at-eof:
  stage: style
  only:
    - merge_requests
  cache: { }
  script:
    - ./check/style/check-newline-at-eof.sh .

check-yaml-format:
  stage: style
  only:
    refs:
      - merge_requests
    changes:
      - "**/*.{yml,yaml}"
  cache: { }
  script:
    - yamllint .

check-java-format:
  stage: style
  only:
    refs:
      - merge_requests
    changes:
      - "**/*.java"
  cache: { }
  script:
    ./check/style/check-java-format.sh

job:update-condeowners:
  stage: build
  image: harbor.9realms.co/private/python-ci
  variables:
    GIT_STRATEGY: clone
    GIT_DEPTH: 0
  only:
    refs:
      - schedules
    variables:
      - $SCHEDULE_TYPE == "updatecodeowners"
  script:
    - python python/generatecodeownersfile.py ${GITLAB_READ_TOKEN} ${GITLAB_WRITE_TOKEN}

liquibase-verify:
  stage: build
  image: harbor.9realms.co/private/liquibase:latest
  only:
    refs:
      - merge_requests
    changes:
      - db/**/*
  except:
    - master
    - /^release-.*$/i
    - test
    - prod
  services:
    - name: harbor.9realms.co/private/postgres-cron
      alias: postgres
      entrypoint:
        - "docker-entrypoint.sh"
        - "-c"
        - "config_file=/etc/postgres.conf"
    - name: harbor.9realms.co/private/mysql8
      alias: mysql
  cache: {}
  script:
    - export LIQUIBASE_COMMAND_URL=${LIQUIBASE_COMMAND_SOLID_URL}
    - cd db/solid && liquibase tag init && liquibase.sh && liquibase rollback init

maven-javadoc:
  stage: release
  only:
    refs:
      - master
    changes:
      - java/**/*.java
      - java/**/pom.xml
  except:
    - /^release-.*$/i
    - test
    - prod
  cache:
    - <<: *maven-cache
      policy: pull
  services:
    - name: harbor.9realms.co/private/postgres-cron
      alias: postgres
      entrypoint:
        - "docker-entrypoint.sh"
        - "-c"
        - "config_file=/etc/postgres.conf"
    - name: harbor.9realms.co/private/mysql8
      alias: mysql
  before_script:
    - make migrate
    - cd java
    - rm -rf target/site/apidocs
  script:
    - make javadoc
  allow_failure: true

sonarqube-check:
  stage: sonar-check
  except:
    - master
    - /^release-.*$/i
    - test
    - prod
  only:
    refs:
      - merge_requests
    changes:
      - java/**/*
  cache:
    - <<: *maven-cache
      policy: pull
  services:
    - name: harbor.9realms.co/private/postgres-cron
      alias: postgres
      entrypoint:
        - "docker-entrypoint.sh"
        - "-c"
        - "config_file=/etc/postgres.conf"
    - name: harbor.9realms.co/private/mysql8
      alias: mysql
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
    SONAR_TOKEN: "sqp_a7c4f8472daee4874df11ee0721f478f62b5de8c"
  before_script:
    - make migrate
  script:
    - >-
      cd java && mvn test-compile -T 4
      -P sonar sonar:sonar
      -Dsonar.login=$SONAR_USER
      -Dsonar.password=$SONAR_PASSWD
      -Dsonar.projectKey=engineers_solid_AYGyoZ9VNd7Sfji3mmOd
      -Dsonar.projectVersion=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
      -s .m2/settings.xml
  allow_failure: true

maven-verify:
  stage: test
  variables:
    FF_NETWORK_PER_BUILD: "true"     # activate container-to-container networking
    DOCKER_HOST: tcp://event-system:2375
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  except:
    - master
    - /^release-.*$/i
    - test
    - prod
  only:
    refs:
      - merge_requests
    changes:
      - java/**/*
      - db/**/*
  cache:
    - <<: *maven-cache
  services:
    - name: harbor.9realms.co/public/docker:20.10.16-dind
      alias: event-system
      # in our experience although you'd assume this would be sufficient, this did
      # nothing to prevent connection errors without `DOCKER_TLS_CERTDIR` being set
      # to an empty string, and I would call that beyond mildly infuriating.
      command: [ "--tls=false" ]
    - name: harbor.9realms.co/public/redis
      alias: redis
    - name: harbor.9realms.co/private/s3proxy
      alias: s3-primary
    - name: harbor.9realms.co/private/s3proxy
      alias: s3-secondary
    - name: harbor.9realms.co/private/mongo
      alias: mongo
    - name: harbor.9realms.co/private/postgres-cron
      alias: postgres
      entrypoint:
        - "docker-entrypoint.sh"
        - "-c"
        - "config_file=/etc/postgres.conf"
    - name: harbor.9realms.co/private/mysql8
      alias: mysql
    - name: harbor.9realms.co/private/rabbitmq
      alias: rabbitmq
    - name: harbor.9realms.co/public/firestore-emulator
      alias: firestore-emulator
    - name: harbor.9realms.co/public/elasticsearch
      alias: elasticsearch
      command:
        - "bin/elasticsearch"
        - "-Ediscovery.type=single-node"
        - "-Ebootstrap.memory_lock=true"
    - name: harbor.9realms.co/private/ci-opensearch
      alias: opensearch
    - name: harbor.9realms.co/public/sftp:debian
      alias: sftp
      command:
        - "sftp_user:s+ft%p_p?ass:1001::upload,upload2,OUT/Custom_Doc,OUT/Estimate,/"
  before_script:
    - make migrate-test
    - sysctl -w vm.max_map_count=262144
    - docker compose -f docker/event/docker-compose.yml up -d
  script:
    - make check
    - >-
      awk -F "," '{
        total_instructions += $4 + $5;
        covered_instructions += $5;
        total_lines += $8 + $9;
        covered_lines += $9;
      } END {
        print covered_instructions, "/" ,total_instructions, "instructions covered";
        print "instruction coverage: "100*covered_instructions/total_instructions"%";
        print covered_lines, "/" ,total_lines, "lines covered";
        print "line coverage: "100*covered_lines/total_lines"%";
      }'
      java/test-report-aggregate/target/site/jacoco-aggregate/jacoco.csv
  coverage: '/\d+.\d+ \% covered/'
  artifacts:
    paths:
      - "java/test-report-aggregate/target/site/jacoco-aggregate/index.html"
      - "java/test-report-aggregate/target/site/jacoco-aggregate/jacoco.xml"
    reports:
      junit:
        - "java/**/target/surefire-reports/TEST-*.xml"
        - "java/**/target/failsafe-reports/TEST-*.xml"

test-visualization:
  image: registry.gitlab.com/haynes/jacoco2cobertura:1.0.9
  stage: sonar-check
  allow_failure: true
  except:
    - master
    - /^release-.*$/i
    - test
    - prod
  only:
    refs:
      - merge_requests
    changes:
      - java/**/*
      - db/**/*
  cache: {}
  script:
    - >-
      python /opt/cover2cover.py
      java/test-report-aggregate/target/site/jacoco-aggregate/jacoco.xml
      $CI_PROJECT_DIR/src/main/java/ > java/test-report-aggregate/target/site/cobertura.xml
    - >-
      cat java/test-report-aggregate/target/site/jacoco-aggregate/index.html | grep -o '.*'
  coverage: "/Total.*?([0-9]{1,3})%/"
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: java/test-report-aggregate/target/site/cobertura.xml

maven-test-release:
  stage: release
  only:
    refs:
      - /^test-.*$/i
  except:
    - schedules
  cache:
    - <<: *maven-cache
      policy: pull
  services:
    - name: harbor.9realms.co/private/postgres-cron
      alias: postgres
      entrypoint:
        - "docker-entrypoint.sh"
        - "-c"
        - "config_file=/etc/postgres.conf"
    - name: harbor.9realms.co/private/mysql8
      alias: mysql
  before_script:
    - mkdir -p ~/.docker
    - echo $DOCKER_CONFIG > ~/.docker/config.json
    - make migrate
    - cd java
  script:
    - >-
      mvn $MAVEN_CLI_OPTS -P master deploy -T 2 -DskipTests
      -Dchangelist=-SNAPSHOT
      -Drevision=${CI_COMMIT_REF_NAME#test-}

liquibase-deploy-snapshot:
  stage: release
  image: harbor.9realms.co/public/docker:latest
  services:
    - name: harbor.9realms.co/public/docker:20.10.16-dind
      alias: docker
  only:
    refs:
      - /^test-.*$/i
  except:
    - schedules
  cache: {}
  before_script:
    - cd db
  script:
    - ./build-and-push.sh liquibase-solid  ${CI_COMMIT_REF_NAME#test-}-TEST solid

# 这个步骤存在问题，暂时关闭
# deploy-pages:
#   stage: release
#   only:
#     refs:
#       - master
#     changes:
#       - java/**/*
#   except:
#     - schedules
#   services:
#     - name: harbor.9realms.co/private/postgres-cron
#       alias: postgres
#       entrypoint:
#         - "docker-entrypoint.sh"
#         - "-c"
#         - "config_file=/etc/postgres.conf"
#     - name: harbor.9realms.co/private/mysql8
#       alias: mysql
#   before_script:
#     - make migrate
#     - cd java
#   script:
#     - mvn $MAVEN_CLI_OPTS -P master package -DskipTests
#     - mvn $MAVEN_CLI_OPTS -P master javadoc:aggregate
#     - mv target/site/apidocs public
#   artifacts:
#     paths:
#       - public

deploy-sonarqube:
  stage: release
  only:
    refs:
      - master
    changes:
      - java/**/*
  except:
    - schedules
  cache:
    - <<: *maven-cache
    - <<: *sonar-cache
  services:
    - name: harbor.9realms.co/private/postgres-cron
      alias: postgres
      entrypoint:
        - "docker-entrypoint.sh"
        - "-c"
        - "config_file=/etc/postgres.conf"
    - name: harbor.9realms.co/private/mysql8
      alias: mysql
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
    SONAR_TOKEN: "sqp_a7c4f8472daee4874df11ee0721f478f62b5de8c"
  before_script:
    - make migrate
  script:
    - >-
      cd java && mvn test-compile -T 4
      -P sonar sonar:sonar
      -Dsonar.login=$SONAR_USER
      -Dsonar.password=$SONAR_PASSWD
      -Dsonar.projectKey=engineers_solid_AYGyoZ9VNd7Sfji3mmOd
      -Dsonar.branch.name=master
      -s .m2/settings.xml

maven-release:
  stage: release
  only:
    refs:
      - /^release-.*$/i # run only on branch or tag that starts with 'release-'.
    changes:
      - java/**/*
  cache:
    - <<: *maven-cache
      policy: pull
  services:
    - name: harbor.9realms.co/private/postgres-cron
      alias: postgres
      entrypoint:
        - "docker-entrypoint.sh"
        - "-c"
        - "config_file=/etc/postgres.conf"
    - name: harbor.9realms.co/private/mysql8
      alias: mysql
  before_script:
    - mkdir -p ~/.docker
    - echo $DOCKER_CONFIG > ~/.docker/config.json
    - make migrate
    - cd java
  script:
    - >-
      mvn $MAVEN_CLI_OPTS -P release deploy -T 2 -DskipTests
      -Dchangelist=-RELEASE
      -Drevision=${CI_COMMIT_REF_NAME#release-}

liquibase-release:
  stage: release
  image: harbor.9realms.co/public/docker:latest
  services:
    - name: harbor.9realms.co/public/docker:20.10.16-dind
      alias: docker
  only:
    refs:
      - /^release-.*$/i
    changes:
      - db/**/*
  cache: {}
  before_script:
    - cd db
  script:
    - ./build-and-push.sh liquibase-solid ${CI_COMMIT_REF_NAME#release-}-RELEASE solid

pre-commit:
  stage: pre-commit
  only:
    refs:
      - merge_requests
  image: harbor.9realms.co/private/pre-commit:latest
  variables:
    PRE_COMMIT_HOME: ${CI_PROJECT_DIR}/.cache/pre-commit
  cache:
    key: pre-commit-cache
    paths:
      - ${PRE_COMMIT_HOME}
  before_script:
    - bash /opt/check_pre_commit_config.sh
  script:
    - pre-commit run --all-files --hook-stage commit

gitleaks-check:
  stage: pre-commit
  only:
    refs:
      - merge_requests
  image:
    name: harbor.9realms.co/public/gitleaks:latest
    entrypoint: [""]
  script:
    - gitleaks detect -c .gitleaks.toml --source="$CI_PROJECT_DIR" -v --no-git -r "$CI_PROJECT_DIR/gitleaks-report.json"
  artifacts:
    paths:
      - gitleaks-report.json
    expire_in: 1 week
    expose_as: 'gitleaks report'
    when: always

.build_docker_command:
  stage: release
  image: harbor.9realms.co/public/docker:latest
  services:
    - name: harbor.9realms.co/public/docker:20.10.16-dind
      alias: docker
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    HARBOR_REGISTRY_IMAGE: harbor.9realms.co/solid
  script:
    - docker login -u $HARBOR_REGISTRY_USER -p $HARBOR_REGISTRY_PASSWORD $HARBOR_CI_REGISTRY
    - docker build -f $DOCKEFILE -t $HARBOR_REGISTRY_IMAGE/$NAME:$CI_COMMIT_REF_NAME .
    - docker push $HARBOR_REGISTRY_IMAGE/$NAME:$CI_COMMIT_REF_NAME

.sbom-check:
  variables:
    SBOM_PATH: "${CI_PROJECT_DIR}/java/target/bom.xml"
    SCANMETHOD: Source_code_scanning
    PROJECT_NAME: solid/solid
  except:
    - schedules
  image: harbor.9realms.co/private/ci-base:git-dde408e4
  script:
    - |
      if [[ $dependency_check == "NO" ]];then
        echo check skip
      else
        mvn org.cyclonedx:cyclonedx-maven-plugin:2.9.1:makeAggregateBom -s .m2/settings.xml
        python3 /opt/python-script/sbom_handle.py --step=upload_sbom
      fi
  after_script:
    - python3 /opt/python-script/sbom_handle.py --step=upload_nocobase

.check_file_change:
  variables:
    GIT_STRATEGY: clone
    GIT_DEPTH: 0
  before_script:
    - cd $WORKDIR
    - target_branch_commit=`git ls-remote | grep refs/heads/${CI_DEFAULT_BRANCH}$ | awk '{print $1}'`
    - echo target_branch_commit $target_branch_commit
    - git diff --name-only $CI_COMMIT_SHA $target_branch_commit
    - change_files=`git diff --name-only $CI_COMMIT_SHA $target_branch_commit`
    - echo "change_files $change_files"
    - |
      if echo "$change_files" | egrep ${FILE_CHANGE}$;then
        echo There are dependency updates that need to be checked
      else
        echo Dependency is not updated, skip
        export dependency_check='NO'
      fi

sbom-check-mr:
  stage: test
  extends: .sbom-check
  variables:
    PAREN_PROJECT_NAME: solid/solid
    PROJECT_VERSION: ${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME}
    TIGGER_TYPE: "mr"
    WORKDIR: java
  before_script:
    - cd $WORKDIR
  only:
    refs:
      - merge_requests
    changes:
      - java/**/pom.xml

sbom-check-tag:
  stage: pre-release
  extends:
    - .check_file_change
    - .sbom-check
  variables:
    PAREN_PROJECT_NAME: solid/solid
    PROJECT_VERSION: ${CI_COMMIT_TAG}
    TIGGER_TYPE: "tag"
    WORKDIR: java
    FILE_CHANGE: "^java.*pom.xml"
  only:
    refs:
      - /^release-.*$/i
      - /^test-.*$/i
    changes:
      - java/**/pom.xml

sbom-check:
  stage: release
  extends: .sbom-check
  variables:
    PAREN_PROJECT_NAME: solid
    PROJECT_VERSION: master
    TIGGER_TYPE: "merge_event"
    WORKDIR: java
  before_script:
    - |
      cd $WORKDIR
      MERGE_INFO=$(curl --header "PRIVATE-TOKEN: ${GITLAB_READ_TOKEN}" \
      "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/repository/commits/${CI_COMMIT_SHA}/merge_requests")
      SOURCE_BRANCH=$(echo $MERGE_INFO | jq -r '.[0].source_branch')
      echo "Source branch was: $SOURCE_BRANCH"
      export SUB_PROJECT_VERSION=$SOURCE_BRANCH
  only:
    refs:
      - master
    changes:
      - java/**/pom.xml

.code-owner:
  cache: {}
  variables:
    GIT_STRATEGY: clone
    GIT_DEPTH: 0
  except:
    - schedules
  image: harbor.9realms.co/ops/ops-ci:latest
  only:
    refs:
      - merge_requests
  script:
    - python3 /opt/check_code_owner.py

check-commit-emails&&show-code-owner:
  stage: pre-commit
  variables:
    CODE_OWNERS_STEP: SHOW_CODE_OWNERS
  before_script:
    - python3 /opt/check_email.py
  extends:
    - .code-owner

check-code-owner:
  stage: release
  extends:
    - .code-owner
